import { Event } from '../types';

export const mockEvents: Event[] = [
  {
    id: '1',
    title: 'The Weeknd',
    artist: 'Concert',
    date: 'December 20, 6:00 PM',
    venue: 'Madison Square Garden',
    price: 'From $60',
    rating: 4.8,
    imageUrl: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=300&fit=crop',
    category: 'music',
    isFavorite: false
  },
  {
    id: '2',
    title: 'Madonna',
    artist: 'Concert',
    date: 'March 8, 6:00 PM',
    venue: 'Madison Square Garden',
    price: 'From $100',
    rating: 4.4,
    imageUrl: 'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?w=400&h=300&fit=crop',
    category: 'music',
    isFavorite: true
  },
  {
    id: '3',
    title: 'Ariana Grande',
    artist: 'Concert',
    date: 'November 30, 6:00 PM',
    venue: 'Madison Square Garden',
    price: 'From $120',
    rating: 4.4,
    imageUrl: 'https://images.unsplash.com/photo-1540039155733-5bb30b53aa14?w=400&h=300&fit=crop',
    category: 'music',
    isFavorite: false
  },
  {
    id: '4',
    title: 'Linkin Park',
    artist: 'Concert',
    date: 'June 2, 6:00 PM',
    venue: 'Madison Square Garden',
    price: 'From $120',
    rating: 4.9,
    imageUrl: 'https://images.unsplash.com/photo-1501386761578-eac5c94b800a?w=400&h=300&fit=crop',
    category: 'music',
    isFavorite: false
  },
  {
    id: '5',
    title: 'Billie Eilish',
    artist: 'Concert',
    date: 'March 6, 6:00 PM',
    venue: 'Madison Square Garden',
    price: 'From $80',
    rating: 4.7,
    imageUrl: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=300&fit=crop',
    category: 'music',
    isFavorite: true
  },
  {
    id: '6',
    title: 'Modern Art Exhibition',
    artist: 'Art Show',
    date: 'January 15, 2:00 PM',
    venue: 'Metropolitan Museum',
    price: 'From $25',
    rating: 4.6,
    imageUrl: 'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=400&h=300&fit=crop',
    category: 'art',
    isFavorite: false
  },
  {
    id: '7',
    title: 'Shakespeare Festival',
    artist: 'Theater',
    date: 'February 10, 7:30 PM',
    venue: 'Broadway Theater',
    price: 'From $45',
    rating: 4.5,
    imageUrl: 'https://images.unsplash.com/photo-1507924538820-ede94a04019d?w=400&h=300&fit=crop',
    category: 'theater',
    isFavorite: false
  },
  {
    id: '8',
    title: 'NBA Finals',
    artist: 'Sports',
    date: 'June 15, 8:00 PM',
    venue: 'Madison Square Garden',
    price: 'From $200',
    rating: 4.9,
    imageUrl: 'https://images.unsplash.com/photo-1546519638-68e109498ffc?w=400&h=300&fit=crop',
    category: 'sports',
    isFavorite: true
  },
  {
    id: '9',
    title: 'Food & Wine Festival',
    artist: 'Culinary Event',
    date: 'April 20, 12:00 PM',
    venue: 'Central Park',
    price: 'From $35',
    rating: 4.3,
    imageUrl: 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=400&h=300&fit=crop',
    category: 'food',
    isFavorite: false
  },
  {
    id: '10',
    title: 'Tech Conference 2024',
    artist: 'Business',
    date: 'May 5, 9:00 AM',
    venue: 'Convention Center',
    price: 'From $150',
    rating: 4.2,
    imageUrl: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87?w=400&h=300&fit=crop',
    category: 'tech',
    isFavorite: false
  }
];

export const getEventsByCategory = (category: string): Event[] => {
  if (category === 'all') return mockEvents;
  return mockEvents.filter(event => event.category === category);
};

export const getFavoriteEvents = (): Event[] => {
  return mockEvents.filter(event => event.isFavorite);
};

export const getUpcomingEvents = (): Event[] => {
  // For demo purposes, return first 6 events as "upcoming"
  return mockEvents.slice(0, 6);
};

export const getRecommendedEvents = (): Event[] => {
  // For demo purposes, return events with rating > 4.5 as "recommended"
  return mockEvents.filter(event => event.rating > 4.5);
};
