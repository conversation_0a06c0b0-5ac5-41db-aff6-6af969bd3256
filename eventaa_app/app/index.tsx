import React, { useState } from 'react';
import { View, StyleSheet, SafeAreaView, ScrollView, FlatList } from 'react-native';
import { useThemeColor } from '../hooks/useThemeColor';
import { SearchHeader } from '../components/home/<USER>';
import { CategoryFilter } from '../components/home/<USER>';
import { SectionHeader } from '../components/home/<USER>';
import { EventCard } from '../components/home/<USER>';
import { BottomNavigation } from '../components/navigation/BottomNavigation';
import { mockEvents, getEventsByCategory, getRecommendedEvents, getUpcomingEvents } from '../data/mockEvents';
import { router } from 'expo-router';

const HomeScreen = () => {
    const [selectedCategory, setSelectedCategory] = useState('music');
    const backgroundColor = useThemeColor({ light: '#f9fafb', dark: '#0f0f0f' }, 'background');

    const recommendedEvents = getRecommendedEvents();
    const upcomingEvents = getUpcomingEvents();

    const handleCategorySelect = (categoryId: string) => {
        setSelectedCategory(categoryId);
    };

    const handleEventPress = (eventId: string) => {
        console.log('Event pressed:', eventId);
        router.push('/onboarding')
    };

    const handleFavoritePress = (eventId: string, isFavorite: boolean) => {
        console.log('Favorite toggled:', eventId, isFavorite);
    };

    const renderEventCard = ({ item }: { item: any }) => (
        <EventCard
            id={item.id}
            title={item.title}
            artist={item.artist}
            date={item.date}
            venue={item.venue}
            price={item.price}
            rating={item.rating}
            imageUrl={item.imageUrl}
            category={item.category}
            onPress={() => handleEventPress(item.id)}
            onFavoritePress={handleFavoritePress}
            isFavorite={item.isFavorite}
        />
    );

    const renderHorizontalEventCard = ({ item }: { item: any }) => (
        <EventCard
            id={item.id}
            title={item.title}
            artist={item.artist}
            date={item.date}
            venue={item.venue}
            price={item.price}
            rating={item.rating}
            imageUrl={item.imageUrl}
            category={item.category}
            onPress={() => handleEventPress(item.id)}
            onFavoritePress={handleFavoritePress}
            isFavorite={item.isFavorite}
            horizontal={true}
        />
    );

    return (
        <SafeAreaView style={[styles.container, { backgroundColor }]}>
            <ScrollView showsVerticalScrollIndicator={false} style={styles.scrollView}>
                <SearchHeader
                    onSearchPress={() => console.log('Search pressed')}
                    onFilterPress={() => console.log('Filter pressed')}
                />

                <CategoryFilter
                    onCategorySelect={handleCategorySelect}
                    selectedCategory={selectedCategory}
                />

                <SectionHeader
                    title="Based on your interests"
                    onViewAllPress={() => console.log('View all interests')}
                />

                <FlatList
                    data={recommendedEvents}
                    renderItem={renderHorizontalEventCard}
                    keyExtractor={(item) => item.id}
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    contentContainerStyle={styles.eventsList}
                    ItemSeparatorComponent={() => <View style={styles.eventSeparator} />}
                />

                <SectionHeader
                    title="Upcoming events"
                    onViewAllPress={() => console.log('View all upcoming')}
                />

                <FlatList
                    data={upcomingEvents.slice(0, 4)}
                    renderItem={renderEventCard}
                    keyExtractor={(item) => `upcoming-${item.id}`}
                    numColumns={2}
                    scrollEnabled={false}
                    contentContainerStyle={styles.eventsGrid}
                    columnWrapperStyle={styles.eventsRow}
                />

                <View style={styles.bottomSpacing} />
            </ScrollView>

            <BottomNavigation
                onTabPress={(tabId) => console.log('Tab pressed:', tabId)}
                activeTab="events"
            />
        </SafeAreaView>
    );
};

export default HomeScreen;

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    scrollView: {
        flex: 1,
    },
    eventsList: {
        paddingHorizontal: 20,
    },
    eventSeparator: {
        width: 16,
    },
    eventsGrid: {
        paddingHorizontal: 20,
    },
    eventsRow: {
        justifyContent: 'space-between',
    },
    bottomSpacing: {
        height: 20,
    },
});
