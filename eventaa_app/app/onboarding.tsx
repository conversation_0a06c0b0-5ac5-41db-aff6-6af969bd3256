import React from 'react';
import { OnboardingScreen } from '../components/onboarding/OnboardingScreen';
import { router } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';

const OnboardingPage = () => {
  const handleComplete = async () => {
    try {
      await AsyncStorage.setItem('hasSeenOnboarding', 'true');
      router.replace('/');
    } catch (error) {
      console.error('Error saving onboarding status:', error);
      router.replace('/');
    }
  };

  const handleSkip = async () => {
    try {
      await AsyncStorage.setItem('hasSeenOnboarding', 'true');
      router.replace('/');
    } catch (error) {
      console.error('Error saving onboarding status:', error);
      router.replace('/');
    }
  };

  return (
    <OnboardingScreen
      onComplete={handleComplete}
      onSkip={handleSkip}
    />
  );
};

export default OnboardingPage;
