import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { SafeAreaView, TouchableOpacity, View, Text } from 'react-native';
import 'react-native-reanimated';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Colors } from '@/constants/Colors';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Feather, Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useState } from 'react';

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const [loaded] = useFonts({
    'Macan-Regular': require('../assets/fonts/MacanPanWeb-Regular.ttf'),
    'Macan-Medium': require('../assets/fonts/MacanPanWeb-Medium.ttf'),
    'Macan-Bold': require('../assets/fonts/MacanPanWeb-Bold.ttf'),
    'Macan-Light': require('../assets/fonts/MacanPanWeb-Light.ttf'),
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  const [notificationCount, setNotificationCount] = useState(3);
  const [chatCount, setChatCount] = useState(2);

  const handleSearchPress = () => {
    console.log('Search pressed');
  };

  const handleNotificationsPress = () => {
    console.log('Notifications pressed');
  };

  const handleChatPress = () => {
    console.log('Chat pressed');
  };

  const Badge = ({ count }: { count: number }) => {
    if (count === 0) return null;

    return (
      <View style={{
        position: 'absolute',
        top: -2,
        right: -2,
        backgroundColor: '#ef4444',
        borderRadius: 10,
        minWidth: 20,
        height: 20,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 4,
      }}>
        <Text style={{
          color: 'white',
          fontSize: 12,
          fontWeight: 'bold',
          textAlign: 'center',
        }}>
          {count > 99 ? '99+' : count.toString()}
        </Text>
      </View>
    );
  };

  const HeaderRightButtons = () => (
    <View style={{ flexDirection: 'row', alignItems: 'center', marginRight: 16 }}>
      <TouchableOpacity
        onPress={handleSearchPress}
        style={{ padding: 8 }}
        activeOpacity={0.7}
      >
        <Feather
          name="search"
          size={24}
          color={colorScheme === 'dark' ? colors.text : colors.text}
        />
      </TouchableOpacity>

      <TouchableOpacity
        onPress={handleNotificationsPress}
        style={{ padding: 8, position: 'relative' }}
        activeOpacity={0.7}
      >
        <Ionicons
          name="notifications-outline"
          size={26}
          color={colorScheme === 'dark' ? colors.text : colors.text}
        />
        <Badge count={notificationCount} />
      </TouchableOpacity>

      <TouchableOpacity
        onPress={handleChatPress}
        style={{ padding: 8, position: 'relative' }}
        activeOpacity={0.7}
      >
        <Ionicons
          name="chatbox-outline"
          size={26}
          color={colorScheme === 'dark' ? colors.text : colors.text}
        />
        <Badge count={chatCount} />
      </TouchableOpacity>
    </View>
  );

  if (!loaded) {
    return null;
  }

  return (
    <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
      <SafeAreaView style={{ flex: 1, backgroundColor: colors.background }}>
        <Stack screenOptions={{
          headerShown: false,
        }}>
          <Stack.Screen
            name="index"
          />
          <Stack.Screen name="onboarding"/>
          <Stack.Screen name="+not-found" />
        </Stack>
      </SafeAreaView>
    </ThemeProvider>
  );
}
