const primaryColor = '#5D3FD3';
const primaryHoverColor = '#4C2FC7';
const primaryLightColor = '#E8E4FF';
const primaryDarkLightColor = '#3B1F8B';

export const Colors = {
  light: {
    text: '#111827',
    textSecondary: '#4b5563',
    textMuted: '#6b7280',
    textLight: '#9ca3af',
    background: '#f9fafb',
    card: '#ffffff',
    border: '#e5e7eb',
    borderLight: '#f3f4f6',

    primary: primaryColor,
    primaryHover: primaryHoverColor,
    primaryLight: primaryLightColor,
    secondary: '#6366f1',
    success: '#10b981',
    warning: '#f97316',
    danger: '#ef4444',
    info: '#0ea5e9',

    tabIconDefault: '#9ca3af',
    tabIconSelected: primaryColor,
    tabBackground: '#ffffff',

    headerBackground: primaryColor,
    headerText: '#ffffff',
  },
  dark: {
    text: '#ffffff',
    textSecondary: '#d1d5db',
    textMuted: '#9ca3af',
    textLight: '#6b7280',
    background: '#0f0f0f',
    card: '#1a1a1a',
    border: '#2a2a2a',
    borderLight: '#3a3a3a',

    primary: primaryColor,
    primaryHover: primaryHoverColor,
    primaryLight: primaryDarkLightColor,
    secondary: '#818cf8',
    success: '#34d399',
    warning: '#fb923c',
    danger: '#f87171',
    info: '#38bdf8',

    tabIconDefault: '#71717a',
    tabIconSelected: primaryColor,
    tabBackground: '#27272a',

    headerBackground: primaryColor,
    headerText: '#ffffff',
  },
};
