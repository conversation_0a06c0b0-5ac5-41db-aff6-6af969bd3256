import { StyleSheet, Text, type TextProps } from 'react-native';

import { useThemeColor } from '@/hooks/useThemeColor';
import { Fonts } from '@/constants/Fonts';

export type ThemedTextProps = TextProps & {
  lightColor?: string;
  darkColor?: string;
  type?: 'default' | 'title' | 'defaultSemiBold' | 'subtitle' | 'link' | 'heading' | 'caption';
  weight?: 'regular' | 'medium' | 'bold' | 'light';
  size?: keyof typeof Fonts.size;
};

export function ThemedText({
  style,
  lightColor,
  darkColor,
  type = 'default',
  weight,
  size,
  ...rest
}: ThemedTextProps) {
  const color = useThemeColor({ light: lightColor, dark: darkColor }, 'text');

  let fontFamily = Fonts.regular.fontFamily;
  if (weight) {
    fontFamily = Fonts[weight].fontFamily;
  } else if (type === 'title' || type === 'subtitle' || type === 'heading') {
    fontFamily = Fonts.bold.fontFamily;
  } else if (type === 'defaultSemiBold') {
    fontFamily = Fonts.medium.fontFamily;
  }

  let fontSize = Fonts.size.base;
  if (size) {
    fontSize = Fonts.size[size];
  }

  return (
    <Text
      style={[
        { color, fontFamily },
        type === 'default' ? styles.default : undefined,
        type === 'title' ? styles.title : undefined,
        type === 'defaultSemiBold' ? styles.defaultSemiBold : undefined,
        type === 'subtitle' ? styles.subtitle : undefined,
        type === 'link' ? styles.link : undefined,
        type === 'heading' ? styles.heading : undefined,
        type === 'caption' ? styles.caption : undefined,
        size ? { fontSize } : undefined,
        style,
      ]}
      {...rest}
    />
  );
}

const styles = StyleSheet.create({
  default: {
    fontSize: Fonts.size.base,
    lineHeight: 24,
  },
  defaultSemiBold: {
    fontSize: Fonts.size.base,
    lineHeight: 24,
  },
  title: {
    fontSize: Fonts.size['3xl'],
    lineHeight: 36,
  },
  subtitle: {
    fontSize: Fonts.size.xl,
    lineHeight: 28,
  },
  heading: {
    fontSize: Fonts.size['2xl'],
    lineHeight: 32,
  },
  caption: {
    fontSize: Fonts.size.sm,
    lineHeight: 20,
  },
  link: {
    lineHeight: 24,
    fontSize: Fonts.size.base,
    color: '#ef4444', // Primary color
    textDecorationLine: 'underline',
  },
});
