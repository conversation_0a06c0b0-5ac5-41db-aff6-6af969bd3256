import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StatusBar,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColor } from '../../hooks/useThemeColor';

interface SignUpScreenProps {
  onSignUpSuccess: () => void;
  onLoginPress: () => void;
  onLocationRequest: () => void;
}

export const SignUpScreen: React.FC<SignUpScreenProps> = ({
  onSignUpSuccess,
  onLoginPress,
  onLocationRequest,
}) => {
  const [email, setEmail] = useState('');
  const [confirmationCode, setConfirmationCode] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [step, setStep] = useState<'email' | 'verification' | 'password'>('email');

  const backgroundColor = useThemeColor({ light: '#ffffff', dark: '#000000' }, 'background');
  const textColor = useThemeColor({ light: '#000000', dark: '#ffffff' }, 'text');

  const handleEmailSubmit = async () => {
    if (!email) {
      Alert.alert('Error', 'Please enter your email');
      return;
    }

    setIsLoading(true);
    try {
      // TODO: Implement actual email verification API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      setStep('verification');
    } catch (error) {
      Alert.alert('Error', 'Failed to send verification code. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerificationSubmit = async () => {
    if (!confirmationCode) {
      Alert.alert('Error', 'Please enter the confirmation code');
      return;
    }

    setIsLoading(true);
    try {
      // TODO: Implement actual verification API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      setStep('password');
    } catch (error) {
      Alert.alert('Error', 'Invalid confirmation code. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePasswordSubmit = async () => {
    if (!password || password.length < 6) {
      Alert.alert('Error', 'Password must be at least 6 characters');
      return;
    }

    setIsLoading(true);
    try {
      // TODO: Implement actual signup API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      onLocationRequest();
    } catch (error) {
      Alert.alert('Error', 'Sign up failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const renderEmailStep = () => (
    <>
      <View style={styles.inputContainer}>
        <Text style={[styles.label, { color: textColor }]}>Email</Text>
        <View style={styles.inputWrapper}>
          <Ionicons name="mail-outline" size={20} color="#9ca3af" style={styles.inputIcon} />
          <TextInput
            style={[styles.input, { color: textColor }]}
            placeholder="Enter your email"
            placeholderTextColor="#9ca3af"
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
            autoCorrect={false}
          />
        </View>
      </View>

      <TouchableOpacity
        style={[styles.submitButton, isLoading && styles.submitButtonDisabled]}
        onPress={handleEmailSubmit}
        disabled={isLoading}
      >
        <LinearGradient
          colors={['#5D3FD3', '#7C3AED']}
          style={styles.submitGradient}
        >
          <Text style={styles.submitButtonText}>
            {isLoading ? 'Sending...' : 'Send Verification Code'}
          </Text>
        </LinearGradient>
      </TouchableOpacity>
    </>
  );

  const renderVerificationStep = () => (
    <>
      <View style={styles.inputContainer}>
        <Text style={[styles.label, { color: textColor }]}>Confirmation Code</Text>
        <View style={styles.inputWrapper}>
          <Ionicons name="shield-checkmark-outline" size={20} color="#9ca3af" style={styles.inputIcon} />
          <TextInput
            style={[styles.input, { color: textColor }]}
            placeholder="Enter 6-digit code"
            placeholderTextColor="#9ca3af"
            value={confirmationCode}
            onChangeText={setConfirmationCode}
            keyboardType="number-pad"
            maxLength={6}
          />
        </View>
        <Text style={styles.helperText}>
          We sent a verification code to {email}
        </Text>
      </View>

      <TouchableOpacity
        style={[styles.submitButton, isLoading && styles.submitButtonDisabled]}
        onPress={handleVerificationSubmit}
        disabled={isLoading}
      >
        <LinearGradient
          colors={['#5D3FD3', '#7C3AED']}
          style={styles.submitGradient}
        >
          <Text style={styles.submitButtonText}>
            {isLoading ? 'Verifying...' : 'Verify Code'}
          </Text>
        </LinearGradient>
      </TouchableOpacity>
    </>
  );

  const renderPasswordStep = () => (
    <>
      <View style={styles.inputContainer}>
        <Text style={[styles.label, { color: textColor }]}>Password</Text>
        <View style={styles.inputWrapper}>
          <Ionicons name="lock-closed-outline" size={20} color="#9ca3af" style={styles.inputIcon} />
          <TextInput
            style={[styles.input, { color: textColor }]}
            placeholder="Create a password"
            placeholderTextColor="#9ca3af"
            value={password}
            onChangeText={setPassword}
            secureTextEntry={!showPassword}
            autoCapitalize="none"
            autoCorrect={false}
          />
          <TouchableOpacity
            onPress={() => setShowPassword(!showPassword)}
            style={styles.eyeIcon}
          >
            <Ionicons
              name={showPassword ? "eye-outline" : "eye-off-outline"}
              size={20}
              color="#9ca3af"
            />
          </TouchableOpacity>
        </View>
        <Text style={styles.helperText}>
          Password must be at least 6 characters
        </Text>
      </View>

      <TouchableOpacity
        style={[styles.submitButton, isLoading && styles.submitButtonDisabled]}
        onPress={handlePasswordSubmit}
        disabled={isLoading}
      >
        <LinearGradient
          colors={['#5D3FD3', '#7C3AED']}
          style={styles.submitGradient}
        >
          <Text style={styles.submitButtonText}>
            {isLoading ? 'Creating Account...' : 'Create Account'}
          </Text>
        </LinearGradient>
      </TouchableOpacity>
    </>
  );

  const getStepTitle = () => {
    switch (step) {
      case 'email':
        return 'Create Account';
      case 'verification':
        return 'Verify Email';
      case 'password':
        return 'Set Password';
      default:
        return 'Sign Up';
    }
  };

  const getStepSubtitle = () => {
    switch (step) {
      case 'email':
        return 'Enter your email to get started';
      case 'verification':
        return 'Check your email for the code';
      case 'password':
        return 'Create a secure password';
      default:
        return '';
    }
  };

  return (
    <View style={[styles.container, { backgroundColor }]}>
      <StatusBar barStyle="dark-content" backgroundColor={backgroundColor} />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.header}>
            <View style={styles.logoContainer}>
              <LinearGradient
                colors={['#5D3FD3', '#7C3AED']}
                style={styles.logoGradient}
              >
                <Ionicons name="calendar" size={32} color="#ffffff" />
              </LinearGradient>
            </View>
            <Text style={[styles.title, { color: textColor }]}>{getStepTitle()}</Text>
            <Text style={styles.subtitle}>{getStepSubtitle()}</Text>
          </View>

          <View style={styles.form}>
            {step === 'email' && renderEmailStep()}
            {step === 'verification' && renderVerificationStep()}
            {step === 'password' && renderPasswordStep()}
          </View>

          <View style={styles.footer}>
            <Text style={styles.footerText}>Already have an account? </Text>
            <TouchableOpacity onPress={onLoginPress}>
              <Text style={styles.loginText}>Sign In</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingTop: 60,
    paddingBottom: 40,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logoContainer: {
    marginBottom: 24,
  },
  logoGradient: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
    fontFamily: 'Macan-Bold',
  },
  subtitle: {
    fontSize: 16,
    color: '#6b7280',
    fontFamily: 'Macan-Regular',
  },
  form: {
    flex: 1,
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    fontFamily: 'Macan-Medium',
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 12,
    paddingHorizontal: 16,
    height: 56,
    backgroundColor: '#f9fafb',
  },
  inputIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
    fontFamily: 'Macan-Regular',
  },
  eyeIcon: {
    padding: 4,
  },
  helperText: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 4,
    fontFamily: 'Macan-Regular',
  },
  submitButton: {
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 24,
  },
  submitButtonDisabled: {
    opacity: 0.7,
  },
  submitGradient: {
    paddingVertical: 16,
    alignItems: 'center',
  },
  submitButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
    fontFamily: 'Macan-Medium',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  footerText: {
    color: '#6b7280',
    fontSize: 14,
    fontFamily: 'Macan-Regular',
  },
  loginText: {
    color: '#5D3FD3',
    fontSize: 14,
    fontWeight: '600',
    fontFamily: 'Macan-Medium',
  },
});
