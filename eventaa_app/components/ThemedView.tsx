import { View, type ViewProps } from 'react-native';

import { Colors } from '@/constants/Colors';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useColorScheme } from '@/hooks/useColorScheme';

export type ThemedViewProps = ViewProps & {
  lightColor?: string;
  darkColor?: string;
  variant?: 'default' | 'card' | 'header';
  useShadow?: boolean;
  borderColor?: keyof typeof Colors.light & keyof typeof Colors.dark;
};

export function ThemedView({
  style,
  lightColor,
  darkColor,
  variant = 'default',
  useShadow = false,
  borderColor,
  ...otherProps
}: ThemedViewProps) {
  const colorScheme = useColorScheme() ?? 'light';

  // Determine background color based on variant or explicit color props
  let backgroundColor;
  if (lightColor || darkColor) {
    backgroundColor = useThemeColor({ light: lightColor, dark: darkColor }, 'background');
  } else if (variant === 'card') {
    backgroundColor = Colors[colorScheme].card;
  } else if (variant === 'header') {
    backgroundColor = Colors[colorScheme].headerBackground;
  } else {
    backgroundColor = Colors[colorScheme].background;
  }

  // Apply border color if specified
  const borderStyle = borderColor ? {
    borderColor: Colors[colorScheme][borderColor],
    borderWidth: 1,
  } : undefined;

  // Apply shadow if requested
  const shadowStyle = useShadow ? (colorScheme === 'light' ? styles.lightShadow : styles.darkShadow) : undefined;

  return (
    <View
      style={[
        { backgroundColor },
        borderStyle,
        shadowStyle,
        style
      ]}
      {...otherProps}
    />
  );
}

const styles = {
  lightShadow: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  darkShadow: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 4,
  },
};
