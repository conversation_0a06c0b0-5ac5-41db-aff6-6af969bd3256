import React from 'react';
import { StyleSheet, View, ViewProps, ViewStyle, StyleProp } from 'react-native';
import { useThemeColor } from '../../hooks/useThemeColor';

export type CardProps = ViewProps & {
  children: React.ReactNode;
  style?: StyleProp<ViewStyle>;
  backgroundColor?: string;
  elevation?: number;
  borderRadius?: number;
  padding?: number;
};

export function Card({
  style,
  backgroundColor,
  children,
  elevation = 2,
  borderRadius = 8,
  padding = 16,
  ...otherProps
}: CardProps) {
  const defaultBackgroundColor = useThemeColor({ light: 'white', dark: '#1c1c1e' }, 'background');

  return (
    <View
      style={[
        styles.card,
        {
          backgroundColor: backgroundColor || defaultBackgroundColor,
          elevation: elevation,
          borderRadius: borderRadius,
          padding: padding,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: elevation / 2 },
          shadowOpacity: 0.1,
          shadowRadius: elevation,
        },
        style,
      ]}
      {...otherProps}
    >
      {children}
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    width: '100%',
    marginVertical: 8,
  },
});
