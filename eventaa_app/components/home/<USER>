import React, { useState } from 'react';
import { View, ScrollView, TouchableOpacity, StyleSheet } from 'react-native';
import { MaterialIcons, MaterialCommunityIcons, FontAwesome5, Ionicons } from '@expo/vector-icons';
import { useThemeColor } from '../../hooks/useThemeColor';
import { ThemedText } from '../ThemedText';
import { Category } from '../../types';

interface CategoryFilterProps {
  onCategorySelect?: (categoryId: string) => void;
  selectedCategory?: string;
}

export const CategoryFilter: React.FC<CategoryFilterProps> = ({
  onCategorySelect,
  selectedCategory = 'music'
}) => {
  const [activeCategory, setActiveCategory] = useState(selectedCategory);

  const backgroundColor = useThemeColor({ light: '#ffffff', dark: '#1a1a1a' }, 'card');
  const textColor = useThemeColor({ light: '#000000', dark: '#ffffff' }, 'text');
  const mutedTextColor = useThemeColor({ light: '#6b7280', dark: '#9ca3af' }, 'textMuted');

  const categories: Category[] = [
    {
      id: 'music',
      name: 'Music',
      icon: <MaterialIcons name="music-note" size={18} color={activeCategory === 'music' ? '#ffffff' : mutedTextColor} />
    },
    {
      id: 'art',
      name: 'Art',
      icon: <MaterialIcons name="palette" size={18} color={activeCategory === 'art' ? '#ffffff' : mutedTextColor} />
    },
    {
      id: 'theater',
      name: 'Theater',
      icon: <MaterialIcons name="theater-comedy" size={18} color={activeCategory === 'theater' ? '#ffffff' : mutedTextColor} />
    },
    {
      id: 'sports',
      name: 'Sports',
      icon: <MaterialIcons name="sports-soccer" size={18} color={activeCategory === 'sports' ? '#ffffff' : mutedTextColor} />
    },
    {
      id: 'food',
      name: 'Food',
      icon: <MaterialIcons name="restaurant" size={18} color={activeCategory === 'food' ? '#ffffff' : mutedTextColor} />
    },
    {
      id: 'business',
      name: 'Business',
      icon: <MaterialIcons name="business" size={18} color={activeCategory === 'business' ? '#ffffff' : mutedTextColor} />
    },
    {
      id: 'tech',
      name: 'Tech',
      icon: <MaterialIcons name="computer" size={18} color={activeCategory === 'tech' ? '#ffffff' : mutedTextColor} />
    }
  ];

  const handleCategoryPress = (categoryId: string) => {
    setActiveCategory(categoryId);
    onCategorySelect?.(categoryId);
  };

  const getCategoryStyle = (categoryId: string) => {
    const isActive = activeCategory === categoryId;
    return [
      styles.categoryChip,
      {
        backgroundColor: isActive ? '#5D3FD3' : backgroundColor,
        borderColor: isActive ? '#5D3FD3' : useThemeColor({ light: '#e5e7eb', dark: '#2a2a2a' }, 'border'),
      }
    ];
  };

  const getCategoryTextStyle = (categoryId: string) => {
    const isActive = activeCategory === categoryId;
    return [
      styles.categoryText,
      {
        color: isActive ? '#ffffff' : textColor,
      }
    ];
  };

  return (
    <View style={styles.container}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {categories.map((category) => (
          <TouchableOpacity
            key={category.id}
            style={getCategoryStyle(category.id)}
            onPress={() => handleCategoryPress(category.id)}
            activeOpacity={0.7}
          >
            {category.icon}
            <ThemedText style={getCategoryTextStyle(category.id)}>
              {category.name}
            </ThemedText>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 16,
  },
  scrollContent: {
    paddingHorizontal: 20,
    gap: 12,
  },
  categoryChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    gap: 8,
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '500',
  },
});
