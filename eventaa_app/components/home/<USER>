import React from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { useThemeColor } from '../../hooks/useThemeColor';
import { ThemedText } from '../ThemedText';

interface SectionHeaderProps {
  title: string;
  showViewAll?: boolean;
  onViewAllPress?: () => void;
  viewAllText?: string;
}

export const SectionHeader: React.FC<SectionHeaderProps> = ({
  title,
  showViewAll = true,
  onViewAllPress,
  viewAllText = "View all"
}) => {
  const textColor = useThemeColor({ light: '#000000', dark: '#ffffff' }, 'text');
  const linkColor = useThemeColor({ light: '#5D3FD3', dark: '#8B7CF6' }, 'primary');

  return (
    <View style={styles.container}>
      <ThemedText style={[styles.title, { color: textColor }]}>
        {title}
      </ThemedText>
      
      {showViewAll && (
        <TouchableOpacity 
          onPress={onViewAllPress}
          activeOpacity={0.7}
        >
          <ThemedText style={[styles.viewAllText, { color: linkColor }]}>
            {viewAllText}
          </ThemedText>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '500',
  },
});
