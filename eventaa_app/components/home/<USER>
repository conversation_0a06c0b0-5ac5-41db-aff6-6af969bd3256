import React, { useState } from 'react';
import { View, TouchableOpacity, Image, StyleSheet, Dimensions } from 'react-native';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useThemeColor } from '../../hooks/useThemeColor';
import { ThemedText } from '../ThemedText';

interface EventCardProps {
  id: string;
  title: string;
  artist?: string;
  date: string;
  venue: string;
  price: string;
  rating: number;
  imageUrl: string;
  category: string;
  onPress?: () => void;
  onFavoritePress?: (id: string, isFavorite: boolean) => void;
  isFavorite?: boolean;
  horizontal?: boolean;
}

const { width } = Dimensions.get('window');
const cardWidth = (width - 60) / 2; // 20px padding on each side + 20px gap
const horizontalCardWidth = width * 0.7; // For horizontal scrolling

export const EventCard: React.FC<EventCardProps> = ({
  id,
  title,
  artist,
  date,
  venue,
  price,
  rating,
  imageUrl,
  category,
  onPress,
  onFavoritePress,
  isFavorite = false,
  horizontal = false
}) => {
  const [favorite, setFavorite] = useState(isFavorite);

  const cardBackground = useThemeColor({ light: '#ffffff', dark: '#1a1a1a' }, 'card');
  const textColor = useThemeColor({ light: '#000000', dark: '#ffffff' }, 'text');
  const mutedTextColor = useThemeColor({ light: '#6b7280', dark: '#9ca3af' }, 'textMuted');

  const handleFavoritePress = () => {
    const newFavoriteState = !favorite;
    setFavorite(newFavoriteState);
    onFavoritePress?.(id, newFavoriteState);
  };

  const getPriceBadgeColor = () => {
    switch (category.toLowerCase()) {
      case 'music':
        return '#5D3FD3';
      case 'art':
        return '#10b981';
      case 'theater':
        return '#f97316';
      case 'sports':
        return '#ef4444';
      default:
        return '#5D3FD3';
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        { backgroundColor: cardBackground },
        horizontal ? { width: horizontalCardWidth } : { width: cardWidth }
      ]}
      onPress={onPress}
      activeOpacity={0.9}
    >
      <View style={styles.imageContainer}>
        <Image source={{ uri: imageUrl }} style={styles.image} />

        {/* Rating Badge */}
        <View style={styles.ratingBadge}>
          <Ionicons name="star" size={12} color="#FFD700" />
          <ThemedText style={styles.ratingText}>{rating.toFixed(1)}</ThemedText>
        </View>

        {/* Price Badge */}
        <View style={[styles.priceBadge, { backgroundColor: getPriceBadgeColor() }]}>
          <ThemedText style={styles.priceText}>{price}</ThemedText>
        </View>

        {/* Favorite Button */}
        <TouchableOpacity
          style={styles.favoriteButton}
          onPress={handleFavoritePress}
          activeOpacity={0.7}
        >
          <Ionicons
            name={favorite ? "heart" : "heart-outline"}
            size={20}
            color={favorite ? "#ef4444" : "#ffffff"}
          />
        </TouchableOpacity>
      </View>

      <View style={styles.content}>
        <ThemedText style={[styles.title, { color: textColor }]} numberOfLines={1}>
          {title}
        </ThemedText>

        {artist && (
          <ThemedText style={[styles.artist, { color: mutedTextColor }]} numberOfLines={1}>
            {artist}
          </ThemedText>
        )}

        <View style={styles.details}>
          <View style={styles.detailRow}>
            <Ionicons name="calendar-outline" size={14} color={mutedTextColor} />
            <ThemedText style={[styles.detailText, { color: mutedTextColor }]}>
              {date}
            </ThemedText>
          </View>

          <View style={styles.detailRow}>
            <Ionicons name="location-outline" size={14} color={mutedTextColor} />
            <ThemedText style={[styles.detailText, { color: mutedTextColor }]} numberOfLines={1}>
              {venue}
            </ThemedText>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    overflow: 'hidden',
    marginBottom: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  imageContainer: {
    position: 'relative',
    height: 140,
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  ratingBadge: {
    position: 'absolute',
    top: 8,
    left: 8,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: 6,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  ratingText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: '600',
  },
  priceBadge: {
    position: 'absolute',
    bottom: 8,
    left: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  priceText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: '600',
  },
  favoriteButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    padding: 12,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  artist: {
    fontSize: 14,
    marginBottom: 8,
  },
  details: {
    gap: 4,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  detailText: {
    fontSize: 12,
    flex: 1,
  },
});
