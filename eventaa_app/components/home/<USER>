import React from 'react';
import { View, TextInput, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useThemeColor } from '../../hooks/useThemeColor';
import { ThemedText } from '../ThemedText';

interface SearchHeaderProps {
  onSearchPress?: () => void;
  onFilterPress?: () => void;
  placeholder?: string;
}

export const SearchHeader: React.FC<SearchHeaderProps> = ({
  onSearchPress,
  onFilterPress,
  placeholder = "Discover"
}) => {
  const backgroundColor = useThemeColor({ light: '#ffffff', dark: '#1a1a1a' }, 'card');
  const textColor = useThemeColor({ light: '#000000', dark: '#ffffff' }, 'text');
  const placeholderColor = useThemeColor({ light: '#9ca3af', dark: '#6b7280' }, 'textMuted');
  const borderColor = useThemeColor({ light: '#e5e7eb', dark: '#2a2a2a' }, 'border');

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <ThemedText style={styles.appName}>Eventox.</ThemedText>
        <TouchableOpacity style={styles.locationButton}>
          <Ionicons name="location-outline" size={16} color="#5D3FD3" />
        </TouchableOpacity>
      </View>

      <View style={styles.searchContainer}>
        <TouchableOpacity
          style={[styles.searchBar, { backgroundColor, borderColor }]}
          onPress={onSearchPress}
          activeOpacity={0.7}
        >
          <Ionicons name="search" size={20} color={placeholderColor} style={styles.searchIcon} />
          <ThemedText style={[styles.searchPlaceholder, { color: placeholderColor }]}>
            {placeholder}
          </ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.filterButton}
          onPress={onFilterPress}
          activeOpacity={0.7}
        >
          <MaterialIcons name="tune" size={24} color={textColor} />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  appName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#5D3FD3',
  },
  locationButton: {
    padding: 8,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  searchBar: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 25,
    borderWidth: 1,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchPlaceholder: {
    fontSize: 16,
    flex: 1,
  },
  filterButton: {
    padding: 12,
  },
});
