import React, { useState } from 'react';
import { View, TouchableOpacity, StyleSheet, SafeAreaView } from 'react-native';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useThemeColor } from '../../hooks/useThemeColor';
import { ThemedText } from '../ThemedText';
import { TabItem } from '../../types';

interface BottomNavigationProps {
  onTabPress?: (tabId: string) => void;
  activeTab?: string;
}

export const BottomNavigation: React.FC<BottomNavigationProps> = ({
  onTabPress,
  activeTab = 'events'
}) => {
  const [currentTab, setCurrentTab] = useState(activeTab);

  const backgroundColor = useThemeColor({ light: '#ffffff', dark: '#1a1a1a' }, 'card');
  const textColor = useThemeColor({ light: '#6b7280', dark: '#9ca3af' }, 'textMuted');
  const activeColor = useThemeColor({ light: '#5D3FD3', dark: '#8B7CF6' }, 'primary');
  const borderColor = useThemeColor({ light: '#e5e7eb', dark: '#2a2a2a' }, 'border');

  const tabs: TabItem[] = [
    {
      id: 'events',
      label: 'Events',
      icon: 'calendar-outline',
      activeIcon: 'calendar'
    },
    {
      id: 'tickets',
      label: 'Tickets',
      icon: 'ticket-outline',
      activeIcon: 'ticket'
    },
    {
      id: 'favorites',
      label: 'Favorites',
      icon: 'heart-outline',
      activeIcon: 'heart'
    },
    {
      id: 'account',
      label: 'Account',
      icon: 'person-outline',
      activeIcon: 'person'
    }
  ];

  const handleTabPress = (tabId: string) => {
    setCurrentTab(tabId);
    onTabPress?.(tabId);
  };

  const getTabStyle = (tabId: string) => {
    const isActive = currentTab === tabId;
    return [
      styles.tab,
      isActive && styles.activeTab
    ];
  };

  const getIconColor = (tabId: string) => {
    return currentTab === tabId ? activeColor : textColor;
  };

  const getTextStyle = (tabId: string) => {
    const isActive = currentTab === tabId;
    return [
      styles.tabLabel,
      {
        color: isActive ? activeColor : textColor,
        fontWeight: isActive ? '600' : '400'
      }
    ];
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor, borderTopColor: borderColor }]}>
      <View style={styles.tabContainer}>
        {tabs.map((tab) => {
          const isActive = currentTab === tab.id;
          const iconName = isActive && tab.activeIcon ? tab.activeIcon : tab.icon;

          return (
            <TouchableOpacity
              key={tab.id}
              style={getTabStyle(tab.id)}
              onPress={() => handleTabPress(tab.id)}
              activeOpacity={0.7}
            >
              <Ionicons
                name={iconName as any}
                size={24}
                color={getIconColor(tab.id)}
              />
              <ThemedText style={getTextStyle(tab.id)}>
                {tab.label}
              </ThemedText>
            </TouchableOpacity>
          );
        })}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    borderTopWidth: 1,
    paddingTop: 8,
    paddingBottom: 4,
  },
  tabContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 4,
  },
  activeTab: {
    // Additional styling for active tab if needed
  },
  tabLabel: {
    fontSize: 12,
    marginTop: 4,
    textAlign: 'center',
  },
});
