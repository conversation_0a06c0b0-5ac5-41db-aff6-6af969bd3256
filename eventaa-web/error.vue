<template>
  <div class="min-h-screen bg-white dark:bg-zinc-800 flex items-center justify-center px-4">
    <div class="max-w-lg mx-auto text-center">
      <div class="mb-8">
        <img
          src="/icon.png"
          alt="EventaHub Logo"
          class="h-16 w-16 mx-auto rounded-full shadow-lg"
        >
      </div>

      <div class="mb-6">
        <h1 class="text-6xl font-bold text-red-600 dark:text-red-500 mb-2">
          {{ error.statusCode }}
        </h1>
        <h2 class="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
          {{ getErrorTitle() }}
        </h2>
        <p class="text-gray-600 dark:text-gray-400 text-lg">
          {{ getErrorMessage() }}
        </p>
      </div>

      <div class="space-y-4">
        <button
          @click="handleError"
          class="inline-block bg-red-600 hover:bg-red-700 text-white font-semibold py-3 px-8 transition-all duration-200 transform hover:scale-105 shadow-lg"
        >
          {{ getActionText() }}
        </button>

        <div>
          <NuxtLink
            to="/"
            class="inline-block bg-gray-600 hover:bg-gray-700 text-white font-semibold py-3 px-8 transition-all duration-200 text-base shadow-md"
          >
            Back to Home
          </NuxtLink>
        </div>
      </div>

      <div v-if="isDev && error.stack" class="mt-8 text-left">
        <details class="bg-gray-100 dark:bg-gray-700 p-4">
          <summary class="cursor-pointer font-medium text-gray-800 dark:text-gray-200">
            Technical Details (Development Only)
          </summary>
          <pre class="mt-4 text-xs text-gray-700 dark:text-gray-300 overflow-auto">{{ error.stack }}</pre>
        </details>
      </div>

      <div class="mt-12 text-center text-sm text-gray-500 dark:text-gray-400">
        <p>&copy; {{ new Date().getFullYear() }} EventaHub Malawi. All rights reserved.</p>
        <p class="mt-1">Powered by Brainy Technologies</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { NuxtError } from '#app'

const props = defineProps<{
  error: NuxtError
}>()

const isDev = process.dev

const getErrorTitle = () => {
  switch (props.error.statusCode) {
    case 404:
      return 'Page Not Found'
    case 500:
      return 'Internal Server Error'
    case 503:
      return 'Service Unavailable'
    case 403:
      return 'Access Forbidden'
    case 401:
      return 'Unauthorized'
    case 429:
      return 'Too Many Requests'
    default:
      return 'Something went wrong'
  }
}

const getErrorMessage = () => {
  switch (props.error.statusCode) {
    case 404:
      return "The page you're looking for doesn't exist. It might have been moved, deleted, or you entered the wrong URL."
    case 500:
      return "We're experiencing some technical difficulties. Our team has been notified and is working to fix this issue."
    case 503:
      return "We're temporarily unavailable due to maintenance. Please try again in a few minutes."
    case 403:
      return "You don't have permission to access this resource. Please contact support if you believe this is an error."
    case 401:
      return "You need to be logged in to access this page. Please sign in and try again."
    case 429:
      return "You've made too many requests. Please wait a moment before trying again."
    default:
      return props.error.statusMessage || "An unexpected error occurred. Please try again or contact support if the problem persists."
  }
}

const getActionText = () => {
  switch (props.error.statusCode) {
    case 404:
      return 'Go Back'
    case 500:
    case 503:
      return 'Try Again'
    case 401:
      return 'Sign In'
    case 403:
      return 'Contact Support'
    default:
      return 'Refresh Page'
  }
}

const handleError = () => {
  switch (props.error.statusCode) {
    case 404:
      window.history.back()
      break
    case 401:
      navigateTo('/get-started')
      break
    case 403:
      navigateTo('/contact-us')
      break
    case 500:
    case 503:
    default:
      clearError({ redirect: '/' })
      break
  }
}

useHead({
  title: `${props.error.statusCode} - ${getErrorTitle()} | EventaHub`,
  meta: [
    { name: 'description', content: getErrorMessage() },
    { name: 'robots', content: 'noindex, nofollow' }
  ]
})
</script>
