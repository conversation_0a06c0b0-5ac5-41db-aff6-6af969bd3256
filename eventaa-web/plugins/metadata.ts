export default defineNuxtPlugin((nuxtApp) => {
  const client = useHttpClient();

  type CategoriesResponse = any;
  type LocationResponse = any;

  const categories = ref<CategoriesResponse[]>([]);
  const location = ref<LocationResponse>({});

  const fetchCategories = async () => {
    try {
      const categoriesResponse = (await Promise.race([
        client.get<CategoriesResponse>(ENDPOINTS.CATEGORIES.BASE),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error("Categories fetch timeout")), 8000)
        ),
      ])) as CategoriesResponse;
      categories.value = categoriesResponse;
      console.log("Categories loaded successfully");
    } catch (error) {
      console.error("Failed to fetch categories:", error);
      categories.value = [];
    }
  };

  const fetchLocation = async () => {
    try {
      const response: any = await Promise.race([
        $fetch("https://api.ipify.org?format=json"),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error("IP fetch timeout")), 5000)
        ),
      ]);

      if (response?.ip) {
        const formData = new FormData();
        formData.append("ip", String(response.ip));

        const locationResponse = (await Promise.race([
          client.post<LocationResponse>(ENDPOINTS.METADATA.LOCATION, formData),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error("Location fetch timeout")), 8000)
          ),
        ])) as LocationResponse;

        location.value = locationResponse;
        console.log("Location metadata loaded successfully");
      }
    } catch (error) {
      console.error("Failed to fetch location:", error);
      location.value = {};
    }
  };

  Promise.all([
    fetchCategories().catch(() => {}),
    fetchLocation().catch(() => {}),
  ]);

  nuxtApp.provide("categories", categories);
  nuxtApp.provide("location", location);
});
