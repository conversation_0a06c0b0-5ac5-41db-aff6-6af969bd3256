import Echo from "laravel-echo";
import Pusher from "pusher-js";
import { useAuthStore } from "@/store/auth";
import { useVendorStore } from "@/store/vendor";

declare global {
  interface Window {
    Pusher: typeof Pusher;
  }
}

export default defineNuxtPlugin(() => {
  const runtimeConfig = useRuntimeConfig();
  const authStore = useAuthStore();
  window.Pusher = Pusher;

  let echo;

  try {
    const apiBaseUrl = runtimeConfig.public.baseUrl || "";
    const normalizedApiUrl = apiBaseUrl.replace("0.0.0.0", "localhost");
    const wsHost = runtimeConfig.public.reverbHost || "localhost";
    const wsPort = parseInt(runtimeConfig.public.reverbPort || "7001 ");
    const wsScheme = runtimeConfig.public.reverbScheme || "http";

    if (!authStore.token) {
      console.error("No auth token found, skipping Echo initialization");
      echo = null;
      return {
        provide: {
          echo: null,
        },
      };
    }

    const echoConfig = {
      broadcaster: "reverb" as const,
      key:
        runtimeConfig.public.reverbAppKey ||
        "qnJOXLTZdVHelWfwmRVERXg7X2dFpGDu",
      wsHost: wsHost,
      wsPort: wsPort ?? 7001 ,
      wssPort: wsPort ?? 443,
      forceTLS: wsScheme === "https",
      disableStats: false,
      enabledTransports: ["ws", "wss"],
      authEndpoint: `${normalizedApiUrl}broadcasting/auth`,
      auth: {
        headers: {
          Authorization: `Bearer ${authStore.token}`,
          Accept: "application/json",
        },
      },
    };

    echo = new Echo(echoConfig);

    echo.connector.pusher.connection.bind("connecting", () => {
      console.log("Echo attempting to connect...");
    });

    echo.connector.pusher.connection.bind("connected", () => {
      console.log("✅ Echo connected successfully!");
    });

    echo.connector.pusher.connection.bind("disconnected", () => {
      console.log("Echo disconnected");
    });

    echo.connector.pusher.connection.bind("error", (err: any) => {
      console.error("Echo connection error:", err);
      console.error("Error details:", {
        type: err?.type,
        error: err?.error,
        data: err?.data,
      });

      if (err?.type === "WebSocketError") {
        console.warn(
          "WebSocket connection failed. Please ensure the Reverb server is running on port 7001 "
        );
        console.warn(
          "Try accessing http://localhost:7001  in your browser to test if the server is reachable"
        );
        return;
      }

      if (
        err &&
        (err.type === "AuthError" ||
          err.status === 401 ||
          err.error?.status === 401)
      ) {
        console.error(
          "WebSocket authentication error detected, logging out user"
        );
        authStore.clearAuth();

        try {
          const vendorStore = useVendorStore();
          vendorStore.clearPermissions();
        } catch (e) {
          console.error("Failed to clear vendor permissions:", e);
        }

        try {
          const nuxtApp = useNuxtApp();
          if (nuxtApp.$toast) {
            (nuxtApp.$toast as any).error(
              "Your session has expired. Please log in again."
            );
          }
        } catch (e) {
          console.error("Failed to show toast:", e);
        }

        navigateTo("/", { replace: true });
      }
    });
  } catch (error) {
    console.error("Failed to initialize Echo:", error);
    echo = null;
  }

  authStore.$subscribe((_mutation, state) => {
    if (
      echo &&
      echo.connector &&
      echo.connector.options &&
      echo.connector.options.auth
    ) {
      echo.connector.options.auth.headers.Authorization = `Bearer ${
        state.token || ""
      }`;
    }
  });

  return {
    provide: {
      echo,
    },
  };
});
