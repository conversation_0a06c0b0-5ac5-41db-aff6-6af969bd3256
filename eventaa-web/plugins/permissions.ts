import { useAuthStore } from '@/store/auth';
import { useVendorStore } from '@/store/vendor';

export default defineNuxtPlugin(async () => {
  const permissionsLoading = ref<boolean>(false);
  const permissionsRetryCount = ref<number>(0);
  const MAX_RETRIES = 3;
  const RETRY_DELAY = 5000;

  const loadPermissions = async () => {
    const authStore = useAuthStore();
    const vendorStore = useVendorStore();

    if (!authStore.userIsAuthenticated || vendorStore.permissionsLoaded || permissionsLoading.value) {
      return;
    }

    permissionsLoading.value = true;

    try {
      console.log('Loading vendor permissions from plugin');
      const success = await vendorStore.fetchVendorPermissions();

      if (success) {
        console.log('Successfully loaded vendor permissions');
        permissionsRetryCount.value = 0;
      } else {
        throw new Error('Failed to load permissions');
      }
    } catch (error) {
      console.error('Error loading permissions:', error);

      if (permissionsRetryCount.value < MAX_RETRIES) {
        permissionsRetryCount.value++;
        console.log(`Retrying permission load (${permissionsRetryCount.value}/${MAX_RETRIES})`);

        setTimeout(() => {
          permissionsLoading.value = false;
          loadPermissions();
        }, RETRY_DELAY);
      }
    } finally {
      if (permissionsRetryCount.value === 0) {
        permissionsLoading.value = false;
      }
    }

    return vendorStore.permissionsLoaded;
  };

  const authStore = useAuthStore();

  if (authStore.userIsAuthenticated) {
    await loadPermissions();
  } else {
    watch(() => authStore.userIsAuthenticated, (isAuthenticated) => {
      if (isAuthenticated) {
        loadPermissions();
      }
    });
  }

  return {
    provide: {
      loadPermissions
    }
  };
});