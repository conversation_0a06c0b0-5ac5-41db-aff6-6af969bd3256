import { VueReCaptcha, useReCaptcha } from 'vue-recaptcha-v3'

export default defineNuxtPlugin((nuxtApp) => {
  const config = useRuntimeConfig()

  nuxtApp.vueApp.use(VueReCaptcha, {
    siteKey: config.public.recaptchaSiteKey || '6LfEd4crAAAAABGDKXXFC-TTk_wPoY49E_hV_SHB',
    loaderOptions: {
      useRecaptchaNet: false,
      autoHideBadge: false
    }
  })

  return {
    provide: {
      recaptcha: async (action: string) => {
        const recaptchaInstance = useReCaptcha()
        if (recaptchaInstance) {
          await recaptchaInstance.recaptchaLoaded()
          return await recaptchaInstance.executeRecaptcha(action)
        }
        throw new Error('reCAPTCHA not loaded')
      },
      recaptchaLoaded: async () => {
        const recaptchaInstance = useReCaptcha()
        if (recaptchaInstance) {
          return await recaptchaInstance.recaptchaLoaded()
        }
        throw new Error('reCAPTCHA not loaded')
      }
    }
  }
})
