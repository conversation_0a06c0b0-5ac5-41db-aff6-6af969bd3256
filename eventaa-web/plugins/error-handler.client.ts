/**
 * Global error handling plugin for EventaHub
 * Registers global error handlers for unhandled errors and promise rejections
 */

export default defineNuxtPlugin(() => {
  const { handleError, logError } = useErrorHandler()

  // Only run on client-side
  if (process.client) {
    // Handle unhandled JavaScript errors
    window.addEventListener('error', (event) => {
      logError({
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error
      }, 'Global error handler')

      // Optionally handle the error (redirect to error page)
      // handleError(500)
    })

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      logError({
        message: event.reason?.message || 'Unhandled promise rejection',
        reason: event.reason
      }, 'Unhandled promise rejection')

      // Optionally handle the error
      // handleError(500)
    })

    // Add error handling to fetch requests
    const originalFetch = window.fetch
    window.fetch = async (...args) => {
      try {
        const response = await originalFetch(...args)

        // Handle HTTP error responses
        if (!response.ok) {
          const error = new Error(`HTTP ${response.status}: ${response.statusText}`)
          ;(error as any).status = response.status
          ;(error as any).statusText = response.statusText
          ;(error as any).url = response.url

          logError(error, 'Fetch request failed')

          // You might want to handle specific status codes differently
          // For example, redirect to login page for 401 errors
        }

        return response
      } catch (error) {
        logError(error, 'Fetch network error')
        throw error
      }
    }
  }

  // Register global error handler for Nuxt
  if (process.server) {
    // Server-side error handling
    process.on('uncaughtException', (error) => {
      logError(error, 'Uncaught exception (server)')
      console.error('Uncaught Exception:', error)
    })

    process.on('unhandledRejection', (reason, promise) => {
      logError({
        message: 'Unhandled rejection',
        reason,
        promise
      }, 'Unhandled rejection (server)')
      console.error('Unhandled Rejection at:', promise, 'reason:', reason)
    })
  }
})
