import { Bold, Essentials, Heading, BlockQuote, Table, MediaEmbed, Font, FontColor, FontSize, Italic, Mention, Paragraph, Undo, Link, InputNumberView, List, } from 'ckeditor5';

export const editorConfig = {
    plugins: [Bold, Essentials, Heading, Italic, BlockQuote, Table, Font, FontColor, FontSize, MediaEmbed, Mention, Paragraph, Undo, Link, InputNumberView, List],
    toolbar: ['heading',
        '|',
        'bold',
        'italic',
        'link',
        'bulletedList',
        'numberedList',
        'blockQuote',
        'insertTable',
        'mediaEmbed',
        'undo',
        'redo',
        'imageUpload',
        'fontSize',
        'fontColor',
        'highlight'],
}