import { forEach } from "lodash";

type ValidationErrorResponse = {
    errors: Record<string, string[]>;
};

const errorHandlers = {
    getErrorMessages: (obj: any): string[] => {
        if (!obj) return [];
        if (typeof obj === 'object' && obj !== null) {
            return Object.values(obj).flatMap(value => {
                if (Array.isArray(value)) return value;
                if (typeof value === 'string') return [value];
                if (typeof value === 'object' && value !== null) return errorHandlers.getErrorMessages(value);
                return [];
            });
        }
        return [];
    },
    formatErrors: (response: ValidationErrorResponse): string[] => {
        if (!response || !response.errors) return [];
        return Object.values(response.errors).flat();
    },
    parseError: (error: any): Object => {
        return error.message || 'An unexpected error occurred';
    }
};

const handleError = (error: any, toast: any): void => {
    const messages = errorHandlers.parseError(error);
    const errorMessages = errorHandlers.formatErrors(messages as ValidationErrorResponse);
    forEach(errorMessages, (message) => toast.error(message));
};

export const handleErrorWithToast = (error: any, toast: any): void => {
    handleError(error, toast);
};