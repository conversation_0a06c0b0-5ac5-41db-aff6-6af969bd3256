/**
 * Currency formatting utilities
 */

export const CURRENCY_SYMBOL = 'MWK';
export const CURRENCY_CODE = 'MWK';

/**
 * Format a number as Malawian Kwacha currency
 * @param value - The numeric value to format
 * @param options - Formatting options
 * @returns Formatted currency string
 */
export function formatCurrency(
  value: number | string | null | undefined,
  options: {
    showSymbol?: boolean;
    decimals?: number;
    compact?: boolean;
  } = {}
): string {
  const {
    showSymbol = true,
    decimals = 2,
    compact = false
  } = options;

  // Handle null, undefined, or invalid values
  const numValue = Number(value) || 0;

  if (compact && numValue >= 1000000) {
    const millions = numValue / 1000000;
    const formatted = millions.toFixed(1);
    return showSymbol ? `MWK ${formatted}M` : `${formatted}M`;
  }

  if (compact && numValue >= 1000) {
    const thousands = numValue / 1000;
    const formatted = thousands.toFixed(1);
    return showSymbol ? `MWK ${formatted}K` : `${formatted}K`;
  }

  // Format with commas and specified decimal places
  const formatted = numValue.toLocaleString('en-US', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  });

  return showSymbol ? `MWK ${formatted}` : formatted;
}

/**
 * Format currency for display in charts and stats
 * @param value - The numeric value to format
 * @returns Formatted currency string
 */
export function formatCurrencyCompact(value: number | string | null | undefined): string {
  return formatCurrency(value, { compact: true });
}

/**
 * Format currency for detailed views (with full precision)
 * @param value - The numeric value to format
 * @returns Formatted currency string
 */
export function formatCurrencyDetailed(value: number | string | null | undefined): string {
  return formatCurrency(value, { decimals: 2 });
}
