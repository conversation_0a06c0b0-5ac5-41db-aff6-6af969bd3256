#!/bin/bash

# EventaHub Frontend PM2 Setup Script
# This script sets up PM2 for managing the frontend applications

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration variables
WEB_APP_NAME="eventahub-web"
WEB_APP_DIR="/var/www/eventahub/eventaa-web"
APP_USER="www-data"
NODE_VERSION="18"
WEB_PORT="4000"
DOMAIN="eventahub.com"

echo -e "${BLUE}======================================${NC}"
echo -e "${BLUE}   EventaHub Frontend PM2 Setup     ${NC}"
echo -e "${BLUE}======================================${NC}"

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   print_error "This script must be run as root"
   exit 1
fi

print_status "Starting EventaHub Frontend PM2 setup..."

# Update system packages
print_status "Updating system packages..."
apt update

# Install Node.js and npm if not installed
print_status "Installing Node.js and npm..."
if ! command -v node &> /dev/null; then
    curl -fsSL https://deb.nodesource.com/setup_${NODE_VERSION}.x | bash -
    apt-get install -y nodejs
else
    print_status "Node.js already installed: $(node --version)"
fi

# Install PM2 globally
print_status "Installing PM2..."
npm install -g pm2

# Install PM2 logrotate
npm install -g pm2-logrotate

# Configure PM2 logrotate
pm2 install pm2-logrotate
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 7
pm2 set pm2-logrotate:compress true
pm2 set pm2-logrotate:dateFormat YYYY-MM-DD_HH-mm-ss

# Create application directories if they don't exist
print_status "Creating application directories..."
mkdir -p $WEB_APP_DIR

# Set proper ownership
chown -R $APP_USER:$APP_USER $(dirname $WEB_APP_DIR)

# Create PM2 ecosystem configuration
print_status "Creating PM2 ecosystem configuration..."
cat > /var/www/eventahub/ecosystem.config.js << EOF
module.exports = {
  apps: [
    {
      name: '${WEB_APP_NAME}',
      script: 'npm',
      args: 'start',
      cwd: '${WEB_APP_DIR}',
      instances: 2,
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: ${WEB_PORT},
        HOST: '0.0.0.0',
        API_BASE_URL: 'http://localhost:8000/api',
        BASE_URL: 'https://${DOMAIN}',
        WS_HOST: '${DOMAIN}:7001 ',
        VITE_REVERB_APP_KEY: 'eventahub',
        VITE_REVERB_HOST: '${DOMAIN}',
        VITE_REVERB_PORT: 7001 ,
        VITE_REVERB_SCHEME: 'ws'
      },
      env_development: {
        NODE_ENV: 'development',
        PORT: 4000,
        HOST: '0.0.0.0'
      },
      error_file: '/var/log/pm2/${WEB_APP_NAME}-error.log',
      out_file: '/var/log/pm2/${WEB_APP_NAME}-out.log',
      log_file: '/var/log/pm2/${WEB_APP_NAME}-combined.log',
      time: true,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      min_uptime: '10s',
      max_restarts: 10,
      restart_delay: 4000
    }
  ]
};
EOF

# Create PM2 startup script
print_status "Creating PM2 startup script..."
cat > /usr/local/bin/eventahub-frontend-pm2 << 'EOF'
#!/bin/bash

# EventaHub Frontend PM2 Management Script

ECOSYSTEM_FILE="/var/www/eventahub/ecosystem.config.js"
WEB_APP_NAME="eventahub-web"

case "$1" in
    start)
        echo "Starting EventaHub web application..."
        pm2 start $ECOSYSTEM_FILE
        pm2 save
        ;;
    stop)
        echo "Stopping EventaHub web application..."
        pm2 stop $WEB_APP_NAME
        ;;
    restart)
        echo "Restarting EventaHub web application..."
        pm2 restart $WEB_APP_NAME
        ;;
    reload)
        echo "Reloading EventaHub web application..."
        pm2 reload $WEB_APP_NAME
        ;;
    status)
        pm2 status
        ;;
    logs)
        pm2 logs
        ;;
    web-logs)
        pm2 logs $WEB_APP_NAME
        ;;
    monit)
        pm2 monit
        ;;
    delete)
        echo "Deleting EventaHub web application..."
        pm2 delete $WEB_APP_NAME
        ;;
    deploy-web)
        echo "Deploying web application..."
        cd ${WEB_APP_DIR}
        git pull origin main
        npm ci --only=production
        npm run build
        pm2 reload $WEB_APP_NAME
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|reload|status|logs|web-logs|monit|delete|deploy-web}"
        exit 1
        ;;
esac

exit 0
EOF

chmod +x /usr/local/bin/eventahub-frontend-pm2

# Create PM2 log directory
mkdir -p /var/log/pm2
chown -R $APP_USER:$APP_USER /var/log/pm2

# Create systemd service for PM2
print_status "Creating systemd service for PM2..."
cat > /etc/systemd/system/eventahub-frontend.service << EOF
[Unit]
Description=EventaHub Frontend PM2 Service
After=network.target

[Service]
Type=forking
User=$APP_USER
Group=$APP_USER
Environment=PATH=/usr/bin:/usr/local/bin
Environment=PM2_HOME=/home/<USER>/.pm2
WorkingDirectory=/var/www/eventahub

ExecStart=/usr/local/bin/pm2 start ecosystem.config.js
ExecReload=/usr/local/bin/pm2 reload all
ExecStop=/usr/local/bin/pm2 kill

Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# Setup PM2 startup script for the user
print_status "Setting up PM2 startup..."
sudo -u $APP_USER pm2 startup systemd -u $APP_USER --hp /home/<USER>

# Create Nginx configuration for frontend apps
print_status "Creating Nginx configuration for frontend..."
cat > /etc/nginx/sites-available/eventahub-frontend << EOF
# Nuxt.js Web Application
server {
    listen 80;
    server_name $DOMAIN www.$DOMAIN;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-XSS-Protection "1; mode=block";
    add_header X-Content-Type-Options "nosniff";
    add_header Referrer-Policy "strict-origin-when-cross-origin";

    # Proxy to Nuxt.js application
    location / {
        proxy_pass http://127.0.0.1:${WEB_PORT};
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        proxy_read_timeout 86400;
    }

    # Handle static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://127.0.0.1:${WEB_PORT};
        proxy_set_header Host \$host;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF

# Enable the frontend Nginx site
ln -sf /etc/nginx/sites-available/eventahub-frontend /etc/nginx/sites-enabled/

# Test Nginx configuration
nginx -t

# Create deployment scripts for web app
print_status "Creating deployment scripts..."

# Web deployment script
cat > $WEB_APP_DIR/deploy.sh << 'EOF'
#!/bin/bash

# EventaHub Web Application Deployment Script

set -e

WEB_APP_DIR="/var/www/eventahub/eventaa-web"
WEB_APP_NAME="eventahub-web"

echo "Starting web application deployment..."

cd $WEB_APP_DIR

# Pull latest changes
git pull origin main

# Install dependencies
npm ci --only=production

# Build the application
npm run build

# Reload PM2 process
pm2 reload $WEB_APP_NAME

echo "Web application deployment completed successfully!"
EOF

chmod +x $WEB_APP_DIR/deploy.sh

# Create health check script
print_status "Creating health check script..."
cat > /usr/local/bin/eventahub-frontend-health << 'EOF'
#!/bin/bash

# EventaHub Frontend Health Check Script

WEB_PORT="4000"

echo "EventaHub Frontend Health Check"
echo "==============================="

# Check web application
echo -n "Web Application (port $WEB_PORT): "
if curl -s -o /dev/null -w "%{http_code}" http://localhost:$WEB_PORT | grep -q "200\|301\|302"; then
    echo "✓ HEALTHY"
    WEB_STATUS=0
else
    echo "✗ UNHEALTHY"
    WEB_STATUS=1
fi

# PM2 status
echo ""
echo "PM2 Process Status:"
pm2 status

# Exit with error if service is unhealthy
if [ $WEB_STATUS -ne 0 ]; then
    exit 1
fi

exit 0
EOF

chmod +x /usr/local/bin/eventahub-frontend-health

# Create log monitoring script
print_status "Creating log monitoring script..."
cat > /usr/local/bin/eventahub-frontend-logs << 'EOF'
#!/bin/bash

# EventaHub Frontend Log Monitoring Script

case "$1" in
    web|"")
        pm2 logs eventahub-web --lines 100
        ;;
    all)
        pm2 logs --lines 50
        ;;
    errors)
        echo "=== Web Application Errors ==="
        pm2 logs eventahub-web --err --lines 20
        ;;
    follow)
        pm2 logs --lines 0
        ;;
    *)
        echo "Usage: $0 {web|all|errors|follow}"
        echo ""
        echo "  web     - Show web application logs (default)"
        echo "  all     - Show all application logs"
        echo "  errors  - Show only error logs"
        echo "  follow  - Follow logs in real-time"
        exit 1
        ;;
esac
EOF

chmod +x /usr/local/bin/eventahub-frontend-logs

# Setup cron job for health checks
print_status "Setting up health check cron job..."
(crontab -u $APP_USER -l 2>/dev/null | grep -v "eventahub-frontend-health"; echo "*/5 * * * * /usr/local/bin/eventahub-frontend-health > /var/log/frontend-health.log 2>&1") | crontab -u $APP_USER -

# Set proper permissions
print_status "Setting file permissions..."
chown -R $APP_USER:$APP_USER /var/www/eventahub
chmod -R 755 /var/www/eventahub

# Initialize PM2 with the ecosystem file
print_status "Initializing PM2 with ecosystem configuration..."
sudo -u $APP_USER pm2 start /var/www/eventahub/ecosystem.config.js
sudo -u $APP_USER pm2 save

# Enable and start the systemd service
systemctl daemon-reload
systemctl enable eventahub-frontend
systemctl start eventahub-frontend

# Restart Nginx to load new configuration
systemctl restart nginx

print_status "Frontend PM2 setup completed successfully!"
echo ""
echo -e "${GREEN}======================================${NC}"
echo -e "${GREEN}    Frontend Setup Summary           ${NC}"
echo -e "${GREEN}======================================${NC}"
echo -e "Web Application: ${BLUE}http://$DOMAIN${NC} (port $WEB_PORT)"
echo ""
echo -e "${YELLOW}PM2 Management Commands:${NC}"
echo -e "  • Start: ${BLUE}eventahub-frontend-pm2 start${NC}"
echo -e "  • Stop: ${BLUE}eventahub-frontend-pm2 stop${NC}"
echo -e "  • Restart: ${BLUE}eventahub-frontend-pm2 restart${NC}"
echo -e "  • Status: ${BLUE}eventahub-frontend-pm2 status${NC}"
echo -e "  • Logs: ${BLUE}eventahub-frontend-pm2 logs${NC}"
echo -e "  • Monitor: ${BLUE}eventahub-frontend-pm2 monit${NC}"
echo ""
echo -e "${YELLOW}Deployment Commands:${NC}"
echo -e "  • Deploy Web: ${BLUE}eventahub-frontend-pm2 deploy-web${NC}"
echo ""
echo -e "${YELLOW}Monitoring Commands:${NC}"
echo -e "  • Health Check: ${BLUE}eventahub-frontend-health${NC}"
echo -e "  • View Logs: ${BLUE}eventahub-frontend-logs [web|all|errors|follow]${NC}"
echo ""
echo -e "${YELLOW}SystemD Service:${NC}"
echo -e "  • Start: ${BLUE}systemctl start eventahub-frontend${NC}"
echo -e "  • Stop: ${BLUE}systemctl stop eventahub-frontend${NC}"
echo -e "  • Status: ${BLUE}systemctl status eventahub-frontend${NC}"
echo ""
echo -e "${YELLOW}Next Steps:${NC}"
echo "1. Update domain names in /etc/nginx/sites-available/eventahub-frontend"
echo "2. Configure environment variables in /var/www/eventahub/ecosystem.config.js"
echo "3. Install dependencies and build application:"
echo "   cd $WEB_APP_DIR && npm ci && npm run build"
echo "4. Set up SSL certificates for the frontend domain"
echo "5. Test the application is accessible"
echo ""
echo -e "${GREEN}PM2 will automatically restart application on system reboot.${NC}"
echo -e "${GREEN}Health checks run every 5 minutes via cron job.${NC}"
