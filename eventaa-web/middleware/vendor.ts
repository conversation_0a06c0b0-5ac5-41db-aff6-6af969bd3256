import { useAuthStore } from "@/store/auth"
import { useVendorStore } from "@/store/vendor"

export default defineNuxtRouteMiddleware(async (to, _from) => {
    const authStore = useAuthStore();
    const vendorStore = useVendorStore();

    // Basic check - if no token or not authenticated, redirect immediately
    if (!authStore.userIsAuthenticated || !authStore.token) {
        console.log("Vendor middleware: User not authenticated, redirecting to home")

        // Clear auth state to ensure consistency
        authStore.clearAuth()
        vendorStore.clearPermissions()

        // Redirect to home page
        return navigateTo('/', { replace: true })
    }

    if (!vendorStore.permissionsLoaded) {
        try {
            await vendorStore.fetchVendorPermissions()
        } catch (error) {
            console.error('Failed to fetch vendor permissions:', error)
        }
    }

    const hasOnlyVendorRole = vendorStore.hasVendorRole(authStore.user!);
    if (!hasOnlyVendorRole) {
        console.log('User does not have only vendor role, redirecting to home')
        return navigateTo('/', { replace: true })
    }

    if (!to.path.startsWith('/vendor/dashboard')) {
        console.log('Redirecting to vendor dashboard')
        return navigateTo('/vendor/dashboard', { replace: true })
    }
})
