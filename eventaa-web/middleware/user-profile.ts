import { useUserMappingStore } from "@/store/userMapping";

export default defineNuxtRouteMiddleware((to) => {
  if (!to.path.startsWith("/users/")) {
    return;
  }

  const userMappingStore = useUserMappingStore();
  const userIdOrUuid = to.params.id as string;

  const isNumericId = /^\d+$/.test(userIdOrUuid);

  if (isNumericId) {
    const userId = parseInt(userIdOrUuid);
    const uuid = userMappingStore.addMapping(userId);
    return navigateTo(`/users/${uuid}`, { replace: true });
  }
});
