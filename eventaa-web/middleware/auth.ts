import { useAuthStore } from "@/store/auth"

export default defineNuxtRouteMiddleware((_to, _from) => {
    const authStore = useAuthStore()
    const nuxtApp = useNuxtApp();

    if (!authStore.userIsAuthenticated || !authStore.token) {
        console.info("auth middleware: User not authenticated, redirecting to home");
        if (nuxtApp && typeof (nuxtApp as any).$toast?.warning === "function") {
            (nuxtApp as any).$toast.warning("Please login to continue");
        }
        authStore.clearAuth();
        return navigateTo('/', { replace: true })
    }
})
