import { defineStore } from 'pinia'
import { ENDPOINTS } from '@/utils/api'
import { useVendorStore } from '@/store/vendor'
import { useHttpClient } from '@/composables/useHttpClient'

export interface PaymentGateway {
  id: number
  name: string
  slug: string
  description: string
  is_active: boolean
  logo: string
  supported_currencies: string[]
  test_mode: boolean
}

export interface PaymentMethod {
  id: number
  vendor_id: number
  payment_gateway_id: number
  is_default: boolean
  account_details: any
  status: string
  last_used_at: string
  payment_gateway?: PaymentGateway
}

export interface Transaction {
  id: number
  transaction_id: string
  vendor_payment_method_id: number
  user_id: number
  amount: number
  currency: string
  status: string
  payment_type: string
  bookable_id: number
  bookable_type: string
  metadata: any
  payment_date: string
  gateway_response: any
  created_at: string
  updated_at: string
  payment_method?: PaymentMethod
  bookable?: any
  user?: any
}

export interface VendorPaymentsState {
  paymentGateways: PaymentGateway[]
  paymentMethods: PaymentMethod[]
  transactions: Transaction[]
  loading: boolean
  saving: boolean
  error: string | null
  pagination: {
    currentPage: number
    totalPages: number
    perPage: number
    total: number
  }
}

export const useVendorPaymentsStore = defineStore('vendorPayments', {
  state: (): VendorPaymentsState => ({
    paymentGateways: [],
    paymentMethods: [],
    transactions: [],
    loading: false,
    saving: false,
    error: null,
    pagination: {
      currentPage: 1,
      totalPages: 1,
      perPage: 10,
      total: 0
    }
  }),

  getters: {
    getDefaultPaymentMethod: (state) => {
      return state.paymentMethods.find(method => method.is_default) || null
    },

    getActivePaymentMethods: (state) => {
      return state.paymentMethods.filter(method => method.status === 'active')
    },

    getPaymentMethodsByGateway: (state) => (gatewayId: number) => {
      return state.paymentMethods.filter(method => method.payment_gateway_id === gatewayId)
    }
  },

  actions: {
    async fetchPaymentGateways(): Promise<boolean> {
      try {
        this.loading = true
        this.error = null
        const httpClient = useHttpClient()

        const response = await httpClient.get(`${ENDPOINTS.PAYMENTS.BASE}/gateways`)

        if (response && response.data) {
          this.paymentGateways = response.data
          return true
        }

        return false
      } catch (error: any) {
        console.error('Error fetching payment gateways:', error)
        this.error = error.message || 'Failed to fetch payment gateways'
        return false
      } finally {
        this.loading = false
      }
    },

    async fetchPaymentMethods(vendorId: number): Promise<boolean> {
      try {
        this.loading = true
        this.error = null
        const httpClient = useHttpClient()

        const response = await httpClient.get(`${ENDPOINTS.PAYMENTS.BASE}/methods/${vendorId}`)

        if (response && response.data) {
          this.paymentMethods = response.data
          return true
        }

        return false
      } catch (error: any) {
        console.error('Error fetching payment methods:', error)
        this.error = error.message || 'Failed to fetch payment methods'
        return false
      } finally {
        this.loading = false
      }
    },

    async fetchTransactions(page: number = 1, perPage: number = 10): Promise<boolean> {
      try {
        this.loading = true
        this.error = null
        const httpClient = useHttpClient()

        const response = await httpClient.get(`${ENDPOINTS.PAYMENTS.BASE}/vendor-transactions?page=${page}&per_page=${perPage}`)

        if (response) {
          this.transactions = response.data
          this.pagination = {
            currentPage: response.meta.current_page,
            totalPages: response.meta.last_page,
            perPage: response.meta.per_page,
            total: response.meta.total
          }
          return true
        }

        return false
      } catch (error: any) {
        console.error('Error fetching transactions:', error)
        this.error = error.message || 'Failed to fetch transactions'
        return false
      } finally {
        this.loading = false
      }
    },

    async addPaymentMethod(vendorId: number, gatewayId: number, accountDetails: any): Promise<boolean> {
      try {
        this.saving = true
        this.error = null
        const httpClient = useHttpClient()

        const response = await httpClient.post(`${ENDPOINTS.PAYMENTS.BASE}/methods/add`, {
          vendor_id: vendorId,
          payment_gateway_id: gatewayId,
          account_details: accountDetails
        })

        if (response && response.data) {
          // Add the new payment method to the list
          this.paymentMethods.push(response.data)
          return true
        }

        return false
      } catch (error: any) {
        console.error('Error adding payment method:', error)
        this.error = error.message || 'Failed to add payment method'
        return false
      } finally {
        this.saving = false
      }
    },

    async updatePaymentMethod(methodId: number, data: any): Promise<boolean> {
      try {
        this.saving = true
        this.error = null
        const httpClient = useHttpClient()

        // If vendor_id is not provided in data, get it from the store
        if (!data.vendor_id) {
          const vendorStore = useVendorStore();
          if (vendorStore.details?.id) {
            data.vendor_id = vendorStore.details.id;
          } else {
            this.error = 'Vendor ID is required';
            return false;
          }
        }

        const response = await httpClient.put(`${ENDPOINTS.PAYMENTS.BASE}/methods/${methodId}`, data)

        if (response && response.data) {
          // Update the payment method in the list
          const index = this.paymentMethods.findIndex(method => method.id === methodId)
          if (index !== -1) {
            this.paymentMethods[index] = response.data
          }
          return true
        }

        return false
      } catch (error: any) {
        console.error('Error updating payment method:', error)
        this.error = error.message || 'Failed to update payment method'
        return false
      } finally {
        this.saving = false
      }
    },

    async setDefaultPaymentMethod(methodId: number, vendorId?: number): Promise<boolean> {
      // Get vendor ID from the store if not provided
      const vendorStore = useVendorStore();
      const actualVendorId = vendorId || vendorStore.details?.id;

      if (!actualVendorId) {
        this.error = 'Vendor ID is required';
        return false;
      }

      return this.updatePaymentMethod(methodId, {
        is_default: true,
        vendor_id: actualVendorId
      });
    },

    async removePaymentMethod(methodId: number, vendorId?: number): Promise<boolean> {
      try {
        this.saving = true
        this.error = null
        const httpClient = useHttpClient()

        // Get vendor ID from the store if not provided
        const vendorStore = useVendorStore();
        const actualVendorId = vendorId || vendorStore.details?.id;

        if (!actualVendorId) {
          this.error = 'Vendor ID is required';
          return false;
        }

        const response = await httpClient.delete(`${ENDPOINTS.PAYMENTS.BASE}/methods/${methodId}`, {
          data: { vendor_id: actualVendorId }
        })

        if (response) {
          // Remove the payment method from the list
          this.paymentMethods = this.paymentMethods.filter(method => method.id !== methodId)
          return true
        }

        return false
      } catch (error: any) {
        console.error('Error removing payment method:', error)
        this.error = error.message || 'Failed to remove payment method'
        return false
      } finally {
        this.saving = false
      }
    }
  }
})
