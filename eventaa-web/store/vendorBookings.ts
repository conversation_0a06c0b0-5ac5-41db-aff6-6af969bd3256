import { defineStore } from 'pinia'
import { ENDPOINTS } from '@/utils/api'
import { useVendorStore } from './vendor'

interface VendorBooking {
  id: number
  user_id: number
  vendor_id: number
  vendor_service_id: number
  category_id: number
  booking_from: string
  booking_to: string
  number_of_guests: number
  message: string
  status: 'pending' | 'approved' | 'rejected' | 'completed' | 'cancelled'
  rejection_reason?: string
  total_price: number
  is_paid: boolean
  created_at: string
  updated_at: string
  user?: {
    id: number
    name: string
    email: string
    avatar?: string
  }
  vendor?: any
  vendor_service?: {
    id: number
    name: string
    service: {
      id: number
      name: string
    }
  }
  category?: {
    id: number
    name: string
  }
}

interface VendorBookingsState {
  bookings: VendorBooking[]
  currentBooking: VendorBooking | null
  loading: boolean
  error: string | null
  totalBookings: number
  pendingBookings: number
  confirmedBookings: number
  completedBookings: number
  cancelledBookings: number
}

export const useVendorBookingsStore = defineStore('vendorBookings', {
  state: (): VendorBookingsState => ({
    bookings: [],
    currentBooking: null,
    loading: false,
    error: null,
    totalBookings: 0,
    pendingBookings: 0,
    confirmedBookings: 0,
    completedBookings: 0,
    cancelledBookings: 0
  }),

  getters: {
    getBookingsByStatus: (state) => (status: string) => {
      if (status === 'All') {
        return state.bookings
      }
      return state.bookings.filter(booking => booking.status.toLowerCase() === status.toLowerCase())
    },

    getBookingById: (state) => (id: number) => {
      return state.bookings.find(booking => booking.id === id) || null
    },

    getBookingCounts: (state) => {
      return {
        total: state.totalBookings,
        pending: state.pendingBookings,
        confirmed: state.confirmedBookings,
        completed: state.completedBookings,
        cancelled: state.cancelledBookings
      }
    }
  },

  actions: {
    async fetchVendorBookings(params: any = {}): Promise<boolean> {
      try {
        this.loading = true
        this.error = null
        const httpClient = useHttpClient()
        const vendorStore = useVendorStore()

        if (!vendorStore.details?.id) {
          await vendorStore.fetchVendorDetails()
        }

        const response = await httpClient.get(`${ENDPOINTS.VENDORS.BASE}/bookings/vendor`, { params }) as any

        if (response) {
          if (response.data) {
            this.bookings = response.data as VendorBooking[]
          } else if (Array.isArray(response)) {
            this.bookings = response as VendorBooking[]
          } else {
            this.bookings = []
            console.error('Unexpected response format:', response)
          }

          console.log('Fetched vendor bookings:', this.bookings)
          this.updateBookingCounts()
        }

        return true
      } catch (error: any) {
        console.error('Error fetching vendor bookings:', error)
        this.error = error.message || 'Failed to fetch vendor bookings'
        return false
      } finally {
        this.loading = false
      }
    },

    async getBooking(id: number): Promise<VendorBooking | null> {
      try {
        this.loading = true
        this.error = null
        const httpClient = useHttpClient()

        const response = await httpClient.get(`${ENDPOINTS.VENDORS.BASE}/bookings/read/${id}`) as any

        if (response && response.data) {
          this.currentBooking = response.data
          return response.data
        }

        return null
      } catch (error: any) {
        console.error('Error fetching booking details:', error)
        this.error = error.message || 'Failed to fetch booking details'
        return null
      } finally {
        this.loading = false
      }
    },

    async updateBookingStatus(id: number, status: string, rejectionReason?: string): Promise<boolean> {
      try {
        this.loading = true
        this.error = null
        const httpClient = useHttpClient()

        const data: any = { status }
        if (rejectionReason && status === 'rejected') {
          data.rejection_reason = rejectionReason
        }

        const response = await httpClient.put(`${ENDPOINTS.VENDORS.BASE}/bookings/status/${id}`, data)

        if (response) {

          const index = this.bookings.findIndex(b => b.id === id)
          if (index !== -1) {
            this.bookings[index] = {
              ...this.bookings[index],
              status: status as 'pending' | 'approved' | 'rejected' | 'completed' | 'cancelled',
              rejection_reason: rejectionReason
            }
          }

          this.updateBookingCounts()
          return true
        }

        return false
      } catch (error: any) {
        console.error('Error updating booking status:', error)
        this.error = error.message || 'Failed to update booking status'
        return false
      } finally {
        this.loading = false
      }
    },

    async deleteBooking(id: number): Promise<boolean> {
      try {
        this.loading = true
        this.error = null
        const httpClient = useHttpClient()

        const response = await httpClient.delete(`${ENDPOINTS.VENDORS.BASE}/bookings/delete/${id}`)

        if (response) {

          this.bookings = this.bookings.filter(b => b.id !== id)
          this.updateBookingCounts()
          return true
        }

        return false
      } catch (error: any) {
        console.error('Error deleting booking:', error)
        this.error = error.message || 'Failed to delete booking'
        return false
      } finally {
        this.loading = false
      }
    },

    updateBookingCounts() {
      this.totalBookings = this.bookings.length
      this.pendingBookings = this.bookings.filter(b => b.status === 'pending').length
      this.confirmedBookings = this.bookings.filter(b => b.status === 'approved').length
      this.completedBookings = this.bookings.filter(b => b.status === 'completed').length
      this.cancelledBookings = this.bookings.filter(b => b.status === 'rejected' || b.status === 'cancelled').length
    },

    clearBookingsData() {
      this.bookings = []
      this.currentBooking = null
      this.totalBookings = 0
      this.pendingBookings = 0
      this.confirmedBookings = 0
      this.completedBookings = 0
      this.cancelledBookings = 0
    }
  }
})
