import { defineStore } from "pinia";
import type { AuthResponse, AuthState } from "@/types/api";
import type { User } from "@/types/user";
import { ENDPOINTS } from "@/utils/api";

export const useAuthStore = defineStore("auth", {
  state: (): AuthState => ({
    user: null,
    token: null,
    refresh_token: null,
    message: null,
    isAuthenticated: false,
  }),
  getters: {
    currentUser: (state): User | null => state.user,
    userIsAuthenticated: (state): boolean => state.isAuthenticated,
    authToken: (state): string | null => state.token,
  },
  actions: {
    setAuth(authData: AuthResponse) {
      if (authData.user && authData.token) {
        this.user = authData.user;
        this.token = authData.token;
        this.refresh_token = authData.refresh_token || null;
        this.isAuthenticated = true;
      }
    },
    clearAuth() {
      this.user = null;
      this.token = null;
      this.refresh_token = null;
      this.message = null;
      this.isAuthenticated = false;
    },
    setUser(user: User) {
      this.user = user;
    },
    async refreshUser(): Promise<boolean> {
      if (!this.isAuthenticated || !this.token) {
        return false;
      }

      try {
        const httpClient = useHttpClient();
        const response = await httpClient.get<User>(ENDPOINTS.PROFILE.USER);

        if (response) {
          this.user = response;
          return true;
        }

        return false;
      } catch (error) {
        console.error("Error refreshing user data:", error);
        return false;
      }
    },
    async refreshToken(): Promise<boolean> {
      if (!this.refresh_token) {
        return false;
      }

      try {
        // Use native fetch to avoid circular dependency with http client
        const config = useRuntimeConfig();
        const response = await fetch(`${config.public.apiBaseUrl}${ENDPOINTS.AUTH.REFRESH}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.refresh_token}`
          }
        });

        if (response.ok) {
          const data: { data: AuthResponse } = await response.json();
          if (data.data.token) {
            this.token = data.data.token;
            this.refresh_token = data.data.refresh_token || this.refresh_token;
            return true;
          }
        }

        // If refresh fails, clear auth
        this.clearAuth();
        return false;
      } catch (error) {
        console.error("Error refreshing token:", error);
        this.clearAuth();
        return false;
      }
    },
    async logout() {
      try {
        const httpClient = useHttpClient();
        await httpClient.post(ENDPOINTS.AUTH.LOGOUT);
        this.clearAuth();
        navigateTo("/", { replace: true });
      } catch (error) {
        console.error("Logout error:", error);
      }
    },
  },
  persist: true,
});
