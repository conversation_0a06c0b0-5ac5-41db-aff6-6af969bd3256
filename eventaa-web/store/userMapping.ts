import { defineStore } from "pinia";

interface UserMapping {
  id: number;
  uuid: string;
}

interface UserMappingState {
  mappings: UserMapping[];
}

export const useUserMappingStore = defineStore("userMapping", {
  state: (): UserMappingState => ({
    mappings: [],
  }),

  actions: {
    generateUuid(): string {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
    },

    addMapping(id: number): string {
      this.mappings = this.mappings.filter(m => m.id !== id);
      const uuid = this.generateUuid();
      this.mappings.push({ id, uuid });
      if (this.mappings.length > 50) {
        this.mappings = this.mappings.slice(-50);
      }

      return uuid;
    },

    getIdFromUuid(uuid: string): number | null {
      const mapping = this.mappings.find(m => m.uuid === uuid);
      return mapping ? mapping.id : null;
    },

    removeMapping(uuid: string): void {
      this.mappings = this.mappings.filter(m => m.uuid !== uuid);
    },

    clearMappings(): void {
      this.mappings = [];
    }
  },
});
