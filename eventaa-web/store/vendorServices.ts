import { defineStore } from 'pinia'
import { ENDPOINTS } from '@/utils/api'
import type { Service } from '@/types/vendor'
import { useVendorStore } from '@/store/vendor'

interface AvailableService {
  id: number
  name: string
  description: string
  created_at: string
  updated_at: string
}

interface VendorServicesState {
  services: Service[]
  availableServices: AvailableService[]
  loading: boolean
  error: string | null
  currentService: Service | null
}

export const useVendorServicesStore = defineStore('vendorServices', {
  state: (): VendorServicesState => ({
    services: [],
    availableServices: [],
    loading: false,
    error: null,
    currentService: null
  }),

  getters: {
    getServiceById: (state) => (id: number) => {
      return state.services.find(service => service.id === id) || null
    }
  },

  actions: {
    async fetchVendorServices(): Promise<boolean> {
      try {
        this.loading = true
        this.error = null
        const httpClient = useHttpClient()
        const vendorStore = useVendorStore()

        if (!vendorStore.details?.id) {
          await vendorStore.fetchVendorDetails()
        }

        const vendorId = vendorStore.details?.id

        if (!vendorId) {
          throw new Error('Vendor ID not found')
        }

        const response = await httpClient.get<any>(`${ENDPOINTS.VENDORS.BASE}/services/get/${vendorId}`)
        if (response) {
          // Handle both paginated and non-paginated responses
          this.services = response.data || response
        }

        return true
      } catch (error: any) {
        console.error('Error fetching vendor services:', error)
        this.error = error.message || 'Failed to fetch vendor services'
        return false
      } finally {
        this.loading = false
      }
    },

    async fetchAvailableServices(): Promise<boolean> {
      try {
        this.loading = true
        this.error = null
        const httpClient = useHttpClient()

        const response = await httpClient.get<{ data: AvailableService[] }>(ENDPOINTS.SERVICES.BASE)
        if (response && response.data) {
          this.availableServices = response.data
        }

        return true
      } catch (error: any) {
        console.error('Error fetching available services:', error)
        this.error = error.message || 'Failed to fetch available services'
        return false
      } finally {
        this.loading = false
      }
    },

    async createService(serviceData: Partial<Service>): Promise<Service | null> {
      try {
        this.loading = true
        this.error = null
        const httpClient = useHttpClient()

        const response = await httpClient.post<any>(`${ENDPOINTS.VENDORS.BASE}/services/create`, serviceData)
        if (response) {
          // Get the created service from the response
          const createdService = response.data || response

          // Refresh the services list to ensure we have the latest data
          await this.fetchVendorServices()

          return createdService
        }

        return null
      } catch (error: any) {
        console.error('Error creating vendor service:', error)
        this.error = error.message || 'Failed to create vendor service'
        return null
      } finally {
        this.loading = false
      }
    },

    async getService(id: number): Promise<Service | null> {
      try {
        this.loading = true
        this.error = null
        const httpClient = useHttpClient()

        const response = await httpClient.get<any>(`${ENDPOINTS.VENDORS.BASE}/services/read/${id}`)
        if (response) {
          return response.data || response
        }
        return null
      } catch (error: any) {
        console.error('Error fetching vendor service:', error)
        this.error = error.message || 'Failed to fetch vendor service'
        return null
      } finally {
        this.loading = false
      }
    },

    async updateService(id: number, serviceData: Partial<Service>): Promise<Service | null> {
      try {
        this.loading = true
        this.error = null
        const httpClient = useHttpClient()

        const response = await httpClient.put<any>(`${ENDPOINTS.VENDORS.BASE}/services/update/${id}`, serviceData)
        if (response) {
          // Get the updated service from the response
          const updatedService = response.data || response

          // Refresh the services list to ensure we have the latest data
          await this.fetchVendorServices()

          return updatedService
        }

        return null
      } catch (error: any) {
        console.error('Error updating vendor service:', error)
        this.error = error.message || 'Failed to update vendor service'
        return null
      } finally {
        this.loading = false
      }
    },

    async deleteService(id: number): Promise<boolean> {
      try {
        this.loading = true
        this.error = null
        const httpClient = useHttpClient()

        await httpClient.delete(`${ENDPOINTS.VENDORS.BASE}/services/delete/${id}`)

        // Refresh the services list to ensure we have the latest data
        await this.fetchVendorServices()

        return true
      } catch (error: any) {
        console.error('Error deleting vendor service:', error)
        this.error = error.message || 'Failed to delete vendor service'
        return false
      } finally {
        this.loading = false
      }
    },

    setCurrentService(service: Service | null) {
      this.currentService = service
    }
  }
})
