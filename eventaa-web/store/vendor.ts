import { defineStore } from 'pinia'
import type { Permission, Role } from '@/types/role'
import type { ApiVendor } from '@/types/vendor'
import { ENDPOINTS } from '@/utils/api'
import type { User } from '~/types/user'

interface VendorAnalytics {
  bookings: {
    total: number
    pending_approval: number
    growth_percentage: number
  }
  revenue: {
    total: number
    pending: number
    growth_percentage: number
  }
  ratings: {
    average: number
    total_reviews: number
    growth: number
  }
  profile_views: {
    total: number
    period: string
    growth_percentage: number
  }
  period: string
  start_date: string
  end_date: string
}

interface VendorState {
  roles: Role[]
  permissions: Permission[]
  permissionsLoaded: boolean
  details: ApiVendor | null
  detailsLoading: boolean
  analytics: VendorAnalytics | null
  analyticsLoading: boolean
}

export const useVendorStore = defineStore('vendor', {
  state: (): VendorState => ({
    roles: <Role[]>[],
    permissions: <Permission[]>[],
    permissionsLoaded: <boolean>false,
    details: null,
    detailsLoading: false,
    analytics: null,
    analyticsLoading: false
  }),

  getters: {
    getPermissionsByCategory: (state): { [key: string]: Permission[]; } => {
      const permissionsByCategory: { [key: string]: Permission[] } = {}

      state.permissions.forEach(permission => {
        if (!permissionsByCategory[permission.category]) {
          permissionsByCategory[permission.category] = []
        }
        permissionsByCategory[permission.category].push(permission)
      })

      return permissionsByCategory
    },
    vendor: (state) => state.details
  },

  actions: {
    async fetchVendorPermissions(): Promise<boolean> {
      try {
        const httpClient = useHttpClient()

        const rolesResponse = await httpClient.get<{ roles: Role[]}>(ENDPOINTS.ROLES.BASE)
        if (rolesResponse) {
          this.roles = rolesResponse.roles;
        }

        const vendorRole = this.roles.find(role => role.name === 'vendor')
        if (vendorRole) {
          const permissionsEndpoint = `${ENDPOINTS.ROLES.USERS}/${vendorRole.id}/permissions`

          const permissionsResponse = await httpClient.get<{ permissions: Permission[]; }>(permissionsEndpoint)
          if (permissionsResponse) {
            if (permissionsResponse.permissions) {
              this.permissions = permissionsResponse.permissions;
            } else if (Array.isArray(permissionsResponse)) {
              this.permissions = permissionsResponse;
            } else {
              console.error('Unexpected permissions response format:', permissionsResponse)
            }
          }
        } else {
          console.error('Vendor role not found in roles list')
        }

        this.permissionsLoaded = true
        return true
      } catch (error) {
        console.error('Error fetching vendor permissions:', error)
        return false
      }
    },

    async fetchVendorDetails(): Promise<boolean> {
      try {
        this.detailsLoading = true
        const httpClient = useHttpClient()

        const response = await httpClient.get<ApiVendor>(`${ENDPOINTS.VENDORS.BASE}/profile`)
        if (response) {
          this.details = response
        }

        return true
      } catch (error) {
        console.error('Error fetching vendor details:', error)
        return false
      } finally {
        this.detailsLoading = false
      }
    },

    async fetchVendorProfile(): Promise<ApiVendor | null> {
      if (this.details) {
        return this.details;
      }

      const success = await this.fetchVendorDetails();
      return success ? this.details : null;
    },

    async fetchVendorAnalytics(period: string = '30days'): Promise<boolean> {
      try {
        this.analyticsLoading = true
        const httpClient = useHttpClient()

        const response = await httpClient.get<VendorAnalytics>(ENDPOINTS.VENDORS.ANALYTICS + `?period=${period}`)
        if (response) {
          this.analytics = response
        }

        return true
      } catch (error) {
        console.error('Error fetching vendor analytics:', error)
        return false
      } finally {
        this.analyticsLoading = false
      }
    },

    async updateVendorDetails(data: any): Promise<boolean> {
      try {
        if (!this.details?.id) {
          throw new Error('Vendor ID not found')
        }

        const httpClient = useHttpClient()

        const formData = new FormData()

        // Add all fields from data object to formData
        Object.keys(data).forEach(key => {
          if (data[key] !== undefined && data[key] !== null) {
            if (typeof data[key] === 'boolean') {
              formData.append(key, data[key] ? '1' : '0')
            } else {
              formData.append(key, data[key])
            }
          }
        })

        const response = await httpClient.post(
          `${ENDPOINTS.VENDORS.BASE}/update/${this.details.id}`,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          }
        )

        if (response) {
          // Update the local vendor details
          await this.fetchVendorDetails()
          return true
        }

        return false
      } catch (error: any) {
        console.error('Error updating vendor details:', error)
        return false
      }
    },

    hasPermissions(requiredPermissions: string[]): boolean {
      // If no permissions are required, or the array is empty, allow access
      if (!requiredPermissions || requiredPermissions.length === 0) {
        return true
      }

      // If permissions haven't been loaded yet, allow access to all items
      // This ensures sidebar items are visible while permissions are being loaded
      if (!this.permissionsLoaded || this.permissions.length === 0) {
        return true
      }

      // Check if the user has all the required permissions
      return requiredPermissions.every(permission =>
        this.permissions.some(p => p.name === permission)
      )
    },
    hasAnyPermission(permissions: string[]): boolean {
      // If no permissions are required, or the array is empty, allow access
      if (!permissions || permissions.length === 0) {
        return true
      }

      // If permissions haven't been loaded yet, allow access to all items
      if (!this.permissionsLoaded || this.permissions.length === 0) {
        return true
      }

      // Check if the user has any of the required permissions
      return permissions.some(permission =>
        this.permissions.some(p => p.name === permission)
      )
    },
    hasVendorRole: (user: User): boolean => {
      if (!user || !user.roles || !Array.isArray(user.roles)) {
        console.log('User has no roles or roles is not an array')
        return false
      }
      const hasVendorRole = user.roles.some((role: string) => role.toLowerCase() === 'vendor');
      return hasVendorRole;
    },
    clearPermissions() {
      this.roles = []
      this.permissions = []
      this.permissionsLoaded = false
    },

    clearVendorData() {
      this.details = null
      this.analytics = null
      this.roles = []
      this.permissions = []
      this.permissionsLoaded = false
    }
  },

  persist: true
})
