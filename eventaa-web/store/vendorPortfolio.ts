import { defineStore } from 'pinia'
import { ENDPOINTS } from '@/utils/api'
import { useVendorStore } from './vendor'

export interface PortfolioItem {
  id: number
  vendor_id: number
  title: string
  description: string | null
  image_path: string
  media_type: 'image' | 'video'
  category: string | null
  is_featured: boolean
  created_at: string
  updated_at: string
}

export interface PortfolioCategory {
  name: string
  count: number
}

export interface PortfolioState {
  items: PortfolioItem[]
  categories: string[]
  loading: boolean
  saving: boolean
  error: string | null
  currentPage: number
  totalPages: number
  totalItems: number
  selectedCategory: string
}

export const useVendorPortfolioStore = defineStore('vendorPortfolio', {
  state: (): PortfolioState => ({
    items: [],
    categories: [],
    loading: false,
    saving: false,
    error: null,
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    selectedCategory: 'all'
  }),

  getters: {
    getPortfolioItems: (state) => state.items,
    getCategories: (state) => {
      const categoryCounts: Record<string, number> = { 'all': state.totalItems }

      state.items.forEach(item => {
        if (item.category) {
          categoryCounts[item.category] = (categoryCounts[item.category] || 0) + 1
        }
      })

      return Object.entries(categoryCounts).map(([name, count]) => ({ name, count }))
    },
    getFeaturedItems: (state) => state.items.filter(item => item.is_featured),
    getItemsByCategory: (state) => (category: string) => {
      if (category === 'all') return state.items
      return state.items.filter(item => item.category === category)
    }
  },

  actions: {
    async fetchPortfolioItems(page: number = 1, category: string = 'all'): Promise<boolean> {
      try {
        this.loading = true
        this.error = null
        const vendorStore = useVendorStore()
        const vendorId = vendorStore.details?.id

        if (!vendorId) {
          this.error = 'Vendor ID not found'
          return false
        }

        const httpClient = useHttpClient()
        const params = new URLSearchParams()
        params.append('page', page.toString())
        if (category !== 'all') {
          params.append('category', category)
        }

        const response = await httpClient.get<{
          portfolio_items: {
            data: PortfolioItem[],
            current_page: number,
            last_page: number,
            total: number
          },
          categories: string[]
        }>(`${ENDPOINTS.VENDORS.PORTFOLIO.GET}/${vendorId}?${params.toString()}`)

        if (response && response.portfolio_items) {
          this.items = response.portfolio_items.data
          this.currentPage = response.portfolio_items.current_page
          this.totalPages = response.portfolio_items.last_page
          this.totalItems = response.portfolio_items.total
          this.categories = response.categories || []
          this.selectedCategory = category
          return true
        }

        return false
      } catch (error: any) {
        console.error('Error fetching portfolio items:', error)
        this.error = error.message || 'Failed to fetch portfolio items'
        return false
      } finally {
        this.loading = false
      }
    },

    async addPortfolioItem(formData: FormData): Promise<boolean> {
      try {
        this.saving = true
        this.error = null
        const httpClient = useHttpClient()

        const response = await httpClient.post(ENDPOINTS.VENDORS.PORTFOLIO.CREATE, formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })

        if (response) {
          // Refresh the portfolio items
          await this.fetchPortfolioItems(1, this.selectedCategory)
          return true
        }

        return false
      } catch (error: any) {
        console.error('Error adding portfolio item:', error)
        this.error = error.message || 'Failed to add portfolio item'
        throw error // Re-throw the error to handle validation errors in the component
      } finally {
        this.saving = false
      }
    },

    async updatePortfolioItem(id: number, formData: FormData): Promise<boolean> {
      try {
        this.saving = true
        this.error = null
        const httpClient = useHttpClient()

        const response = await httpClient.post(`${ENDPOINTS.VENDORS.PORTFOLIO.UPDATE}/${id}`, formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })

        if (response) {
          // Refresh the portfolio items
          await this.fetchPortfolioItems(this.currentPage, this.selectedCategory)
          return true
        }

        return false
      } catch (error: any) {
        console.error('Error updating portfolio item:', error)
        this.error = error.message || 'Failed to update portfolio item'
        throw error // Re-throw the error to handle validation errors in the component
      } finally {
        this.saving = false
      }
    },

    async deletePortfolioItem(id: number): Promise<boolean> {
      try {
        this.saving = true
        this.error = null
        const httpClient = useHttpClient()

        const response = await httpClient.delete(`${ENDPOINTS.VENDORS.PORTFOLIO.DELETE}/${id}`)

        if (response) {
          // Refresh the portfolio items
          await this.fetchPortfolioItems(this.currentPage, this.selectedCategory)
          return true
        }

        return false
      } catch (error: any) {
        console.error('Error deleting portfolio item:', error)
        this.error = error.message || 'Failed to delete portfolio item'
        return false
      } finally {
        this.saving = false
      }
    },

    setCategory(category: string): void {
      this.selectedCategory = category
      this.fetchPortfolioItems(1, category)
    }
  }
})
