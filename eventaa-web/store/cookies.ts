import { defineStore } from 'pinia'

export interface CookiePreferences {
    necessary: boolean
    analytics: boolean
    marketing: boolean
    personalizedAds: boolean
}

export const useCookieConsentStore = defineStore('cookieConsent', {
    state: () => ({
        accepted: false,
        preferences: {
            necessary: true,
            analytics: false,
            marketing: false,
            personalizedAds: false
        },
        cookieConsentVersion: '1.0'
    }),

    getters: {
        isConsentAccepted(): boolean {
            return this.accepted
        },
        getCurrentPreferences(): CookiePreferences {
            return this.preferences
        }
    },

    actions: {
        acceptAllCookies() {
            this.accepted = true
            this.preferences = {
                necessary: true,
                analytics: true,
                marketing: true,
                personalizedAds: true
            }
            this.saveCookieConsent()
        },

        acceptNecessaryCookies() {
            this.accepted = true
            this.preferences = {
                necessary: true,
                analytics: false,
                marketing: false,
                personalizedAds: false
            }
            this.saveCookieConsent()
        },

        updatePreferences(newPreferences: Partial<CookiePreferences>) {
            this.preferences = {
                ...this.preferences,
                ...newPreferences
            }
            this.accepted = true
            this.saveCookieConsent()
        },
        saveCookieConsent() {
            try {
                localStorage.setItem('cookieConsent', JSON.stringify({
                    accepted: this.accepted,
                    preferences: this.preferences,
                    version: this.cookieConsentVersion,
                    timestamp: new Date().toISOString()
                }));
            } catch (error) {
                console.error('Error saving cookie consent:', error)
            }
        },

        loadCookieConsent() {
            try {
                const savedConsent = localStorage.getItem('cookieConsent')
                if (savedConsent) {
                    const parsedConsent = JSON.parse(savedConsent)

                    if (parsedConsent.version === this.cookieConsentVersion) {
                        this.accepted = parsedConsent.accepted
                        this.preferences = parsedConsent.preferences
                    } else {
                        this.resetConsent()
                    }
                }
            } catch (error) {
                console.error('Error loading cookie consent:', error)
                this.resetConsent()
            }
        },

        resetConsent() {
            this.accepted = false
            this.preferences = {
                necessary: true,
                analytics: false,
                marketing: false,
                personalizedAds: false
            }
            localStorage.removeItem('cookieConsent')
        },
        isCookieTypeAllowed(type: keyof CookiePreferences): boolean {
            return this.preferences[type]
        }
    },

    persist: true
})