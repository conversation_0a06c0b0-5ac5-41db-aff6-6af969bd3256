import { defineStore } from 'pinia'
import type { SidebarItem } from '@/types'
import { useAuthStore } from '@/store/auth'
import { ENDPOINTS } from '@/utils/api'
import { useHttpClient } from '@/composables/useHttpClient'

export interface SidebarSection {
    title: string;
    items: SidebarItem[];
    roles?: string[];
}

export interface SidebarItemWithRoles extends SidebarItem {
    roles?: string[];
}

export const useSidebarStore = defineStore("sidebar", {
    state: () => ({
        isOpen: false,
        active: <string>"",
        eventCount: 0,
        unreadMessages: 0,
        unreadNotifications: 0,
        pendingReviews: 0,
        allSections: <SidebarSection[]>[
            {
                title: "Overview",
                items: [
                    {
                        title: "Dashboard",
                        link: "/dashboard",
                        icon: "heroicons:home"
                    },
                    {
                        title: "Analytics",
                        link: "/dashboard/analytics",
                        icon: "heroicons:chart-bar",
                        roles: ['admin', 'host']
                    },
                    {
                        title: "Calendar",
                        link: "/dashboard/calendar",
                        icon: "heroicons:calendar",
                        roles: ['admin', 'host']
                    }
                ]
            },
            {
                title: "Event Management",
                roles: ['admin', 'host'],
                items: [
                    {
                        title: "Events",
                        link: "/dashboard/manage-events",
                        icon: "heroicons:folder"
                    },
                    {
                        title: "Tickets",
                        link: "/dashboard/tickets",
                        icon: "heroicons:qr-code"
                    },
                    {
                        title: "Attendees",
                        link: "/dashboard/attendees",
                        icon: "heroicons:users"
                    },
                    {
                        title: "Check-ins",
                        link: "/dashboard/check-ins",
                        icon: "heroicons:clipboard-document-check"
                    }
                ]
            },
            {
                title: "Vendor Management",
                roles: ['admin'],
                items: [
                    {
                        title: "Vendors",
                        link: "/dashboard/vendors",
                        icon: "pepicons-print:people"
                    },
                    {
                        title: "Approval",
                        link: "/dashboard/vendor-approval",
                        icon: "fluent:check-24-regular"
                    },
                    {
                        title: "Categories",
                        link: "/dashboard/vendor-categories",
                        icon: "iconamoon:category-light"
                    }
                ]
            },
            {
                title: "Venue Management",
                roles: ['admin', 'host', 'vendor'],
                items: [
                    {
                        title: "Venues",
                        link: "/dashboard/venues",
                        icon: "heroicons:building-office-2"
                    },
                    {
                        title: "Bookings",
                        link: "/dashboard/venue-bookings",
                        icon: "heroicons:calendar-days"
                    }
                ]
            },
            {
                title: "Finance",
                roles: ['admin', 'host'],
                items: [

                    {
                        title: "Reports",
                        link: "/dashboard/reports",
                        icon: "heroicons:document-text"
                    },
                    {
                        title: "Currencies",
                        link: "/dashboard/currencies",
                        icon: "heroicons:banknotes",
                        roles: ['admin']
                    }
                ]
            },
            {
                title: "Marketing",
                roles: ['admin', 'host'],
                items: [
                    {
                        title: "Sponsors",
                        link: "/dashboard/sponsors",
                        icon: "heroicons:briefcase"
                    }
                ]
            },
            {
                title: "Finances",
                roles: ['admin', 'host'],
                items: [
                    {
                        title: "Payments",
                        link: "/dashboard/payments",
                        icon: "heroicons:credit-card"
                    },
                    {
                        title: "Refunds",
                        link: "/dashboard/refunds",
                        icon: "heroicons:arrow-uturn-left"
                    },
                    {
                        title: "Withdrawals",
                        link: "/dashboard/withdrawal",
                        icon: "heroicons:banknotes",
                        roles: ['host']
                    },

                ]
            },
            {
                title: "Communication",
                items: [
                    {
                        title: "Messages",
                        link: "/dashboard/messages",
                        icon: "heroicons:chat-bubble-left-right"
                    },
                    {
                        title: "Notifications",
                        link: "/dashboard/notifications",
                        icon: "heroicons:bell"
                    },
                    {
                        title: "Reviews",
                        link: "/dashboard/reviews",
                        icon: "heroicons:star"
                    }
                ]
            },
            {
                title: "System",
                roles: ['admin'],
                items: [
                    {
                        title: "Users",
                        link: "/dashboard/users",
                        icon: "heroicons:user-group"
                    },
                    {
                        title: "Host Requests",
                        link: "/dashboard/host-requests",
                        icon: "heroicons:user-plus"
                    },
                    {
                        title: "Roles",
                        link: "/dashboard/roles",
                        icon: "heroicons:user-circle"
                    },
                    {
                        title: "Permissions",
                        link: "/dashboard/permissions",
                        icon: "heroicons:lock-closed"
                    },
                    {
                        title: "Settings",
                        link: "/dashboard/settings",
                        icon: "heroicons:cog-6-tooth"
                    }
                ]
            }
        ]
    }),
    getters: {
        sections: (state) => {
            const authStore = useAuthStore();
            const userRoles = authStore.user?.roles || [];

            return state.allSections
                .map(section => {
                    if (section.roles && section.roles.length > 0) {
                        const hasRequiredRole = section.roles.some(role => userRoles.includes(role));
                        if (!hasRequiredRole) {
                            return null;
                        }
                    }

                    const filteredItems = section.items.filter(item => {
                        if (!item.roles || item.roles.length === 0) {
                            return true;
                        }
                        return item.roles.some(role => userRoles.includes(role));
                    }).map(item => {
                        let count = item.count;

                        // Set counts for specific items
                        if (item.title === 'Events') {
                            count = state.eventCount;
                        } else if (item.title === 'Messages') {
                            count = state.unreadMessages;
                        } else if (item.title === 'Notifications') {
                            count = state.unreadNotifications;
                        } else if (item.title === 'Reviews') {
                            count = state.pendingReviews;
                        }

                        return {
                            ...item,
                            count
                        };
                    });

                    if (filteredItems.length === 0) {
                        return null;
                    }

                    return {
                        ...section,
                        items: filteredItems
                    };
                })
                .filter(section => section !== null) as SidebarSection[];
        }
    },
    actions: {
        toggle() {
            this.isOpen = !this.isOpen;
        },
        async fetchEventCount() {
            try {
                const httpClient = useHttpClient();
                const response: any = await httpClient.get(`${ENDPOINTS.EVENTS.USER}`);
                let activeEventCount = 0;

                if (response && response.events && response.events.data) {
                    // Filter for active events (published and not ended)
                    const now = new Date();
                    activeEventCount = response.events.data.filter((event: any) => {
                        // Must be published
                        if (!event.published_at) return false;

                        // Must not have ended (if end date exists)
                        if (event.end && new Date(event.end) < now) return false;

                        return true;
                    }).length;
                } else if (response && response.events && Array.isArray(response.events)) {
                    // Filter for active events (published and not ended)
                    const now = new Date();
                    activeEventCount = response.events.filter((event: any) => {
                        // Must be published
                        if (!event.published_at) return false;

                        // Must not have ended (if end date exists)
                        if (event.end && new Date(event.end) < now) return false;

                        return true;
                    }).length;
                }

                this.eventCount = activeEventCount;
            } catch (error) {
                console.error('Error fetching event count:', error);
                this.eventCount = 0;
            }
        },
        updateEventCount(count: number) {
            this.eventCount = count;
        },
        incrementEventCount() {
            this.eventCount++;
        },
        decrementEventCount() {
            if (this.eventCount > 0) {
                this.eventCount--;
            }
        },
        async fetchUnreadMessages() {
            try {
                const httpClient = useHttpClient();
                const response: any = await httpClient.get(`${ENDPOINTS.MESSAGES.GET_CONVERSATIONS}?unread_count=1`);
                if (response && response.data) {
                    this.unreadMessages = response.data.reduce((sum: number, conv: any) => sum + (conv.unread_count || 0), 0);
                } else {
                    this.unreadMessages = 0;
                }
            } catch (error) {
                console.error('Error fetching unread messages count:', error);
                this.unreadMessages = 0;
            }
        },
        async fetchUnreadNotifications() {
            try {
                const httpClient = useHttpClient();
                const response: any = await httpClient.get(`${ENDPOINTS.NOTIFICATIONS.GET}?unread=1&per_page=1`);
                if (response && typeof response.total === 'number') {
                    this.unreadNotifications = response.total;
                } else {
                    this.unreadNotifications = 0;
                }
            } catch (error) {
                console.error('Error fetching unread notifications count:', error);
                this.unreadNotifications = 0;
            }
        },
        async fetchPendingReviews() {
            try {
                const httpClient = useHttpClient();
                const response: any = await httpClient.get(`${ENDPOINTS.REVIEWS.STATS}`);
                if (response && response.data && typeof response.data.flaggedReviews === 'number') {
                    this.pendingReviews = response.data.flaggedReviews;
                } else {
                    this.pendingReviews = 0;
                }
            } catch (error) {
                console.error('Error fetching pending reviews count:', error);
                this.pendingReviews = 0;
            }
        },
        async fetchAllCommunicationCounts() {
            await Promise.all([
                this.fetchUnreadMessages(),
                this.fetchUnreadNotifications(),
                this.fetchPendingReviews()
            ]);
        },
        incrementUnreadMessages() {
            this.unreadMessages++;
        },
        decrementUnreadMessages() {
            if (this.unreadMessages > 0) {
                this.unreadMessages--;
            }
        },
        incrementUnreadNotifications() {
            this.unreadNotifications++;
        },
        decrementUnreadNotifications() {
            if (this.unreadNotifications > 0) {
                this.unreadNotifications--;
            }
        },
        incrementPendingReviews() {
            this.pendingReviews++;
        },
        decrementPendingReviews() {
            if (this.pendingReviews > 0) {
                this.pendingReviews--;
            }
        }
    },
    persist: true
})
