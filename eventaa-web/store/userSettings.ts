import { defineStore } from 'pinia'
import { ENDPOINTS } from '@/utils/api'

export interface NotificationSetting {
  id: string
  type: 'email' | 'sms' | 'push'
  key: string
  label: string
  description: string
  enabled: boolean
}

export interface SecuritySession {
  id: string
  device: string
  browser: string
  ip: string
  lastActive: string
  isCurrent: boolean
}

interface ApiResponse<T> {
  message?: string
  settings?: T
  sessions?: T | Record<string, T>
}

export interface UserSettingsState {
  notificationSettings: NotificationSetting[]
  securitySessions: SecuritySession[]
  twoFactorEnabled: boolean
  loading: boolean
  saving: boolean
  error: string | null
}

export const useUserSettingsStore = defineStore('userSettings', {
  state: (): UserSettingsState => ({
    notificationSettings: [],
    securitySessions: [],
    twoFactorEnabled: false,
    loading: false,
    saving: false,
    error: null
  }),

  getters: {
    getEmailNotifications: (state) => {
      return state.notificationSettings.filter(setting => setting.type === 'email')
    },
    getSmsNotifications: (state) => {
      return state.notificationSettings.filter(setting => setting.type === 'sms')
    },
    getPushNotifications: (state) => {
      return state.notificationSettings.filter(setting => setting.type === 'push')
    }
  },

  actions: {
    async fetchNotificationSettings(): Promise<boolean> {
      try {
        this.loading = true
        this.error = null
        const httpClient = useHttpClient()

        const response = await httpClient.get<ApiResponse<any[]>>(`${ENDPOINTS.SETTINGS.BASE}/get`)

        if (response && response.settings) {
          const settings = response.settings.map((setting: any) => ({
            id: setting.id.toString(),
            type: setting.type,
            key: setting.key,
            label: setting.name,
            description: setting.description,
            enabled: setting.user_enabled || false
          }))

          this.notificationSettings = settings
          return true
        }

        return false
      } catch (error: any) {
        console.error('Error fetching notification settings:', error)
        this.error = error.message || 'Failed to fetch notification settings'
        return false
      } finally {
        this.loading = false
      }
    },

    async saveNotificationSettings(settings: NotificationSetting[]): Promise<boolean> {
      try {
        this.saving = true
        this.error = null
        const httpClient = useHttpClient()

        const formattedSettings = settings.map(setting => ({
          id: setting.id,
          enabled: setting.enabled
        }))

        const response = await httpClient.post<{message: string}>(`${ENDPOINTS.SETTINGS.CREATE}`, {
          settings: formattedSettings
        })

        if (response) {
          this.notificationSettings = settings
          return true
        }

        return false
      } catch (error: any) {
        console.error('Error saving notification settings:', error)
        this.error = error.message || 'Failed to save notification settings'
        return false
      } finally {
        this.saving = false
      }
    },

    async fetchSecuritySessions(): Promise<boolean> {
      try {
        this.loading = true
        this.error = null
        const httpClient = useHttpClient()

        const response = await httpClient.get<ApiResponse<any>>(`${ENDPOINTS.SETTINGS.BASE}/sessions`)

        if (response && response.sessions) {
          const sessionsArray = Array.isArray(response.sessions)
            ? response.sessions
            : Object.values(response.sessions)

          this.securitySessions = sessionsArray.map(session => ({
            id: session.id,
            device: session.device,
            browser: session.browser,
            ip: session.ip_address,
            lastActive: session.last_active,
            isCurrent: session.is_current
          }))
          return true
        }

        return false
      } catch (error: any) {
        console.error('Error fetching security sessions:', error)
        this.error = error.message || 'Failed to fetch security sessions'
        return false
      } finally {
        this.loading = false
      }
    },

    async revokeSession(sessionId: string): Promise<boolean> {
      try {
        this.saving = true
        this.error = null
        const httpClient = useHttpClient()

        const response = await httpClient.post<{message: string}>(`${ENDPOINTS.SETTINGS.REVOKE_SESSION}`, {
          session_id: sessionId
        })

        if (response) {
          this.securitySessions = this.securitySessions.filter(session => session.id !== sessionId)
          return true
        }

        return false
      } catch (error: any) {
        console.error('Error revoking session:', error)
        this.error = error.message || 'Failed to revoke session'
        return false
      } finally {
        this.saving = false
      }
    },

    async changePassword(currentPassword: string, newPassword: string): Promise<boolean> {
      try {
        this.saving = true
        this.error = null
        const httpClient = useHttpClient()

        const response = await httpClient.post<{message: string}>(`${ENDPOINTS.AUTH.RESET_PASSWORD}`, {
          password: newPassword,
          confirm_password: newPassword
        })

        if (response) {
          return true
        }

        return false
      } catch (error: any) {
        console.error('Error changing password:', error)
        this.error = error.message || 'Failed to change password'
        return false
      } finally {
        this.saving = false
      }
    },

    async fetchTwoFactorStatus(): Promise<boolean> {
      try {
        this.loading = true
        this.error = null
        const httpClient = useHttpClient()

        const response = await httpClient.get<{enabled: boolean}>(`${ENDPOINTS.TWO_FACTOR.STATUS}`)

        if (response) {
          this.twoFactorEnabled = response.enabled
          return true
        }

        return false
      } catch (error: any) {
        console.error('Error fetching two-factor status:', error)
        this.error = error.message || 'Failed to fetch two-factor status'
        return false
      } finally {
        this.loading = false
      }
    },

    async enableTwoFactor(): Promise<boolean> {
      try {
        this.saving = true
        this.error = null
        const httpClient = useHttpClient()

        const response = await httpClient.post<{message: string, status: boolean}>(`${ENDPOINTS.TWO_FACTOR.ENABLE}`)

        if (response && response.status) {
          this.twoFactorEnabled = true
          return true
        }

        return false
      } catch (error: any) {
        console.error('Error enabling two-factor authentication:', error)
        this.error = error.message || 'Failed to enable two-factor authentication'
        return false
      } finally {
        this.saving = false
      }
    },

    async disableTwoFactor(): Promise<boolean> {
      try {
        this.saving = true
        this.error = null
        const httpClient = useHttpClient()

        const response = await httpClient.post<{message: string, status: boolean}>(`${ENDPOINTS.TWO_FACTOR.DISABLE}`)

        if (response && response.status) {
          this.twoFactorEnabled = false
          return true
        }

        return false
      } catch (error: any) {
        console.error('Error disabling two-factor authentication:', error)
        this.error = error.message || 'Failed to disable two-factor authentication'
        return false
      } finally {
        this.saving = false
      }
    }
  }
})
