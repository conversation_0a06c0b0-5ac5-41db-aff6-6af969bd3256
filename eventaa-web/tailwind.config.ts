/** @type {import('tailwindcss').Config} */

import FormKitTailwind from '@formkit/themes/tailwindcss'
/** @type {import('tailwindcss').Config} */

module.exports = {
  darkMode: 'class',
  content: [
    "./components/**/*.{js,vue,ts}",
    "./layouts/**/*.vue",
    "./pages/**/*.vue",
    "./plugins/**/*.{js,ts}",
    "./nuxt.config.{js,ts}",
    "./app.vue",
    "./node_modules/vue-tailwind-datepicker/**/*.js",
    './node_modules/@formkit/themes/dist/tailwindcss/genesis/index.cjs',
    './vueform.config.ts',
    './node_modules/@vueform/vueform/themes/tailwind/**/*.vue',
    './node_modules/@vueform/vueform/themes/tailwind/**/*.js',
  ],
  theme: {
    extend: {

    },
  },
  plugins: [
    FormKitTailwind,
    require('@vueform/vueform/tailwind'),
  ],
  css: []
}