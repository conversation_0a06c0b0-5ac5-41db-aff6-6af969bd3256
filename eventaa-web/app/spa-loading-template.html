<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>EventaHub Malawi - Loading...</title>
  <link rel="icon" type="image/x-icon" href="/favicon.ico">
  <link rel="icon" type="image/png" sizes="32x32" href="/icon.png">
  <style>
    .spa-loading-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      opacity: 1;
      transition: opacity 0.5s ease-out;
    }

    @media (prefers-color-scheme: dark) {
      .spa-loading-container {
        background: linear-gradient(135deg, #18181b 0%, #27272a 100%);
        color: #f4f4f5;
      }
    }

    .spa-loading-logo {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      margin-bottom: 2rem;
      box-shadow: 0 10px 25px rgba(239, 68, 68, 0.2);
      animation: logoFloat 3s ease-in-out infinite;
    }

    .spa-loading-title {
      font-size: 2rem;
      font-weight: 700;
      color: #111827;
      margin-bottom: 0.5rem;
      letter-spacing: -0.025em;
    }

    @media (prefers-color-scheme: dark) {
      .spa-loading-title {
        color: #f4f4f5;
      }
    }

    .spa-loading-subtitle {
      font-size: 1rem;
      color: #6b7280;
      margin-bottom: 3rem;
      text-align: center;
      max-width: 400px;
    }

    @media (prefers-color-scheme: dark) {
      .spa-loading-subtitle {
        color: #a1a1aa;
      }
    }

    .core-loader {
      border: 5px solid;
      border-color: #ef4444 transparent;
      border-radius: 50%;
      display: inline-block;
      box-sizing: border-box;
      animation: rotation 1s linear infinite;
      width: 50px;
      height: 50px;
      margin-bottom: 1rem;
    }

    @media (prefers-color-scheme: dark) {
      .core-loader {
        border-color: #ef4444 transparent;
      }
    }

    .spa-loading-text {
      font-size: 0.875rem;
      color: #9ca3af;
      font-weight: 500;
    }

    @media (prefers-color-scheme: dark) {
      .spa-loading-text {
        color: #71717a;
      }
    }

    .spa-loading-progress {
      width: 200px;
      height: 4px;
      background-color: #e5e7eb;
      border-radius: 2px;
      overflow: hidden;
      margin-top: 1rem;
    }

    @media (prefers-color-scheme: dark) {
      .spa-loading-progress {
        background-color: #3f3f46;
      }
    }

    .spa-loading-progress-bar {
      height: 100%;
      background: linear-gradient(90deg, #ef4444, #dc2626);
      border-radius: 2px;
      animation: progressLoad 2s ease-in-out infinite;
    }

    @keyframes logoFloat {

      0%,
      100% {
        transform: translateY(0px);
      }

      50% {
        transform: translateY(-10px);
      }
    }

    @keyframes rotation {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    @keyframes progressLoad {
      0% {
        width: 0%;
        transform: translateX(-100%);
      }

      50% {
        width: 100%;
        transform: translateX(0%);
      }

      100% {
        width: 100%;
        transform: translateX(100%);
      }
    }

    /* Responsive design */
    @media (max-width: 640px) {
      .spa-loading-logo {
        width: 60px;
        height: 60px;
        margin-bottom: 1.5rem;
      }

      .spa-loading-title {
        font-size: 1.5rem;
      }

      .spa-loading-subtitle {
        font-size: 0.875rem;
        margin-bottom: 2rem;
        padding: 0 1rem;
      }

      .core-loader {
        width: 40px;
        height: 40px;
      }

      .spa-loading-progress {
        width: 150px;
      }
    }

    .spa-loading-fade-out {
      opacity: 0;
      transition: opacity 0.5s ease-out;
    }
  </style>
</head>

<body>
  <div id="__nuxt">
    <div class="spa-loading-container" id="spa-loading">
      <img src="/icon.png" alt="EventaHub" class="spa-loading-logo" />

      <h1 class="spa-loading-title">EventaHub Malawi</h1>
      <p class="spa-loading-subtitle">
        Your premium event booking platform. Discover amazing events, venues, vendors and more...
      </p>

      <div class="core-loader"></div>
      <p class="spa-loading-text" id="loading-status">Loading your experience...</p>

      <div class="spa-loading-progress">
        <div class="spa-loading-progress-bar"></div>
      </div>

    </div>
  </div>

  <script>
    let hideLoadingCalled = false;
    let loadingStartTime = Date.now();

    const statusMessages = [
      'Loading your experience...',
      'Initializing components...',
      'Setting up everything for you...',
      'Almost ready...'
    ];

    let messageIndex = 0;
    const statusElement = document.getElementById('loading-status');

    function updateStatus() {
      if (statusElement && messageIndex < statusMessages.length - 1) {
        messageIndex++;
        statusElement.textContent = statusMessages[messageIndex];
      }
    }

    const statusInterval = setInterval(updateStatus, 2000);

    function hideLoading() {
      if (hideLoadingCalled) return;
      hideLoadingCalled = true;

      clearInterval(statusInterval);

      const loadingElement = document.getElementById('spa-loading');
      if (loadingElement) {
        loadingElement.classList.add('spa-loading-fade-out');
        setTimeout(() => {
          loadingElement.style.display = 'none';
        }, 500);
      }
    }

    window.addEventListener('load', function () {
      setTimeout(hideLoading, 100);
    });

    setTimeout(hideLoading, 8000);

    window.addEventListener('nuxt:ready', hideLoading);
    window.addEventListener('app:mounted', hideLoading);

    setTimeout(function checkContent() {
      const nuxtElement = document.querySelector('#__nuxt > *:not(#spa-loading)');
      if (nuxtElement && nuxtElement.offsetHeight > 0) {
        hideLoading();
      } else if (Date.now() - loadingStartTime < 8000) {
        setTimeout(checkContent, 500);
      }
    }, 2000);

    document.addEventListener('DOMContentLoaded', function () {
      const loadingElement = document.getElementById('spa-loading');
      if (loadingElement) {
        loadingElement.style.transition = 'opacity 0.5s ease-out';
      }
    });
  </script>
</body>

</html>
