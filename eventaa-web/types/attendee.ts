import type { EventItem } from './index';

export interface Attendee {
  id: number;
  name: string;
  email: string;
  avatar?: string;
  event: {
    id: number;
    title: string;
    date: string;
  };
  ticket: {
    type: string;
    price: number;
  };
  purchaseDate: string;
  status: 'confirmed' | 'checked-in' | 'no-show' | 'cancelled';
}

export interface AttendeeFilters {
  eventId: string;
  status: string;
  ticketType: string;
}

export interface AttendeeResponse {
  data: Attendee[];
  total: number;
  current_page: number;
  per_page: number;
  last_page: number;
}

export interface AttendeeStats {
  total_attendees: number;
  checked_in: number;
  not_checked_in: number;
  total_revenue: number;
}

export interface EventsResponse {
  events: {
    current_page: number;
    data: EventItem[];
    first_page_url: string;
    from: number;
    last_page: number;
    last_page_url: string;
    links: Array<{
      url: string | null;
      label: string;
      active: boolean;
    }>;
    next_page_url: string | null;
    path: string;
    per_page: number;
    prev_page_url: string | null;
    to: number;
    total: number;
  };
}
