import type { Package } from "./ticket";
import type { User } from "./user";

type EventItem = {
    id: number;
    slug?: string;
    user_id: number;
    user: User,
    type_id: number;
    visibility_id: number;
    title: string;
    description: string;
    location: string;
    district_id: number;
    category?: {
        icon: string;
        name: string;
    },
    category_id: number;
    latitude: string;
    longitude: string;
    cover_art: string;
    start: string;
    end: string;
    ticket_price: number;
    created_at: string;
    updated_at: string;
    ticket_banner: string | null;
    published_at: string | null;
    attendees_count: number;
    likes_count: number;
    shares_count: number;
    attendees_avatars: string;
    likes_avatars?: string; // Optional field for future use
    is_liked: boolean;
    is_attending?: boolean;
    is_attendee?: boolean;
    tiers: Package[];
    ratings: Rating[];
    highlights: Highlight[];
    visibility?: Visibility;
    meeting_link?: MeetingLink;
    sponsors: SponsorItem[];
    ratings_avg_organization: number;
    ratings_avg_content: number;
    ratings_avg_technical: number;
    ratings_avg_engagement: number;
    ratings_avg_rating: number;
}

type Highlight = {
    id: number;
    text: string;
    timestamp: string;
    category: 'positive' | 'negative' | 'neutral';
}

type Rating = {
    id: number;
    event_id: number;
    user_id: number;
    rating: number;
    review: string;
    created_at: string;
    updated_at: string;
    organization: number;
    content: number;
    technical: number;
    engagement: number;
    user?: User;
}

type VenueRating = {
    iid: number;
    comment: string;
    rating: number;
    created_at: string;
    updated_at: string;
    user: User;
}

type SponsorItem = {
    id: number;
    event_id: number;
    sponsor_id: number;
    created_at: string;
    updated_at: string;
    sponsor: {
        id: number;
        name: string;
        logo: string;
        latitude: string;
        longitude: string;
        address: string;
        city: string;
        country: string;
        created_at: string;
        updated_at: string;
    };
};


type MeetingLink = {
    id: number;
    link: string;
}

type Visibility = {
    name: string;
    id: number;
}

type Category = {
    id: number;
    name: string;
    icon?: string;
    events_count?: number;
    description?: string;
    created_at?: string;
}

interface GeoCoordinate {
    lat: number;
    lng: number;
}

type Tier = {
    id: number;
    name: string;
    description: string;
    price: number;
}

type TicketCartItem = {
    sn: number;
    event: EventItem;
    tier: Package;
    quantity: number;
    total: number;
}

type SidebarItem = {
    title: string;
    link: string;
    icon?: string;
    count?: number;
    permissions?: string[];
    roles?: string[];
}

type Sponsor = {
    id: number;
    name: string;
    address: string;
    city: string;
    country: string;
    latitude: string;
    longitude: string;
    logo: string;
    created_at: string;
    updated_at: string;
}

type Location = {
    street?: string;
    city?: string;
    country?: string;
    latlong?: {
        lat?: number;
        lng?: number;
    };
    district?: string;
}

interface Article {
    id: number;
    title: string;
    content: string;
    image: string;
    user_id: number;
    category_id: number;
    slug: string;
    is_published: number;
    published_at: string | null;
    created_at: string;
    updated_at: string;
    user: User;
    category: Category;
}

interface PaginationLink {
    url: string | null;
    label: string;
    active: boolean;
}

interface PaginatedResponse {
    current_page: number;
    data: Article[];
    first_page_url: string;
    from: number;
    last_page: number;
    last_page_url: string;
    links: PaginationLink[];
    next_page_url: string | null;
    path: string;
    per_page: number;
    prev_page_url: string | null;
    to: number;
    total: number;
}

interface ArticleFilters {
    dateFrom: string;
    dateTo: string;
    status: 'All' | 'Published' | 'Draft';
    categoryId: number | 'All';
}

interface PlanAttributes {
    support: string;
    duration: string;
    analytics: boolean;
    custom_branding: boolean;
    featured_events: boolean;
    ticket_scanning: boolean;
    payment_gateways: string[];
    sms_notifications: boolean;
    multiple_organizers: boolean;
    event_page_customization: boolean;
}

interface Plan {
    id: number;
    name: string;
    tickets_limit: number;
    price: string;
    attributes: PlanAttributes;
    created_at: string;
    updated_at: string;
}

interface Price {
    id?: number;
    name: string;
    amount: string;
    currency: string;
    attributes: string[];
}

interface VenueForm {
    id?: number;
    name: string;
    activities: string[];
    address: string;
    slug: string;
    city: string;
    state: string;
    zip: string;
    country: string;
    phone: string;
    email: string;
    website: string | null;
    description: string;
    logo: any;
    capacity: number | null;
    latitude: number | null;
    longitude: number | null;
    images: File[];
    videos: File[];
    prices: Price[];
};


interface Currency {
    id: number;
    name: string;
    created_at: string | null;
    updated_at: string | null;
}

interface VenuePrice {
    id: number;
    venue_id: number;
    price: string;
    ammenities: string;
    created_at: string;
    updated_at: string;
    currency_id: number;
    currency: Currency;
}

interface VenueImage {
    id: number;
    venue_id: number;
    image_path: string;
    created_at: string;
    updated_at: string;
}

export interface VenueBooking {
    id: number;
    venue_id: number;
    user_id: number;
    category_id: number;
    booking_from: string;
    booking_to: string;
    number_of_guests: number;
    message: string;
    status: "pending" | "confirmed" | "rejected";
    venue_price_id: number;
    created_at: string;
    updated_at: string;
    venue?: {
        id: number;
        name: string;
        [key: string]: any;
    };
    user?: {
        id: number;
        name: string;
        email: string;
        avatar?: string;
        [key: string]: any;
    };
    venue_price?: {
        id: number;
        amount: number;
        currency?: {
            code: string;
            [key: string]: any;
        };
        [key: string]: any;
    };
}

interface Venue {
    id: number;
    name: string;
    slug: string;
    address: string;
    city: string;
    state: string;
    zip: string;
    country: string;
    phone: string;
    email: string;
    website: string;
    description: string;
    amenities: string;
    logo: string;
    capacity: number;
    latitude: string;
    longitude: string;
    created_at: string;
    updated_at: string;
    user_id: number;
    ratings_avg_rating: number | null;
    user: User;
    images: VenueImage[];
    prices: VenuePrice[];
    activities: {
        category: Category[]
    };
    ratings: VenueRating[];
    bookings: VenueBooking[];
    venue_price?: VenuePrice;
}


export {
    VenueForm,
    Venue,
    Currency,
    VenueImage,
    VenuePrice,
    Price,
    Plan,
    PlanAttributes,
    Sponsor,
    SidebarItem,
    EventItem,
    Category,
    Tier,
    TicketCartItem,
    GeoCoordinate,
    SponsorItem,
    Location,
    Highlight,
    Article,
    ArticleFilters,
    PaginatedResponse,
    PaginationLink
}

export type { Transaction } from './transaction';
