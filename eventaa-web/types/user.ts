import type { Category, EventItem, Venue } from ".";
import type { ApiVendor } from "./vendor";

export interface Profile {
  id: number;
  user_id: number;
  about: string | null;
  is_organization: boolean;
  facebook: string | null;
  twitter: string | null;
  instagram: string | null;
  tiktok: string | null;
  created_at: string;
  updated_at: string;
}


export interface User {
  id: number
  name: string
  email: string
  avatar: string
  google_id: string | null
  facebook_id: string | null
  email_verified_at: string | null
  is_verified: boolean
  created_at: string
  updated_at: string
  deactivation_date: string | null
  android_app_token: string | null
  phone: string | null
  profile?: Profile,
  events: EventItem[],
  vendors?: ApiVendor[],
  venues?: Venue[],
  interests: Category[],
  twitter_url: string | null
  facebook_url: string | null
  followers_count: number
  following_count: number
  ratings_avg_rating: number
  is_following?: boolean
  roles?: string[]
  two_factor_enabled?: boolean
}
