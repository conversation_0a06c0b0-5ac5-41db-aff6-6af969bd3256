import type { User } from "./user";

export interface Permission {
  id: number;
  name: string;
  guard_name: string;
  description?: string;
  created_at: string;
  updated_at: string;
  roles?: Role[];
}

export interface Role {
  id: number;
  name: string;
  guard_name: string;
  created_at: string;
  updated_at: string;
  permissions?: Permission[];
  users?: User[];
}

export interface PermissionsByCategory {
  [category: string]: Permission[];
}
