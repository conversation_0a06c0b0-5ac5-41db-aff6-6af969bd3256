declare namespace google.maps {
    export class DirectionsService {
        route(request: DirectionsRequest): Promise<DirectionsResult>;
    }

    export class DirectionsRenderer {
        constructor(options?: DirectionsRendererOptions);
        setMap(map: google.maps.Map | null): void;
        setDirections(directions: DirectionsResult): void;
    }

    export interface DirectionsRequest {
        origin: string | google.maps.LatLng | google.maps.Place | google.maps.LatLngLiteral;
        destination: string | google.maps.LatLng | google.maps.Place | google.maps.LatLngLiteral;
        travelMode: google.maps.TravelMode;
    }

    export interface DirectionsResult {
        routes: Array<DirectionsRoute>;
    }

    export class Geocoder {
        constructor();
        geocode(request: GeocoderRequest): Promise<GeocoderResponse>;
    }

    export interface GeocoderRequest {
        address?: string;
        location?: LatLng | LatLngLiteral;
        placeId?: string;
        bounds?: LatLngBounds | LatLngBoundsLiteral;
        componentRestrictions?: GeocoderComponentRestrictions;
        region?: string;
    }

    export interface GeocoderResponse {
        results: GeocoderResult[];
        status: GeocoderStatus;
    }

    export interface GeocoderResult {
        address_components: GeocoderAddressComponent[];
        formatted_address: string;
        geometry: GeocoderGeometry;
        place_id: string;
        types: string[];
    }

    export interface GeocoderAddressComponent {
        long_name: string;
        short_name: string;
        types: string[];
    }

    export interface GeocoderGeometry {
        location: LatLng;
        location_type: GeocoderLocationType;
        viewport: LatLngBounds;
        bounds?: LatLngBounds;
    }

    export enum GeocoderStatus {
        OK = 'OK',
        ZERO_RESULTS = 'ZERO_RESULTS',
        OVER_QUERY_LIMIT = 'OVER_QUERY_LIMIT',
        REQUEST_DENIED = 'REQUEST_DENIED',
        INVALID_REQUEST = 'INVALID_REQUEST',
        UNKNOWN_ERROR = 'UNKNOWN_ERROR'
    }

    export enum GeocoderLocationType {
        APPROXIMATE = 'APPROXIMATE',
        GEOMETRIC_CENTER = 'GEOMETRIC_CENTER',
        RANGE_INTERPOLATED = 'RANGE_INTERPOLATED',
        ROOFTOP = 'ROOFTOP'
    }

    export interface GeocoderComponentRestrictions {
        country: string | string[];
    }

    export class PlacesService {
        constructor(attrContainer: HTMLDivElement | Map);
        findPlaceFromQuery(request: FindPlaceFromQueryRequest): Promise<PlacesServiceResponse>;
        findPlaceFromPhoneNumber(request: FindPlaceFromPhoneNumberRequest): Promise<PlacesServiceResponse>;
        getDetails(request: PlaceDetailsRequest): Promise<PlaceResult>;
        nearbySearch(request: PlaceSearchRequest): Promise<PlacesServiceResponse>;
        textSearch(request: TextSearchRequest): Promise<PlacesServiceResponse>;
    }

    export interface PlacesServiceResponse {
        results: PlaceResult[];
        status: PlacesServiceStatus;
    }

    export interface PlaceResult {
        place_id: string;
        name: string;
        geometry: {
            location: LatLng;
            viewport?: LatLngBounds;
        };
        formatted_address?: string;
        types?: string[];
        rating?: number;
        photos?: PlacePhoto[];
        opening_hours?: PlaceOpeningHours;
    }

    export interface PlacePhoto {
        height: number;
        width: number;
        getUrl(opts: PhotoOptions): string;
    }

    export interface PhotoOptions {
        maxHeight?: number;
        maxWidth?: number;
    }

    export interface PlaceOpeningHours {
        isOpen(date?: Date): boolean;
        periods: PlaceOpeningHoursPeriod[];
        weekday_text: string[];
    }

    export interface PlaceOpeningHoursPeriod {
        open: PlaceOpeningHoursTime;
        close: PlaceOpeningHoursTime;
    }

    export interface PlaceOpeningHoursTime {
        day: number;
        time: string;
    }

    export interface FindPlaceFromQueryRequest {
        query: string;
        fields: string[];
    }

    export interface FindPlaceFromPhoneNumberRequest {
        phoneNumber: string;
        fields: string[];
    }

    export interface PlaceDetailsRequest {
        placeId: string;
        fields?: string[];
    }

    export interface PlaceSearchRequest {
        location: LatLng | LatLngLiteral;
        radius: number;
        type?: string;
        keyword?: string;
    }

    export interface TextSearchRequest {
        query: string;
        location?: LatLng | LatLngLiteral;
        radius?: number;
        bounds?: LatLngBounds | LatLngBoundsLiteral;
    }

    export enum PlacesServiceStatus {
        OK = 'OK',
        ZERO_RESULTS = 'ZERO_RESULTS',
        OVER_QUERY_LIMIT = 'OVER_QUERY_LIMIT',
        REQUEST_DENIED = 'REQUEST_DENIED',
        INVALID_REQUEST = 'INVALID_REQUEST',
        UNKNOWN_ERROR = 'UNKNOWN_ERROR',
        NOT_FOUND = 'NOT_FOUND'
    }

    export interface Place {
        location: LatLng;
        placeId: string;
        name?: string;
    }

    export class Autocomplete {
        constructor(inputField: HTMLInputElement, options?: AutocompleteOptions);
        getPlace(): PlaceResult;
        setBounds(bounds: LatLngBounds | LatLngBoundsLiteral): void;
        setComponentRestrictions(restrictions: ComponentRestrictions): void;
        setTypes(types: string[]): void;
    }

    export interface AutocompleteOptions {
        bounds?: LatLngBounds | LatLngBoundsLiteral;
        componentRestrictions?: ComponentRestrictions;
        placeIdOnly?: boolean;
        strictBounds?: boolean;
        types?: string[];
    }

    export interface ComponentRestrictions {
        country: string | string[];
    }

    export interface MapMouseEvent {
        stop(): void;
        latLng: LatLng;
        domEvent: Event;
    }

    export interface MapsEventListener {
        remove(): void;
    }

    export class event {
        static addListener(instance: Object, eventName: string, handler: Function): MapsEventListener;
        static addDomListener(instance: Element, eventName: string, handler: Function, capture?: boolean): MapsEventListener;
        static clearInstanceListeners(instance: Object): void;
        static clearListeners(instance: Object, eventName: string): void;
        static removeListener(listener: MapsEventListener): void;
        static trigger(instance: Object, eventName: string, ...args: any[]): void;
    }

    export interface DirectionsRoute {
        overview_path: Array<LatLng>;
        legs: Array<DirectionsLeg>;
    }

    export interface DirectionsLeg {
        distance: Distance;
        duration: Duration;
        steps: Array<DirectionsStep>;
    }

    export interface DirectionsStep {
        distance: Distance;
        duration: Duration;
        instructions: string;
    }

    export interface Distance {
        text: string;
        value: number;
    }

    export interface Duration {
        text: string;
        value: number;
    }

    export interface DirectionsRendererOptions {
        suppressMarkers?: boolean;
        polylineOptions?: PolylineOptions;
    }

    export interface PolylineOptions {
        strokeColor?: string;
        strokeWeight?: number;
        strokeOpacity?: number;
    }

    export class LatLng {
        constructor(lat: number, lng: number, noWrap?: boolean);
        lat(): number;
        lng(): number;
        toString(): string;
        toJSON(): LatLngLiteral;
        equals(other: LatLng): boolean;
    }

    export interface LatLngLiteral {
        lat: number;
        lng: number;
    }

    export class LatLngBounds {
        constructor(sw?: LatLng | LatLngLiteral, ne?: LatLng | LatLngLiteral);
        contains(latLng: LatLng | LatLngLiteral): boolean;
        equals(other: LatLngBounds | LatLngBoundsLiteral): boolean;
        extend(point: LatLng | LatLngLiteral): LatLngBounds;
        getCenter(): LatLng;
        getNorthEast(): LatLng;
        getSouthWest(): LatLng;
        intersects(other: LatLngBounds | LatLngBoundsLiteral): boolean;
        isEmpty(): boolean;
        toJSON(): LatLngBoundsLiteral;
        toString(): string;
        union(other: LatLngBounds | LatLngBoundsLiteral): LatLngBounds;
    }

    export interface LatLngBoundsLiteral {
        east: number;
        north: number;
        south: number;
        west: number;
    }

    export class Map {
        constructor(mapDiv: HTMLElement, opts?: MapOptions);
        setCenter(latLng: LatLng | LatLngLiteral): void;
        setZoom(zoom: number): void;
        getCenter(): LatLng;
        getZoom(): number;
        panTo(latLng: LatLng | LatLngLiteral): void;
        panBy(x: number, y: number): void;
        getBounds(): LatLngBounds;
        fitBounds(bounds: LatLngBounds | LatLngBoundsLiteral, padding?: number | Padding): void;
    }

    export interface MapOptions {
        center?: LatLng | LatLngLiteral;
        zoom?: number;
        mapTypeId?: string;
        disableDefaultUI?: boolean;
        zoomControl?: boolean;
        mapTypeControl?: boolean;
        scaleControl?: boolean;
        streetViewControl?: boolean;
        rotateControl?: boolean;
        fullscreenControl?: boolean;
        styles?: MapTypeStyle[];
        gestureHandling?: string;
    }

    export interface Padding {
        top: number;
        right: number;
        bottom: number;
        left: number;
    }

    export interface MapTypeStyle {
        stylers: MapTypeStyler[];
        elementType?: string;
        featureType?: string;
    }

    export interface MapTypeStyler {
        hue?: string;
        lightness?: number;
        saturation?: number;
        gamma?: number;
        invert_lightness?: boolean;
        visibility?: string;
        color?: string;
        weight?: number;
    }

    export enum TravelMode {
        DRIVING = 'DRIVING',
        WALKING = 'WALKING',
        BICYCLING = 'BICYCLING',
        TRANSIT = 'TRANSIT'
    }

    export namespace places {
        export class PlaceAutocompleteElement {
            constructor(options?: PlaceAutocompleteElementOptions);
            getFormData(): FormData;
            getPlace(): PlaceResult;
            mount(container: HTMLElement): void;
            unmount(): void;
        }
        
        export interface PlaceAutocompleteElementOptions {
            componentRestrictions?: ComponentRestrictions;
            fields?: string[];
            strictBounds?: boolean;
            types?: string[];
        }
        
        export class Autocomplete {
            constructor(inputField: HTMLInputElement, options?: AutocompleteOptions);
            getPlace(): PlaceResult;
            setBounds(bounds: LatLngBounds | LatLngBoundsLiteral): void;
            setComponentRestrictions(restrictions: ComponentRestrictions): void;
            setTypes(types: string[]): void;
        }
        
        export interface AutocompleteOptions {
            bounds?: LatLngBounds | LatLngBoundsLiteral;
            componentRestrictions?: ComponentRestrictions;
            placeIdOnly?: boolean;
            strictBounds?: boolean;
            types?: string[];
        }
        
        export class PlacesService {
            constructor(attrContainer: HTMLDivElement | Map);
            findPlaceFromQuery(request: FindPlaceFromQueryRequest): Promise<PlacesServiceResponse>;
            findPlaceFromPhoneNumber(request: FindPlaceFromPhoneNumberRequest): Promise<PlacesServiceResponse>;
            getDetails(request: PlaceDetailsRequest): Promise<PlaceResult>;
            nearbySearch(request: PlaceSearchRequest): Promise<PlacesServiceResponse>;
            textSearch(request: TextSearchRequest): Promise<PlacesServiceResponse>;
        }
        
        export interface PlacesServiceResponse {
            results: PlaceResult[];
            status: PlacesServiceStatus;
        }
        
        export enum PlacesServiceStatus {
            OK = 'OK',
            ZERO_RESULTS = 'ZERO_RESULTS',
            OVER_QUERY_LIMIT = 'OVER_QUERY_LIMIT',
            REQUEST_DENIED = 'REQUEST_DENIED',
            INVALID_REQUEST = 'INVALID_REQUEST',
            UNKNOWN_ERROR = 'UNKNOWN_ERROR',
            NOT_FOUND = 'NOT_FOUND'
        }
        
        export interface SearchBox {
            constructor(inputField: HTMLInputElement, options?: SearchBoxOptions);
            getBounds(): LatLngBounds;
            getPlaces(): PlaceResult[];
            setBounds(bounds: LatLngBounds | LatLngBoundsLiteral): void;
        }
        
        export interface SearchBoxOptions {
            bounds?: LatLngBounds | LatLngBoundsLiteral;
        }
    }
}