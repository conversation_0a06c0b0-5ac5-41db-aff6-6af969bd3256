export interface Package {
    id?: string;
    name: string;
    seat: string;
    description: string;
    banner?: string;
    price: string;
    event_id?: number;
    is_refundable: boolean;
    refund_fee_percentage?: number;
}

export interface Ticket {
    id: number;
    event_id: number;
    tier_id?: number;
    uuid: string;
    name: string;
    price: number;
    banner?: string;
    description?: string;
    status: 'active' | 'inactive' | 'sold_out';
    quantity_available: number;
    quantity_sold: number;
    sale_start_date?: string;
    sale_end_date?: string;
    is_refundable: boolean;
    refund_fee_percentage: number;
    scanned: boolean;
    scanned_at?: string;
    metadata?: Record<string, any>;
    created_at: string;
    updated_at: string;
    tier?: {
        id: number;
        name: string;
        price: number;
        banner?: string;
        is_refundable: boolean;
        refund_fee_percentage?: number;
    };
}

export interface TicketPurchase {
    id: number;
    user_id: number;
    event_id: number;
    ticket_id: number;
    payment_id: number;
    purchase_reference: string;
    quantity: number;
    unit_price: number;
    total_amount: number;
    fees: number;
    taxes: number;
    status: 'pending' | 'completed' | 'cancelled' | 'refunded';
    purchased_at?: string;
    attendee_name?: string;
    attendee_email?: string;
    attendee_phone?: string;
    attendee_details?: Record<string, any>;
    cancellation_reason?: string;
    cancelled_at?: string;
    refund_amount?: number;
    refunded_at?: string;
    metadata?: Record<string, any>;
    created_at: string;
    updated_at: string;

    // Relationships
    user?: {
        id: number;
        name: string;
        email: string;
        avatar?: string;
    };
    event?: {
        id: number;
        title: string;
        start: string;
        end?: string;
        location?: string;
        banner?: string;
    };
    ticket?: Ticket;
    payment?: {
        id: number;
        amount: number;
        status: string;
        payment_method: string;
        transaction_id: string;
        currency?: {
            code: string;
            symbol: string;
        };
    };
}

export interface RefundRequest {
    id: number;
    purchase_id: number;
    user_id: number;
    reason: string;
    refund_amount: number;
    processing_fee: number;
    status: 'pending' | 'approved' | 'rejected' | 'processed';
    requested_at: string;
    processed_at?: string;
    admin_notes?: string;
    refund_method?: string;
    refund_reference?: string;
    created_at: string;
    updated_at: string;

    // Relationships
    purchase?: TicketPurchase;
    user?: {
        id: number;
        name: string;
        email: string;
    };
}

export interface RefundEligibility {
    eligible: boolean;
    reason?: string;
    refund_amount?: number;
    processing_fee?: number;
    refund_deadline?: string;
    policy_text?: string;
}

export interface RefundCalculation {
    original_amount: number;
    processing_fee: number;
    refund_amount: number;
    fee_percentage: number;
}
