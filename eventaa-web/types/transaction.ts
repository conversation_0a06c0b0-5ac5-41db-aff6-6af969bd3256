import type { User } from './user';

export interface PaymentMethod {
  id: number;
  vendor_id: number;
  payment_gateway_id: number;
  is_default: boolean;
  account_details: Record<string, any>;
  status: string;
  last_used_at: string;
  created_at: string;
  updated_at: string;
}

export interface PaymentGateway {
  id: number;
  name: string;
  slug: string;
  description: string;
  is_active: boolean;
  config: Record<string, any>;
  logo: string;
  supported_currencies: string[];
  test_mode: boolean;
  created_at: string;
  updated_at: string;
}

export interface MobileMoneyProvider {
  name: string;
  ref_id: string;
  country: string;
}

export interface PaymentAuthorization {
  brand: string | null;
  expiry: string | null;
  channel: string;
  provider: string;
  payer_bank: string | null;
  card_number: string | null;
  completed_at: string | null;
  mobile_number: string | null;
  payer_bank_uuid: string | null;
  payer_account_name: string | null;
  payer_account_number: string | null;
  mobile_money_trans_id: string | null;
  payer_bank_receipt_number: string | null;
}

export interface TransactionCharges {
  amount: string;
  currency: string;
}

export interface PaymentCustomer {
  email: string;
  phone: string | null;
  last_name: string;
  created_at: number;
  first_name: string;
  customer_ref: string;
}

export interface GatewayResponseData {
  logs: any[];
  mode: string;
  type: string;
  email: string;
  amount: number;
  mobile: string;
  ref_id: string;
  status: string;
  attempts: number;
  currency: string;
  customer: PaymentCustomer;
  trace_id: string;
  trans_id: string | null;
  charge_id: string;
  last_name: string;
  created_at: string;
  event_type: string;
  first_name: string;
  completed_at: string | null;
  mobile_money: MobileMoneyProvider;
  authorization: PaymentAuthorization;
  transaction_charges: TransactionCharges;
}

export interface GatewayResponse {
  data: {
    data: GatewayResponseData;
    status: string;
    message: string;
  };
  status: boolean;
  charge_id: string;
  is_test_number: boolean;
  verification_job: {
    amount: string;
    status: string;
    currency: string;
    charge_id: string;
    test_payment: boolean;
  };
}

export interface TicketItem {
  event_id: number;
  quantity: number;
  ticket_id: number;
  unit_price: string;
  ticket_name: string;
  total_price: number;
}

export interface TransactionMetadata {
  tickets: TicketItem[];
  charge_id: string;
  operator_id: string;
  phone_number: string;
}

export interface Transaction {
  id: number;
  transaction_id: string;
  vendor_payment_method_id: number | null;
  user_id: number;
  amount: string;
  currency: string;
  status: 'completed' | 'pending' | 'failed' | 'refunded';
  payment_type: string;
  bookable_type: string;
  bookable_id: number;
  metadata: TransactionMetadata;
  payment_date: string;
  gateway_response: GatewayResponse;
  created_at: string;
  updated_at: string;
  payment_method: PaymentMethod | null;
  user: User;
  transactionId?: string;
  date?: string;
  event?: {
    id: number;
    title: string;
    date: string;
  };
  event_name?: string;
  customer_name?: string;
  customer_email?: string;
  paymentMethod?: string;
}
