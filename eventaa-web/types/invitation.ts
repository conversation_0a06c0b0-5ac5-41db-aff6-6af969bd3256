export interface EventInvitation {
  id?: number;
  event_id: number;
  email: string;
  name?: string;
  avatar?: string;
  status: 'pending' | 'sent' | 'accepted' | 'declined';
  invited_by?: number;
  invited_by_name?: string;
  invitation_token?: string;
  message?: string;
  invited_at?: string;
  responded_at?: string;
  resent_at?: string;
  created_at?: string;
  updated_at?: string;
}

export interface InviteUserRequest {
  event_id: number;
  email: string;
  message?: string;
}

export interface InviteUsersRequest {
  event_id: number;
  emails: string[];
  message?: string;
}

export interface EventInvitationResponse {
  data: EventInvitation;
  message: string;
}

export interface EventInvitationsResponse {
  data: EventInvitation[];
  message?: string;
  meta?: {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
}
