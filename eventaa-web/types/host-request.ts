import type { User } from "./user";

export interface HostRequest {
  id: number;
  user_id: number;
  status: "pending" | "approved" | "rejected";
  reason: string;
  admin_notes?: string;
  requested_at: string;
  processed_at?: string;
  processed_by?: number;
  created_at: string;
  updated_at: string;

  // Relationships
  user: User;
  processedBy?: User;
}

export interface HostRequestStatistics {
  total: number;
  pending: number;
  approved: number;
  rejected: number;
}

export interface HostRequestFilters {
  status: "all" | "pending" | "approved" | "rejected";
  search: string;
}

export interface HostRequestResponse {
  data: {
    data: HostRequest[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
    first_page_url: string;
    last_page_url: string;
    next_page_url: string | null;
    prev_page_url: string | null;
    path: string;
    links: Array<{
      url: string | null;
      label: string;
      active: boolean;
    }>;
  };
}

export interface HostRequestPagination {
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
}
