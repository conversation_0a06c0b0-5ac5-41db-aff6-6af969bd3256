import type { EventItem } from './index';

export interface CheckInAttendee {
  id: number;
  name: string;
  email: string;
  avatar?: string;
  ticket: {
    id: string;
    type: string;
    price: number;
  };
  checkInTime?: string;
  status: 'checked-in' | 'expected' | 'no-show' | 'confirmed';
  purchaseDate: string;
}

export interface CheckInFilters {
  eventId: string;
  status: string;
  ticketType: string;
}

export interface CheckInResponse {
  data: CheckInAttendee[];
  total: number;
  current_page: number;
  per_page: number;
  last_page: number;
}

export interface CheckInStats {
  total_attendees: number;
  checked_in: number;
  expected: number;
  no_show: number;
  check_in_rate: number;
}

export interface QRScanResult {
  attendee_id: number;
  ticket_id: string;
  event_id: number;
  valid: boolean;
  message: string;
  attendee?: CheckInAttendee;
}

export interface ExportCheckInsRequest {
  event_id?: string;
  status?: string;
  ticket_type?: string;
  search?: string;
  format: 'csv' | 'excel';
}
