import type { UseFetchOptions } from 'nuxt/app';
import { defu } from 'defu';
import type { ApiResponse } from '@/types/api';
import { useAuthStore } from '@/store/auth';

export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

interface RequestOptions<T> extends Omit<UseFetchOptions<T>, 'method'> {
    requiresAuth?: boolean;
    retry?: number;
    onSuccess?: (data: T) => void;
    onError?: (error: any) => void;
}

export class HttpError extends Error {
    constructor(
        public override message: any,
        public status: number,
        public errors?: Record<string, string[]>
    ) {
        super(message);
        this.name = 'HttpError';
    }
}

export const useHttpClient = () => {
    const config = useRuntimeConfig();
    const createError = (error: any): HttpError => {
        const status = error.statusCode;
        const response = error.data;
        const errors = error.data.errors || {};
        return new HttpError(response, status, errors);
    };

    const defaultOptions = {
        baseURL: config.public.apiBaseUrl,
        retry: 0,
        retryDelay: 300,
        headers: {} as HeadersInit,
        onRequest(_ctx: any) {
            // Request interceptor logic here
        },
        onRequestError(_ctx: any) {
            console.log(_ctx)
        },
        onResponse(_ctx: any) {
            // Response interceptor logic here
        },
        onResponseError: ({ response }: { response: any }) => {
            if (response?.status === 401) {
                console.error('Authentication error: User is not authenticated or token expired');

                const authStore = useAuthStore();
                authStore.clearAuth();

                try {
                    const nuxtApp = useNuxtApp();
                    if (nuxtApp.$toast) {
                        (nuxtApp.$toast as any).error('Session expired. Please log in again.');
                    }

                    navigateTo('/');
                } catch (e) {
                    console.error('Failed to handle logout:', e);
                }
            }
        }
    };

    const request = async <T>(
        endpoint: string,
        method: HttpMethod,
        options: RequestOptions<T> = {}
    ): Promise<T> => {
        const {
            requiresAuth = false,
            retry = defaultOptions.retry,
            ...fetchOptions
        } = options;

        let attempt = 0;
        const maxAttempts = 2;

        while (attempt < maxAttempts) {
            const headers: HeadersInit = {};
            const authenticationStore = useAuthStore();

            if (authenticationStore.token && authenticationStore.isAuthenticated) {
                headers['Authorization'] = `Bearer ${authenticationStore.token}`;
            }

            const mergedOptions: UseFetchOptions<ApiResponse<T>> = defu(
                {
                    method,
                    headers,
                    retry: retry,
                },
                fetchOptions,
                {
                    ...defaultOptions,
                    baseURL: defaultOptions.baseURL as string,
                }
            );

            try {
                const response = await useFetch<ApiResponse<T>>(endpoint, mergedOptions);

                if (!response.error.value) {
                    return response.data.value as T;
                }

                if (response.error.value.statusCode === 401 && attempt === 0 && authenticationStore.refresh_token) {
                    console.log('Token expired, attempting to refresh...');
                    const refreshSuccess = await authenticationStore.refreshToken();

                    if (refreshSuccess) {
                        attempt++;
                        continue;
                    }
                }

                throw createError(response.error.value);

            } catch (error: any) {
                if (error instanceof HttpError) {
                    throw error;
                }

                throw createError(error);
            }
        }

        throw new HttpError('Request failed after retries', 500);
    };
    return {
        get: <T>(endpoint: string, options?: RequestOptions<T>) =>
            request<T>(endpoint, 'GET', options),

        post: <T>(endpoint: string, data?: any, options?: RequestOptions<T>) =>
            request<T>(endpoint, 'POST', { ...options, body: data }),

        put: <T>(endpoint: string, data?: any, options?: RequestOptions<T>) =>
            request<T>(endpoint, 'PUT', { ...options, body: data }),

        patch: <T>(endpoint: string, data?: any, options?: RequestOptions<T>) =>
            request<T>(endpoint, 'PATCH', { ...options, body: data }),

        delete: <T>(endpoint: string, options?: RequestOptions<T>) =>
            request<T>(endpoint, 'DELETE', options),
    };
};
