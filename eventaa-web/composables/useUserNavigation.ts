import { useUserMappingStore } from '@/store/userMapping'
import type { User } from '@/types/user'

/**
 * Composable for handling user profile navigation with UUID obfuscation
 */
export const useUserNavigation = () => {
  const userMappingStore = useUserMappingStore()

  /**
   * Navigate to a user profile using UUID instead of direct ID
   * @param user - User object or user ID
   * @returns Promise that resolves when navigation is complete
   */
  const navigateToUserProfile = async (user: User | number): Promise<void> => {
    const userId = typeof user === 'number' ? user : user.id

    // Remove current mapping and create new one
    const uuid = userMappingStore.addMapping(userId)

    // Navigate using navigateTo for better compatibility
    await navigateTo(`/users/${uuid}`)
  }

  /**
   * Get a user profile URL with UUID
   * @param user - User object or user ID
   * @returns URL string with UUID
   */
  const getUserProfileUrl = (user: User | number): string => {
    const userId = typeof user === 'number' ? user : user.id

    // Create mapping and return URL
    const uuid = userMappingStore.addMapping(userId)

    return `/users/${uuid}`
  }

  /**
   * Get user ID from UUID
   * @param uuid - UUID string
   * @returns User ID or null if not found
   */
  const getUserIdFromUuid = (uuid: string): number | null => {
    return userMappingStore.getIdFromUuid(uuid)
  }

  return {
    navigateToUserProfile,
    getUserProfileUrl,
    getUserIdFromUuid,
  }
}
