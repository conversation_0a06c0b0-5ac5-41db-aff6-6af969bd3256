import { ref, h, createApp, defineComponent, type Component } from 'vue';

interface DialogProps {
    title?: string;
    message?: string;
    show?: boolean;
    data?: Object;
    onClose?: () => void;
    onConfirm?: () => void;
    [key: string]: any;
}

type DialogComponent = Component;

export function useDialog() {
    const show = ref(false);

    const createModal = (): HTMLDivElement => {
        const modal = document.createElement('div');
        modal.id = 'dialog-wrapper';
        document.body.appendChild(modal);
        return modal;
    };

    const mountComponent = (component: Component, container: HTMLDivElement) =>
        createApp(component).mount(container);

    const destroyModal = (container: HTMLDivElement) => {
        document.body.removeChild(container);
    };

    const resolveComponent = (component: DialogComponent, props: DialogProps) => ({
        render: () => h(component, {
            ...props,
            show: show.value,
            onClose: () => {
                if (props.onClose) props.onClose();
                show.value = false;
            },
            onConfirm: () => {
                if (props.onConfirm) props.onConfirm();
                show.value = false;
            }
        })
    });

    const openDialog = async (
        component: DialogComponent,
        props: DialogProps = {}
    ): Promise<boolean> => {
        const modal = createModal();
        const result = await new Promise<boolean>(resolve => {
            const dialogComponent = defineComponent({
                extends: resolveComponent(component, {
                    ...props,
                    onConfirm: () => {
                        if (props.onConfirm) props.onConfirm();
                        resolve(true);
                    },
                    onClose: () => {
                        if (props.onClose) props.onClose();
                        resolve(false);
                    }
                })
            });
            mountComponent(dialogComponent, modal);
            show.value = true;
        });
        show.value = false;
        destroyModal(modal);
        return result;
    };

    const showDialog = async (
        component: DialogComponent,
        props: DialogProps = {}
    ): Promise<boolean> => {
        return openDialog(component, {
            dialogComponent: component,
            dialogProps: props
        });
    };

    return {
        showDialog
    };
}