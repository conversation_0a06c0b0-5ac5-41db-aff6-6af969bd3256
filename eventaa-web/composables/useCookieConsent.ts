import { useCookieConsentStore } from "@/store/cookies"

export const useCookieConsent = () => {

    const store = useCookieConsentStore();

    onMounted(() => {
        store.loadCookieConsent()
    })

    return {
        acceptAllCookies: store.acceptAllCookies,
        acceptNecessaryCookies: store.acceptNecessaryCookies,
        updatePreferences: store.updatePreferences,
        isConsentAccepted: computed(() => store.isConsentAccepted),
        currentPreferences: computed(() => store.getCurrentPreferences),
        isCookieTypeAllowed: store.isCookieTypeAllowed
    }
}