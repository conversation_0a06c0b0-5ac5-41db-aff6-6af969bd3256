// composables/useGoogleMaps.ts
import { ref, onMounted } from 'vue';
import { useRuntimeConfig } from 'nuxt/app';

declare global {
    interface Window {
        initMap: () => void;
    }
}

export const useGoogleMaps = () => {
    const config = useRuntimeConfig();
    const isLoaded = ref(false);
    const loadError = ref<Error | null>(null);

    const loadGoogleMaps = () => {
        return new Promise<void>((resolve, reject) => {
            if (window.google && window.google.maps) {
                isLoaded.value = true;
                resolve();
                return;
            }

            window.initMap = () => {
                isLoaded.value = true;
                resolve();
            };

            const script = document.createElement('script');
            script.src = `https://maps.googleapis.com/maps/api/js?key=${config.public.googleMapsApiKey}&libraries=places,directions&callback=initMap`;
            script.async = true;
            script.defer = true;

            script.onerror = (error) => {
                loadError.value = error as any;
                reject(error);
            };

            document.head.appendChild(script);
        });
    };

    onMounted(() => {
        loadGoogleMaps().catch(console.error);
    });

    return {
        isLoaded,
        loadError,
        loadGoogleMaps
    };
};