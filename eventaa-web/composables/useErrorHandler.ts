/**
 * Error handling composable for EventaHub
 * Provides utilities for error page navigation and handling
 */

export const useErrorHandler = () => {
  /**
   * Navigate to specific error page
   * @param errorCode - HTTP status code
   * @param message - Optional error message
   */
  const navigateToError = (errorCode: number, message?: string) => {
    const route = `/error/${errorCode}`

    // Add query parameters if message is provided
    const query = message ? { message } : {}

    return navigateTo({
      path: route,
      query
    })
  }

  /**
   * Handle different types of errors and navigate accordingly
   * @param error - Error object or status code
   */
  const handleError = (error: any) => {
    let statusCode = 500
    let message = 'An unexpected error occurred'

    // Extract status code from different error formats
    if (typeof error === 'number') {
      statusCode = error
    } else if (error?.status) {
      statusCode = error.status
    } else if (error?.statusCode) {
      statusCode = error.statusCode
    } else if (error?.response?.status) {
      statusCode = error.response.status
    }

    // Extract message from error
    if (error?.message) {
      message = error.message
    } else if (error?.statusText) {
      message = error.statusText
    } else if (error?.response?.statusText) {
      message = error.response.statusText
    }

    // Navigate to appropriate error page
    return navigateToError(statusCode, message)
  }

  /**
   * Check if error page exists for given status code
   * @param statusCode - HTTP status code
   */
  const errorPageExists = (statusCode: number): boolean => {
    const supportedErrorPages = [401, 403, 404, 429, 500, 503]
    return supportedErrorPages.includes(statusCode)
  }

  /**
   * Get user-friendly error message based on status code
   * @param statusCode - HTTP status code
   */
  const getErrorMessage = (statusCode: number): string => {
    const messages: Record<number, string> = {
      400: 'Bad request. Please check your input and try again.',
      401: 'You need to be logged in to access this resource.',
      403: 'You don\'t have permission to access this resource.',
      404: 'The page you\'re looking for doesn\'t exist.',
      408: 'Request timeout. Please try again.',
      429: 'Too many requests. Please wait before trying again.',
      500: 'Internal server error. Our team has been notified.',
      502: 'Bad gateway. The server is temporarily unavailable.',
      503: 'Service unavailable. We\'re performing maintenance.',
      504: 'Gateway timeout. Please try again later.'
    }

    return messages[statusCode] || 'An unexpected error occurred.'
  }

  /**
   * Log error for debugging (in development) and monitoring
   * @param error - Error object
   * @param context - Additional context about where error occurred
   */
  const logError = (error: any, context?: string) => {
    const errorData = {
      timestamp: new Date().toISOString(),
      error: {
        message: error?.message || 'Unknown error',
        status: error?.status || error?.statusCode || 'Unknown',
        stack: error?.stack
      },
      context,
      url: process.client ? window.location.href : 'Server-side',
      userAgent: process.client ? navigator.userAgent : 'Server-side'
    }

    // Log to console in development
    if (process.dev) {
      console.error('EventaHub Error:', errorData)
    }

    // Here you could also send to error tracking service
    // e.g., Sentry, LogRocket, etc.
  }

  /**
   * Show error notification (if notification system is available)
   * @param message - Error message to display
   * @param type - Type of notification
   */
  const showErrorNotification = (message: string, type: 'error' | 'warning' | 'info' = 'error') => {
    // This would integrate with your notification system
    // For now, we'll use a simple alert in development
    if (process.dev) {
      console.warn(`[${type.toUpperCase()}] ${message}`)
    }

    // You could integrate with toast notifications here
    // e.g., useNuxtApp().$toast.error(message)
  }

  /**
   * Retry failed operation with exponential backoff
   * @param operation - Function to retry
   * @param maxRetries - Maximum number of retries
   * @param baseDelay - Base delay in milliseconds
   */
  const retryOperation = async (
    operation: () => Promise<any>,
    maxRetries: number = 3,
    baseDelay: number = 1000
  ) => {
    let lastError: any

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation()
      } catch (error) {
        lastError = error

        // Don't retry on certain error codes
        const statusCode = (error as any)?.status || (error as any)?.statusCode || (error as any)?.response?.status
        if ([400, 401, 403, 404, 422].includes(statusCode)) {
          throw error
        }

        // If this was the last attempt, throw the error
        if (attempt === maxRetries) {
          throw error
        }

        // Wait before retrying (exponential backoff)
        const delay = baseDelay * Math.pow(2, attempt)
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }

    throw lastError
  }

  return {
    navigateToError,
    handleError,
    errorPageExists,
    getErrorMessage,
    logError,
    showErrorNotification,
    retryOperation
  }
}
