interface LocationData {
  latitude: number | null
  longitude: number | null
  city: string | null
  error: string | null
}

export function useGeolocation() {
  const location: Ref<LocationData> = ref({
    latitude: null,
    longitude: null,
    city: null,
    error: null
  })

  const getCurrentLocation = (): Promise<LocationData> => {
    return new Promise((resolve, reject) => {
      // First check if we have a cached location from background fetch
      if (process.client) {
        try {
          const cachedLocation = sessionStorage.getItem('user_location')
          if (cachedLocation) {
            const parsed = JSON.parse(cachedLocation)
            // Use cached location if it's less than 10 minutes old
            if (Date.now() - parsed.timestamp < 600000) {
              location.value.latitude = parsed.latitude
              location.value.longitude = parsed.longitude

              // Try to get city name asynchronously
              getCityFromCoords(parsed.latitude, parsed.longitude)
                .then(city => {
                  location.value.city = city
                  resolve(location.value)
                })
                .catch(() => {
                  location.value.city = 'Current Location'
                  resolve(location.value)
                })
              return
            }
          }
        } catch (error) {
          console.log('Failed to get cached location:', error)
        }
      }

      if (!navigator.geolocation) {
        location.value.error = 'Geolocation is not supported by this browser.'
        reject(location.value)
        return
      }

      navigator.geolocation.getCurrentPosition(
        async (position) => {
          location.value.latitude = position.coords.latitude
          location.value.longitude = position.coords.longitude

          try {
            const city = await getCityFromCoords(position.coords.latitude, position.coords.longitude)
            location.value.city = city

            // Cache the location for future use
            if (process.client) {
              sessionStorage.setItem('user_location', JSON.stringify({
                latitude: position.coords.latitude,
                longitude: position.coords.longitude,
                timestamp: Date.now()
              }))
            }

            resolve(location.value)
          } catch (err) {
            location.value.city = 'Current Location'
            location.value.error = 'Failed to retrieve city information'
            resolve(location.value) // Still resolve with coordinates
          }
        },
        (error) => {
          switch(error.code) {
            case error.PERMISSION_DENIED:
              location.value.error = 'User denied the request for Geolocation.'
              break
            case error.POSITION_UNAVAILABLE:
              location.value.error = 'Location information is unavailable.'
              break
            case error.TIMEOUT:
              location.value.error = 'The request to get user location timed out.'
              break
            default:
              location.value.error = 'An unknown error occurred.'
          }
          reject(location.value)
        },
        {
          enableHighAccuracy: true,
          timeout: 8000, // Increased timeout
          maximumAge: 300000 // Accept cached location up to 5 minutes old
        }
      )
    })
  }

  const getCityFromCoords = async (lat: number, lng: number): Promise<string> => {
    const response = await fetch(
      `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}`
    )

    if (!response.ok) {
      throw new Error('Unable to fetch location details')
    }

    const data = await response.json()
    return data.address.city ||
           data.address.town ||
           data.address.village ||
           data.address.municipality ||
           'Unknown Location'
  }

  return {
    location,
    getCurrentLocation
  }
}
