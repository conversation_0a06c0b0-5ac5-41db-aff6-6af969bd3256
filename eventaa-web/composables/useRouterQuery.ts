import { useRouter, type LocationQueryRaw, type NavigationFailure } from 'vue-router'

export const useRouterQuery = (): {
    replaceQuery: (query: LocationQueryRaw) => Promise<NavigationFailure | void | undefined>
    removeQuery: (query: LocationQueryRaw) => Promise<NavigationFailure | void | undefined>
    removeAllQueries: () => Promise<NavigationFailure | void | undefined>
    replaceOneQuery: (key: string, value: LocationQueryRaw) => Promise<NavigationFailure | void | undefined>
} => {
    const router = useRouter();

    const replaceQuery = (query: LocationQueryRaw): Promise<NavigationFailure | void | undefined> => {
        return router.replace({ query });
    }

    const removeQuery = (query: LocationQueryRaw) => {
        const currentQuery = { ...router.currentRoute.value.query };
        Object.keys(query).forEach(key => delete currentQuery[key]);
        return router.replace({ query: currentQuery });
    }

    const removeAllQueries = (): Promise<NavigationFailure | void | undefined> => {
        return router.replace({ query: {} });
    }

    const replaceOneQuery = (key: string, value: LocationQueryRaw): Promise<NavigationFailure | void | undefined> => {
        const currentQuery = { ...router.currentRoute.value.query };
        currentQuery[key] = value[key] as string | (string | null)[] | null;
        return router.replace({ query: currentQuery });
    }

    return {
        replaceQuery,
        removeQuery,
        removeAllQueries,
        replaceOneQuery
    }
}