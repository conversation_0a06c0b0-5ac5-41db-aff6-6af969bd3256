<template>
  <div class="p-5 sm:p-8">
    <!-- <PERSON> Header -->
    <div class="mb-8 flex flex-col md:flex-row md:items-center md:justify-between">
      <div>
        <h1 class="text-2xl font-bold dashboard-text-primary">User Roles</h1>
        <p class="mt-1 dashboard-text-secondary text-sm">Manage user roles and assign permissions</p>
      </div>
      <div class="mt-4 md:mt-0">
        <button
          @click="openCreateRoleDialog"
          class="px-4 py-2.5 bg-red-600 text-white hover:bg-red-700 flex items-center dashboard-transition shadow-sm"
        >
          <Icon icon="heroicons:plus" class="h-4.5 w-4.5 mr-2" />
          Create New Role
        </button>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Roles List Panel -->
      <div class="lg:col-span-1">
        <div class="dashboard-bg-card shadow-md p-5 border dashboard-border">
          <h2 class="text-lg font-semibold mb-4 dashboard-text-primary flex items-center">
            <Icon icon="heroicons:user-group" class="mr-2 h-5 w-5 dashboard-text-secondary" />
            Roles
          </h2>

          <div v-if="loading" class="py-8">
            <div class="animate-pulse space-y-4">
              <div class="h-12 dashboard-bg-hover"></div>
              <div class="h-12 dashboard-bg-hover"></div>
              <div class="h-12 dashboard-bg-hover"></div>
              <div class="h-12 dashboard-bg-hover"></div>
            </div>
          </div>

          <div v-else-if="roles.length === 0" class="py-10 text-center dashboard-text-secondary">
            <Icon icon="heroicons:folder-open" class="h-12 w-12 mx-auto mb-2 dashboard-text-muted" />
            <p>No roles found</p>
            <button
              @click="openCreateRoleDialog"
              class="mt-3 text-sm text-red-600 hover:text-red-700"
            >
              Create your first role
            </button>
          </div>

          <div v-else class="space-y-2 max-h-[480px] overflow-y-auto pr-1 custom-scrollbar">
            <div
              v-for="role in roles"
              :key="role.id"
              class="p-3 dashboard-bg-hover cursor-pointer dashboard-transition flex items-center"
              :class="{
                'border-l-4 bg-red-50 dark:bg-red-900/10 border-red-600': selectedRole?.id === role.id,
                'border-l-4 border-transparent': selectedRole?.id !== role.id
              }"
              @click="selectRole(role)"
            >
              <div class="flex-1">
                <h3 class="font-medium dashboard-text-primary">{{ role.name }}</h3>
                <p class="dashboard-text-secondary text-xs">{{ role.guard_name || 'web' }}</p>
              </div>
              <div class="flex space-x-1">
                <button
                  class="p-1.5 hover:dashboard-bg-hover dashboard-transition"
                  @click.stop="editRole(role)"
                  title="Edit role"
                >
                  <Icon icon="heroicons:pencil-square" class="w-4 h-4 dashboard-text-secondary" />
                </button>
                <button
                  class="p-1.5 hover:dashboard-bg-hover dashboard-transition"
                  @click.stop="deleteRole(role)"
                  :disabled="role.name === 'administrator'"
                  title="Delete role"
                >
                  <Icon
                    icon="heroicons:trash"
                    class="w-4 h-4 text-red-500"
                    :class="{ 'opacity-50 cursor-not-allowed': role.name === 'administrator' }"
                  />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Role Details -->
      <div class="lg:col-span-2">
        <div class="dashboard-bg-card shadow-md p-6 border dashboard-border h-full">
          <div v-if="!selectedRole" class="py-16 text-center flex flex-col items-center justify-center h-full">
            <div class="p-4 rounded-full bg-red-50 dark:bg-red-900/10 mb-4">
              <Icon icon="heroicons:user-group" class="w-12 h-12 text-red-500" />
            </div>
            <h3 class="mt-2 text-xl font-medium dashboard-text-primary">Select a role to view details</h3>
            <p class="mt-1 dashboard-text-secondary">Or create a new role to get started</p>
            <button
              @click="openCreateRoleDialog"
              class="mt-6 px-4 py-2 border border-red-600 text-red-600 hover:bg-red-600 hover:text-white dashboard-transition"
            >
              Create New Role
            </button>
          </div>

          <div v-else>
            <div class="flex justify-between items-center mb-6 pb-4 border-b dashboard-border">
              <div>
                <div class="flex items-center">
                  <div class="p-2 bg-red-50 dark:bg-red-900/10 mr-3">
                    <Icon icon="heroicons:user-group" class="w-6 h-6 text-red-500" />
                  </div>
                  <div>
                    <h2 class="text-xl font-semibold dashboard-text-primary">{{ selectedRole.name }}</h2>
                    <p class="dashboard-text-secondary text-sm">Guard: {{ selectedRole.guard_name || 'web' }}</p>
                  </div>
                </div>
              </div>
              <div>
                <button
                  class="px-4 py-2 border border-red-600 text-red-600 hover:bg-red-600 hover:text-white dashboard-transition"
                  @click="openPermissionsDialog"
                >
                  Manage Permissions
                </button>
              </div>
            </div>

            <div class="mb-8">
              <div class="flex items-center mb-4">
                <Icon icon="heroicons:shield-check" class="w-5 h-5 text-red-500 mr-2" />
                <h3 class="text-lg font-medium dashboard-text-primary">Permissions</h3>
              </div>

              <div v-if="selectedRole.permissions && selectedRole.permissions.length > 0"
                class="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div
                  v-for="permission in selectedRole.permissions"
                  :key="permission.id"
                  class="flex items-center p-3 dashboard-bg-hover"
                >
                  <Icon icon="heroicons:check-circle" class="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                  <div class="overflow-hidden">
                    <p class="text-sm font-medium dashboard-text-primary truncate">{{ permission.name }}</p>
                    <p class="dashboard-text-secondary text-xs truncate">{{ permission.description || permission.guard_name }}</p>
                  </div>
                </div>
              </div>
              <div v-else class="py-6 text-center dashboard-text-secondary dashboard-bg-hover">
                <Icon icon="heroicons:shield-exclamation" class="w-8 h-8 mx-auto mb-2 dashboard-text-muted" />
                <p>No permissions assigned to this role</p>
                <button
                  @click="openPermissionsDialog"
                  class="mt-2 text-sm text-red-600 hover:text-red-700"
                >
                  Assign permissions
                </button>
              </div>
            </div>

            <div>
              <div class="flex items-center mb-4">
                <Icon icon="heroicons:users" class="w-5 h-5 text-red-500 mr-2" />
                <h3 class="text-lg font-medium dashboard-text-primary">Users with this role</h3>
              </div>

              <div v-if="selectedRole.users && selectedRole.users.length > 0"
                class="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div
                  v-for="user in selectedRole.users"
                  :key="user.id"
                  class="flex items-center p-3 dashboard-bg-hover"
                >
                  <img
                    :src="user.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(user.name)}&background=random`"
                    class="w-10 h-10 rounded-full mr-3 border dashboard-border"
                    :alt="user.name"
                  />
                  <div class="overflow-hidden">
                    <p class="text-sm font-medium dashboard-text-primary truncate">{{ user.name }}</p>
                    <p class="dashboard-text-secondary text-xs truncate">{{ user.email }}</p>
                  </div>
                </div>
              </div>
              <div v-else class="py-6 text-center dashboard-text-secondary dashboard-bg-hover">
                <Icon icon="heroicons:user-slash" class="w-8 h-8 mx-auto mb-2 dashboard-text-muted" />
                <p>No users assigned to this role</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Role Create/Edit Dialog -->
    <div v-if="showRoleDialog" class="fixed inset-0 z-50 overflow-y-auto">
      <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
          <div class="absolute inset-0 bg-gray-900/75 backdrop-blur-sm"></div>
        </div>

        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <div class="inline-block align-bottom dashboard-bg-card text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full border dashboard-border">
          <form @submit.prevent="saveRole">
            <div class="dashboard-bg-card px-6 pt-6 pb-4">
              <div class="text-center sm:text-left">
                <h3 class="text-xl leading-6 font-semibold dashboard-text-primary" id="modal-title">
                  {{ isEditMode ? 'Edit Role' : 'Create New Role' }}
                </h3>
                <p class="mt-1 text-sm dashboard-text-secondary">
                  {{ isEditMode ? 'Update role details' : 'Create a new role to manage permissions' }}
                </p>
                <div class="mt-5 space-y-4">
                  <div>
                    <label for="role-name" class="block text-sm font-medium dashboard-text-primary mb-1">Role Name</label>
                    <input
                      id="role-name"
                      v-model="roleForm.name"
                      type="text"
                      required
                      class="mt-1 block w-full dashboard-border border px-3 py-2.5 dashboard-bg-card dashboard-text-primary focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
                      placeholder="Enter role name"
                    />
                  </div>
                </div>
              </div>
            </div>
            <div class="dashboard-bg-hover px-6 py-4 flex flex-col-reverse sm:flex-row-reverse sm:justify-between">
              <div class="flex flex-col sm:flex-row gap-3">
                <button
                  type="submit"
                  :disabled="saving"
                  class="inline-flex justify-center items-center border border-transparent shadow-sm px-4 py-2.5 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:text-sm dashboard-transition disabled:opacity-50"
                >
                  <Icon v-if="saving" icon="svg-spinners:270-ring" class="h-4 w-4 mr-1.5" />
                  {{ saving ? 'Saving...' : (isEditMode ? 'Update Role' : 'Create Role') }}
                </button>
                <button
                  type="button"
                  class="mt-3 sm:mt-0 inline-flex justify-center dashboard-border border shadow-sm px-4 py-2.5 dashboard-bg-card text-base font-medium dashboard-text-primary hover:dashboard-bg-hover focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:text-sm dashboard-transition"
                  @click="closeRoleDialog"
                >
                  Cancel
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Permissions Dialog -->
    <div v-if="showPermissionsDialog" class="fixed inset-0 z-50 overflow-y-auto">
      <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
          <div class="absolute inset-0 bg-gray-900/75 backdrop-blur-sm"></div>
        </div>

        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <div class="inline-block align-bottom dashboard-bg-card text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl sm:w-full border dashboard-border">
          <div class="dashboard-bg-card px-6 pt-6 pb-4">
            <div class="sm:flex sm:items-start">
              <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
                <div class="flex items-center mb-4">
                  <div class="p-2 bg-red-50 dark:bg-red-900/10 mr-3">
                    <Icon icon="heroicons:shield-check" class="w-6 h-6 text-red-500" />
                  </div>
                  <div>
                    <h3 class="text-xl leading-6 font-semibold dashboard-text-primary" id="modal-title">
                      Manage Permissions
                    </h3>
                    <p class="mt-1 text-sm dashboard-text-secondary">
                      Assign permissions for {{ selectedRole?.name }}
                    </p>
                  </div>
                </div>

                <div class="mt-6">
                  <div class="flex justify-between mb-4">
                    <div class="relative">
                      <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                        <Icon icon="heroicons:magnifying-glass" class="w-4 h-4 dashboard-text-secondary" />
                      </div>
                      <input
                        type="text"
                        class="dashboard-bg-card dashboard-border border pl-10 pr-4 py-2 w-full sm:w-64 dashboard-text-primary focus:ring-2 focus:ring-red-500 focus:border-red-500"
                        placeholder="Search permissions..."
                      />
                    </div>
                    <div class="flex items-center">
                      <button
                        class="text-sm dashboard-text-primary flex items-center hover:text-red-600 dashboard-transition"
                        @click="toggleAllPermissions"
                      >
                        <Icon icon="heroicons:check-circle" class="w-4 h-4 mr-1.5" />
                        Toggle All
                      </button>
                    </div>
                  </div>

                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-[400px] overflow-y-auto pr-2 custom-scrollbar">
                    <div
                      v-for="(permissionGroup, groupName) in permissionGroups"
                      :key="groupName"
                      class="dashboard-border border p-4"
                    >
                      <h4 class="font-medium dashboard-text-primary mb-3 flex items-center">
                        <span class="w-2 h-2 rounded-full bg-red-500 mr-2"></span>
                        {{ groupName }}
                      </h4>
                      <div class="space-y-2">
                        <div
                          v-for="permission in permissionGroup"
                          :key="permission.id"
                          class="flex items-center p-2 hover:dashboard-bg-hover dashboard-transition"
                        >
                          <div class="flex items-center">
                            <input
                              type="checkbox"
                              :id="`perm-${permission.id}`"
                              v-model="selectedPermissions"
                              :value="permission.name"
                              class="h-4 w-4 text-red-600 focus:ring-red-500 rounded dashboard-border"
                            />
                          </div>
                          <label :for="`perm-${permission.id}`" class="ml-3 block text-sm dashboard-text-primary cursor-pointer">
                            {{ permission.name }}
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="dashboard-bg-hover px-6 py-4 flex flex-col-reverse sm:flex-row-reverse sm:justify-between">
            <div class="flex flex-col sm:flex-row gap-3">
              <button
                type="button"
                @click="savePermissions"
                :disabled="savingPermissions"
                class="inline-flex justify-center items-center border border-transparent shadow-sm px-4 py-2.5 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:text-sm dashboard-transition disabled:opacity-50"
              >
                <Icon v-if="savingPermissions" icon="svg-spinners:270-ring" class="h-4 w-4 mr-1.5" />
                {{ savingPermissions ? 'Saving...' : 'Save Permissions' }}
              </button>
              <button
                type="button"
                class="mt-3 sm:mt-0 inline-flex justify-center dashboard-border border shadow-sm px-4 py-2.5 dashboard-bg-card text-base font-medium dashboard-text-primary hover:dashboard-bg-hover focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:text-sm dashboard-transition"
                @click="closePermissionsDialog"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { ENDPOINTS } from '~/utils/api';
import type { Role } from '@/types/role';

definePageMeta({
  layout: "dashboard",
});

useHead({
  title: "Roles | EventaHub Malawi",
  meta: [
    { name: "description", content: "Manage user roles and permissions for your EventaHub organization" }
  ]
});

interface PermissionGroup {
  [key: string]: {
    id: string;
    name: string;
    granted: boolean;
  }[];
}

interface RoleForm {
  name: string;
}

const loading = ref(true);
const roles = ref<Role[]>([]);
const selectedRole = ref<Role | null>(null);
const showPermissionsDialog = ref(false);
const showRoleDialog = ref(false);
const isEditMode = ref(false);
const saving = ref(false);
const savingPermissions = ref(false);
const permissionGroups = ref<PermissionGroup>({});
const selectedPermissions = ref<string[]>([]);
const roleForm = ref<RoleForm>({
  name: ''
});
const editingRoleId = ref<number | null>(null);
const allPermissions = computed(() => {
  let all: string[] = [];
  Object.values(permissionGroups.value).forEach(group => {
    group.forEach(permission => {
      all.push(permission.name);
    });
  });
  return all;
});

const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

async function fetchRoles() {
  try {
    loading.value = true;
    const response: any = await httpClient.get(ENDPOINTS.ROLES.GET);
    roles.value = response.roles || [];
  } catch (error) {
    console.error('Error fetching roles:', error);
    $toast.error('Failed to load roles');
    roles.value = [];
  } finally {
    loading.value = false;
  }
}

async function fetchPermissionGroups() {
  try {
    const response: any = await httpClient.get(ENDPOINTS.PERMISSIONS.CATEGORIES);
    permissionGroups.value = response.permissions || {};
  } catch (error) {
    console.error('Error fetching permission groups:', error);
    $toast.error('Failed to load permission groups');
    permissionGroups.value = {};
  }
}

onMounted(async () => {
  await Promise.all([fetchRoles(), fetchPermissionGroups()]);
});

function selectRole(role: Role) {
  selectedRole.value = role;
}

function openCreateRoleDialog() {
  isEditMode.value = false;
  roleForm.value = {
    name: ''
  };
  editingRoleId.value = null;
  showRoleDialog.value = true;
}

function editRole(role: Role) {
  isEditMode.value = true;
  roleForm.value = {
    name: role.name
  };
  editingRoleId.value = role.id;
  showRoleDialog.value = true;
}

function closeRoleDialog() {
  showRoleDialog.value = false;
  roleForm.value = {
    name: ''
  };
  editingRoleId.value = null;
}

function toggleAllPermissions() {
  // If all permissions are selected, unselect all. Otherwise, select all.
  if (selectedPermissions.value.length === allPermissions.value.length) {
    selectedPermissions.value = [];
  } else {
    selectedPermissions.value = [...allPermissions.value];
  }
}

async function saveRole() {
  try {
    saving.value = true;

    const payload = {
      name: roleForm.value.name
    };

    if (isEditMode.value && editingRoleId.value) {
      await httpClient.put(`${ENDPOINTS.ROLES.UPDATE}/${editingRoleId.value}`, payload);
      $toast.success('Role updated successfully');
    } else {
      await httpClient.post(ENDPOINTS.ROLES.STORE, payload);
      $toast.success('Role created successfully');
    }

    await fetchRoles();
    closeRoleDialog();
  } catch (error: any) {
    console.error('Error saving role:', error);
    if (error.response?.data?.errors) {
      const errors = error.response.data.errors;
      Object.keys(errors).forEach(key => {
        errors[key].forEach((message: string) => {
          $toast.error(message);
        });
      });
    } else {
      $toast.error('Failed to save role');
    }
  } finally {
    saving.value = false;
  }
}

function openPermissionsDialog() {
  if (!selectedRole.value) return;

  // Set currently selected permissions
  selectedPermissions.value = selectedRole.value.permissions?.map(p => p.name) || [];
  showPermissionsDialog.value = true;
}

function closePermissionsDialog() {
  showPermissionsDialog.value = false;
  selectedPermissions.value = [];
}

async function savePermissions() {
  if (!selectedRole.value) return;

  try {
    savingPermissions.value = true;

    await httpClient.post(`${ENDPOINTS.ROLES.ASSIGN_PERMISSIONS}/${selectedRole.value.id}/permissions`, {
      permissions: selectedPermissions.value
    });

    $toast.success('Permissions updated successfully');
    await fetchRoles();

    // Update selected role with new permissions
    const updatedRole = roles.value.find(r => r.id === selectedRole.value?.id);
    if (updatedRole) {
      selectedRole.value = updatedRole;
    }

    closePermissionsDialog();
  } catch (error: any) {
    console.error('Error saving permissions:', error);
    if (error.response?.data?.errors) {
      const errors = error.response.data.errors;
      Object.keys(errors).forEach(key => {
        errors[key].forEach((message: string) => {
          $toast.error(message);
        });
      });
    } else {
      $toast.error('Failed to save permissions');
    }
  } finally {
    savingPermissions.value = false;
  }
}

async function deleteRole(role: Role) {
  try {
    if (role.name === 'administrator') {
      $toast.error('Cannot delete the Administrator role');
      return;
    }

    if (confirm(`Are you sure you want to delete the role "${role.name}"?`)) {
      await httpClient.delete(`${ENDPOINTS.ROLES.DESTROY}/${role.id}`);
      await fetchRoles();
      if (selectedRole.value?.id === role.id) {
        selectedRole.value = null;
      }
      $toast.success(`Role "${role.name}" deleted successfully`);
    }
  } catch (error) {
    console.error('Error deleting role:', error);
    $toast.error('Failed to delete role');
  }
}
</script>
