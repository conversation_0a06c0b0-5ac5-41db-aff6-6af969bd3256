<template>
  <div class="p-6 bg-gray-100 dark:bg-zinc-900">
    <div class="mb-6 flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Currencies</h1>
        <p class="text-gray-500 dark:text-gray-400">Manage system currencies for payments and pricing</p>
      </div>
      <div class="flex space-x-2">
        <button @click="exportCurrencies" class="px-4 py-2 bg-white dark:bg-zinc-800 border border-gray-300 dark:border-zinc-600 shadow-sm hover:bg-gray-50 dark:hover:bg-zinc-700 flex items-center text-gray-700 dark:text-gray-200">
          <Icon icon="vscode-icons:file-type-excel" class="w-5 h-5 mr-1" />
          Export
        </button>
        <button @click="openCreateModal" class="px-4 py-2 bg-red-600 text-white shadow-sm hover:bg-red-700 flex items-center">
          <Icon icon="heroicons:plus" class="w-5 h-5 mr-1" />
          Add Currency
        </button>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
      <div class="bg-white dark:bg-zinc-800 p-4 shadow">
        <div class="flex items-center">
          <div class="p-3 bg-blue-100 dark:bg-blue-900 mr-4">
            <Icon icon="heroicons:banknotes" class="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <p class="text-sm text-gray-500 dark:text-gray-400">Total Currencies</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.total_currencies }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-zinc-800 p-4 shadow">
        <div class="flex items-center">
          <div class="p-3 bg-green-100 dark:bg-green-900 mr-4">
            <Icon icon="heroicons:plus-circle" class="w-6 h-6 text-green-600 dark:text-green-400" />
          </div>
          <div>
            <p class="text-sm text-gray-500 dark:text-gray-400">Recent Currencies</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.recent_currencies }}</p>
            <p class="text-xs text-gray-400">Last 30 days</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-zinc-800 p-4 shadow">
        <div class="flex items-center">
          <div class="p-3 bg-purple-100 dark:bg-purple-900 mr-4">
            <Icon icon="heroicons:chart-bar" class="w-6 h-6 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <p class="text-sm text-gray-500 dark:text-gray-400">Most Used</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ mostUsedCurrency }}</p>
          </div>
        </div>
      </div>
    </div>

    <div class="bg-white dark:bg-zinc-800 p-4 shadow mb-6">
      <div class="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
        <div class="flex-1">
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Icon icon="heroicons:magnifying-glass" class="h-5 w-5 text-gray-400" />
            </div>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search currencies by name"
              class="block w-full pl-10 border-gray-300 dark:border-zinc-600 dark:bg-zinc-700 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500"
            />
          </div>
        </div>

        <Listbox v-model="dateFilter" as="div" class="relative">
          <ListboxButton class="relative w-48 cursor-pointer bg-white dark:bg-zinc-700 py-2 pl-3 pr-10 text-left border border-gray-300 dark:border-zinc-600 focus:outline-none focus:ring-2 focus:ring-red-500 sm:text-sm text-gray-900 dark:text-white">
            <span class="block truncate">{{ formatDateFilter(dateFilter) }}</span>
            <span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
              <Icon icon="heroicons:chevron-up-down" class="h-5 w-5 text-gray-400" aria-hidden="true" />
            </span>
          </ListboxButton>
          <transition
            leave-active-class="transition duration-100 ease-in"
            leave-from-class="opacity-100"
            leave-to-class="opacity-0"
          >
            <ListboxOptions class="absolute z-10 mt-1 max-h-60 w-full overflow-auto bg-white dark:bg-zinc-700 py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
              <ListboxOption v-for="filter in dateFilters" :key="filter.value" :value="filter.value" v-slot="{ active, selected }">
                <div :class="[
                  active ? 'bg-red-600 text-white' : 'text-gray-900 dark:text-white',
                  'relative cursor-pointer select-none py-2 pl-3 pr-9'
                ]">
                  <span :class="[selected ? 'font-semibold' : 'font-normal', 'block truncate']">
                    {{ filter.label }}
                  </span>
                  <span v-if="selected" :class="[
                    active ? 'text-white' : 'text-red-600',
                    'absolute inset-y-0 right-0 flex items-center pr-4'
                  ]">
                    <Icon icon="heroicons:check" class="h-5 w-5" aria-hidden="true" />
                  </span>
                </div>
              </ListboxOption>
            </ListboxOptions>
          </transition>
        </Listbox>
      </div>
    </div>

    <div class="bg-white dark:bg-zinc-800 shadow">
      <div v-if="loading" class="flex justify-center items-center py-20">
        <CoreLoader />
      </div>
      <div v-else>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-zinc-700">
            <thead>
              <tr>
                <th class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Currency</th>
                <th class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Usage</th>
                <th class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Created</th>
                <th class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-zinc-800 divide-y divide-gray-200 dark:divide-zinc-700">
              <tr v-for="currency in filteredCurrencies" :key="currency.id" class="hover:bg-gray-50 dark:hover:bg-zinc-700">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="p-2 bg-blue-100 dark:bg-blue-900 mr-3">
                      <Icon icon="heroicons:banknotes" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <div class="text-sm font-medium text-gray-900 dark:text-white">{{ currency.name }}</div>
                      <div class="text-sm text-gray-500 dark:text-gray-400">ID: {{ currency.id }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900 dark:text-white">{{ getCurrencyUsage(currency.name) }} plans</div>
                  <div class="text-sm text-gray-500 dark:text-gray-400">Active usage</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  {{ formatDate(currency.created_at) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <button @click="openEditModal(currency)" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                      <Icon icon="heroicons:pencil" class="w-5 h-5" />
                    </button>
                    <button @click="openDeleteModal(currency)" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                      <Icon icon="heroicons:trash" class="w-5 h-5" />
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div v-if="filteredCurrencies.length === 0" class="text-center py-12">
          <Icon icon="heroicons:banknotes" class="mx-auto h-12 w-12 text-gray-400" />
          <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No currencies found</h3>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Get started by creating a new currency.</p>
          <div class="mt-6">
            <button @click="openCreateModal" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
              <Icon icon="heroicons:plus" class="w-5 h-5 mr-2" />
              Add Currency
            </button>
          </div>
        </div>

        <div v-if="pagination.total > pagination.per_page" class="bg-white dark:bg-zinc-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-zinc-700 sm:px-6">
          <div class="flex-1 flex justify-between sm:hidden">
            <button @click="previousPage" :disabled="pagination.current_page === 1" class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-zinc-600 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-zinc-700 hover:bg-gray-50 dark:hover:bg-zinc-600">
              Previous
            </button>
            <button @click="nextPage" :disabled="pagination.current_page === pagination.last_page" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-zinc-600 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-zinc-700 hover:bg-gray-50 dark:hover:bg-zinc-600">
              Next
            </button>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700 dark:text-gray-300">
                Showing <span class="font-medium">{{ (pagination.current_page - 1) * pagination.per_page + 1 }}</span>
                to <span class="font-medium">{{ Math.min(pagination.current_page * pagination.per_page, pagination.total) }}</span>
                of <span class="font-medium">{{ pagination.total }}</span> results
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex shadow-sm -space-x-px" aria-label="Pagination">
                <button @click="previousPage" :disabled="pagination.current_page === 1" class="relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-sm font-medium text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-zinc-600">
                  <Icon icon="heroicons:chevron-left" class="h-5 w-5" />
                </button>
                <button @click="nextPage" :disabled="pagination.current_page === pagination.last_page" class="relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-sm font-medium text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-zinc-600">
                  <Icon icon="heroicons:chevron-right" class="h-5 w-5" />
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Create/Edit Modal -->
    <TransitionRoot appear :show="showModal" as="template">
      <Dialog as="div" @close="closeModal" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25" />
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex min-h-full items-center justify-center p-4 text-center">
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel class="w-full max-w-md transform overflow-hidden bg-white dark:bg-zinc-800 p-6 text-left align-middle shadow-xl transition-all">
                <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900 dark:text-white">
                  {{ isEditing ? 'Edit Currency' : 'Add New Currency' }}
                </DialogTitle>
                <div class="mt-4">
                  <FormKit
                    @submit="submitForm"
                    type="form"
                    :actions="false"
                    class="space-y-4"
                  >
                    <FormKit
                      type="text"
                      v-model="form.name"
                      label="Currency Code"
                      placeholder="e.g., USD, EUR, MWK"
                      validation="required|length:3,10"
                      help="Enter the currency code (3-10 characters)"
                      :classes="{
                        input: 'uppercase'
                      }"
                    />

                    <div class="flex justify-end space-x-3 mt-6">
                      <button
                        type="button"
                        @click="closeModal"
                        class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-zinc-700 border border-gray-300 dark:border-zinc-600 hover:bg-gray-50 dark:hover:bg-zinc-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        :disabled="saving"
                        class="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                      >
                        {{ saving ? 'Saving...' : (isEditing ? 'Update' : 'Create') }}
                      </button>
                    </div>
                  </FormKit>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>

    <!-- Delete Confirmation Modal -->
    <TransitionRoot appear :show="showDeleteModal" as="template">
      <Dialog as="div" @close="closeDeleteModal" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25" />
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex min-h-full items-center justify-center p-4 text-center">
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel class="w-full max-w-md transform overflow-hidden bg-white dark:bg-zinc-800 p-6 text-left align-middle shadow-xl transition-all">
                <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900 dark:text-white">
                  Delete Currency
                </DialogTitle>
                <div class="mt-2">
                  <p class="text-sm text-gray-500 dark:text-gray-400">
                    Are you sure you want to delete the currency "{{ currencyToDelete?.name }}"? This action cannot be undone.
                  </p>
                </div>

                <div class="mt-4 flex justify-end space-x-3">
                  <button
                    type="button"
                    @click="closeDeleteModal"
                    class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-zinc-700 border border-gray-300 dark:border-zinc-600 hover:bg-gray-50 dark:hover:bg-zinc-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    @click="deleteCurrency"
                    :disabled="deleting"
                    class="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                  >
                    {{ deleting ? 'Deleting...' : 'Delete' }}
                  </button>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import {
  Dialog,
  DialogPanel,
  DialogTitle,
  TransitionChild,
  TransitionRoot,
  Listbox,
  ListboxButton,
  ListboxOption,
  ListboxOptions
} from '@headlessui/vue'

definePageMeta({
  layout: 'dashboard',
  middleware: 'auth'
})

useHead({
  title: 'Currencies | EventaHub Malawi',
  meta: [
    { name: "description", content: "Manage and configure supported currencies for payments and transactions" }
  ]
})

const { $toast }: any = useNuxtApp()
const httpClient = useHttpClient()

const loading = ref<boolean>(true)
const saving = ref<boolean>(false)
const deleting = ref(false)
const currencies = ref<any[]>([])
const stats = ref({
  total_currencies: 0,
  recent_currencies: 0,
  plan_usage: {} as Record<string, number>
})

const searchQuery = ref('')
const dateFilter = ref(<string>'all')

const showModal = ref<boolean>(false)
const showDeleteModal = ref<boolean>(false)
const isEditing = ref(false)
const currencyToDelete = ref<any>(null)

const form = ref({
  name: ''
})

const pagination = ref({
  current_page: 1,
  last_page: 1,
  per_page: 10,
  total: 0
})

const dateFilters = [
  { value: 'all', label: 'All Time' },
  { value: 'today', label: 'Today' },
  { value: 'week', label: 'This Week' },
  { value: 'month', label: 'This Month' },
  { value: 'year', label: 'This Year' }
]

const filteredCurrencies = computed(() => {
  if (!searchQuery.value) return currencies.value

  return currencies.value.filter(currency =>
    currency.name.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

const mostUsedCurrency = computed(() => {
  const usage = stats.value.plan_usage
  if (!usage || Object.keys(usage).length === 0) return 'N/A'

  const mostUsed = Object.entries(usage).reduce((a, b) =>
    usage[a[0]] > usage[b[0]] ? a : b
  )
  return mostUsed[0]
})

const fetchCurrencies = async () => {
  try {
    loading.value = true
    const params = new URLSearchParams({
      search: searchQuery.value,
      dateFilter: dateFilter.value,
      limit: pagination.value.per_page.toString()
    })

    const response = await httpClient.get(`${ENDPOINTS.CURRENCIES.GET_ALL}?${params}`) as any

    if (response?.data) {
      currencies.value = response.data.currencies
      pagination.value = {
        current_page: response.data.current_page,
        last_page: response.data.last_page,
        per_page: response.data.per_page,
        total: response.data.total
      }
    }
  } catch (error: any) {
    console.error('Error fetching currencies:', error)
    $toast.error('Failed to load currencies')
  } finally {
    loading.value = false
  }
}

const fetchStats = async () => {
  try {
    const response = await httpClient.get(ENDPOINTS.CURRENCIES.STATS) as any
    if (response) {
      stats.value = response
    }
  } catch (error: any) {
    console.error('Error fetching stats:', error)
  }
}

const openCreateModal = () => {
  isEditing.value = false
  form.value = { name: '' }
  showModal.value = true
}

const openEditModal = (currency: any) => {
  isEditing.value = true
  form.value = { name: currency.name }
  showModal.value = true
}

const closeModal = () => {
  showModal.value = false
  form.value = { name: '' }
}

const openDeleteModal = (currency: any) => {
  currencyToDelete.value = currency
  showDeleteModal.value = true
}

const closeDeleteModal = () => {
  showDeleteModal.value = false
  currencyToDelete.value = null
}

const submitForm = async (data: any) => {
  try {
    saving.value = true

    const payload = {
      name: data.text_1.toUpperCase()
    }

    if (isEditing.value && currencyToDelete.value) {
      await httpClient.put(`${ENDPOINTS.CURRENCIES.UPDATE}/${currencyToDelete.value.id}`, payload)
      $toast.success('Currency updated successfully')
    } else {
      await httpClient.post(ENDPOINTS.CURRENCIES.CREATE, payload)
      $toast.success('Currency created successfully')
    }

    closeModal()
    await fetchCurrencies()
    await fetchStats()
  } catch (error: any) {
    console.error('Error saving currency:', error)
    if (error.response?.data?.errors) {
      const errors = error.response.data.errors
      Object.keys(errors).forEach(key => {
        errors[key].forEach((message: string) => {
          $toast.error(message)
        })
      })
    } else {
      $toast.error('Failed to save currency')
    }
  } finally {
    saving.value = false
  }
}

const deleteCurrency = async () => {
  if (!currencyToDelete.value) return

  try {
    deleting.value = true
    await httpClient.delete(`${ENDPOINTS.CURRENCIES.DELETE}/${currencyToDelete.value.id}`)
    $toast.success('Currency deleted successfully')
    closeDeleteModal()
    await fetchCurrencies()
    await fetchStats()
  } catch (error: any) {
    console.error('Error deleting currency:', error)
    $toast.error(error.response?.data?.message || 'Failed to delete currency')
  } finally {
    deleting.value = false
  }
}

const getCurrencyUsage = (currencyName: string): number => {
  return stats.value.plan_usage[currencyName] || 0
}

const formatDate = (date: string): string => {
  return new Date(date).toLocaleDateString()
}

const formatDateFilter = (filter: string): string => {
  const option = dateFilters.find(f => f.value === filter)
  return option?.label || 'All Time'
}

const exportCurrencies = async () => {
  try {
    const data = currencies.value.map(currency => ({
      'Currency Code': currency.name,
      'Usage Count': getCurrencyUsage(currency.name),
      'Created Date': formatDate(currency.created_at)
    }))

    const csv = [
      Object.keys(data[0]).join(','),
      ...data.map(row => Object.values(row).join(','))
    ].join('\n')

    const blob = new Blob([csv], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `currencies-${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    window.URL.revokeObjectURL(url)

    $toast.success('Currencies exported successfully')
  } catch (error) {
    $toast.error('Failed to export currencies')
  }
}

const nextPage = () => {
  if (pagination.value.current_page < pagination.value.last_page) {
    pagination.value.current_page++
    fetchCurrencies()
  }
}

const previousPage = () => {
  if (pagination.value.current_page > 1) {
    pagination.value.current_page--
    fetchCurrencies()
  }
}

let debounceTimer: NodeJS.Timeout
watch([searchQuery, dateFilter], () => {
  clearTimeout(debounceTimer)
  debounceTimer = setTimeout(() => {
    pagination.value.current_page = 1
    fetchCurrencies()
  }, 300)
})

onMounted(() => {
  fetchCurrencies()
  fetchStats()
})
</script>
