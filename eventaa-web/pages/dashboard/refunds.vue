<template>
  <div class="p-6 space-y-6">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
          Refunds Management
        </h1>
        <p class="text-gray-500 dark:text-gray-400 mt-1">
          Manage refund requests for your events
        </p>
      </div>
      <div class="flex space-x-2">
        <button
          @click="refreshData"
          :disabled="loading"
          class="p-2.5 flex items-center justify-center rounded-full bg-white dark:bg-zinc-800 border border-gray-300 dark:border-zinc-600 shadow-sm hover:bg-gray-50 dark:hover:bg-zinc-700 text-gray-700 dark:text-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <Icon icon="famicons:refresh-outline" class="w-5 h-5" />
        </button>
        <button
          @click="exportRefunds"
          :disabled="exporting"
          class="p-2.5 flex items-center justify-center rounded-full bg-white dark:bg-zinc-800 border border-gray-300 dark:border-zinc-600 shadow-sm hover:bg-gray-50 dark:hover:bg-zinc-700 text-gray-700 dark:text-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <img src="@/assets/icons/excel.png" alt="Excel" class="w-5 h-5 object-cover" />
        </button>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div class="bg-white dark:bg-zinc-800 p-6 shadow">
        <div class="flex items-center">
          <div
            class="p-2 bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400"
          >
            <Icon icon="fluent-mdl2:feedback-request-solid" class="w-6 h-6" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-zinc-600 dark:text-zinc-400">
              Total Requests
            </p>
            <p class="text-2xl font-bold text-zinc-900 dark:text-zinc-100">
              {{ statistics?.total_requests || 0 }}
            </p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-zinc-800 p-6 shadow">
        <div class="flex items-center">
          <div
            class="p-2 bg-yellow-100 dark:bg-yellow-900/20 text-yellow-600 dark:text-yellow-400"
          >
            <Icon icon="heroicons:clock" class="w-6 h-6" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-zinc-600 dark:text-zinc-400">
              Pending
            </p>
            <p class="text-2xl font-bold text-zinc-900 dark:text-zinc-100">
              {{ statistics?.pending_requests || 0 }}
            </p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-zinc-800 p-6 shadow">
        <div class="flex items-center">
          <div
            class="p-2 bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400"
          >
            <Icon icon="heroicons:check-circle" class="w-6 h-6" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-zinc-600 dark:text-zinc-400">
              Approved
            </p>
            <p class="text-2xl font-bold text-zinc-900 dark:text-zinc-100">
              {{ statistics?.approved_requests || 0 }}
            </p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-zinc-800 p-6 shadow">
        <div class="flex items-center">
          <div
            class="p-2 bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400"
          >
            <Icon icon="heroicons:banknotes" class="w-6 h-6" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-zinc-600 dark:text-zinc-400">
              Total Refunded
            </p>
            <p class="text-2xl font-bold text-zinc-900 dark:text-zinc-100">
              MK{{ formatNumber(statistics?.total_refund_amount || 0) }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <div class="bg-white dark:bg-zinc-800 p-4 shadow">
      <div
        class="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4"
      >
        <div class="flex-1">
          <CoreSearchBar
            v-model="filters.search"
            placeholder="Search by reference, customer, or event..."
            :max-history-items="10"
            @update:model-value="debouncedSearch"
          />
        </div>

        <div class="flex items-center space-x-3">
          <CoreSelect
            :options="statusOptions"
            v-model="tempFilters.status"
            @update:model-value="quickFilterByStatus"
          />

          <CoreSelect
            :options="eventOptions"
            v-model="tempFilters.event_id"
            @update:model-value="quickFilterByEvent"
          />

          <CoreSelect
            :options="dateOptions"
            v-model="quickDateFilter"
            @update:model-value="handleQuickDateFilter"
          />

          <Popover class="relative">
            <PopoverButton
              class="p-2.5 flex items-center justify-center rounded-full bg-white dark:bg-zinc-800 border border-gray-300 dark:border-zinc-600 shadow-sm hover:bg-gray-50 dark:hover:bg-zinc-700 text-gray-700 dark:text-gray-200"
            >
              <div class="relative">
                <Icon
                  icon="heroicons:funnel"
                  class="w-5 h-5"
                />
                <span
                  v-if="activeFilterCount > 0"
                  class="absolute -top-2 -right-2 bg-red-600 text-white text-xs w-4 h-4 flex items-center justify-center rounded-full"
                >
                  {{ activeFilterCount }}
                </span>
              </div>
            </PopoverButton>

            <transition
              enter-active-class="transition duration-200 ease-out"
              enter-from-class="translate-y-1 opacity-0"
              enter-to-class="translate-y-0 opacity-100"
              leave-active-class="transition duration-150 ease-in"
              leave-from-class="translate-y-0 opacity-100"
              leave-to-class="translate-y-1 opacity-0"
            >
              <PopoverPanel
                class="absolute right-0 z-[9999] mt-2 w-96 bg-white dark:bg-zinc-800 shadow-xl border-gray-200 dark:border-zinc-700 focus:outline-none"
              >
                <div class="p-6">
                  <div class="flex items-center justify-between mb-6">
                    <h3
                      class="text-lg font-semibold text-gray-900 dark:text-zinc-100"
                    >
                      Filter Refunds
                    </h3>
                    <button
                      @click="clearFilters"
                      class="text-sm text-gray-500 dark:text-zinc-400 hover:text-gray-700 dark:hover:text-zinc-200 transition"
                    >
                      Clear all
                    </button>
                  </div>

                  <div class="space-y-6">
                    <!-- Status Filter -->
                    <div>
                      <label
                        class="block text-base font-semibold text-gray-700 dark:text-zinc-300 mb-3"
                      >
                        Status
                      </label>
                      <div class="relative">
                        <select
                          v-model="tempFilters.status"
                          class="w-full px-3 py-2 shadow-sm bg-white dark:bg-zinc-700 text-gray-900 appearance-none pr-8"
                        >
                          <option value="all">All Statuses</option>
                          <option value="pending">Pending</option>
                          <option value="approved">Approved</option>
                          <option value="rejected">Rejected</option>
                          <option value="completed">Completed</option>
                        </select>
                        <div class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                          <Icon icon="mi:select" class="w-5 h-5 text-gray-500 dark:text-zinc-400"/>
                        </div>
                      </div>
                    </div>

                    <!-- Event Filter -->
                    <div>
                      <label
                        class="block text-base font-semibold text-gray-700 dark:text-zinc-300 mb-3"
                      >
                        Event
                      </label>
                      <div class="relative">
                        <select
                          v-model="tempFilters.event_id"
                          class="w-full px-3 py-2 shadow-sm bg-white dark:bg-zinc-700 text-gray-900 appearance-none pr-8"
                        >
                          <option value="">All Events</option>
                          <option
                            v-for="event in userEvents"
                            :key="event.id"
                            :value="event.id"
                          >
                            {{ event.title }}
                          </option>
                        </select>
                        <div class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                          <Icon icon="mi:select" class="w-5 h-5 text-gray-500 dark:text-zinc-400"/>
                        </div>
                      </div>
                    </div>

                    <div>
                      <label
                        class="block text-base font-semibold text-gray-700 dark:text-zinc-300 mb-3"
                      >
                        Date Range
                      </label>
                      <datepicker
                        v-model="tempFilters.dateRange"
                        range
                        :enable-time-picker="false"
                        format="dd/MM/yyyy"
                        placeholder="Select date range"
                        class="w-full"
                        :text-input="true"
                        auto-apply
                        @cleared="clearDateRange"
                      />
                    </div>
                  </div>

                  <!-- Action Buttons -->
                  <div
                    class="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-zinc-700 mt-6"
                  >
                    <PopoverButton
                      @click="clearFilters"
                      class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-zinc-200 bg-white dark:bg-zinc-700 border border-gray-300 dark:border-zinc-600 hover:bg-gray-50 dark:hover:bg-zinc-600 transition"
                    >
                      Clear All
                    </PopoverButton>
                    <PopoverButton
                      @click="applyFilters"
                      class="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 transition"
                    >
                      Apply Filters
                    </PopoverButton>
                  </div>
                </div>
              </PopoverPanel>
            </transition>
          </Popover>
        </div>
      </div>

      <!-- Active Filters Display -->
      <div
        v-if="activeFilters.length > 0"
        class="mt-4 flex flex-wrap items-center gap-3"
      >
        <span class="text-base font-medium text-gray-700 dark:text-zinc-300"
          >Active Filters:</span
        >
        <div
          v-for="filter in activeFilters"
          :key="filter.key"
          class="flex items-center bg-red-600 dark:bg-red-900/20 rounded-full shadow-sm px-3 py-1 text-sm"
        >
          <span class="text-red-100 dark:text-red-200">{{ filter.label }}</span>
          <button
            @click="removeFilter(filter.key)"
            class="ml-2 text-red-100 dark:text-red-200 hover:text-red-200 dark:hover:text-red-200"
          >
            <Icon icon="heroicons:x-mark" class="w-3 h-3" />
          </button>
        </div>
        <button
          @click="clearFilters"
          class="text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 underline"
        >
          Clear all
        </button>
      </div>
    </div>

    <!-- Refunds Table -->
    <div class="bg-white dark:bg-zinc-800 shadow">
      <div class="px-6 py-4 border-b border-zinc-200 dark:border-zinc-700">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-zinc-900 dark:text-zinc-100">
            Refund Requests
          </h3>
          <div class="text-sm text-zinc-600 dark:text-zinc-400">
            {{ pagination?.total || 0 }} total requests
          </div>
        </div>
      </div>

      <div v-if="loading" class="p-8 flex flex-col items-center justify-center">
        <CoreLoader />
      </div>

      <div v-else-if="refunds.length === 0" class="p-8 text-center">
        <Icon
          icon="heroicons:document-text"
          class="w-12 h-12 text-zinc-400 mx-auto mb-4"
        />
        <h3 class="text-lg font-medium text-zinc-900 dark:text-zinc-100 mb-2">
          No refund requests found
        </h3>
        <p class="text-zinc-600 dark:text-zinc-400">
          {{
            filters.search || filters.status !== "all" || filters.event_id
              ? "Try adjusting your filters"
              : "No refund requests have been made for your events yet"
          }}
        </p>
        <button
          @click="clearFilters"
          class="mt-4 inline-flex items-center px-4 py-2 text-sky-600 bg-white dark:bg-zinc-700 hover:bg-gray-50 dark:hover:bg-zinc-600 focus:outline-none focus:ring-2 focus:ring-red-500 text-base font-medium"
        >
          <Icon icon="heroicons:arrow-path" class="w-4 h-4 mr-2" />
          Clear Filters
        </button>
      </div>

      <div v-else class="overflow-x-auto">
        <table class="w-full divide-y divide-zinc-200 dark:divide-zinc-700">
          <thead class="bg-gray-50 dark:bg-zinc-700">
            <tr>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-zinc-400 uppercase tracking-wider"
              >
                Reference
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-zinc-400 uppercase tracking-wider"
              >
                Customer
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-zinc-400 uppercase tracking-wider"
              >
                Event
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-zinc-400 uppercase tracking-wider"
              >
                Amount
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-zinc-400 uppercase tracking-wider"
              >
                Status
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-zinc-400 uppercase tracking-wider"
              >
                Date
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-zinc-400 uppercase tracking-wider"
              >
                Actions
              </th>
            </tr>
          </thead>
          <tbody
            class="bg-white dark:bg-zinc-800 divide-y divide-gray-200 dark:divide-zinc-700"
          >
            <tr
              v-for="refund in refunds"
              :key="refund.id"
              class="hover:bg-gray-50 dark:hover:bg-zinc-700 transition-colors"
            >
              <td
                class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-zinc-100"
              >
                <span class="font-mono">{{ refund.refund_reference }}</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div>
                  <div
                    class="text-sm font-medium text-gray-900 dark:text-zinc-100"
                  >
                    {{ refund.user?.name }}
                  </div>
                  <div class="text-sm text-gray-500 dark:text-zinc-400">
                    {{ refund.user?.email }}
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900 dark:text-zinc-100">
                  {{ refund.ticket_purchase?.event?.title }}
                </div>
                <div class="text-sm text-gray-500 dark:text-zinc-400">
                  {{ refund.ticket_purchase?.ticket?.name }}
                </div>
              </td>
              <td
                class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-zinc-100"
              >
                MK{{ formatNumber(refund.refund_amount) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  :class="getStatusBadgeClass(refund.status)"
                  class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                >
                  {{ getStatusText(refund.status) }}
                </span>
              </td>
              <td
                class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-zinc-400"
              >
                {{ formatDate(refund.created_at) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex items-center space-x-2">
                  <RefundsRefundDetailModal
                    :refund="refund"
                    @close="closeDetailModal"
                    @approve="handleApprove"
                    @reject="handleReject"
                  />
                  <button
                    v-if="refund.status === 'pending'"
                    @click="approveRefund(refund)"
                    :disabled="processingRefund === refund.id"
                    class="inline-flex items-center rounded-full px-3 py-1 bg-green-600 text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed text-xs font-medium"
                  >
                    <Icon icon="heroicons:check" class="w-3 h-3 mr-1" />
                    {{
                      processingRefund === refund.id
                        ? "Processing..."
                        : "Approve"
                    }}
                  </button>
                  <button
                    v-if="refund.status === 'pending'"
                    @click="rejectRefund(refund)"
                    :disabled="processingRefund === refund.id"
                    class="inline-flex items-center rounded-full px-3 py-1 bg-red-600 text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed text-xs font-medium"
                  >
                    <Icon icon="heroicons:x-mark" class="w-3 h-3 mr-1" />
                    {{
                      processingRefund === refund.id
                        ? "Processing..."
                        : "Reject"
                    }}
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        v-if="pagination"
        class="px-6 py-4 border-t border-gray-200 dark:border-zinc-700 bg-gray-50 dark:bg-zinc-700"
      >
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-700 dark:text-zinc-300">
            Showing
            {{ (pagination.current_page - 1) * pagination.per_page + 1 }} to
            {{
              Math.min(
                pagination.current_page * pagination.per_page,
                pagination.total
              )
            }}
            of {{ pagination.total }} results
          </div>
          <CorePagination
            :current-page="pagination.current_page"
            :total-pages="pagination.last_page"
            :total-items="pagination.total"
            :per-page="pagination.per_page"
            @page-change="handlePageChange"
          />
        </div>
      </div>
    </div>

    <RefundsRefundRejectModal
      v-if="refundToReject"
      :refund="refundToReject"
      :open="showRejectModal"
      @close="closeRejectModal"
      @confirm="handleRejectConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { Popover, PopoverButton, PopoverPanel } from "@headlessui/vue";

definePageMeta({
  middleware: ["auth"],
  layout: "dashboard",
});

const { $toast } = useNuxtApp() as any;
const httpClient = useHttpClient();

const loading = ref(false);
const exporting = ref(false);
const processingRefund = ref<number | null>(null);
const refunds = ref<any[]>([]);
const statistics = ref<any>(null);
const userEvents = ref<any[]>([]);
const pagination = ref<any>(null);
const selectedRefund = ref<any>(null);
const refundToReject = ref<any>(null);
const showDetailModal = ref(false);
const showRejectModal = ref(false);
const quickDateFilter = ref('');

// Options for dropdown selects
const statusOptions = ref([
  { value: 'all', label: 'All Statuses' },
  { value: 'pending', label: 'Pending' },
  { value: 'approved', label: 'Approved' },
  { value: 'rejected', label: 'Rejected' },
  { value: 'completed', label: 'Completed' },
]);

const dateOptions = ref([
  { value: '', label: 'All Time' },
  { value: 'today', label: 'Today' },
  { value: 'yesterday', label: 'Yesterday' },
  { value: 'week', label: 'This Week' },
  { value: 'month', label: 'This Month' },
  { value: 'year', label: 'This Year' },
  { value: 'custom', label: 'Custom Range' },
]);

const filters = ref({
  search: "",
  status: "all",
  event_id: "",
  date_from: "",
  date_to: "",
  page: 1,
  per_page: 10,
});

const tempFilters = ref({
  status: "all",
  event_id: "",
  dateRange: null as any,
});

const activeFilterCount = computed(() => {
  let count = 0;
  if (filters.value.status !== "all") count++;
  if (filters.value.event_id) count++;
  if (filters.value.date_from || filters.value.date_to) count++;
  return count;
});

const eventOptions = computed(() => {
  const options = [{ value: '', label: 'All Events' }];
  if (userEvents.value && userEvents.value.length) {
    userEvents.value.forEach(event => {
      options.push({ value: event.id, label: event.title });
    });
  }
  return options;
});

// Quick filtering functions
const quickFilterByStatus = (value: string): void => {
  filters.value.status = value;
  filters.value.page = 1;
  fetchRefunds();
};

const quickFilterByEvent = (value: string): void => {
  filters.value.event_id = value;
  filters.value.page = 1;
  fetchRefunds();
};

const handleQuickDateFilter = (value: string): void => {
  // Reset date filters
  filters.value.date_from = '';
  filters.value.date_to = '';

  const today = new Date();

  switch(value) {
    case 'today':
      filters.value.date_from = today.toISOString().split('T')[0];
      filters.value.date_to = today.toISOString().split('T')[0];
      break;
    case 'yesterday':
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      filters.value.date_from = yesterday.toISOString().split('T')[0];
      filters.value.date_to = yesterday.toISOString().split('T')[0];
      break;
    case 'week':
      const startOfWeek = new Date(today);
      startOfWeek.setDate(today.getDate() - today.getDay());
      filters.value.date_from = startOfWeek.toISOString().split('T')[0];
      filters.value.date_to = today.toISOString().split('T')[0];
      break;
    case 'month':
      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      filters.value.date_from = startOfMonth.toISOString().split('T')[0];
      filters.value.date_to = today.toISOString().split('T')[0];
      break;
    case 'year':
      const startOfYear = new Date(today.getFullYear(), 0, 1);
      filters.value.date_from = startOfYear.toISOString().split('T')[0];
      filters.value.date_to = today.toISOString().split('T')[0];
      break;
    case 'custom':
      // Open date picker dialog or do nothing if it's already in filter popover
      break;
    default:
      // All time - no date filtering
      break;
  }

  if (value !== 'custom' && value !== '') {
    filters.value.page = 1;
    fetchRefunds();
  }
};

const activeFilters = computed(() => {
  const active = [];
  if (filters.value.status !== "all") {
    active.push({
      key: "status",
      label: `Status: ${getStatusText(filters.value.status)}`,
    });
  }
  if (filters.value.event_id) {
    const event = userEvents.value.find((e) => e.id == filters.value.event_id);
    active.push({
      key: "event_id",
      label: `Event: ${event?.title || "Unknown"}`,
    });
  }
  if (filters.value.date_from && filters.value.date_to) {
    active.push({
      key: "dateRange",
      label: `Date: ${filters.value.date_from} - ${filters.value.date_to}`,
    });
  } else if (filters.value.date_from) {
    active.push({
      key: "dateRange",
      label: `From: ${filters.value.date_from}`,
    });
  } else if (filters.value.date_to) {
    active.push({
      key: "dateRange",
      label: `To: ${filters.value.date_to}`,
    });
  }
  return active;
});

let searchTimeout: NodeJS.Timeout | null = null;
const debouncedSearch = () => {
  if (searchTimeout) clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    filters.value.page = 1;
    fetchRefunds();
  }, 500);
};

const fetchRefunds = async (): Promise<void> => {
  try {
    loading.value = true;
    const params = new URLSearchParams();

    Object.entries(filters.value).forEach(([key, value]) => {
      if (value && value !== "all" && value !== "") {
        params.append(key, value.toString());
      }
    });

    const response = (await httpClient.get(
      `${ENDPOINTS.REFUNDS.GET}?${params.toString()}`
    )) as any;
    refunds.value = response.data.data;
    pagination.value = {
      current_page: response.data.current_page,
      last_page: response.data.last_page,
      total: response.data.total,
      per_page: response.data.per_page,
    };
  } catch (error) {
    console.error("Error fetching refunds:", error);
    $toast.error("Failed to fetch refunds");
  } finally {
    loading.value = false;
  }
};

const fetchStatistics = async (): Promise<void> => {
  try {
    const params = new URLSearchParams();
    if (filters.value.event_id)
      params.append("event_id", filters.value.event_id);
    if (filters.value.date_from)
      params.append("date_from", filters.value.date_from);
    if (filters.value.date_to) params.append("date_to", filters.value.date_to);

    const response = (await httpClient.get(
      `${ENDPOINTS.REFUNDS.STATISTICS}?${params.toString()}`
    )) as any;
    statistics.value = response.data;
  } catch (error) {
    console.error("Error fetching statistics:", error);
  }
};

const fetchUserEvents = async (): Promise<void> => {
  try {
    const response = (await httpClient.get(ENDPOINTS.EVENTS.USER)) as any;
    userEvents.value = response.data;
  } catch (error) {
    console.error("Error fetching user events:", error);
  }
};

const refreshData = async (): Promise<void> => {
  await Promise.all([fetchRefunds(), fetchStatistics()]);
};

const applyFilters = (): void => {
  filters.value.status = tempFilters.value.status;
  filters.value.event_id = tempFilters.value.event_id;

  // Handle date range from datepicker
  if (tempFilters.value.dateRange && Array.isArray(tempFilters.value.dateRange)) {
    filters.value.date_from = tempFilters.value.dateRange[0]
      ? new Date(tempFilters.value.dateRange[0]).toISOString().split('T')[0]
      : "";
    filters.value.date_to = tempFilters.value.dateRange[1]
      ? new Date(tempFilters.value.dateRange[1]).toISOString().split('T')[0]
      : "";
  } else {
    filters.value.date_from = "";
    filters.value.date_to = "";
  }

  filters.value.page = 1;
  fetchRefunds();
  $toast.success("Filters applied");
};

const removeFilter = (key: string): void => {
  switch (key) {
    case "status":
      filters.value.status = "all";
      tempFilters.value.status = "all";
      break;
    case "event_id":
      filters.value.event_id = "";
      tempFilters.value.event_id = "";
      break;
    case "date_from":
    case "date_to":
    case "dateRange":
      filters.value.date_from = "";
      filters.value.date_to = "";
      tempFilters.value.dateRange = null;
      break;
  }
  filters.value.page = 1;
  fetchRefunds();
};

const clearFilters = (): void => {
  filters.value = {
    search: "",
    status: "all",
    event_id: "",
    date_from: "",
    date_to: "",
    page: 1,
    per_page: 10,
  };
  tempFilters.value = {
    status: "all",
    event_id: "",
    dateRange: null,
  };
  quickDateFilter.value = '';
  fetchRefunds();
};

const handlePageChange = (page: number): void => {
  filters.value.page = page;
  fetchRefunds();
};

const clearDateRange = (): void => {
  tempFilters.value.dateRange = null;
};

const closeDetailModal = (): void => {
  selectedRefund.value = null;
  showDetailModal.value = false;
};

const approveRefund = (refund: any): void => {
  selectedRefund.value = refund;
  handleApprove({ admin_notes: "" });
};

const rejectRefund = (refund: any): void => {
  refundToReject.value = refund;
  showRejectModal.value = true;
};

const closeRejectModal = (): void => {
  refundToReject.value = null;
  showRejectModal.value = false;
};

const handleApprove = async (data: { admin_notes: string }): Promise<void> => {
  if (!selectedRefund.value) return;

  try {
    processingRefund.value = selectedRefund.value.id;
    await httpClient.post(
      `${ENDPOINTS.REFUNDS.APPROVE}/${selectedRefund.value.id}/approve`,
      data
    );

    $toast.success("Refund approved successfully");
    closeDetailModal();
    await refreshData();
  } catch (error) {
    console.error("Error approving refund:", error);
    $toast.error("Failed to approve refund");
  } finally {
    processingRefund.value = null;
  }
};

const handleReject = (data: {
  rejection_reason: string;
  admin_notes?: string;
}): void => {
  handleRejectConfirm(data);
};

const handleRejectConfirm = async (data: {
  rejection_reason: string;
  admin_notes?: string;
}): Promise<void> => {
  if (!refundToReject.value) return;

  try {
    processingRefund.value = refundToReject.value.id;
    await httpClient.post(
      `${ENDPOINTS.REFUNDS.REJECT}/${refundToReject.value.id}/reject`,
      data
    );

    $toast.success("Refund rejected successfully");
    closeRejectModal();
    await refreshData();
  } catch (error) {
    console.error("Error rejecting refund:", error);
    $toast.error("Failed to reject refund");
  } finally {
    processingRefund.value = null;
  }
};

const exportRefunds = async (): Promise<void> => {
  try {
    exporting.value = true;
    const params = new URLSearchParams();

    Object.entries(filters.value).forEach(([key, value]) => {
      if (
        value &&
        value !== "all" &&
        value !== "" &&
        key !== "page" &&
        key !== "per_page"
      ) {
        params.append(key, value.toString());
      }
    });

    const response = await httpClient.get(
      `${ENDPOINTS.REFUNDS.EXPORT}?${params.toString()}`,
      {
        responseType: "blob",
      }
    ) as Blob;

    // Create download link
    const url = window.URL.createObjectURL(response);
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute(
      "download",
      `refunds-${new Date().toISOString().split("T")[0]}.csv`
    );
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);

    $toast.success("Refunds exported successfully");
  } catch (error) {
    console.error("Error exporting refunds:", error);
    $toast.error("Failed to export refunds");
  } finally {
    exporting.value = false;
  }
};

const formatNumber = (value: number): string => {
  return new Intl.NumberFormat().format(value || 0);
};

const formatDate = (date: string): string => {
  return new Date(date).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};

const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    pending: "Pending",
    approved: "Approved",
    rejected: "Rejected",
    completed: "Completed",
  };
  return statusMap[status] || status;
};

const getStatusBadgeClass = (status: string): string => {
  const classMap: Record<string, string> = {
    pending:
      "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",
    approved:
      "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",
    rejected: "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400",
    completed:
      "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
  };
  return (
    classMap[status] ||
    "bg-zinc-100 text-zinc-800 dark:bg-zinc-700 dark:text-zinc-300"
  );
};



onMounted(async () => {
  tempFilters.value = {
    status: filters.value.status,
    event_id: filters.value.event_id,
    dateRange: null,
  };
  quickDateFilter.value = '';

  await Promise.all([fetchUserEvents(), fetchRefunds(), fetchStatistics()]);
});
</script>
