<template>
  <div class="space-y-6 p-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold dashboard-text-primary">Host Requests</h1>
        <p class="dashboard-text-muted">
          Manage user requests to become event hosts
        </p>
      </div>
      <div class="flex space-x-2">
        <button
          @click="refreshData"
          class="p-2.5 flex items-center justify-center rounded-full bg-white dark:bg-zinc-800 border border-gray-300 dark:border-zinc-600 shadow-sm hover:bg-gray-50 dark:hover:bg-zinc-700 text-gray-700 dark:text-gray-200"
        >
          <Icon icon="famicons:refresh-outline" class="w-5 h-5" />
        </button>
        <button
          @click="exportData"
          class="p-2.5 flex items-center justify-center rounded-full bg-white dark:bg-zinc-800 border border-gray-300 dark:border-zinc-600 shadow-sm hover:bg-gray-50 dark:hover:bg-zinc-700 text-gray-700 dark:text-gray-200"
        >
          <img
            src="@/assets/icons/excel.png"
            alt="excel-icon"
            class="w-5 h-5 object-cover"
          />
        </button>
      </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <StatsCard
        title="Total Requests"
        :value="statistics.total || 0"
        icon="heroicons:document-text"
        iconColor="blue"
        :showGrowth="false"
      />

      <StatsCard
        title="Pending"
        :value="statistics.pending || 0"
        icon="heroicons:clock"
        iconColor="yellow"
        :showGrowth="false"
      />

      <StatsCard
        title="Approved"
        :value="statistics.approved || 0"
        icon="heroicons:check-circle"
        iconColor="green"
        :showGrowth="false"
      />

      <StatsCard
        title="Rejected"
        :value="statistics.rejected || 0"
        icon="heroicons:x-circle"
        iconColor="red"
        :showGrowth="false"
      />
    </div>

    <!-- Filters -->
    <div class="dashboard-bg-card dashboard-shadow">
      <div class="p-6 dashboard-border border-b">
        <div
          class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0"
        >
          <div
            class="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4"
          >
            <!-- Status Filter -->
            <div class="min-w-0 flex-1 sm:max-w-xs">
              <Select
                v-model="filters.status"
                :options="statusOptions"
                @update:modelValue="() => fetchRequests(1)"
              />
            </div>

            <!-- Search -->
            <div class="min-w-0 flex-1 sm:max-w-xs">
              <SearchBar
                v-model="filters.search"
                placeholder="Search by name or email..."
                :maxHistoryItems="5"
                @update:modelValue="debouncedSearch"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Requests Table -->
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y dashboard-border-light">
          <thead class="dashboard-bg-hover">
            <tr>
              <th
                class="px-6 py-3 text-left text-xs font-medium dashboard-text-muted uppercase tracking-wider"
              >
                User
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium dashboard-text-muted uppercase tracking-wider"
              >
                Status
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium dashboard-text-muted uppercase tracking-wider"
              >
                Requested
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium dashboard-text-muted uppercase tracking-wider"
              >
                Processed By
              </th>
              <th
                class="px-6 py-3 text-right text-xs font-medium dashboard-text-muted uppercase tracking-wider"
              >
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="dashboard-bg-card divide-y dashboard-border-light">
            <tr v-if="loading" v-for="i in 5" :key="i" class="animate-pulse">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div
                    class="h-10 w-10 bg-gray-200 dark:bg-gray-700 rounded-full"
                  ></div>
                  <div class="ml-4">
                    <div
                      class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-24"
                    ></div>
                    <div
                      class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-32 mt-1"
                    ></div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div
                  class="h-6 bg-gray-200 dark:bg-gray-700 rounded w-16"
                ></div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div
                  class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20"
                ></div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div
                  class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-16"
                ></div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right">
                <div
                  class="h-8 bg-gray-200 dark:bg-gray-700 rounded w-16 ml-auto"
                ></div>
              </td>
            </tr>

            <tr v-else-if="requests.length === 0">
              <td colspan="5" class="px-6 py-12 text-center">
                <div class="flex flex-col items-center">
                  <Icon
                    icon="heroicons:document-text"
                    class="h-12 w-12 dashboard-text-muted mb-4"
                  />
                  <h3 class="text-sm font-medium dashboard-text-primary mb-1">
                    No host requests found
                  </h3>
                  <p class="text-sm dashboard-text-muted">
                    No requests match your current filters.
                  </p>
                </div>
              </td>
            </tr>

            <tr
              v-else
              v-for="request in requests"
              :key="request.id"
              class="hover:dashboard-bg-hover dashboard-transition"
            >
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10">
                    <div
                      class="h-10 w-10 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center"
                    >
                      <span
                        class="text-sm font-medium text-red-600 dark:text-red-300"
                      >
                        {{ request.user.name.charAt(0).toUpperCase() }}
                      </span>
                    </div>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium dashboard-text-primary">
                      {{ request.user.name }}
                    </div>
                    <div class="text-sm dashboard-text-muted">
                      {{ request.user.email }}
                    </div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  :class="getStatusBadgeClass(request.status)"
                  class="px-2 py-1 text-xs font-medium uppercase tracking-wide"
                >
                  {{ request.status }}
                </span>
              </td>
              <td
                class="px-6 py-4 whitespace-nowrap text-sm dashboard-text-muted"
              >
                {{ formatDate(request.requested_at) }}
              </td>
              <td
                class="px-6 py-4 whitespace-nowrap text-sm dashboard-text-muted"
              >
                {{ request.processedBy?.name || "-" }}
              </td>
              <td
                class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium"
              >
                <button
                  @click="viewRequest(request)"
                  class="dashboard-text-brand hover:text-red-800 dashboard-transition mr-3"
                >
                  View
                </button>
                <button
                  v-if="request.status === 'pending'"
                  @click="approveRequest(request)"
                  class="text-green-600 hover:text-green-800 dashboard-transition mr-3"
                >
                  Approve
                </button>
                <button
                  v-if="request.status === 'pending'"
                  @click="rejectRequest(request)"
                  class="text-red-600 hover:text-red-800 dashboard-transition"
                >
                  Reject
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div
        v-if="pagination.total > pagination.per_page"
        class="px-6 py-4 dashboard-border border-t"
      >
        <div class="flex items-center justify-between">
          <div class="text-sm dashboard-text-muted">
            Showing
            {{ (pagination.current_page - 1) * pagination.per_page + 1 }} to
            {{
              Math.min(
                pagination.current_page * pagination.per_page,
                pagination.total
              )
            }}
            of {{ pagination.total }} results
          </div>
          <Pagination
            :currentPage="pagination.current_page"
            :totalPages="pagination.last_page"
            :totalItems="pagination.total"
            :perPage="pagination.per_page"
            @page-change="changePage"
          />
        </div>
      </div>
    </div>

    <!-- Host Request Modal -->
    <HostRequestModal
      :is-open="showRequestModal"
      :request="selectedRequest"
      @close="closeRequestModal"
      @updated="handleRequestUpdated"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { useHttpClient } from "@/composables/useHttpClient";
import { ENDPOINTS } from "@/utils/api";
import dayjs from "dayjs";
import type {
  HostRequest,
  HostRequestStatistics,
  HostRequestFilters,
  HostRequestResponse,
  HostRequestPagination,
} from "@/types/host-request";
import HostRequestModal from "@/components/admin/HostRequestModal.vue";
import StatsCard from "@/components/core/StatsCard.vue";
import Select from "@/components/core/Select.vue";
import SearchBar from "@/components/core/SearchBar.vue";
import Pagination from "@/components/core/Pagination.vue";

definePageMeta({
  layout: "dashboard",
  middleware: ["auth"],
});

useHead({
  title: "Host Requests | Admin Dashboard",
});

const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const loading = ref(true);
const requests = ref<HostRequest[]>([]);
const statistics = ref<HostRequestStatistics>({
  total: 0,
  pending: 0,
  approved: 0,
  rejected: 0,
});
const showRequestModal = ref(false);
const selectedRequest = ref<HostRequest | null>(null);

const statusOptions = [
  { value: "all", label: "All Status" },
  { value: "pending", label: "Pending" },
  { value: "approved", label: "Approved" },
  { value: "rejected", label: "Rejected" },
];

const filters = reactive<HostRequestFilters>({
  status: "all",
  search: "",
});

const pagination = ref<HostRequestPagination>({
  current_page: 1,
  last_page: 1,
  per_page: 10,
  total: 0,
});

const fetchRequests = async (page = 1) => {
  loading.value = true;
  try {
    const params: any = {
      page,
      per_page: pagination.value.per_page,
    };

    if (filters.status !== "all") {
      params.status = filters.status;
    }

    if (filters.search.trim()) {
      params.search = filters.search.trim();
    }

    const response = await httpClient.get<HostRequestResponse>(
      ENDPOINTS.HOST_REQUESTS.INDEX,
      { params }
    );

    if (response) {
      requests.value = response.data.data;
      pagination.value = {
        current_page: response.data.current_page,
        last_page: response.data.last_page,
        per_page: response.data.per_page,
        total: response.data.total,
      };
    }
  } catch (error) {
    console.error("Error fetching host requests:", error);
    $toast.error("Failed to load host requests");
  } finally {
    loading.value = false;
  }
};

const fetchStatistics = async () => {
  try {
    const response = await httpClient.get<HostRequestStatistics>(
      ENDPOINTS.HOST_REQUESTS.STATISTICS
    );
    if (response) {
      statistics.value = response;
    }
  } catch (error) {
    console.error("Error fetching statistics:", error);
  }
};

// Simple debounce function
const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout;
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

const debouncedSearch = debounce(() => {
  fetchRequests(1);
}, 500);

const changePage = (page: number) => {
  if (page >= 1 && page <= pagination.value.last_page) {
    fetchRequests(page);
  }
};

const formatDate = (date: string) => {
  return dayjs(date).format("MMM D, YYYY");
};

const getStatusBadgeClass = (status: string) => {
  switch (status) {
    case "pending":
      return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
    case "approved":
      return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
    case "rejected":
      return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
  }
};

const viewRequest = (request: HostRequest) => {
  selectedRequest.value = request;
  showRequestModal.value = true;
};

const approveRequest = async (request: HostRequest) => {
  selectedRequest.value = request;
  showRequestModal.value = true;
};

const rejectRequest = async (request: HostRequest) => {
  selectedRequest.value = request;
  showRequestModal.value = true;
};

const closeRequestModal = () => {
  showRequestModal.value = false;
  selectedRequest.value = null;
};

const handleRequestUpdated = (updatedRequest: HostRequest) => {
  // Update the request in the list
  const index = requests.value.findIndex((r) => r.id === updatedRequest.id);
  if (index !== -1) {
    requests.value[index] = updatedRequest;
  }

  // Refresh statistics
  fetchStatistics();
};

// Refresh data function
const refreshData = async () => {
  $toast.info('Refreshing data...');
  try {
    await Promise.all([
      fetchRequests(1),
      fetchStatistics()
    ]);
    $toast.success('Data refreshed successfully');
  } catch (error) {
    console.error('Error refreshing data:', error);
    $toast.error('Failed to refresh data');
  }
};

// Export function
const exportData = async () => {
  try {
    const params: any = {};

    if (filters.status !== "all") {
      params.status = filters.status;
    }

    if (filters.search.trim()) {
      params.search = filters.search.trim();
    }

    $toast.info("Generating export file...");

    const response = await httpClient.get(
      ENDPOINTS.HOST_REQUESTS.EXPORT,
      {
        params,
        responseType: "blob",
      }
    ) as Blob;

    // Create download link
    const url = window.URL.createObjectURL(response);
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute(
      "download",
      `host-requests-${dayjs().format("YYYY-MM-DD")}.csv`
    );
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);

    $toast.success("Host requests exported successfully");
  } catch (error) {
    console.error("Error exporting host requests:", error);
    $toast.error("Failed to export data");
  }
};

onMounted(async () => {
  await Promise.all([fetchRequests(), fetchStatistics()]);
});
</script>
