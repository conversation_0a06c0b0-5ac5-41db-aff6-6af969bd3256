<template>
  <div class="dashboard-bg-main min-h-screen">
    <div class="p-6">
      <div class="mb-6 flex justify-between items-center">
        <div class="flex items-center space-x-4">
          <button @click="$router.back()" class="p-2 text-black dark:text-zinc-100 dashboard-transition">
            <Icon icon="heroicons:arrow-left" class="w-6 h-6" />
          </button>
          <div>
            <h1 class="text-2xl font-bold dashboard-text-primary">Add New Vendor</h1>
            <p class="dashboard-text-muted">Create a new vendor profile</p>
          </div>
        </div>
      </div>

      <!-- Stepper -->
      <div class="mb-8">
        <div class="flex mb-4 text-sm font-medium dashboard-text-secondary space-x-4">
          <div v-for="(step, index) in steps" :key="index" class="flex-1 flex flex-col items-center">
            <div class="flex items-center justify-center w-8 h-8 mb-2" :class="{
              'bg-red-600 text-white': currentStep === index,
              'bg-zinc-200 dark:bg-zinc-700 text-zinc-600 dark:text-zinc-400': currentStep !== index && !stepValidation[index],
              'bg-green-600 text-white': currentStep !== index && stepValidation[index],
              'bg-red-500 text-white': stepErrors[index]
            }">
              <span v-if="stepErrors[index]">!</span>
              <span v-else>{{ index + 1 }}</span>
            </div>
            <span :class="{
              'text-red-600 font-bold': currentStep === index,
              'text-zinc-400 dark:text-zinc-500': currentStep !== index && !stepValidation[index],
              'text-green-600': currentStep !== index && stepValidation[index],
              'text-red-500': stepErrors[index]
            }">
              {{ step.label }}
            </span>
          </div>
        </div>
      </div>

      <FormKit type="form" id="vendorForm" @submit="handleSubmit" :actions="false" #default="{ }">
        <div v-if="formError" class="bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-400 p-4 mb-4">
          {{ formError }}
        </div>
        <!-- Step 1: Basic Information -->
        <div v-if="currentStep === 0" class="space-y-6">
          <FormKit type="text" name="name" id="name" label="Business Name" validation="required"
            validation-visibility="live" :validation-messages="{ required: 'Business name is required' }"
            v-model="form.name" />

          <FormKit type="email" name="business_email" id="business_email" label="Business Email"
            validation="required|email" validation-visibility="live" :validation-messages="{
              required: 'Email is required',
              email: 'Please enter a valid email address'
            }" v-model="form.business_email" />

          <FormKit type="text" name="phone" id="phone" label="Phone Number" validation="required"
            validation-visibility="live" :validation-messages="{ required: 'Phone number is required' }"
            v-model="form.phone" />

          <FormKit type="url" name="website" id="website" label="Website" v-model="form.website" />

          <FormKit type="text" name="location" id="location" label="Location" validation="required"
            validation-visibility="live" :validation-messages="{ required: 'Location is required' }"
            v-model="form.location" />

          <FormKit type="textarea" name="bio" id="bio" label="Tell us about your business" validation="required"
            validation-visibility="live" :validation-messages="{ required: 'Bio is required' }"
            v-model="form.bio" />
        </div>

        <!-- Step 2: Logo & Contact -->
        <div v-else-if="currentStep === 1" class="space-y-6">
          <div class="dashboard-bg-card dashboard-shadow overflow-hidden">
            <div class="px-6 py-4 dashboard-border border-b">
              <h3 class="text-lg font-medium dashboard-text-primary">Logo</h3>
            </div>
            <div class="px-6 py-4">
              <div class="flex items-center space-x-6">
                <div class="flex-shrink-0">
                  <img v-if="logoPreview" :src="logoPreview" alt="Logo preview" class="h-24 w-24 rounded-full object-cover" />
                  <div v-else class="h-24 w-24 rounded-full bg-zinc-200 dark:bg-zinc-700 flex items-center justify-center">
                    <Icon icon="heroicons:building-storefront" class="h-12 w-12 text-zinc-400 dark:text-zinc-600" />
                  </div>
                </div>
                <div>
                  <input
                    ref="logoInput"
                    type="file"
                    accept="image/*"
                    @change="handleLogoUpload"
                    class="hidden"
                  />
                  <button
                    type="button"
                    @click="logoInput?.click()"
                    class="px-4 py-2 dashboard-border text-sm font-medium dashboard-text-secondary dashboard-bg-card hover:dashboard-bg-hover dashboard-transition"
                  >
                    Upload Logo
                  </button>
                  <p class="text-xs dashboard-text-muted mt-1">PNG, JPG up to 2MB</p>
                </div>
              </div>
            </div>
          </div>

          <FormKit type="text" name="facebook" id="facebook" label="Facebook" v-model="form.facebook" />
          <FormKit type="text" name="instagram" id="instagram" label="Instagram" v-model="form.instagram" />
          <FormKit type="text" name="twitter" id="twitter" label="Twitter" v-model="form.twitter" />
        </div>

        <!-- Step 3: Services -->
        <div v-else-if="currentStep === 2" class="space-y-6">
          <div class="dashboard-bg-card dashboard-shadow overflow-hidden">
            <div class="px-6 py-4 dashboard-border border-b">
              <h3 class="text-lg font-medium dashboard-text-primary">Services</h3>
            </div>
            <div class="px-6 py-4">
              <div class="space-y-4">
                <div v-for="(service, index) in form.services" :key="index" class="flex items-center space-x-4 p-4 dashboard-border">
                  <div class="flex-1">
                    <select
                      v-model="service.service_id"
                      class="block w-full dashboard-input dashboard-border focus:border-red-500 focus:ring-red-500"
                    >
                      <option value="">Select a service</option>
                      <option v-for="availableService in availableServices" :key="availableService.id" :value="availableService.id">
                        {{ availableService.name }}
                      </option>
                    </select>
                  </div>
                  <button
                    type="button"
                    @click="removeService(index)"
                    class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-200 dashboard-transition"
                  >
                    <Icon icon="heroicons:trash" class="w-5 h-5" />
                  </button>
                </div>
                <button
                  type="button"
                  @click="addService"
                  class="flex items-center px-4 py-2 border border-dashed dashboard-border text-sm font-medium dashboard-text-secondary hover:dashboard-bg-hover dashboard-transition"
                >
                  <Icon icon="heroicons:plus" class="w-4 h-4 mr-2" />
                  Add Service
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Navigation -->
        <div class="mt-8 flex justify-between">
          <button type="button" class="px-4 py-2 dashboard-bg-card dashboard-text-secondary dashboard-border hover:dashboard-bg-hover dashboard-transition"
            @click="prevStep" v-if="currentStep !== 0">
            Back
          </button>
          <div v-else></div>
          <button type="button" @click="nextStep" v-if="currentStep < steps.length - 1"
            class="px-4 py-2 bg-red-600 text-white hover:bg-red-700 dashboard-transition">
            Next
          </button>
          <button v-else type="submit" :disabled="loading"
            class="px-4 py-2 bg-red-600 text-white hover:bg-red-700 disabled:opacity-50 flex items-center dashboard-transition">
            <Icon v-if="loading" icon="heroicons:arrow-path" class="w-4 h-4 mr-2 animate-spin" />
            {{ loading ? 'Creating...' : 'Create Vendor' }}
          </button>
        </div>
      </FormKit>
  </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue';
import { useFormKitNodeById } from '@formkit/vue';
import { ENDPOINTS } from '@/utils/api';

definePageMeta({
  layout: "dashboard",
});

useHead({
  title: "Add New Vendor | EventaHub Malawi",
  meta: [
    { name: "description", content: "Create a new vendor profile with business details, services, and contact information" }
  ]
});

interface Step {
  label: string;
}

const router = useRouter();
const loading = ref<boolean>(false);
const logoPreview = ref<string | null>(null);
const logoFile = ref<File | null>(null);
const logoInput = ref<HTMLInputElement | null>(null);
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

// Stepper state
const currentStep = ref<number>(0);
const formError = ref<string>('');
const stepValidation = reactive<{ [key: number]: boolean }>({
  0: false,
  1: false,
  2: false
});
const stepErrors = reactive<{ [key: number]: boolean }>({
  0: false,
  1: false,
  2: false
});

const steps: Step[] = [
  { label: 'Basic Info' },
  { label: 'Logo & Social' },
  { label: 'Services' }
];

const form = ref({
  name: '',
  business_email: '',
  phone: '',
  website: '',
  location: '',
  bio: '',
  facebook: '',
  instagram: '',
  twitter: '',
  services: [] as Array<{ service_id: string }>
});

const availableServices = ref<any[]>([]);

const fetchServices = async () => {
  try {
    const response = await httpClient.get<any[]>(ENDPOINTS.SERVICES.BASE);
    availableServices.value = response || [];
  } catch (error) {
    console.error('Error fetching services:', error);
  }
};

const handleLogoUpload = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];

  if (file) {
    if (file.size > 2 * 1024 * 1024) {
      $toast.warning('File size must be less than 2MB');
      return;
    }

    logoFile.value = file;
    const reader = new FileReader();
    reader.onload = (e) => {
      logoPreview.value = e.target?.result as string;
    };
    reader.readAsDataURL(file);
  }
};

const addService = () => {
  form.value.services.push({ service_id: '' });
};

const removeService = (index: number) => {
  form.value.services.splice(index, 1);
};

// Stepper functions
const validateCurrentStep = async (): Promise<boolean> => {
  const formNode = useFormKitNodeById('vendorForm');
  if (!formNode?.value) return false;

  try {
    await formNode.value.settled;
    return formNode.value.context?.state.valid || false;
  } catch (error) {
    return false;
  }
};

const nextStep = async (): Promise<void> => {
  formError.value = '';
  const isValid = await validateCurrentStep();
  if (isValid) {
    stepValidation[currentStep.value] = true;
    stepErrors[currentStep.value] = false;
    if (currentStep.value < steps.length - 1) {
      currentStep.value++;
    }
  } else {
    stepErrors[currentStep.value] = true;
    stepValidation[currentStep.value] = false;
  }
};

const prevStep = (): void => {
  if (currentStep.value > 0) {
    currentStep.value--;
  }
};

const handleSubmit = async (): Promise<void> => {
  formError.value = '';
  const isValid = await validateCurrentStep();
  if (isValid) {
    stepValidation[currentStep.value] = true;
    stepErrors[currentStep.value] = false;
    try {
      loading.value = true;
      const formData = buildVendorFormData();
      await httpClient.post(`${ENDPOINTS.VENDORS.CREATE}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      $toast.success('Vendor created successfully');
      router.push('/dashboard/vendors');
    } catch (error: any) {
      console.error('Error creating vendor:', error);
      $toast.error(error.response?.data?.message || 'Failed to create vendor');
    } finally {
      loading.value = false;
    }
  } else {
    stepErrors[currentStep.value] = true;
    stepValidation[currentStep.value] = false;
  }
};

const buildVendorFormData = (): FormData => {
  const formData = new FormData();

  // Add basic form data
  Object.entries(form.value).forEach(([key, value]) => {
    if (key === 'services') {
      const services = value as Array<{ service_id: string }>;
      formData.append('services', JSON.stringify(services.filter(s => s.service_id)));
    } else if (value) {
      formData.append(key, value as string);
    }
  });

  // Add logo if uploaded
  if (logoFile.value) {
    formData.append('logo', logoFile.value);
  }

  return formData;
};

onMounted(() => {
  fetchServices();
  addService();
});
</script>
