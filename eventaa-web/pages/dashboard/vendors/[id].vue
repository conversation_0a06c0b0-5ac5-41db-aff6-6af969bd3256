<template>
  <div class="p-6 bg-gray-100 dark:bg-gray-900 min-h-screen">
    <div class="mb-6 flex justify-between items-center">
      <div class="flex items-center space-x-4">
        <button @click="$router.back()" class="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200">
          <Icon icon="heroicons:arrow-left" class="w-6 h-6" />
        </button>
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Vendor Details</h1>
          <p class="text-gray-500 dark:text-gray-400">View and manage vendor information</p>
        </div>
      </div>
      <div class="flex space-x-2">
        <button @click="editVendor" class="px-4 py-2 bg-red-600 text-white shadow-sm hover:bg-red-700 flex items-center">
          <Icon icon="heroicons:pencil-square" class="w-5 h-5 mr-1" />
          Edit
        </button>
        <button v-if="vendor?.status === 'pending'" @click="approveVendor" class="px-4 py-2 bg-green-600 text-white shadow-sm hover:bg-green-700 flex items-center">
          <Icon icon="heroicons:check-circle" class="w-5 h-5 mr-1" />
          Approve
        </button>
        <button v-if="vendor?.status === 'pending'" @click="rejectVendor" class="px-4 py-2 bg-red-600 text-white shadow-sm hover:bg-red-700 flex items-center">
          <Icon icon="heroicons:x-circle" class="w-5 h-5 mr-1" />
          Reject
        </button>
        <button v-if="vendor?.status === 'approved'" @click="showSuspendDialog = true" class="px-4 py-2 bg-orange-600 text-white shadow-sm hover:bg-orange-700 flex items-center">
          <Icon icon="heroicons:pause-circle" class="w-5 h-5 mr-1" />
          Suspend
        </button>
        <button v-if="vendor?.status === 'suspended'" @click="reactivateVendor" class="px-4 py-2 bg-green-600 text-white shadow-sm hover:bg-green-700 flex items-center">
          <Icon icon="heroicons:play-circle" class="w-5 h-5 mr-1" />
          Reactivate
        </button>
      </div>
    </div>

    <div v-if="loading" class="flex justify-center items-center py-20">
      <CoreLoader />
    </div>

    <div v-else-if="!vendor" class="flex flex-col justify-center items-center py-20">
      <Icon icon="heroicons:exclamation-triangle" class="w-16 h-16 text-gray-400 dark:text-gray-600 mb-4" />
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Vendor not found</h3>
      <p class="text-gray-500 dark:text-gray-400 text-center max-w-md">
        The vendor you're looking for doesn't exist or has been removed.
      </p>
    </div>

    <div v-else class="space-y-6">
      <!-- Vendor Info Card -->
      <div class="bg-white dark:bg-gray-800 shadow overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">Vendor Information</h3>
        </div>
        <div class="px-6 py-4">
          <div class="flex items-start space-x-6">
            <div class="flex-shrink-0">
              <img class="h-24 w-24 rounded-full" :src="`${runtimeConfig.public.baseUrl}storage/${vendor.logo}`" alt="" />
            </div>
            <div class="flex-1 min-w-0">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 class="text-xl font-bold text-gray-900 dark:text-white mb-2">{{ vendor.name }}</h4>
                  <div class="space-y-2">
                    <div class="flex items-center">
                      <Icon icon="heroicons:envelope" class="w-4 h-4 text-gray-400 mr-2" />
                      <span class="text-sm text-gray-600 dark:text-gray-400">{{ vendor.business_email }}</span>
                    </div>
                    <div class="flex items-center">
                      <Icon icon="heroicons:phone" class="w-4 h-4 text-gray-400 mr-2" />
                      <span class="text-sm text-gray-600 dark:text-gray-400">{{ vendor.phone }}</span>
                    </div>
                    <div class="flex items-center">
                      <Icon icon="heroicons:map-pin" class="w-4 h-4 text-gray-400 mr-2" />
                      <span class="text-sm text-gray-600 dark:text-gray-400">{{ vendor.location }}</span>
                    </div>
                    <div v-if="vendor.website" class="flex items-center">
                      <Icon icon="heroicons:globe-alt" class="w-4 h-4 text-gray-400 mr-2" />
                      <a :href="vendor.website" target="_blank" class="text-sm text-blue-600 dark:text-blue-400 hover:underline">{{ vendor.website }}</a>
                    </div>
                  </div>
                </div>
                <div>
                  <div class="space-y-4">
                    <div>
                      <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full"
                        :class="{
                          'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200': vendor.status === 'pending',
                          'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200': vendor.status === 'approved',
                          'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200': vendor.status === 'rejected',
                          'bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200': vendor.status === 'suspended'
                        }">
                        {{ formatStatus(vendor.status) }}
                      </span>
                    </div>
                    <div class="flex items-center">
                      <Icon icon="heroicons:star" class="w-5 h-5 text-yellow-400 mr-1" />
                      <span class="text-sm font-medium text-gray-900 dark:text-white">{{ vendor.ratings_avg_rating?.toFixed(1) || 'N/A' }}</span>
                      <span class="text-sm text-gray-500 dark:text-gray-400 ml-1">({{ vendor.ratings_count || 0 }} reviews)</span>
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                      <span class="font-medium">Joined:</span> {{ formatDate(vendor.created_at) }}
                    </div>
                    <div v-if="vendor.created_at" class="text-sm text-gray-600 dark:text-gray-400">
                      <span class="font-medium">Approved:</span> {{ formatDate(vendor.created_at) }}
                    </div>
                  </div>
                </div>
              </div>
              <div v-if="vendor.bio" class="mt-4">
                <h5 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Description</h5>
                <p class="text-sm text-gray-600 dark:text-gray-400">{{ vendor.bio }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 shadow overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">Services</h3>
        </div>
        <div class="px-6 py-4">
          <div v-if="vendor.services && vendor.services.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div v-for="service in vendor.services" :key="service.id" class="border border-gray-200 dark:border-gray-700 p-4">
              <h4 class="font-medium text-gray-900 dark:text-white">{{ service.service.name }}</h4>
              <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ service.service.description }}</p>
            </div>
          </div>
          <div v-else class="text-center py-8">
            <Icon icon="heroicons:briefcase" class="w-12 h-12 text-gray-400 dark:text-gray-600 mx-auto mb-4" />
            <p class="text-gray-500 dark:text-gray-400">No services available</p>
          </div>
        </div>
      </div>

      <!-- Portfolio/Media Card -->
      <div class="bg-white dark:bg-gray-800 shadow overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">Portfolio</h3>
        </div>
        <div class="px-6 py-4">
          <div v-if="vendor.media && vendor.media.length > 0" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            <div v-for="media in vendor.media" :key="media.id" class="aspect-square">
              <img :src="`${runtimeConfig.public.baseUrl}storage/${media.path}`" :alt="media.title" class="w-full h-full object-cover" />
            </div>
          </div>
          <div v-else class="text-center py-8">
            <Icon icon="heroicons:photo" class="w-12 h-12 text-gray-400 dark:text-gray-600 mx-auto mb-4" />
            <p class="text-gray-500 dark:text-gray-400">No portfolio images available</p>
          </div>
        </div>
      </div>

      <!-- Pricing Card -->
      <div class="bg-white dark:bg-gray-800 shadow overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">Pricing</h3>
        </div>
        <div class="px-6 py-4">
          <div v-if="vendor.prices && vendor.prices.length > 0" class="space-y-4">
            <div v-for="price in vendor.prices" :key="price.id" class="flex justify-between items-center border-b border-gray-200 dark:border-gray-700 pb-2">
              <div>
                <span class="font-medium text-gray-900 dark:text-white">{{ price.description || 'Base Price' }}</span>
              </div>
              <div class="text-right">
                <span class="text-lg font-bold text-gray-900 dark:text-white">{{ price.currency.name }} {{ price.price }}</span>
              </div>
            </div>
          </div>
          <div v-else class="text-center py-8">
            <Icon icon="heroicons:currency-dollar" class="w-12 h-12 text-gray-400 dark:text-gray-600 mx-auto mb-4" />
            <p class="text-gray-500 dark:text-gray-400">No pricing information available</p>
          </div>
        </div>
      </div>

      <!-- Recent Reviews Card -->
      <div class="bg-white dark:bg-gray-800 shadow overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">Recent Reviews</h3>
          <button @click="viewAllReviews" class="text-sm text-blue-600 dark:text-blue-400 hover:underline">View All</button>
        </div>
        <div class="px-6 py-4">
          <div v-if="vendor.ratings && vendor.ratings.length > 0" class="space-y-4">
            <div v-for="rating in vendor.ratings.slice(0, 3)" :key="rating.id" class="border-b border-gray-200 dark:border-gray-700 pb-4 last:border-b-0">
              <div class="flex items-start space-x-3">
                <img class="h-8 w-8 rounded-full" :src="`${runtimeConfig.public.baseUrl}storage/avatars/${rating.user.avatar}`" alt="" />
                <div class="flex-1">
                  <div class="flex items-center space-x-2">
                    <span class="font-medium text-gray-900 dark:text-white">{{ rating.user.name }}</span>
                    <div class="flex items-center">
                      <Icon v-for="i in 5" :key="i" icon="heroicons:star"
                        :class="i <= rating.rating ? 'text-yellow-400' : 'text-gray-300 dark:text-gray-600'"
                        class="w-4 h-4" />
                    </div>
                  </div>
                  <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ rating.comment }}</p>
                  <span class="text-xs text-gray-500 dark:text-gray-500">{{ formatDate(rating.created_at) }}</span>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="text-center py-8">
            <Icon icon="heroicons:chat-bubble-left-ellipsis" class="w-12 h-12 text-gray-400 dark:text-gray-600 mx-auto mb-4" />
            <p class="text-gray-500 dark:text-gray-400">No reviews yet</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Suspend Confirmation Dialog -->
    <TransitionRoot appear :show="showSuspendDialog" as="template">
      <Dialog as="div" @close="showSuspendDialog = false" class="relative z-50">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25" />
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex min-h-full items-center justify-center p-4 text-center">
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel class="w-full max-w-md transform overflow-hidden bg-white dark:bg-gray-800 p-6 text-left align-middle shadow-xl transition-all">
                <div class="flex items-center mb-4">
                  <Icon icon="heroicons:exclamation-triangle" class="w-12 h-12 text-orange-500 mr-4" />
                  <div>
                    <DialogTitle as="h3" class="text-lg font-medium text-gray-900 dark:text-white">
                      Suspend Vendor
                    </DialogTitle>
                    <p class="text-sm text-gray-500 dark:text-gray-400">This action will suspend the vendor's account</p>
                  </div>
                </div>

                <div class="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4 mb-4">
                  <div class="flex">
                    <Icon icon="heroicons:exclamation-triangle" class="w-5 h-5 text-orange-400 mr-2 mt-0.5" />
                    <div class="text-sm">
                      <p class="text-orange-800 dark:text-orange-200 font-medium">Warning</p>
                      <p class="text-orange-700 dark:text-orange-300 mt-1">
                        Suspending <strong>{{ vendor?.name }}</strong> will:
                      </p>
                      <ul class="list-disc list-inside text-orange-700 dark:text-orange-300 mt-2 space-y-1">
                        <li>Hide their profile from public view</li>
                        <li>Prevent them from receiving new bookings</li>
                        <li>Disable their vendor dashboard access</li>
                        <li>Require manual reactivation by an admin</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div class="flex justify-end space-x-3">
                  <button
                    @click="showSuspendDialog = false"
                    class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600"
                  >
                    Cancel
                  </button>
                  <button
                    @click="confirmSuspendVendor"
                    :disabled="suspending"
                    class="px-4 py-2 text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 disabled:opacity-50 flex items-center"
                  >
                    <Icon v-if="suspending" icon="heroicons:arrow-path" class="w-4 h-4 mr-2 animate-spin" />
                    {{ suspending ? 'Suspending...' : 'Suspend Vendor' }}
                  </button>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue';
import { ENDPOINTS } from '@/utils/api';
import type { ApiVendor } from '~/types/vendor';

definePageMeta({
  layout: "dashboard",
});

useHead({
  title: "Vendor | EventaHub Malawi",
  meta: [
    { name: "description", content: "Manage a vendor's profile, services, and bookings" }
  ]
});

const route = useRoute();
const router = useRouter();
const runtimeConfig = useRuntimeConfig();
const loading = ref(true);
const vendor = ref<ApiVendor | null>(null);
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const showSuspendDialog = ref(false);
const suspending = ref(false);

const vendorSlug = computed(() => route.params.id as string);

const fetchVendor = async () => {
  try {
    loading.value = true;
    const response = await httpClient.get<ApiVendor>(`${ENDPOINTS.VENDORS.READ}/${vendorSlug.value}`);
    vendor.value = response;
  } catch (error) {
    console.error('Error fetching vendor:', error);
    $toast.error('Failed to load vendor details');
    vendor.value = null;
  } finally {
    loading.value = false;
  }
};

const formatStatus = (status: string | undefined): string => {
  if (!status) return 'Unknown';
  switch (status) {
    case 'pending': return 'Pending Approval';
    case 'approved': return 'Approved';
    case 'rejected': return 'Rejected';
    case 'suspended': return 'Suspended';
    default: return status;
  }
};

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

const editVendor = (): void => {
  router.push(`/dashboard/vendors/${vendor.value?.id}/edit`);
};

const approveVendor = async (): Promise<void> => {
  try {
    await httpClient.post(`${ENDPOINTS.VENDORS.APPROVE}/${vendor.value?.id}`);
    $toast.success(`${vendor.value?.name} has been approved`);
    await fetchVendor();
  } catch (error) {
    console.error('Error approving vendor:', error);
    $toast.error('Failed to approve vendor');
  }
};

const rejectVendor = async (): Promise<void> => {
  try {
    await httpClient.post(`${ENDPOINTS.VENDORS.REJECT}/${vendor.value?.id}`);
    $toast.success(`${vendor.value?.name} has been rejected`);
    await fetchVendor();
  } catch (error) {
    console.error('Error rejecting vendor:', error);
    $toast.error('Failed to reject vendor');
  }
};

const confirmSuspendVendor = async (): Promise<void> => {
  try {
    suspending.value = true;
    await httpClient.post(`${ENDPOINTS.VENDORS.SUSPEND}/${vendor.value?.id}`);
    $toast.success(`${vendor.value?.name} has been suspended`);
    showSuspendDialog.value = false;
    await fetchVendor();
  } catch (error) {
    console.error('Error suspending vendor:', error);
    $toast.error('Failed to suspend vendor');
  } finally {
    suspending.value = false;
  }
};

const reactivateVendor = async (): Promise<void> => {
  try {
    await httpClient.post(`${ENDPOINTS.VENDORS.REACTIVATE}/${vendor.value?.id}`);
    $toast.success(`${vendor.value?.name} has been reactivated`);
    await fetchVendor();
  } catch (error) {
    console.error('Error reactivating vendor:', error);
    $toast.error('Failed to reactivate vendor');
  }
};

const viewAllReviews = (): void => {
  router.push(`/dashboard/vendors/${vendor.value?.id}/reviews`);
};

onMounted(() => {
  fetchVendor();
});
</script>
