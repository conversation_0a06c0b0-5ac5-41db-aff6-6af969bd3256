<template>
  <div class="dashboard-bg-main min-h-screen">
    <div v-if="isLoading" class="flex justify-center items-center h-64">
      <div class="flex flex-col items-center">
        <CoreLoader/>
        <p class="mt-4 dashboard-text-muted">Loading dashboard...</p>
      </div>
    </div>

    <div v-else class="p-6">
      <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-8">
        <div class="mb-4 sm:mb-0">
          <h1 class="text-2xl font-bold dashboard-text-primary mb-2">Welcome Back, {{ userName }}</h1>
          <p class="dashboard-text-muted text-lg">{{ welcomeMessage }}</p>
        </div>
        <div class="flex space-x-3">
          <button @click="refreshDashboard"
            class="dashboard-text-secondary p-2.5 rounded-full bg-white border border-zinc-200 dark:bg-zinc-900 dark:border-zinc-700 flex items-center hover:dashboard-bg-hover dashboard-transition">
            <Icon icon="heroicons:arrow-path" class="w-5 h-5" :class="{ 'animate-spin': isRefreshing }" />
          </button>
        </div>
      </div>

      <!-- Statistics Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <template v-if="isLoading">
          <DashboardSkeletonStatisticsCard v-for="i in 8" :key="i" />
        </template>
        <template v-else>
          <DashboardCardsStatistics
            title="Net Sales"
            :value="formatCurrency(computedStats.totalRevenue)"
            :subValue="`${formatCurrency(computedStats.grossSales)} gross sales`"
            icon="heroicons:currency-dollar"
            :growth="computedStats.revenueGrowth"
          />
          <DashboardCardsStatistics
            title="Tickets Sold"
            :value="`${computedStats.ticketsSold}/${computedStats.ticketsTotal}`"
            :subValue="`${computedStats.paidTickets} paid + ${computedStats.freeTickets} free`"
            icon="heroicons:ticket"
            :growth="computedStats.ticketsGrowth"
          />
          <DashboardCardsStatistics
            title="Page Views"
            :value="`${computedStats.pageViews}`"
            :subValue="`${computedStats.socialViews} from social media`"
            icon="heroicons:chart-bar"
            :growth="computedStats.viewsGrowth"
          />
          <DashboardCardsStatistics
            title="Active Users"
            :value="`${computedStats.activeUsers}`"
            :subValue="`${computedStats.newUsers} new this week`"
            icon="heroicons:users"
            :growth="computedStats.usersGrowth"
          />
          <DashboardCardsStatistics
            title="Newsletter Subscribers"
            :value="`${computedStats.newsletterSubscribers}`"
            :subValue="`${computedStats.emailsSent} emails sent this month`"
            icon="heroicons:envelope"
            :growth="computedStats.newsletterGrowth"
          />
          <DashboardCardsStatistics
            title="Total Events"
            :value="`${computedStats.totalEvents}`"
            :subValue="`${computedStats.upcomingEvents} upcoming`"
            icon="heroicons:calendar-days"
            :growth="computedStats.eventsGrowth"
          />
          <DashboardCardsStatistics
            title="Conversion Rate"
            :value="`${computedStats.conversionRate}%`"
            :subValue="`Views to ticket purchases`"
            icon="heroicons:chart-pie"
            :growth="computedStats.conversionRateGrowth"
          />
          <DashboardCardsStatistics
            title="Emails Sent"
            :value="`${computedStats.emailsSent}`"
            :subValue="`Newsletter & notifications`"
            icon="heroicons:paper-airplane"
            :growth="computedStats.emailsSentGrowth"
          />
        </template>
      </div>

      <div v-if="hasAdminRole" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <template v-if="isLoading">
          <DashboardSkeletonStatisticsCard v-for="i in 8" :key="i" />
        </template>
        <template v-else>
          <DashboardCardsStatistics
            title="Vendors"
            :value="`${computedStats.adminStats.totalVendors}`"
            :subValue="`${computedStats.adminStats.pendingVendors} pending approval`"
            icon="game-icons:shop"
            :growth="computedStats.adminStats.vendorsGrowth"
          />
          <DashboardCardsStatistics
            title="Venues"
            :value="`${computedStats.adminStats.totalVenues}`"
            :subValue="`${computedStats.adminStats.activeVenues} active`"
            icon="gis:road-map"
            :growth="computedStats.adminStats.venuesGrowth"
          />
          <DashboardCardsStatistics
            title="Total Events"
            :value="`${computedStats.adminStats.totalEvents}`"
            :subValue="`${computedStats.adminStats.upcomingEvents} upcoming`"
            icon="heroicons:calendar"
            :growth="computedStats.adminStats.eventsGrowth"
          />
          <DashboardCardsStatistics
            title="Platform Revenue"
            :value="formatCurrency(computedStats.adminStats.platformRevenue)"
            :subValue="`${computedStats.adminStats.subscriptions} subscriptions`"
            icon="game-icons:coins"
            :growth="computedStats.adminStats.revenueGrowth"
          />
          <DashboardCardsStatistics
            title="Newsletter Subscribers"
            :value="`${computedStats.adminStats.totalNewsletterSubscribers}`"
            :subValue="`${computedStats.adminStats.activeNewsletterSubscribers} active`"
            icon="heroicons:envelope"
            :growth="computedStats.adminStats.newsletterGrowth"
          />
          <DashboardCardsStatistics
            title="Emails Sent"
            :value="`${computedStats.adminStats.emailsSent}`"
            :subValue="`Platform notifications`"
            icon="heroicons:paper-airplane"
            :growth="computedStats.adminStats.newsletterGrowth"
          />
          <DashboardCardsStatistics
            title="Email Open Rate"
            :value="`${computedStats.adminStats.emailOpenRate}%`"
            :subValue="`Newsletter engagement`"
            icon="heroicons:eye"
            :growth="0"
          />
          <DashboardCardsStatistics
            title="Email Click Rate"
            :value="`${computedStats.adminStats.emailClickRate}%`"
            :subValue="`Newsletter clicks`"
            icon="heroicons:cursor-arrow-rays"
            :growth="0"
          />
        </template>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div class="dashboard-bg-card p-6 dashboard-shadow">
          <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6">
            <h2 class="text-lg font-semibold dashboard-text-primary mb-2 sm:mb-0">
              {{ hasAdminRole ? 'Platform Revenue' : 'Event Revenue' }}
            </h2>
            <select v-model="chartPeriod"
                    class="dashboard-bg-input dashboard-border dashboard-text-secondary text-sm px-3 py-2 focus:dashboard-focus-primary">
              <option value="7days">Last 7 Days</option>
              <option value="30days">Last 30 Days</option>
              <option value="90days">Last 90 Days</option>
              <option value="year">Last Year</option>
            </select>
          </div>
          <div class="h-72">
            <template v-if="isLoading">
              <DashboardSkeletonChart />
            </template>
            <template v-else>
              <DashboardChartsLine
                :data="revenueChartData"
                :options="revenueChartOptions"
              />
            </template>
          </div>
        </div>

        <div class="dashboard-bg-card p-6 dashboard-shadow">
          <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6">
            <h2 class="text-lg font-semibold dashboard-text-primary mb-2 sm:mb-0">
              {{ hasAdminRole ? 'User Growth' : 'Ticket Sales' }}
            </h2>
            <select v-model="chartPeriod"
                    class="dashboard-bg-input dashboard-border dashboard-text-secondary text-sm px-3 py-2 focus:dashboard-focus-primary">
              <option value="7days">Last 7 Days</option>
              <option value="30days">Last 30 Days</option>
              <option value="90days">Last 90 Days</option>
              <option value="year">Last Year</option>
            </select>
          </div>
          <div class="h-72">
            <template v-if="isLoading">
              <DashboardSkeletonChart />
            </template>
            <template v-else>
              <DashboardChartsBar
                :data="secondaryChartData"
                :options="defaultChartOptions"
              />
            </template>
          </div>
        </div>
      </div>

      <!-- Recent Activity Table -->
      <div class="dashboard-bg-card p-6 dashboard-shadow">
        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6">
          <h2 class="text-lg font-semibold dashboard-text-primary mb-2 sm:mb-0">
            {{ hasAdminRole ? 'Recent Platform Activity' : 'Recent Event Activity' }}
          </h2>
          <button class="dashboard-text-brand text-sm font-medium hover:dashboard-text-primary dashboard-transition">
            View All
          </button>
        </div>

        <div class="overflow-x-auto">
          <table class="w-full text-left">
            <thead>
              <tr class="dashboard-border-light border-b">
                <th class="p-3 text-xs font-semibold dashboard-text-muted uppercase tracking-wider">
                  {{ hasAdminRole ? 'User' : 'Attendee' }}
                </th>
                <th class="p-3 text-xs font-semibold dashboard-text-muted uppercase tracking-wider">
                  {{ hasAdminRole ? 'Action' : 'Ticket Type' }}
                </th>
                <th class="p-3 text-xs font-semibold dashboard-text-muted uppercase tracking-wider">
                  {{ hasAdminRole ? 'Resource' : 'Price' }}
                </th>
                <th class="p-3 text-xs font-semibold dashboard-text-muted uppercase tracking-wider">
                  {{ hasAdminRole ? 'Date' : 'Purchase Date' }}
                </th>
                <th class="p-3 text-xs font-semibold dashboard-text-muted uppercase tracking-wider">
                  Status
                </th>
              </tr>
            </thead>
            <tbody class="divide-y dashboard-border-light">
              <template v-if="isActivitiesLoading">
                <tr v-for="i in 4" :key="i">
                  <td v-for="j in 5" :key="j" class="p-3">
                    <div class="h-4 dashboard-bg-hover rounded animate-pulse"></div>
                  </td>
                </tr>
              </template>
              <template v-else-if="activities.length === 0">
                <tr>
                  <td colspan="5" class="p-8 text-center">
                    <div class="flex flex-col items-center">
                      <Icon icon="heroicons:document-text" class="w-12 h-12 dashboard-text-muted mb-3" />
                      <p class="dashboard-text-muted text-sm">No recent activity found</p>
                    </div>
                  </td>
                </tr>
              </template>
              <template v-else>
                <tr v-for="(activity, index) in activities" :key="index"
                    class="hover:dashboard-bg-hover dashboard-transition">
                  <td class="p-3">
                    <div class="text-sm font-medium dashboard-text-primary">{{ activity.user }}</div>
                  </td>
                  <td class="p-3">
                    <div class="text-sm dashboard-text-secondary">{{ activity.action }}</div>
                  </td>
                  <td class="p-3">
                    <div class="text-sm dashboard-text-secondary">
                      {{ hasAdminRole ? activity.resource : formatCurrency(activity.price) }}
                    </div>
                  </td>
                  <td class="p-3">
                    <div class="text-sm dashboard-text-muted">{{ formatDate(activity.date) }}</div>
                  </td>
                  <td class="p-3">
                    <span class="inline-flex items-center px-2.5 py-0.5 text-xs font-medium rounded-full"
                          :class="getStatusBadgeClass(activity.status)">
                      {{ activity.status }}
                    </span>
                  </td>
                </tr>
              </template>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { useAuthStore } from '@/store/auth';
import { ENDPOINTS } from '@/utils/api';
import { useCurrency } from '@/composables/useCurrency';

definePageMeta({
  layout: "dashboard",
  middleware: ['auth']
});

useHead({
  title: "Dashboard | EventaHub Malawi",
  meta: [
    { name: "description", content: "Manage your events, track sales, and monitor engagement in your EventaHub dashboard" }
  ]
});

// Stores and utilities
const authStore = useAuthStore();
const httpClient = useHttpClient();
const { $toast, $echo }: any = useNuxtApp();
const { formatCurrency } = useCurrency();

// State
const isLoading = ref(true);
const isActivitiesLoading = ref(true);
const isRefreshing = ref(false);
const activities = ref<any[]>([]);
const chartPeriod = ref('30days');
const revenueChartData = ref<any>({ labels: [], values: [] });
const secondaryChartData = ref<any>({ labels: [], values: [] });

// User information
const userName = computed(() => authStore.user?.name || 'User');

// Role checks
const hasAdminRole = computed(() => authStore.user?.roles?.includes('admin') || false);
const hasHostRole = computed(() => authStore.user?.roles?.includes('host') || false);

// Initialize stats with default values
const stats = ref<any>({
  totalRevenue: 0,
  grossSales: 0,
  revenueGrowth: 0,
  ticketsSold: 0,
  ticketsTotal: 0,
  paidTickets: 0,
  freeTickets: 0,
  ticketsGrowth: 0,
  pageViews: 0,
  socialViews: 0,
  viewsGrowth: 0,
  activeUsers: 0,
  newUsers: 0,
  usersGrowth: 0,
  // Newsletter stats
  newsletterSubscribers: 0,
  newsletterGrowth: 0,
  emailsSent: 0,
  emailsSentGrowth: 0,
  // Additional stats
  totalEvents: 0,
  upcomingEvents: 0,
  eventsGrowth: 0,
  conversionRate: 0,
  conversionRateGrowth: 0,
  adminStats: hasAdminRole.value ? {
    totalVendors: 0,
    pendingVendors: 0,
    vendorsGrowth: 0,
    totalVenues: 0,
    activeVenues: 0,
    venuesGrowth: 0,
    totalEvents: 0,
    upcomingEvents: 0,
    eventsGrowth: 0,
    platformRevenue: 0,
    subscriptions: 0,
    revenueGrowth: 0,
    // Admin newsletter stats
    totalNewsletterSubscribers: 0,
    activeNewsletterSubscribers: 0,
    newsletterGrowth: 0,
    emailsSent: 0,
    emailOpenRate: 0,
    emailClickRate: 0
  } : undefined
});

// Welcome message based on role
const welcomeMessage = computed(() => {
  if (hasAdminRole.value) {
    return 'Manage your platform and monitor overall performance';
  } else if (hasHostRole.value) {
    return 'Manage your events and track your performance';
  }
  return 'View your dashboard and manage your account';
});

// Computed properties for derived stats
const computedStats = computed(() => {
  const baseStats = stats.value;

  // Calculate conversion rate if possible
  const conversionRate = baseStats.pageViews > 0
    ? ((baseStats.ticketsSold / baseStats.pageViews) * 100).toFixed(1)
    : 0;

  return {
    ...baseStats,
    conversionRate,
    conversionRateGrowth: baseStats.conversionRateGrowth || 0
  };
});

// Chart options for revenue chart
const revenueChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  scales: {
    y: {
      type: 'linear' as const,
      beginAtZero: true,
      ticks: {
        callback: function(this: any, tickValue: string | number) {
          return formatCurrency(Number(tickValue));
        }
      }
    }
  },
  plugins: {
    legend: {
      display: false
    }
  }
};

// Chart options for user/ticket charts (no currency formatting)
const defaultChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  scales: {
    y: {
      type: 'linear' as const,
      beginAtZero: true,
      ticks: {
        callback: function(this: any, tickValue: string | number) {
          return Number(tickValue).toLocaleString();
        }
      }
    }
  },
  plugins: {
    legend: {
      display: false
    }
  }
};

const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  }).format(date);
};

const getStatusBadgeClass = (status: string): string => {
  switch (status.toLowerCase()) {
    case 'completed':
    case 'approved':
    case 'active':
      return 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400';
    case 'pending':
    case 'in progress':
      return 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-400';
    case 'cancelled':
    case 'rejected':
    case 'failed':
      return 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-400';
    default:
      return 'bg-zinc-100 dark:bg-zinc-800 text-zinc-800 dark:text-zinc-400';
  }
};

// Refresh dashboard data
const refreshDashboard = async () => {
  if (isRefreshing.value) return;

  isRefreshing.value = true;
  try {
    await Promise.all([
      fetchDashboardData(),
      fetchActivities(),
      fetchChartData()
    ]);
    $toast.success('Dashboard refreshed successfully');
  } catch (error) {
    console.error('Error refreshing dashboard:', error);
    $toast.error('Failed to refresh dashboard');
  } finally {
    isRefreshing.value = false;
  }
};

const fetchDashboardData = async () => {
  try {
    isLoading.value = true;
    const endpoint = hasAdminRole.value ? ENDPOINTS.DASHBOARD.ADMIN.STATS : ENDPOINTS.DASHBOARD.HOST.STATS;

    // Fetch both dashboard and newsletter stats in parallel
    const [dashboardResponse, newsletterResponse] = await Promise.all([
      httpClient.get<any>(endpoint),
      httpClient.get<any>(ENDPOINTS.NEWSLETTER.STATS).catch(() => ({ data: null })) // Handle newsletter stats failure gracefully
    ]);

    // Use the dashboard response directly as it comes from the API
    if (dashboardResponse) {
      stats.value = {
        ...dashboardResponse,
        // Add default values for fields that might not exist in API response
        newsletterSubscribers: 0,
        newsletterGrowth: 0,
        emailsSent: 0,
        emailsSentGrowth: 0,
        totalEvents: 0,
        upcomingEvents: 0,
        eventsGrowth: 0,
        conversionRate: 0,
        conversionRateGrowth: 0
      };
    }

    // Merge newsletter stats if available
    if (newsletterResponse?.data) {
      if (hasAdminRole.value && stats.value.adminStats) {
        stats.value.adminStats = {
          ...stats.value.adminStats,
          totalNewsletterSubscribers: newsletterResponse.data.total_subscribers || 0,
          activeNewsletterSubscribers: newsletterResponse.data.active_subscribers || 0,
          newsletterGrowth: newsletterResponse.data.recent_subscriptions || 0,
          emailsSent: Object.values(newsletterResponse.data.preferences_breakdown || {}).reduce((a: any, b: any) => Number(a) + Number(b), 0),
          emailOpenRate: newsletterResponse.data.open_rate || 0,
          emailClickRate: newsletterResponse.data.click_rate || 0
        };
      } else {
        // For host role, add newsletter stats to main stats
        stats.value.newsletterSubscribers = newsletterResponse.data.total_subscribers || 0;
        stats.value.newsletterGrowth = newsletterResponse.data.recent_subscriptions || 0;
        stats.value.emailsSent = Object.values(newsletterResponse.data.preferences_breakdown || {}).reduce((a: any, b: any) => Number(a) + Number(b), 0);
      }
    }
  } catch (error: any) {
    console.error('Error fetching dashboard data:', error);
    $toast.error('Failed to load dashboard data. Please try again later.');
  } finally {
    isLoading.value = false;
  }
};const fetchActivities = async () => {
  try {
    isActivitiesLoading.value = true;
    const endpoint = hasAdminRole.value ? ENDPOINTS.DASHBOARD.ADMIN.ACTIVITIES : ENDPOINTS.DASHBOARD.HOST.ACTIVITIES;
    const response = await httpClient.get<any>(endpoint);
    activities.value = response;
  } catch (error) {
    console.error('Error fetching activities:', error);
    $toast.error('Failed to load activities');
  } finally {
    isActivitiesLoading.value = false;
  }
};

const fetchChartData = async () => {
  try {
    const [revenueResponse, secondaryResponse] = await Promise.all([
      httpClient.get<any>(
        hasAdminRole.value
          ? `${ENDPOINTS.DASHBOARD.ADMIN.REVENUE_CHART}?period=${chartPeriod.value}`
          : `${ENDPOINTS.DASHBOARD.HOST.REVENUE_CHART}?period=${chartPeriod.value}`
      ),
      httpClient.get<any>(
        hasAdminRole.value
          ? `${ENDPOINTS.DASHBOARD.ADMIN.USER_GROWTH_CHART}?period=${chartPeriod.value}`
          : `${ENDPOINTS.DASHBOARD.HOST.TICKET_SALES_CHART}?period=${chartPeriod.value}`
      )
    ]);

    revenueChartData.value = revenueResponse;
    secondaryChartData.value = secondaryResponse;
  } catch (error) {
    console.error('Error fetching chart data:', error);
    $toast.error('Failed to load chart data');
  }
};

watch(chartPeriod, () => {
  fetchChartData();
});

const setupWebSocketListeners = () => {
  if (!$echo) {
    console.warn('WebSocket connection not available');
    return;
  }

  try {
    const channel = hasAdminRole.value
      ? $echo.private('dashboard.admin')
      : $echo.private(`dashboard.host.${authStore.user?.id}`);

    channel.listen('.DashboardStatsUpdated', (event: any) => {
      if (event.stats) {
        stats.value = event.stats;
        $toast.info('Dashboard data updated');
      }
    });

    channel.error((error: any) => {
      console.error('WebSocket error:', error);
      $toast.error('Real-time updates unavailable');
    });
  } catch (error) {
    console.error('Error setting up WebSocket:', error);
  }
};

onMounted(async () => {
  await Promise.all([
    fetchDashboardData(),
    fetchActivities(),
    fetchChartData()
  ]);
  setupWebSocketListeners();
});

onUnmounted(() => {
  if ($echo) {
    const channelName = hasAdminRole.value
      ? 'dashboard.admin'
      : `dashboard.host.${authStore.user?.id}`;
    $echo.leave(channelName);
  }
});
</script>

<style scoped>
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}
</style>
