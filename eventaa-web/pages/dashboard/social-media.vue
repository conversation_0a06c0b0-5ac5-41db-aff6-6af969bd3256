<template>
  <div class="p-6 bg-gray-100">
    <div class="mb-6 flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold">Social Media</h1>
        <p class="text-gray-500">Manage social media posts and campaigns</p>
      </div>
      <div class="flex space-x-2">
        <button class="px-4 py-2 bg-white border border-gray-300 rounded shadow-sm hover:bg-gray-50 flex items-center">
          <Icon icon="heroicons:arrow-down-tray" class="w-5 h-5 text-gray-600 mr-1" />
          Export
        </button>
        <button class="px-4 py-2 bg-red-600 text-white rounded shadow-sm hover:bg-red-700 flex items-center">
          <Icon icon="heroicons:plus" class="w-5 h-5 mr-1" />
          Create Post
        </button>
      </div>
    </div>

    <!-- Social Media Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <div class="bg-white p-4 shadow rounded">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-blue-100 mr-4">
            <Icon icon="mdi:facebook" class="w-6 h-6 text-blue-600" />
          </div>
          <div>
            <p class="text-sm text-gray-500">Facebook</p>
            <p class="text-2xl font-bold">{{ stats.facebook.followers }}</p>
            <div class="flex items-center text-sm">
              <span :class="stats.facebook.growth >= 0 ? 'text-green-600' : 'text-red-600'">
                <Icon :icon="stats.facebook.growth >= 0 ? 'heroicons:arrow-up' : 'heroicons:arrow-down'" class="w-4 h-4 inline" />
                {{ Math.abs(stats.facebook.growth) }}%
              </span>
              <span class="text-gray-500 ml-1">vs last month</span>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white p-4 shadow rounded">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-pink-100 mr-4">
            <Icon icon="mdi:instagram" class="w-6 h-6 text-pink-600" />
          </div>
          <div>
            <p class="text-sm text-gray-500">Instagram</p>
            <p class="text-2xl font-bold">{{ stats.instagram.followers }}</p>
            <div class="flex items-center text-sm">
              <span :class="stats.instagram.growth >= 0 ? 'text-green-600' : 'text-red-600'">
                <Icon :icon="stats.instagram.growth >= 0 ? 'heroicons:arrow-up' : 'heroicons:arrow-down'" class="w-4 h-4 inline" />
                {{ Math.abs(stats.instagram.growth) }}%
              </span>
              <span class="text-gray-500 ml-1">vs last month</span>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white p-4 shadow rounded">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-sky-100 mr-4">
            <Icon icon="mdi:twitter" class="w-6 h-6 text-sky-600" />
          </div>
          <div>
            <p class="text-sm text-gray-500">Twitter</p>
            <p class="text-2xl font-bold">{{ stats.twitter.followers }}</p>
            <div class="flex items-center text-sm">
              <span :class="stats.twitter.growth >= 0 ? 'text-green-600' : 'text-red-600'">
                <Icon :icon="stats.twitter.growth >= 0 ? 'heroicons:arrow-up' : 'heroicons:arrow-down'" class="w-4 h-4 inline" />
                {{ Math.abs(stats.twitter.growth) }}%
              </span>
              <span class="text-gray-500 ml-1">vs last month</span>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white p-4 shadow rounded">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-red-100 mr-4">
            <Icon icon="mdi:youtube" class="w-6 h-6 text-red-600" />
          </div>
          <div>
            <p class="text-sm text-gray-500">YouTube</p>
            <p class="text-2xl font-bold">{{ stats.youtube.subscribers }}</p>
            <div class="flex items-center text-sm">
              <span :class="stats.youtube.growth >= 0 ? 'text-green-600' : 'text-red-600'">
                <Icon :icon="stats.youtube.growth >= 0 ? 'heroicons:arrow-up' : 'heroicons:arrow-down'" class="w-4 h-4 inline" />
                {{ Math.abs(stats.youtube.growth) }}%
              </span>
              <span class="text-gray-500 ml-1">vs last month</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white p-4 shadow mb-6">
      <div class="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
        <div class="flex-1">
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Icon icon="heroicons:magnifying-glass" class="h-5 w-5 text-gray-400" />
            </div>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search posts"
              class="block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
            />
          </div>
        </div>
        <div>
          <select
            v-model="platformFilter"
            class="border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
          >
            <option value="all">All Platforms</option>
            <option value="facebook">Facebook</option>
            <option value="instagram">Instagram</option>
            <option value="twitter">Twitter</option>
            <option value="youtube">YouTube</option>
          </select>
        </div>
        <div>
          <select
            v-model="statusFilter"
            class="border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
          >
            <option value="all">All Statuses</option>
            <option value="draft">Draft</option>
            <option value="scheduled">Scheduled</option>
            <option value="published">Published</option>
            <option value="failed">Failed</option>
          </select>
        </div>
        <div>
          <select
            v-model="dateFilter"
            class="border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
          >
            <option value="all">All Time</option>
            <option value="today">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="year">This Year</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Social Media Posts -->
    <div class="bg-white shadow">
      <div v-if="loading" class="flex justify-center items-center py-20">
        <CoreLoader />
      </div>
      <div v-else>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead>
              <tr>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Post</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Platform</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Performance</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="post in filteredPosts" :key="post.id" class="hover:bg-gray-50">
                <td class="px-6 py-4">
                  <div class="flex items-start">
                    <div class="flex-shrink-0 h-12 w-12 rounded overflow-hidden">
                      <img v-if="post.image" :src="post.image" class="h-12 w-12 object-cover" alt="" />
                      <div v-else class="h-12 w-12 bg-gray-100 flex items-center justify-center">
                        <Icon icon="heroicons:photo" class="h-6 w-6 text-gray-400" />
                      </div>
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900">{{ truncateText(post.content, 50) }}</div>
                      <div class="text-sm text-gray-500">{{ post.event ? `Event: ${post.event}` : '' }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <Icon :icon="getPlatformIcon(post.platform)" class="h-5 w-5 mr-2" :class="getPlatformIconColor(post.platform)" />
                    <span class="text-sm text-gray-900">{{ formatPlatform(post.platform) }}</span>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex flex-col space-y-1 text-sm">
                    <div class="flex items-center">
                      <Icon icon="heroicons:heart" class="h-4 w-4 text-red-500 mr-1" />
                      <span>{{ post.performance.likes }}</span>
                    </div>
                    <div class="flex items-center">
                      <Icon icon="heroicons:chat-bubble-left-ellipsis" class="h-4 w-4 text-blue-500 mr-1" />
                      <span>{{ post.performance.comments }}</span>
                    </div>
                    <div class="flex items-center">
                      <Icon icon="heroicons:arrow-path-rounded-square" class="h-4 w-4 text-green-500 mr-1" />
                      <span>{{ post.performance.shares }}</span>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(post.date || '') }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                    :class="{
                      'bg-gray-100 text-gray-800': post.status === 'draft',
                      'bg-yellow-100 text-yellow-800': post.status === 'scheduled',
                      'bg-green-100 text-green-800': post.status === 'published',
                    }">
                    {{ formatStatus(post.status) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <button @click="viewPost(post)" class="text-gray-600 hover:text-gray-900">
                      <Icon icon="heroicons:eye" class="w-5 h-5" />
                    </button>
                    <button
                      v-if="post.status === 'draft'"
                      @click="editPost(post)"
                      class="text-blue-600 hover:text-blue-900"
                    >
                      <Icon icon="heroicons:pencil-square" class="w-5 h-5" />
                    </button>
                    <button
                      v-if="post.status === 'draft'"
                      @click="schedulePost(post)"
                      class="text-green-600 hover:text-green-900"
                    >
                      <Icon icon="heroicons:calendar" class="w-5 h-5" />
                    </button>
                    <button
                      v-if="post.status === 'scheduled'"
                      @click="cancelPost(post)"
                      class="text-red-600 hover:text-red-900"
                    >
                      <Icon icon="heroicons:x-circle" class="w-5 h-5" />
                    </button>
                    <button
                      v-if="post.status === 'published'"
                      @click="duplicatePost(post)"
                      class="text-purple-600 hover:text-purple-900"
                    >
                      <Icon icon="heroicons:document-duplicate" class="w-5 h-5" />
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div class="flex-1 flex justify-between sm:hidden">
            <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              Previous
            </button>
            <button @click="nextPage" :disabled="currentPage === totalPages" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              Next
            </button>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700">
                Showing <span class="font-medium">{{ paginationStart }}</span> to <span class="font-medium">{{ paginationEnd }}</span> of <span class="font-medium">{{ totalItems }}</span> posts
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                  <span class="sr-only">Previous</span>
                  <Icon icon="heroicons:chevron-left" class="h-5 w-5" />
                </button>
                <button v-for="page in displayedPages" :key="page" @click="goToPage(page)" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium" :class="page === currentPage ? 'z-10 bg-red-50 border-red-500 text-red-600' : 'text-gray-500 hover:bg-gray-50'">
                  {{ page }}
                </button>
                <button @click="nextPage" :disabled="currentPage === totalPages" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                  <span class="sr-only">Next</span>
                  <Icon icon="heroicons:chevron-right" class="h-5 w-5" />
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { ENDPOINTS } from '~/utils/api';

definePageMeta({
  layout: "dashboard",
});

useHead({
  title: "Social Media | EventaHub Malawi",
  meta: [
    { name: "description", content: "Manage social media campaigns, schedule posts, and track engagement metrics for your events" }
  ]
});

interface SocialStats {
  facebook: {
    followers: number;
    growth: number;
  };
  instagram: {
    followers: number;
    growth: number;
  };
  twitter: {
    followers: number;
    growth: number;
  };
  youtube: {
    subscribers: number;
    growth: number;
  };
}

interface PostPerformance {
  likes: number;
  comments: number;
  shares: number;
}

interface SocialPost {
  id: number;
  content: string;
  image?: string;
  platform: 'facebook' | 'instagram' | 'twitter' | 'youtube';
  event?: string;
  date?: string;
  status: 'published' | 'scheduled' | 'draft';
  performance: PostPerformance;
}

interface ApiResponse<T> {
  data: T;
  message?: string;
  status: number;
}

interface PostsResponse {
  posts: SocialPost[];
  total: number;
}

const loading = ref(true);
const searchQuery = ref('');
const platformFilter = ref('all');
const statusFilter = ref('all');
const dateFilter = ref('all');
const currentPage = ref(1);
const itemsPerPage = 10;
const totalItems = ref(0);
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const stats = ref<SocialStats>({
  facebook: {
    followers: 0,
    growth: 0
  },
  instagram: {
    followers: 0,
    growth: 0
  },
  twitter: {
    followers: 0,
    growth: 0
  },
  youtube: {
    subscribers: 0,
    growth: 0
  }
});

const posts = ref<SocialPost[]>([]);

async function fetchStats() {
  try {
    const response = await httpClient.get<ApiResponse<SocialStats>>(ENDPOINTS.SOCIAL_MEDIA.STATS);
    stats.value = response.data;
  } catch (error) {
    console.error('Error fetching social media stats:', error);
    $toast.error('Failed to load social media statistics');
  }
}

async function fetchPosts() {
  try {
    loading.value = true;
    const params = new URLSearchParams({
      page: currentPage.value.toString(),
      limit: itemsPerPage.toString(),
      search: searchQuery.value,
      platform: platformFilter.value !== 'all' ? platformFilter.value : '',
      status: statusFilter.value !== 'all' ? statusFilter.value : '',
      date_filter: dateFilter.value !== 'all' ? dateFilter.value : ''
    });

    const response = await httpClient.get<ApiResponse<PostsResponse>>(`${ENDPOINTS.SOCIAL_MEDIA.BASE}?${params.toString()}`);
    posts.value = response.data.posts;
    totalItems.value = response.data.total;
  } catch (error) {
    console.error('Error fetching social media posts:', error);
    $toast.error('Failed to load social media posts');
  } finally {
    loading.value = false;
  }
}

async function createPost(post: Partial<SocialPost>) {
  try {
    await httpClient.post(ENDPOINTS.SOCIAL_MEDIA.POST, post);
    await fetchPosts();
    $toast.success('Post created successfully');
  } catch (error) {
    console.error('Error creating post:', error);
    $toast.error('Failed to create post');
  }
}

async function schedulePost(post: SocialPost) {
  try {
    await httpClient.post(ENDPOINTS.SOCIAL_MEDIA.SCHEDULE, {
      id: post.id,
      scheduled_date: '2024-06-15'
    });
    await fetchPosts();
    $toast.success('Post has been scheduled');
  } catch (error) {
    console.error('Error scheduling post:', error);
    $toast.error('Failed to schedule post');
  }
}

async function duplicatePost(post: SocialPost) {
  try {
    await httpClient.post(ENDPOINTS.SOCIAL_MEDIA.POST, {
      content: post.content,
      image: post.image,
      platform: post.platform,
      event: post.event,
      status: 'draft'
    });
    await fetchPosts();
    $toast.success('Post has been duplicated');
  } catch (error) {
    console.error('Error duplicating post:', error);
    $toast.error('Failed to duplicate post');
  }
}

const filteredPosts = computed(() => {
  let result = [...posts.value];

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(p =>
      p.content.toLowerCase().includes(query) ||
      (p.event && p.event.toLowerCase().includes(query))
    );
  }

  if (platformFilter.value !== 'all') {
    result = result.filter(p => p.platform === platformFilter.value);
  }

  if (statusFilter.value !== 'all') {
    result = result.filter(p => p.status === statusFilter.value);
  }

  if (dateFilter.value !== 'all' && dateFilter.value !== 'custom') {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    switch (dateFilter.value) {
      case 'today':
        result = result.filter(p => p.date && new Date(p.date) >= today);
        break;
      case 'week':
        const weekStart = new Date(today);
        weekStart.setDate(today.getDate() - today.getDay());
        result = result.filter(p => p.date && new Date(p.date) >= weekStart);
        break;
      case 'month':
        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
        result = result.filter(p => p.date && new Date(p.date) >= monthStart);
        break;
      case 'year':
        const yearStart = new Date(today.getFullYear(), 0, 1);
        result = result.filter(p => p.date && new Date(p.date) >= yearStart);
        break;
    }
  }

  const start = (currentPage.value - 1) * itemsPerPage;
  const end = start + itemsPerPage;
  return result.slice(start, end);
});

const totalPages = computed(() => Math.ceil(totalItems.value / itemsPerPage));

const paginationStart = computed(() => {
  if (totalItems.value === 0) return 0;
  return (currentPage.value - 1) * itemsPerPage + 1;
});

const paginationEnd = computed(() => {
  return Math.min(currentPage.value * itemsPerPage, totalItems.value);
});

const displayedPages = computed(() => {
  const pages = [];
  const maxPagesToShow = 5;

  if (totalPages.value <= maxPagesToShow) {
    for (let i = 1; i <= totalPages.value; i++) {
      pages.push(i);
    }
  } else {
    const leftSide = Math.floor(maxPagesToShow / 2);
    const rightSide = maxPagesToShow - leftSide - 1;

    if (currentPage.value > leftSide && currentPage.value < totalPages.value - rightSide) {
      for (let i = currentPage.value - leftSide; i <= currentPage.value + rightSide; i++) {
        pages.push(i);
      }
    } else if (currentPage.value <= leftSide) {
      for (let i = 1; i <= maxPagesToShow; i++) {
        pages.push(i);
      }
    } else {
      for (let i = totalPages.value - maxPagesToShow + 1; i <= totalPages.value; i++) {
        pages.push(i);
      }
    }
  }

  return pages;
});

function formatDate(dateString: string): string {
  if (!dateString) return 'Not scheduled';
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}

function formatStatus(status: string): string {
  return status.charAt(0).toUpperCase() + status.slice(1);
}

function formatPlatform(platform: string): string {
  return platform.charAt(0).toUpperCase() + platform.slice(1);
}

function getPlatformIcon(platform: string): string {
  switch (platform) {
    case 'facebook':
      return 'mdi:facebook';
    case 'instagram':
      return 'mdi:instagram';
    case 'twitter':
      return 'mdi:twitter';
    case 'youtube':
      return 'mdi:youtube';
    default:
      return 'heroicons:globe-alt';
  }
}

function getPlatformIconColor(platform: string): string {
  switch (platform) {
    case 'facebook':
      return 'text-blue-600';
    case 'instagram':
      return 'text-pink-600';
    case 'twitter':
      return 'text-sky-600';
    case 'youtube':
      return 'text-red-600';
    default:
      return 'text-gray-600';
  }
}

function truncateText(text: string, maxLength: number): string {
  if (!text) return '';
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
}

function prevPage(): void {
  if (currentPage.value > 1) {
    currentPage.value--;
    fetchPosts();
  }
}

function nextPage(): void {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
    fetchPosts();
  }
}

function goToPage(page: number): void {
  currentPage.value = page;
  fetchPosts();
}

function viewPost(post: SocialPost): void {
  console.log('View post:', post);
  $toast.info(`Viewing post on ${formatPlatform(post.platform)}`);
}

function editPost(post: SocialPost): void {
  console.log('Edit post:', post);
  $toast.info(`Editing post for ${formatPlatform(post.platform)}`);
}

function cancelPost(post: SocialPost): void {
  console.log('Cancel post:', post);
  // This would call an API to update the post status
  $toast.success('Post has been moved back to drafts');
}

watch([searchQuery, platformFilter, statusFilter, dateFilter], () => {
  currentPage.value = 1;
  fetchPosts();
});

onMounted(async () => {
  await Promise.all([fetchStats(), fetchPosts()]);
});
</script>
