<template>
  <div class="p-3 sm:p-6 bg-gray-100 dark:bg-zinc-900">
    <div class="mb-4 sm:mb-6 flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
      <div>
        <h1 class="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">Calendar</h1>
        <p class="text-sm sm:text-base text-gray-500 dark:text-gray-400">View and manage all your scheduled events</p>
      </div>
      <div class="flex space-x-2">
        <button
          @click="exportCalendar"
          class="p-2.5 bg-white dark:bg-zinc-800 rounded-full border border-gray-300 dark:border-zinc-600 shadow-sm hover:bg-gray-50 dark:hover:bg-zinc-700 text-gray-700 dark:text-gray-200 flex items-center"
        >
          <Icon icon="vscode-icons:file-type-excel" class="w-4 h-4 sm:w-5 sm:h-5" />
        </button>
        <NuxtLink
          to="/dashboard/manage-events/create"
          class="px-3 sm:px-4 py-2 bg-red-600 text-white shadow-sm hover:bg-red-700 flex items-center text-sm sm:text-base"
        >
          <Icon icon="heroicons:plus" class="w-4 h-4 sm:w-5 sm:h-5 mr-1" />
          <span class="hidden sm:inline">Add Event</span>
          <span class="sm:hidden">Add</span>
        </NuxtLink>
      </div>
    </div>

    <div v-if="loading" class="flex justify-center items-center h-64">
      <CoreLoader />
    </div>
    <div v-else class="bg-white dark:bg-zinc-800 shadow">
      <div class="p-3 sm:p-4 border-b border-gray-200 dark:border-zinc-600 flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-3 sm:space-y-0">
        <div class="flex items-center justify-center sm:justify-start space-x-2">
          <button
            class="p-2 hover:bg-gray-100 dark:hover:bg-zinc-700 text-gray-700 dark:text-gray-200"
            @click="previousPeriod"
          >
            <Icon icon="heroicons:chevron-left" class="w-4 h-4 sm:w-5 sm:h-5" />
          </button>
          <h2 class="text-base sm:text-lg font-semibold text-gray-900 dark:text-white text-center">{{ formatCurrentPeriod }}</h2>
          <button
            class="p-2 hover:bg-gray-100 dark:hover:bg-zinc-700 text-gray-700 dark:text-gray-200"
            @click="nextPeriod"
          >
            <Icon icon="heroicons:chevron-right" class="w-4 h-4 sm:w-5 sm:h-5" />
          </button>
        </div>
        <div class="flex items-center justify-center space-x-2">
          <button
            @click="refreshEvents"
            :disabled="loading"
            class="p-2 hover:bg-gray-100 dark:hover:bg-zinc-700 text-gray-700 dark:text-gray-200 disabled:opacity-50"
          >
            <Icon icon="heroicons:arrow-path" class="w-4 h-4 sm:w-5 sm:h-5" :class="{ 'animate-spin': loading }" />
          </button>
          <button
            @click="goToToday"
            class="px-2 sm:px-3 py-1 text-xs sm:text-sm bg-red-600 text-white hover:bg-red-700"
          >
            Today
          </button>
          <div class="flex space-x-1">
            <button
              v-for="view in views"
              :key="view.value"
              @click="currentView = view.value"
              class="px-2 sm:px-3 py-1 text-xs sm:text-sm"
              :class="currentView === view.value ? 'bg-red-600 text-white' : 'bg-gray-200 dark:bg-zinc-600 text-gray-700 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-zinc-500'"
            >
              <span class="hidden sm:inline">{{ view.label }}</span>
              <span class="sm:hidden">{{ view.label.charAt(0) }}</span>
            </button>
          </div>
        </div>
      </div>

      <div v-if="currentView === 'month'" class="p-2 sm:p-4">
        <div class="grid grid-cols-7 gap-px bg-gray-200 dark:bg-zinc-600">
          <div v-for="day in daysOfWeek" :key="day" class="bg-gray-100 dark:bg-zinc-700 p-1 sm:p-2 text-center text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300">
            <span class="hidden sm:inline">{{ day }}</span>
            <span class="sm:hidden">{{ day.charAt(0) }}</span>
          </div>

          <div
            v-for="(day, index) in calendarDays"
            :key="index"
            class="min-h-[60px] sm:min-h-[100px] p-1 sm:p-2 border border-gray-100 dark:border-zinc-600"
            :class="{
              'bg-white dark:bg-zinc-800': day.isCurrentMonth,
              'bg-gray-50 dark:bg-zinc-700': !day.isCurrentMonth,
              'bg-blue-50 dark:bg-blue-900/20': day.isToday
            }"
          >
            <div class="flex justify-between items-start">
              <span
                class="text-xs sm:text-sm font-medium"
                :class="{
                  'text-gray-400 dark:text-gray-500': !day.isCurrentMonth,
                  'text-gray-900 dark:text-white': day.isCurrentMonth && !day.isToday,
                  'bg-blue-600 text-white w-5 h-5 sm:w-7 sm:h-7 rounded-full flex items-center justify-center font-bold shadow-lg text-xs sm:text-sm': day.isToday
                }"
              >
                {{ day.day }}
              </span>
              <span v-if="getEventsForDay(day.date).length > 0" class="text-xs bg-red-500 text-white px-1 sm:px-2 py-0.5 sm:py-1 rounded-full font-semibold shadow-sm">
                {{ getEventsForDay(day.date).length }}
              </span>
            </div>

            <div class="mt-1 space-y-1 max-h-[40px] sm:max-h-[80px] overflow-y-auto">
              <div
                v-for="event in getEventsForDay(day.date).slice(0, currentView === 'month' ? 2 : 3)"
                :key="event.id"
                class="text-xs p-1 sm:p-2 cursor-pointer transition-all duration-200 transform hover:scale-105 shadow-sm"
                :class="getCategoryColor(event.category?.name)"
                @click="openEventDetails(event)"
              >
                <div class="font-semibold truncate">{{ event.title }}</div>
                <div v-if="event.start_time" class="text-xs opacity-90 mt-0.5 hidden sm:flex items-center">
                  <Icon icon="heroicons:clock" class="w-3 h-3 mr-1" />
                  {{ event.start_time }}
                </div>
              </div>
              <div
                v-if="getEventsForDay(day.date).length > 2"
                class="text-xs text-gray-500 dark:text-gray-400 p-1 cursor-pointer hover:text-gray-700 dark:hover:text-gray-200"
                @click="currentView = 'agenda'"
              >
                +{{ getEventsForDay(day.date).length - 2 }} more
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-else-if="currentView === 'week'" class="p-2 sm:p-4 overflow-x-auto">
        <div class="min-w-[600px] grid grid-cols-8 gap-px bg-gray-200">
          <div class="bg-white">
            <div class="h-8 sm:h-10"></div>
            <div v-for="hour in hours" :key="hour" class="h-16 sm:h-20 border-b border-gray-100 text-xs text-gray-500 pr-2 text-right">
              {{ formatHour(hour) }}
            </div>
          </div>

          <div v-for="day in weekDays" :key="day.date.getTime()" class="bg-white dark:bg-zinc-800">
            <div class="h-8 sm:h-10 p-1 sm:p-2 text-center border-b border-gray-200 dark:border-zinc-600">
              <div class="text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300">{{ day.name }}</div>
              <div
                class="text-xs sm:text-sm text-gray-900 dark:text-white"
                :class="{ 'bg-blue-600 text-white w-5 h-5 sm:w-7 sm:h-7 rounded-full flex items-center justify-center mx-auto font-bold shadow-lg': day.isToday }"
              >
                {{ day.date.getDate() }}
              </div>
            </div>

            <div v-for="hour in hours" :key="hour" class="h-16 sm:h-20 border-b border-gray-100 dark:border-zinc-700 relative p-1">
              <div
                v-for="event in getEventsForDayAndHour(day.date, hour)"
                :key="event.id"
                @click="openEventDetails(event)"
                class="text-xs p-1 sm:p-2 rounded-md cursor-pointer transition-all duration-200 hover:shadow-md mb-1"
                :class="getCategoryColor(event.category?.name)"
              >
                <div class="font-semibold truncate">{{ event.title }}</div>
                <div class="opacity-90 hidden sm:flex items-center mt-0.5">
                  <Icon icon="heroicons:clock" class="w-3 h-3 mr-1" />
                  {{ formatEventTime(event) }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-else-if="currentView === 'day'" class="p-2 sm:p-4">
        <div class="grid grid-cols-1 gap-px">
          <div v-for="hour in hours" :key="hour" class="flex border-b border-gray-100 dark:border-zinc-700">
            <div class="w-16 sm:w-20 py-3 sm:py-4 text-xs sm:text-sm text-gray-500 dark:text-gray-400 text-right pr-2 sm:pr-4">
              {{ formatHour(hour) }}
            </div>
            <div class="flex-1 min-h-[50px] sm:min-h-[60px] relative p-1 sm:p-2">
              <div
                v-for="event in getEventsForDayAndHour(currentDate, hour)"
                :key="event.id"
                @click="openEventDetails(event)"
                class="text-xs sm:text-sm p-2 sm:p-3 cursor-pointer transition-all duration-200 hover:shadow-lg mb-2 border-l-4"
                :class="getCategoryColor(event.category?.name)"
              >
                <div class="font-semibold text-sm sm:text-base mb-1">{{ event.title }}</div>
                <div class="text-xs opacity-90 flex items-center">
                  <Icon icon="heroicons:clock" class="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                  {{ formatEventTime(event) }}
                </div>
                <div v-if="event.location" class="text-xs opacity-80 hidden sm:flex items-center mt-1">
                  <Icon icon="heroicons:map-pin" class="w-4 h-4 mr-1" />
                  {{ event.location }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-else-if="currentView === 'agenda'" class="p-2 sm:p-4">
        <div class="space-y-3 sm:space-y-4">
          <div v-for="(group, date) in groupedEvents" :key="date" class="border-b border-gray-200 dark:border-zinc-600 pb-3 sm:pb-4">
            <h3 class="font-medium mb-2 text-gray-900 dark:text-white text-sm sm:text-base">{{ formatDate(date) }}</h3>
            <div class="space-y-2">
              <div
                v-for="event in group"
                :key="event.id"
                class="flex p-3 sm:p-4 border border-gray-200 dark:border-zinc-600 hover:shadow-lg dark:hover:shadow-zinc-700/50 cursor-pointer transition-all duration-200 transform hover:scale-[1.02]"
                :class="getCategoryColor(event.category?.name).replace('border-l-4', 'border-l-4 hover:border-l-6')"
                @click="openEventDetails(event)"
              >
                <div class="ml-2 sm:ml-3 flex-1">
                  <div class="font-semibold text-base sm:text-lg mb-1 sm:mb-2">{{ event.title }}</div>
                  <div class="flex items-center text-xs sm:text-sm opacity-90 mb-1">
                    <Icon icon="heroicons:clock" class="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
                    {{ formatEventTime(event) }}
                  </div>
                  <div v-if="event.location" class="hidden sm:flex items-center text-xs sm:text-sm opacity-80 mb-1">
                    <Icon icon="heroicons:map-pin" class="w-4 h-4 mr-2" />
                    {{ event.location }}
                  </div>
                  <div v-if="event.category" class="flex items-center text-xs sm:text-sm opacity-80">
                    <Icon icon="heroicons:tag" class="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
                    {{ event.category.name }}
                  </div>
                </div>
                <div class="text-gray-400 dark:text-gray-500 self-center">
                  <Icon icon="heroicons:chevron-right" class="w-4 h-4 sm:w-5 sm:h-5" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-if="!loading && events.length === 0" class="bg-white dark:bg-zinc-800 shadow p-6 sm:p-12 text-center">
      <Icon icon="heroicons:calendar-days" class="mx-auto h-10 w-10 sm:h-12 sm:w-12 text-gray-400 dark:text-gray-500" />
      <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No events scheduled</h3>
      <p class="mt-1 text-xs sm:text-sm text-gray-500 dark:text-gray-400">Get started by creating your first event.</p>
      <div class="mt-4 sm:mt-6">
        <NuxtLink
          to="/dashboard/manage-events/create"
          class="inline-flex items-center px-3 sm:px-4 py-2 border border-transparent shadow-sm text-xs sm:text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
        >
          <Icon icon="heroicons:plus" class="w-4 h-4 sm:w-5 sm:h-5 mr-1 sm:mr-2" />
          Create Event
        </NuxtLink>
      </div>
    </div>
    <EventsView v-if="selectedEventItem" ref="eventsViewRef" :event="selectedEventItem" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { useHttpClient } from '@/composables/useHttpClient'
import { ENDPOINTS } from '@/utils/api'
import EventsView from '~/components/events/dashboard/view.vue'

definePageMeta({
  layout: "dashboard",
  middleware: 'auth'
})

useHead({
  title: "Calendar | EventaHub Malawi",
  meta: [
    { name: "description", content: "View and manage all your scheduled events in EventaHub's calendar" }
  ]
})

interface Event {
  id: number
  title: string
  description?: string
  start_date: string
  end_date: string
  start_time?: string
  end_time?: string
  location?: string
  status: string
  category?: {
    id: number
    name: string
  }
  user?: {
    id: number
    name: string
  }
}

interface CalendarDay {
  date: Date
  day: number
  isCurrentMonth: boolean
  isToday: boolean
}

interface WeekDay {
  name: string
  date: Date
  isToday: boolean
}

const { $toast }: any = useNuxtApp()
const httpClient = useHttpClient()

// State
const loading = ref(true)
const currentDate = ref(new Date())
const currentView = ref('month')
const events = ref<Event[]>([])
const selectedEventItem = ref<any>(null)
const eventsViewRef = ref<any>(null)

const views = [
  { label: 'Month', value: 'month' },
  { label: 'Week', value: 'week' },
  { label: 'Day', value: 'day' },
  { label: 'Agenda', value: 'agenda' },
]

const daysOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
const hours = Array.from({ length: 24 }, (_, i) => i)

// Computed properties
const formatCurrentPeriod = computed(() => {
  const date = currentDate.value
  if (currentView.value === 'month') {
    // On mobile, show shorter month format
    if (typeof window !== 'undefined' && window.innerWidth < 640) {
      return date.toLocaleString('default', { month: 'short', year: 'numeric' })
    }
    return date.toLocaleString('default', { month: 'long', year: 'numeric' })
  } else if (currentView.value === 'week') {
    const startOfWeek = new Date(date)
    startOfWeek.setDate(date.getDate() - date.getDay())
    const endOfWeek = new Date(startOfWeek)
    endOfWeek.setDate(startOfWeek.getDate() + 6)
    // On mobile, show shorter date format
    if (typeof window !== 'undefined' && window.innerWidth < 640) {
      return `${startOfWeek.toLocaleDateString('default', { month: 'short', day: 'numeric' })} - ${endOfWeek.toLocaleDateString('default', { month: 'short', day: 'numeric' })}`
    }
    return `${startOfWeek.toLocaleDateString()} - ${endOfWeek.toLocaleDateString()}`
  } else {
    // On mobile, show shorter day format
    if (typeof window !== 'undefined' && window.innerWidth < 640) {
      return date.toLocaleDateString('default', { month: 'short', day: 'numeric' })
    }
    return date.toLocaleDateString('default', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })
  }
})

const calendarDays = computed((): CalendarDay[] => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth()

  const firstDayOfMonth = new Date(year, month, 1)
  const lastDayOfMonth = new Date(year, month + 1, 0)

  const daysInMonth = lastDayOfMonth.getDate()
  const firstDayOfWeek = firstDayOfMonth.getDay()

  const days: CalendarDay[] = []

  const prevMonthLastDay = new Date(year, month, 0).getDate()
  for (let i = firstDayOfWeek - 1; i >= 0; i--) {
    const date = new Date(year, month - 1, prevMonthLastDay - i)
    days.push({
      date,
      day: date.getDate(),
      isCurrentMonth: false,
      isToday: isSameDay(date, new Date())
    })
  }

  for (let i = 1; i <= daysInMonth; i++) {
    const date = new Date(year, month, i)
    days.push({
      date,
      day: i,
      isCurrentMonth: true,
      isToday: isSameDay(date, new Date())
    })
  }

  const remainingDays = 42 - days.length
  for (let i = 1; i <= remainingDays; i++) {
    const date = new Date(year, month + 1, i)
    days.push({
      date,
      day: i,
      isCurrentMonth: false,
      isToday: isSameDay(date, new Date())
    })
  }

  return days
})

const weekDays = computed((): WeekDay[] => {
  const date = new Date(currentDate.value)
  const day = date.getDay()
  date.setDate(date.getDate() - day)

  return Array.from({ length: 7 }, (_, i) => {
    const d = new Date(date)
    d.setDate(d.getDate() + i)
    return {
      name: daysOfWeek[i],
      date: d,
      isToday: isSameDay(d, new Date())
    }
  })
})

const groupedEvents = computed(() => {
  const grouped: Record<string, Event[]> = {}

  events.value.forEach(event => {
    const dateKey = event.start_date
    if (!grouped[dateKey]) {
      grouped[dateKey] = []
    }
    grouped[dateKey].push(event)
  })

  return Object.keys(grouped)
    .sort()
    .reduce((obj: Record<string, Event[]>, key) => {
      obj[key] = grouped[key]
      return obj
    }, {})
})

// Methods
const fetchEvents = async () => {
  try {
    loading.value = true
    let startDate: Date
    let endDate: Date

    if (currentView.value === 'month') {
      startDate = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth(), 1)
      endDate = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() + 1, 0)
    } else if (currentView.value === 'week') {
      const date = new Date(currentDate.value)
      const day = date.getDay()
      startDate = new Date(date)
      startDate.setDate(date.getDate() - day)
      endDate = new Date(startDate)
      endDate.setDate(startDate.getDate() + 6)
    } else {
      startDate = new Date(currentDate.value)
      endDate = new Date(currentDate.value)
    }

    const params = new URLSearchParams({
      start_date: startDate.toISOString().split('T')[0],
      end_date: endDate.toISOString().split('T')[0]
    })

    let response: any

    try {
      const calendarUrl = `${ENDPOINTS.EVENTS.CALENDAR}?${params}`
      response = await httpClient.get(calendarUrl)
    } catch (calendarError: any) {
      if (calendarError.status === 401) {
        $toast.error('Authentication required. Please log in again.')
        await navigateTo('/auth/login')
        return
      }

      try {
        const userUrl = `${ENDPOINTS.EVENTS.USER}?${params}`
        response = await httpClient.get(userUrl)
      } catch (userError: any) {
        if (userError.status === 401) {
          $toast.error('Authentication required. Please log in again.')
          await navigateTo('/auth/login')
          return
        }

        try {
          response = await httpClient.get(ENDPOINTS.EVENTS.GET_ALL)
        } catch (getAllError: any) {
          try {
            response = await httpClient.get(ENDPOINTS.EVENTS.BASE)
          } catch (baseError: any) {
            response = await httpClient.get(ENDPOINTS.EVENTS.GET)
          }
        }
      }
    }

    if (response?.data?.events) {
      events.value = response.data.events
    } else if (response?.events) {
      events.value = response.events
    } else if (response?.data && Array.isArray(response.data)) {
      events.value = response.data
    } else if (Array.isArray(response)) {
      events.value = response
    } else {
      events.value = []
    }

  } catch (error: any) {
    if (error.status === 401) {
      $toast.error('Authentication required. Please log in again.')
      await navigateTo('/auth/login')
    } else {
      $toast.error('Failed to load calendar events. Please try again.')
    }
    events.value = []
  } finally {
    loading.value = false
  }
}

const getEventsForDay = (date: Date): Event[] => {
  const dateStr = date.toISOString().split('T')[0]
  return events.value.filter(event => {
    const eventDate = new Date(event.start_date).toISOString().split('T')[0]
    return eventDate === dateStr
  })
}

const getEventsForDayAndHour = (date: Date, hour: number): Event[] => {
  const dateStr = date.toISOString().split('T')[0]
  return events.value.filter(event => {
    const eventDate = new Date(event.start_date).toISOString().split('T')[0]
    if (eventDate !== dateStr) return false
    if (!event.start_time) return false

    const eventHour = parseInt(event.start_time.split(':')[0])
    return eventHour === hour
  })
}

const isSameDay = (date1: Date, date2: Date): boolean => {
  return date1.getFullYear() === date2.getFullYear() &&
         date1.getMonth() === date2.getMonth() &&
         date1.getDate() === date2.getDate()
}

const formatHour = (hour: number): string => {
  if (hour === 0) return '12 AM'
  if (hour === 12) return '12 PM'
  if (hour < 12) return `${hour} AM`
  return `${hour - 12} PM`
}

const formatEventTime = (event: Event): string => {
  if (!event.start_time) return ''
  if (event.end_time) {
    return `${event.start_time} - ${event.end_time}`
  }
  return event.start_time
}

const formatDate = (date: string | Date): string => {
  if (!date) return ''
  return new Date(date).toLocaleDateString('en-US', {
    weekday: 'short',
    month: 'short',
    day: 'numeric'
  })
}

const getCategoryColor = (categoryName?: string): string => {
  const colors = {
    'conference': 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 border-l-4 border-blue-500',
    'workshop': 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 border-l-4 border-green-500',
    'seminar': 'bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 border-l-4 border-purple-500',
    'meeting': 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300 border-l-4 border-orange-500',
    'social': 'bg-pink-100 dark:bg-pink-900/30 text-pink-800 dark:text-pink-300 border-l-4 border-pink-500',
    'training': 'bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-300 border-l-4 border-indigo-500',
    'networking': 'bg-teal-100 dark:bg-teal-900/30 text-teal-800 dark:text-teal-300 border-l-4 border-teal-500',
    'default': 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 border-l-4 border-red-500'
  }

  if (!categoryName) return colors.default

  const normalizedCategory = categoryName.toLowerCase()
  return colors[normalizedCategory as keyof typeof colors] || colors.default
}

const previousPeriod = () => {
  const date = new Date(currentDate.value)
  if (currentView.value === 'month') {
    date.setMonth(date.getMonth() - 1)
  } else if (currentView.value === 'week') {
    date.setDate(date.getDate() - 7)
  } else {
    date.setDate(date.getDate() - 1)
  }
  currentDate.value = date
  fetchEvents()
}

const nextPeriod = () => {
  const date = new Date(currentDate.value)
  if (currentView.value === 'month') {
    date.setMonth(date.getMonth() + 1)
  } else if (currentView.value === 'week') {
    date.setDate(date.getDate() + 7)
  } else {
    date.setDate(date.getDate() + 1)
  }
  currentDate.value = date
  fetchEvents()
}

const goToToday = () => {
  currentDate.value = new Date()
  fetchEvents()
}

const refreshEvents = () => {
  fetchEvents()
}

const openEventDetails = async (event: Event) => {
  const eventItem = {
    id: event.id,
    slug: event.title.toLowerCase().replace(/\s+/g, '-'),
    user_id: event.user?.id || 0,
    user: event.user || { id: 0, name: 'Unknown' },
    type_id: 1,
    visibility_id: 1,
    title: event.title,
    description: event.description || '',
    location: event.location || '',
    district_id: 1,
    category: event.category,
    category_id: event.category?.id || 0,
    latitude: '0',
    longitude: '0',
    cover_art: 'default.jpg',
    start: event.start_date,
    end: event.end_date,
    ticket_price: 0,
    created_at: event.start_date,
    updated_at: event.start_date,
    ticket_banner: null,
    published_at: event.start_date,
    attendees_count: 0,
    likes_count: 0,
    shares_count: 0,
    attendees_avatars: '',
    is_liked: false,
    is_attending: false,
    is_attendee: false,
    tiers: [],
    ratings: [],
    highlights: [],
    sponsors: [],
    ratings_avg_organization: 0,
    ratings_avg_content: 0,
    ratings_avg_technical: 0,
    ratings_avg_engagement: 0,
    ratings_avg_rating: 0
  }

  selectedEventItem.value = eventItem
  // Wait for the component to be mounted, then call the openModal method
  await nextTick()
  if (eventsViewRef.value && eventsViewRef.value.openModal) {
    eventsViewRef.value.openModal()
  }
}

const exportCalendar = async () => {
  try {
    if (events.value.length === 0) {
      $toast.error('No events to export')
      return
    }

    const data = events.value.map(event => ({
      'Event Title': event.title,
      'Start Date': event.start_date,
      'End Date': event.end_date,
      'Start Time': event.start_time || '',
      'End Time': event.end_time || '',
      'Location': event.location || '',
      'Status': event.status,
      'Category': event.category?.name || '',
      'Organizer': event.user?.name || ''
    }))

    const csv = [
      Object.keys(data[0]).join(','),
      ...data.map(row => Object.values(row).map(val => `"${val}"`).join(','))
    ].join('\n')

    const blob = new Blob([csv], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `calendar-events-${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    window.URL.revokeObjectURL(url)

    $toast.success('Calendar exported successfully')
  } catch (error) {
    $toast.error('Failed to export calendar')
  }
}

// Lifecycle
onMounted(() => {
  fetchEvents()
})

watch(currentView, () => {
  events.value = []
  fetchEvents()
})

watch(currentDate, () => {
  events.value = []
  fetchEvents()
})
</script>
