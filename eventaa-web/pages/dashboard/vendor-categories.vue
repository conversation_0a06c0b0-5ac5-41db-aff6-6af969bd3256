<template>
  <div class="p-6 bg-zinc-100 dark:bg-zinc-900 min-h-screen">
    <div class="mb-6 flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Vendor Categories</h1>
        <p class="text-gray-500 dark:text-gray-400">Manage categories for vendor services</p>
      </div>
      <button
        @click="openAddCategoryModal"
        class="px-4 py-2 bg-red-600 text-white shadow-sm hover:bg-red-700 flex items-center"
      >
        <Icon icon="heroicons:plus" class="w-5 h-5 mr-1" />
        Add Category
      </button>
    </div>

    <div class="bg-white dark:bg-zinc-800 shadow">
      <div v-if="loading" class="flex justify-center items-center py-20">
        <CoreLoader />
      </div>
      <div v-else>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead>
              <tr>
                <th class="px-6 py-3 bg-zinc-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Name</th>
                <th class="px-6 py-3 bg-zinc-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Description</th>
                <th class="px-6 py-3 bg-zinc-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Vendors</th>
                <th class="px-6 py-3 bg-zinc-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Created</th>
                <th class="px-6 py-3 bg-zinc-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 bg-zinc-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-zinc-800 divide-y divide-gray-200 dark:divide-gray-700">
              <tr v-if="categories.length === 0">
                <td colspan="6" class="px-6 py-20 text-center">
                  <Icon icon="heroicons:tag" class="w-16 h-16 text-gray-400 dark:text-gray-600 mx-auto mb-4" />
                  <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No categories found</h3>
                  <p class="text-gray-500 dark:text-gray-400 mb-4">Get started by creating your first vendor category</p>
                  <button @click="openAddCategoryModal" class="px-4 py-2 bg-red-600 text-white shadow-sm hover:bg-red-700">
                    Add Category
                  </button>
                </td>
              </tr>
              <tr v-for="category in categories" :key="category.id" class="hover:bg-zinc-50 dark:hover:bg-zinc-700">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10 bg-zinc-200 dark:bg-zinc-700 rounded-full flex items-center justify-center">
                      <Icon :icon="category.icon || 'heroicons:tag'" class="h-5 w-5 text-gray-600 dark:text-gray-400" />
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900 dark:text-white">{{ category.name }}</div>
                      <div class="text-sm text-gray-500 dark:text-gray-400">{{ category.slug || generateSlug(category.name) }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4">
                  <div class="text-sm text-gray-900 dark:text-white">{{ truncateText(String(category.description), 100) }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  {{ category.vendors_count || 0 }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  {{ formatDate(category.created_at) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                    :class="category.is_active ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-zinc-100 dark:bg-zinc-900 text-gray-800 dark:text-gray-200'">
                    {{ category.is_active ? 'Active' : 'Inactive' }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <button @click="editCategory(category)" class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-200" title="Edit">
                      <Icon icon="heroicons:pencil-square" class="w-5 h-5" />
                    </button>
                    <button @click="confirmDeleteCategory(category)" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-200" title="Delete">
                      <Icon icon="heroicons:trash" class="w-5 h-5" />
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div v-if="totalItems > 0" class="bg-white dark:bg-zinc-800 px-4 py-3 flex items-center justify-between border-t border-zinc-200 dark:border-zinc-700 sm:px-6">
          <div class="flex-1 flex justify-between sm:hidden">
            <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-4 py-2 border border-zinc-300 dark:border-zinc-600 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-700 hover:bg-zinc-50 dark:hover:bg-zinc-600 disabled:opacity-50">
              Previous
            </button>
            <button @click="nextPage" :disabled="currentPage === totalPages" class="ml-3 relative inline-flex items-center px-4 py-2 border border-zinc-300 dark:border-zinc-600 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-700 hover:bg-zinc-50 dark:hover:bg-zinc-600 disabled:opacity-50">
              Next
            </button>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700 dark:text-gray-300">
                Showing <span class="font-medium">{{ paginationStart }}</span> to <span class="font-medium">{{ paginationEnd }}</span> of <span class="font-medium">{{ totalItems }}</span> categories
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex shadow-sm -space-x-px" aria-label="Pagination">
                <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-2 py-2 border border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-zinc-50 dark:hover:bg-zinc-600 disabled:opacity-50">
                  <span class="sr-only">Previous</span>
                  <Icon icon="heroicons:chevron-left" class="h-5 w-5" />
                </button>
                <button v-for="page in displayedPages" :key="page" @click="goToPage(page)" class="relative inline-flex items-center px-4 py-2 border border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-sm font-medium" :class="page === currentPage ? 'z-10 bg-red-50 dark:bg-red-900 border-red-500 text-red-600 dark:text-red-200' : 'text-gray-500 dark:text-gray-400 hover:bg-zinc-50 dark:hover:bg-zinc-600'">
                  {{ page }}
                </button>
                <button @click="nextPage" :disabled="currentPage === totalPages" class="relative inline-flex items-center px-2 py-2 border border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-zinc-50 dark:hover:bg-zinc-600 disabled:opacity-50">
                  <span class="sr-only">Next</span>
                  <Icon icon="heroicons:chevron-right" class="h-5 w-5" />
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Add/Edit Category Modal -->
    <TransitionRoot appear :show="isCategoryModalOpen" as="template">
      <Dialog as="div" @close="closeCategoryModal" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25 dark:bg-opacity-75" />
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex min-h-full items-center justify-center p-4 text-center">
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel class="w-full max-w-md transform overflow-hidden bg-white dark:bg-zinc-800 p-6 text-left align-middle shadow-xl transition-all">
                <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900 dark:text-white">
                  {{ isEditing ? 'Edit Category' : 'Add New Category' }}
                </DialogTitle>
                <div class="mt-4">
                  <form @submit.prevent="saveCategory">
                    <div class="mb-4">
                      <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Name</label>
                      <input
                        id="name"
                        v-model="categoryForm.name"
                        type="text"
                        class="w-full border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500"
                        required
                      />
                    </div>
                    <div class="mb-4">
                      <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Description</label>
                      <textarea
                        id="description"
                        v-model="categoryForm.description"
                        rows="3"
                        class="w-full border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500"
                      ></textarea>
                    </div>
                    <div class="mb-4">
                      <label for="icon" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Icon</label>
                      <input
                        id="icon"
                        v-model="categoryForm.icon"
                        type="text"
                        class="w-full border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500"
                        placeholder="heroicons:tag"
                      />
                      <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Enter an icon name from Heroicons or other icon libraries</p>
                    </div>
                    <div class="mb-4 flex items-center">
                      <input
                        id="isActive"
                        v-model="categoryForm.isActive"
                        type="checkbox"
                        class="h-4 w-4 text-red-600 focus:ring-red-500 border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700"
                      />
                      <label for="isActive" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">Active</label>
                    </div>
                    <div class="mt-6 flex justify-end space-x-3">
                      <button
                        type="button"
                        class="inline-flex justify-center border border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-zinc-50 dark:hover:bg-zinc-600 focus:outline-none"
                        @click="closeCategoryModal"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        :disabled="submitting"
                        class="inline-flex justify-center border border-transparent bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700 focus:outline-none disabled:opacity-50 items-center"
                      >
                        <Icon v-if="submitting" icon="heroicons:arrow-path" class="w-4 h-4 mr-2 animate-spin" />
                        {{ submitting ? 'Saving...' : (isEditing ? 'Update' : 'Create') }}
                      </button>
                    </div>
                  </form>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>

    <!-- Delete Confirmation Modal -->
    <TransitionRoot appear :show="isDeleteModalOpen" as="template">
      <Dialog as="div" @close="closeDeleteModal" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25 dark:bg-opacity-75" />
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex min-h-full items-center justify-center p-4 text-center">
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel class="w-full max-w-md transform overflow-hidden bg-white dark:bg-zinc-800 p-6 text-left align-middle shadow-xl transition-all">
                <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900 dark:text-white">
                  Delete Category
                </DialogTitle>
                <div class="mt-2">
                  <p class="text-sm text-gray-500 dark:text-gray-400">
                    Are you sure you want to delete the category "{{ categoryToDelete?.name }}"? This action cannot be undone.
                  </p>
                  <p class="text-sm text-red-500 dark:text-red-400 mt-2" v-if="Number(categoryToDelete?.vendors_count || 0) > 0">
                    Warning: This category is used by {{ categoryToDelete?.vendors_count }} vendors. Deleting it may affect their listings.
                  </p>
                </div>

                <div class="mt-4 flex justify-end space-x-2">
                  <button
                    type="button"
                    class="inline-flex justify-center border border-transparent bg-zinc-100 dark:bg-zinc-700 px-4 py-2 text-sm font-medium text-gray-900 dark:text-gray-300 hover:bg-zinc-200 dark:hover:bg-zinc-600 focus:outline-none"
                    @click="closeDeleteModal"
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    :disabled="deleting"
                    class="inline-flex justify-center border border-transparent bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700 focus:outline-none disabled:opacity-50 items-center"
                    @click="deleteCategory"
                  >
                    <Icon v-if="deleting" icon="heroicons:arrow-path" class="w-4 h-4 mr-2 animate-spin" />
                    {{ deleting ? 'Deleting...' : 'Delete' }}
                  </button>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue';
import { ENDPOINTS } from '@/utils/api';

definePageMeta({
  layout: "dashboard",
});

useHead({
  title: "Vendor Categories | EventaHub Malawi",
  meta: [
    { name: "description", content: "Manage vendor categories on our platform" }
  ]
});

interface Category {
  id: number;
  name: string;
  slug?: string;
  description?: string;
  icon?: string;
  vendors_count?: number;
  created_at: string;
  updated_at: string;
  is_active: boolean;
}

interface CategoryResponse {
  data: Category[];
  total: number;
  current_page: number;
  per_page: number;
  last_page: number;
}

const loading = ref(true);
const submitting = ref(false);
const deleting = ref(false);
const currentPage = ref(1);
const itemsPerPage = 10;
const totalItems = ref(0);
const isCategoryModalOpen = ref(false);
const isDeleteModalOpen = ref(false);
const isEditing = ref(false);
const categoryToDelete = ref<Category | null>(null);
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const categoryForm = ref({
  id: null as number | null,
  name: '',
  description: '',
  icon: 'heroicons:tag',
  isActive: true
});

const categories = ref<Category[]>([]);

const totalPages = computed(() => Math.ceil(totalItems.value / itemsPerPage));

const paginationStart = computed(() => {
  if (totalItems.value === 0) return 0;
  return (currentPage.value - 1) * itemsPerPage + 1;
});

const paginationEnd = computed(() => {
  return Math.min(currentPage.value * itemsPerPage, totalItems.value);
});

const displayedPages = computed(() => {
  const pages = [];
  const maxPagesToShow = 5;

  if (totalPages.value <= maxPagesToShow) {
    for (let i = 1; i <= totalPages.value; i++) {
      pages.push(i);
    }
  } else {
    const leftSide = Math.floor(maxPagesToShow / 2);
    const rightSide = maxPagesToShow - leftSide - 1;

    if (currentPage.value > leftSide && currentPage.value < totalPages.value - rightSide) {
      for (let i = currentPage.value - leftSide; i <= currentPage.value + rightSide; i++) {
        pages.push(i);
      }
    } else if (currentPage.value <= leftSide) {
      for (let i = 1; i <= maxPagesToShow; i++) {
        pages.push(i);
      }
    } else {
      for (let i = totalPages.value - maxPagesToShow + 1; i <= totalPages.value; i++) {
        pages.push(i);
      }
    }
  }

  return pages;
});

const fetchCategories = async (): Promise<void> => {
  try {
    loading.value = true;
    const params = new URLSearchParams();
    params.append('page', currentPage.value.toString());
    params.append('per_page', itemsPerPage.toString());

    const response = await httpClient.get<CategoryResponse>(
      `${ENDPOINTS.SERVICES.GET}?${params.toString()}`
    );

    categories.value = response.data || [];
    totalItems.value = response.total || 0;
    currentPage.value = response.current_page || 1;
  } catch (error) {
    console.error('Error fetching categories:', error);
    $toast.error('Failed to load categories');
    categories.value = [];
    totalItems.value = 0;
  } finally {
    loading.value = false;
  }
};

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const truncateText = (text: string, maxLength: number): string => {
  if (!text) return '';
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
};

const generateSlug = (name: string): string => {
  return name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
};

const prevPage = (): void => {
  if (currentPage.value > 1) {
    currentPage.value--;
    fetchCategories();
  }
};

const nextPage = (): void => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
    fetchCategories();
  }
};

const goToPage = (page: number): void => {
  currentPage.value = page;
  fetchCategories();
};

const openAddCategoryModal = (): void => {
  isEditing.value = false;
  categoryForm.value = {
    id: null,
    name: '',
    description: '',
    icon: 'heroicons:tag',
    isActive: true
  };
  isCategoryModalOpen.value = true;
};

const editCategory = (category: Category): void => {
  isEditing.value = true;
  categoryForm.value = {
    id: category.id,
    name: category.name,
    description: category.description || '',
    icon: category.icon || 'heroicons:tag',
    isActive: category.is_active
  };
  isCategoryModalOpen.value = true;
};

const closeCategoryModal = (): void => {
  isCategoryModalOpen.value = false;
};

const saveCategory = async (): Promise<void> => {
  try {
    submitting.value = true;

    const payload = {
      name: categoryForm.value.name,
      description: categoryForm.value.description,
      icon: categoryForm.value.icon,
      is_active: categoryForm.value.isActive
    };

    if (isEditing.value && categoryForm.value.id) {
      await httpClient.put(`${ENDPOINTS.SERVICES.UPDATE}/${categoryForm.value.id}`, payload);
      $toast.success('Category updated successfully');
    } else {
      await httpClient.post(ENDPOINTS.SERVICES.CREATE, payload);
      $toast.success('Category created successfully');
    }

    closeCategoryModal();
    await fetchCategories();
  } catch (error: any) {
    console.error('Error saving category:', error);
    $toast.error(error.response?.data?.message || 'Failed to save category');
  } finally {
    submitting.value = false;
  }
};

const confirmDeleteCategory = (category: Category): void => {
  categoryToDelete.value = category;
  isDeleteModalOpen.value = true;
};

const closeDeleteModal = (): void => {
  isDeleteModalOpen.value = false;
  categoryToDelete.value = null;
};

const deleteCategory = async (): Promise<void> => {
  if (!categoryToDelete.value) return;

  try {
    deleting.value = true;
    await httpClient.delete(`${ENDPOINTS.SERVICES.DELETE}/${categoryToDelete.value.id}`);
    $toast.success(`Category "${categoryToDelete.value.name}" deleted successfully`);
    closeDeleteModal();
    await fetchCategories();
  } catch (error: any) {
    console.error('Error deleting category:', error);
    $toast.error(error.response?.data?.message || 'Failed to delete category');
  } finally {
    deleting.value = false;
  }
};

onMounted(() => {
  fetchCategories();
});
</script>
