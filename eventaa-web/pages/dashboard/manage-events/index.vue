<template>
  <div class="h-screen p-6 bg-gray-100 dark:bg-zinc-900">
    <div class="mb-6 flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
          Event Management
        </h1>
        <p class="text-gray-500 dark:text-gray-400">
          Manage your events, bookings, and attendees
        </p>
      </div>
      <div class="flex space-x-2">
        <button
          @click="fetchUserEvents"
          class="p-2.5 flex items-center justify-center rounded-full bg-white dark:bg-zinc-800 border border-gray-300 dark:border-zinc-600 shadow-sm hover:bg-gray-50 dark:hover:bg-zinc-700 text-gray-700 dark:text-gray-200"
        >
          <Icon icon="famicons:refresh-outline" class="w-5 h-5" />
        </button>
        <button
          @click="exportToPDF"
          class="p-2.5 flex items-center justify-center rounded-full bg-white dark:bg-zinc-800 border border-gray-300 dark:border-zinc-600 shadow-sm hover:bg-gray-50 dark:hover:bg-zinc-700 text-gray-700 dark:text-gray-200"
        >
          <Icon icon="vscode-icons:file-type-pdf2" class="w-5 h-5" />
        </button>
        <button
          @click="exportToExcel"
          class="p-2.5 flex items-center justify-center rounded-full bg-white dark:bg-zinc-800 border border-gray-300 dark:border-zinc-600 shadow-sm hover:bg-gray-50 dark:hover:bg-zinc-700 text-gray-700 dark:text-gray-200"
        >
          <Icon icon="vscode-icons:file-type-excel" class="w-5 h-5" />
        </button>
        <CorePrimaryButton
          @click="$router.push('/dashboard/manage-events/create')"
          text="Create Event"
          start-icon="heroicons:plus"
        />
      </div>
    </div>

    <div class="bg-white dark:bg-zinc-800 p-4 shadow mb-6">
      <div
        class="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4"
      >
        <div class="flex-1">
          <CoreSearchBar
            v-model="searchQuery"
            placeholder="Search by title, description, location..."
            :max-history-items="10"
          />
        </div>
        <CoreSelect :options="statusOptions" v-model="selectedStatus" />
        <CoreSelect :options="dateRangeOptions" v-model="selectedDateRange" />
        <div class="relative">
          <button
            v-if="hasActiveFilters"
            @click="clearFilters"
            class="p-2.5 flex items-center justify-center rounded-full bg-white dark:bg-zinc-800 border border-gray-300 dark:border-zinc-600 shadow-sm hover:bg-gray-50 dark:hover:bg-zinc-700 text-gray-700 dark:text-gray-200 relative"
          >
            <Icon icon="heroicons:funnel" class="w-5 h-5" />
            <span class="absolute -top-2 -right-2 inline-flex items-center justify-center w-5 h-5 text-xs font-medium text-white bg-red-500 rounded-full">
              {{ activeFiltersCount }}
            </span>
          </button>
        </div>
      </div>
    </div>

    <div class="bg-white dark:bg-zinc-800 shadow">
      <div v-if="loading" class="flex justify-center items-center py-20">
        <CoreLoader />
      </div>
      <div v-else>
        <div class="overflow-x-auto">
          <table
            class="min-w-full divide-y divide-gray-200 dark:divide-zinc-700"
          >
            <thead>
              <tr>
                <th
                  class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                >
                  Title
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                >
                  Location
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                >
                  Start Date
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                >
                  End Date
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                >
                  Status
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                >
                  Attendees
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                >
                  Actions
                </th>
              </tr>
            </thead>
            <tbody
              class="bg-white dark:bg-zinc-800 divide-y divide-gray-200 dark:divide-zinc-700"
            >
              <tr
                v-for="event in filteredEvents"
                :key="event.id"
                class="hover:bg-gray-50 dark:hover:bg-zinc-700"
              >
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900 dark:text-white">
                    {{ event.title }}
                  </div>
                  <div class="text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs">
                    {{ event.description?.replace(/<[^>]*>/g, "") || "" }}
                  </div>
                </td>
                <td
                  class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"
                >
                  {{ event.location }}
                </td>
                <td
                  class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"
                >
                  {{ event.start }}
                </td>
                <td
                  class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"
                >
                  {{ event.end }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    class="px-2 rounded-full inline-flex text-xs leading-5 font-semibold"
                    :class="{
                      'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200':
                        event.status === 'Published' || event.status === 'Live',
                      'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200':
                        event.status === 'Upcoming' || event.status === 'Draft',
                      'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200':
                        event.status === 'Completed',
                      'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200':
                        event.status === 'Cancelled',
                    }"
                  >
                    {{ event.status }}
                  </span>
                </td>
                <td
                  class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"
                >
                  {{ event.attendees_count || 0 }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <EventsDashboardView :event="event" />
                    <button
                      @click="$router.push(`/dashboard/manage-events/edit/${event.slug}`)"
                      class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300"
                    >
                      <Icon icon="heroicons:pencil" class="w-5 h-5" />
                    </button>
                    <EventsDeleteDialog
                      :event="event"
                      @onEventDeleted="fetchUserEvents()"
                    />
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div
          class="bg-white dark:bg-zinc-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-zinc-700 sm:px-6"
        >
          <div class="flex-1 flex justify-between sm:hidden">
            <button
              @click="prevPage"
              :disabled="currentPage === 1"
              class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-zinc-600 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-zinc-700 hover:bg-gray-50 dark:hover:bg-zinc-600"
            >
              Previous
            </button>
            <button
              @click="nextPage"
              :disabled="currentPage === totalPages"
              class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-zinc-600 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-zinc-700 hover:bg-gray-50 dark:hover:bg-zinc-600"
            >
              Next
            </button>
          </div>
          <div
            class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"
          >
            <div>
              <p class="text-sm text-gray-700 dark:text-gray-300">
                Showing
                <span class="font-medium">{{ paginationStart }}</span> to
                <span class="font-medium">{{ paginationEnd }}</span> of
                <span class="font-medium">{{ totalItems }}</span> events
              </p>
            </div>
            <div>
              <CorePagination
                :current-page="currentPage"
                :total-pages="totalPages"
                :total-items="totalItems"
                :per-page="itemsPerPage"
                @page-change="handlePageChange"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Event Details Modal -->
    <TransitionRoot appear :show="viewDialogOpen" as="template">
      <Dialog as="div" @close="closeViewDialog" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black/25 dark:bg-black/50" />
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex min-h-full items-center justify-center p-4 text-center">
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel
                class="w-full max-w-3xl transform overflow-hidden bg-white dark:bg-zinc-800 text-left align-middle shadow-xl transition-all"
              >
                <div class="px-6 py-4 border-b border-gray-200 dark:border-zinc-700">
                  <DialogTitle
                    as="h3"
                    class="text-lg font-medium leading-6 text-gray-900 dark:text-gray-100"
                  >
                    {{ selectedEvent?.title }}
                  </DialogTitle>
                </div>

                <div class="p-6">
                  <div class="space-y-4">
                    <div>
                      <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Description
                      </h4>
                      <div
                        class="mt-1 text-sm text-gray-500 dark:text-gray-400"
                        v-html="selectedEvent?.description"
                      ></div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Location
                        </h4>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                          {{ selectedEvent?.location }}
                        </p>
                      </div>

                      <div>
                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Status
                        </h4>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                          {{ selectedEvent?.status }}
                        </p>
                      </div>

                      <div>
                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Start Date
                        </h4>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                          {{ selectedEvent?.start }}
                        </p>
                      </div>

                      <div>
                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">
                          End Date
                        </h4>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                          {{ selectedEvent?.end }}
                        </p>
                      </div>

                      <div>
                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Published At
                        </h4>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                          {{ selectedEvent?.published_at }}
                        </p>
                      </div>

                      <div>
                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Date Created
                        </h4>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                          {{ selectedEvent?.created_at }}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="px-6 py-4 bg-gray-50 dark:bg-zinc-700 flex justify-end">
                  <CorePrimaryButton @click="closeViewDialog" text="Close" />
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
import {
  Dialog,
  DialogPanel,
  DialogTitle,
  TransitionRoot,
  TransitionChild,
} from "@headlessui/vue";
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import dayjs from "dayjs";
import type { EventItem } from "@/types";
import type { SelectOption } from "@/components/core/Select.vue";
import { ENDPOINTS } from "@/utils/api";

definePageMeta({
  layout: "dashboard",
});

useHead({
  title: "Event Management - EventaHub Malawi",
});

const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const loading = ref<boolean>(false);
const events = ref<EventItem[]>([]);
const searchQuery = ref<string>("");
const currentPage = ref<number>(1);
const itemsPerPage = ref<number>(25);
const totalItems = ref<number>(0);
const viewDialogOpen = ref<boolean>(false);

interface DisplayEventItem extends EventItem {
  status?: string;
}

const selectedEvent = ref<DisplayEventItem | null>(null);

const statusOptions: SelectOption[] = [
  { value: "", label: "All Status" },
  { value: "published", label: "Published" },
  { value: "draft", label: "Draft" },
  { value: "cancelled", label: "Cancelled" },
];

const dateRangeOptions: SelectOption[] = [
  { value: "", label: "All Dates" },
  { value: "today", label: "Today" },
  { value: "week", label: "This Week" },
  { value: "month", label: "This Month" },
  { value: "upcoming", label: "Upcoming" },
  { value: "past", label: "Past Events" },
];

const selectedStatus = ref<SelectOption>(statusOptions[0]);
const selectedDateRange = ref<SelectOption>(dateRangeOptions[0]);

const formattedEvents = computed(() => {
  return events.value.map((event) => {
    return {
      ...event,
      start: dayjs(event.start).format("DD/MM/YYYY HH:mm"),
      end: dayjs(event.end).format("DD/MM/YYYY HH:mm"),
      created_at: dayjs(event.created_at).format("DD/MM/YYYY"),
      published_at: event.published_at
        ? dayjs(event.published_at).format("DD/MM/YYYY")
        : "Not Published",
      status: getEventStatus(event),
    };
  });
});

const filteredEvents = computed(() => {
  let filtered = [...formattedEvents.value];

  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase().trim();
    filtered = filtered.filter(
      (event) =>
        event.title?.toLowerCase().includes(query) ||
        event.description?.toLowerCase().includes(query) ||
        event.location?.toLowerCase().includes(query)
    );
  }

  if (selectedStatus.value.value) {
    const statusVal = String(selectedStatus.value.value).toLowerCase();
    filtered = filtered.filter(
      (event) => event.status?.toLowerCase() === statusVal
    );
  }

  if (selectedDateRange.value.value) {
    const now = dayjs();
    filtered = filtered.filter((event) => {
      const eventStart = dayjs(event.start, "DD/MM/YYYY HH:mm");

      switch (selectedDateRange.value.value) {
        case "today":
          return eventStart.isSame(now, "day");
        case "week":
          return (
            eventStart.isAfter(now.startOf("week")) &&
            eventStart.isBefore(now.endOf("week"))
          );
        case "month":
          return (
            eventStart.isAfter(now.startOf("month")) &&
            eventStart.isBefore(now.endOf("month"))
          );
        case "upcoming":
          return eventStart.isAfter(now);
        case "past":
          return eventStart.isBefore(now);
        default:
          return true;
      }
    });
  }

  return filtered;
});

const hasActiveFilters = computed(() => {
  return (
    searchQuery.value.trim() !== "" ||
    selectedStatus.value.value !== "" ||
    selectedDateRange.value.value !== ""
  );
});

const activeFiltersCount = computed(() => {
  let count = 0;
  if (searchQuery.value.trim()) count++;
  if (selectedStatus.value.value) count++;
  if (selectedDateRange.value.value) count++;
  return count;
});

const totalPages = computed(() => Math.ceil(filteredEvents.value.length / itemsPerPage.value));

const paginationStart = computed(() => {
  if (filteredEvents.value.length === 0) return 0;
  return (currentPage.value - 1) * itemsPerPage.value + 1;
});

const paginationEnd = computed(() => {
  return Math.min(currentPage.value * itemsPerPage.value, filteredEvents.value.length);
});

const getEventStatus = (event: EventItem): string => {
  const now = dayjs();
  const eventStart = dayjs(event.start);
  const eventEnd = dayjs(event.end);

  if (!event.published_at) return "Draft";
  if (eventEnd.isBefore(now)) return "Completed";
  if (eventStart.isAfter(now)) return "Upcoming";
  if (eventStart.isBefore(now) && eventEnd.isAfter(now)) return "Live";
  return "Published";
};

const fetchUserEvents = async (): Promise<void> => {
  loading.value = true;
  try {
    const response = await httpClient.get<any>(ENDPOINTS.EVENTS.USER);

    if (response) {
      events.value = response.events.data;
      totalItems.value = response.events.total;
    }
  } catch (error) {
    $toast.error("An error occurred while fetching events.");
  } finally {
    loading.value = false;
  }
};

const viewEvent = (event: DisplayEventItem): void => {
  selectedEvent.value = event;
  viewDialogOpen.value = true;
};

const closeViewDialog = (): void => {
  viewDialogOpen.value = false;
  selectedEvent.value = null;
};

const deleteEvent = async (event: DisplayEventItem): Promise<void> => {
  if (confirm(`Are you sure you want to delete "${event.title}"?`)) {
    try {
      await httpClient.delete(`${ENDPOINTS.EVENTS.BASE}/${event.id}`);
      $toast.success("Event deleted successfully!");
      await fetchUserEvents();
    } catch (error) {
      $toast.error("Failed to delete event.");
    }
  }
};

const handlePageChange = (page: number): void => {
  currentPage.value = page;
};

const prevPage = (): void => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

const nextPage = (): void => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};

const exportToPDF = async (): Promise<void> => {
  loading.value = true;
  try {
    $toast.info('Generating PDF...');

    const doc = new jsPDF();
    const tableColumn = ["Title", "Location", "Start Date", "End Date", "Status", "Attendees", "Likes"];
    const tableRows: any[] = [];

    formattedEvents.value.forEach(event => {
      const eventData = [
        event.title,
        event.location,
        event.start,
        event.end,
        event.status || 'N/A',
        event.attendees_count || 0,
        event.likes_count || 0
      ];
      tableRows.push(eventData);
    });

    // Add title
    doc.setFontSize(20);
    doc.text('Events Export', 14, 22);
    doc.setFontSize(12);
    doc.text(`Generated on: ${dayjs().format('MMMM D, YYYY')}`, 14, 32);
    doc.text(`Total Events: ${formattedEvents.value.length}`, 14, 42);

    autoTable(doc, {
      head: [tableColumn],
      body: tableRows,
      startY: 50,
      theme: 'grid',
      styles: {
        fontSize: 10,
        cellPadding: 3,
        overflow: 'linebreak',
      },
      headStyles: {
        fillColor: [220, 38, 38],
        textColor: 255,
        fontStyle: 'bold',
      },
      alternateRowStyles: {
        fillColor: [245, 245, 245]
      }
    });

    doc.save(`events-export-${dayjs().format('YYYY-MM-DD')}.pdf`);
    $toast.success('PDF exported successfully!');
  } catch (error) {
    console.error('Export error:', error);
    $toast.error('An error occurred while exporting to PDF.');
  } finally {
    loading.value = false;
  }
};

const exportToExcel = async (): Promise<void> => {
  loading.value = true;
  try {
    interface ExportRow {
      Title: string;
      Description: string;
      Location: string;
      "Start Date": string;
      "End Date": string;
      Status: string;
      "Published At": string;
      "Date Created": string;
      Attendees: number;
      Likes: number;
      Category: string;
    }

    const dataToExport: ExportRow[] = formattedEvents.value.map((event) => ({
      Title: event.title,
      Description: event.description?.replace(/<[^>]*>/g, "") || "",
      Location: event.location,
      "Start Date": event.start,
      "End Date": event.end,
      Status: event.status || "",
      "Published At": event.published_at,
      "Date Created": event.created_at,
      Attendees: event.attendees_count || 0,
      Likes: event.likes_count || 0,
      Category: event.category?.name || "Uncategorized",
    }));

    if (dataToExport.length === 0) {
      $toast.info("No events to export.");
      loading.value = false;
      return;
    }

    const headers = Object.keys(dataToExport[0]).join(",");
    const csv = dataToExport.map((row) =>
      Object.values(row)
        .map((value) => `"${String(value).replace(/"/g, '""')}"`)
        .join(",")
    );

    const csvContent = [headers, ...csv].join("\n");
    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");

    link.setAttribute("href", url);
    link.setAttribute(
      "download",
      `events-export-${dayjs().format("YYYY-MM-DD")}.csv`
    );
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    $toast.success("Events exported to Excel successfully!");
  } catch (error) {
    console.error("Export error:", error);
    $toast.error("An error occurred while exporting to Excel.");
  } finally {
    loading.value = false;
  }
};

const clearFilters = (): void => {
  searchQuery.value = "";
  currentPage.value = 1;
  selectedStatus.value = statusOptions[0];
  selectedDateRange.value = dateRangeOptions[0];
};

// Watch for changes in filters to reset pagination
watch([searchQuery, selectedStatus, selectedDateRange], () => {
  currentPage.value = 1;
});

onMounted(() => {
  fetchUserEvents();
});
</script>

<style lang="css" scoped></style>
