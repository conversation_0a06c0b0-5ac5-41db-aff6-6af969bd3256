<template>
    <CoreOverlay :loading="loading" text="Processing, please wait..." />
    <div class="flex flex-col min-h-screen">
        <div class="min-h-screen relative">
            <div class="w-full bg-pattern backdrop-blur-sm">
                <div class="px-4 py-4 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <h1 class="text-2xl font-semibold dark:text-white">Create Event</h1>
                    </div>
                </div>

                <div class="px-4 py-4">
                    <CoreStepper :steps="formattedSteps" :current-step="currentStep"
                        :progress-percentage="progressPercentage" />
                </div>
            </div>

            <div class="mx-5 shadow-sm bg-white dark:bg-zinc-900 dark:border-zinc-800">
                <!-- Step 1: Basic Information -->
                <EventsCreateBasicInformationForm
                    v-if="currentStep === 1"
                    ref="basicFormRef"
                    :loading="loading"
                    v-model:title="title"
                    v-model:description="description"
                    v-model:selectedCategory="selectedCategory"
                    v-model:selectedLocationType="selectedLocationType"
                    v-model:selectedVisibility="selectedVisibility"
                    v-model:dateRange="dateRange"
                    v-model:location="location"
                    v-model:meetingLink="meetingLink"
                    @submit="onFormSubmit"
                    @submit-invalid="onFormInvalid"
                    @date-cleared="isCleared"
                    @cover-selected="onCoverPicker"
                    @file-removed="onFileRemoved"
                />

                <!-- Step 2: Tickets -->
                <EventsCreateTicketsForm
                    v-else-if="currentStep === 2"
                    ref="ticketsFormRef"
                    :generation-response="generationResponse"
                    @submit="onGenerateTickets"
                    @submit-invalid="onFormInvalid"
                    @skip="onSkip(currentStep)"
                    @update:selections="updateSelections"
                    @next="onNext"
                />

                <!-- Step 3: Sponsors -->
                <EventsCreateSponsorsForm
                    v-else-if="currentStep === 3"
                    @submit="onSponsorSubmit"
                    @skip="onSkip(currentStep)"
                    @update:selectedSponsors="getSelectedSponsors"
                />

                <!-- Step 4: Publish -->
                <EventsCreatePublishForm
                    v-else-if="currentStep === 4"
                    ref="publishFormRef"
                    :event-details="eventDetails"
                    v-model:publishType="publishType"
                    v-model:scheduleDate="scheduleDate"
                    @submit="onPublishSubmit"
                />
            </div>

            <!-- Step Navigation -->
            <EventsCreateStepNavigation
                :current-step="currentStep"
                @previous="currentStep--"
                @next="navigateNextStep"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import type { Category, EventItem, Sponsor } from '@/types';
import dayjs from 'dayjs';

definePageMeta({
    layout: "dashboard",
});
useHead({
    title: "Create Event - EventaHub Malawi",
});

const currentStep = ref<number>(1);
const route = useRoute();
const routerQuery = useRouterQuery();

// Component refs
const basicFormRef = ref();
const ticketsFormRef = ref();
const publishFormRef = ref();

const title = ref<string>("");
const steps = ref([
    { name: 'Basic Information', completed: false },
    { name: 'Tickets', completed: false },
    { name: 'Sponsors', completed: false },
    { name: 'Publish', completed: false }
])

const formattedSteps = computed(() => {
    return steps.value.map((step, index) => ({
        step: index + 1,
        label: step.name,
        sublabel: step.completed ? 'Completed' : ''
    }));
});

const progressPercentage = computed(() => {
    const completedSteps = steps.value.filter(step => step.completed).length;
    return Math.round((completedSteps / steps.value.length) * 100);
});

const description = ref<string>("");
const selectedCategory = ref<Category>({ id: 0, name: "select Category", icon: "other.png" });
const selectedLocationType = ref<string>("");
const selectedVisibility = ref<string>("");
const dateRange = ref<any>([]);
const location = ref();
const coverArt = ref();
const meetingLink = ref<string>("");
const meetingIcon = ref<string>("globe");
const loading = ref<boolean>(false);
const { $toast }: any = useNuxtApp();
const httpClient = useHttpClient();
const selections = ref<any>([]);
const sponsors = ref<Sponsor[]>([]);
const generationResponse = ref<string>("");

const { start, finish } = useLoadingIndicator();

const publishType = ref<string>('immediate');
const scheduleDate = ref<string>('');
const eventDetails = ref<EventItem>()

const getLocationOnBasis = computed((): string => {
    if (location.value?.address) {
        return location.value.address;
    }
    const street = location.value?.street || '';
    const city = location.value?.city || '';
    const country = location.value?.country || '';
    const parts = [street, city, country].filter(part => part).join(', ');
    return parts || "";
});

const onSkip = (step: number) => {
    routerQuery.replaceOneQuery("step", { step: step + 1 });
    currentStep.value = step + 1;
}

const onFormInvalid = (e: any) => {
    console.log(e);
};

const stepActions = {
    1: () => basicFormRef.value?.triggerSubmit(),
    2: () => onGenerateTickets(),
    3: () => onSponsorSubmit(),
    4: () => onPublishSubmit(),
    default: () => currentStep.value++
};

const navigateNextStep = () => {
    const action = stepActions[currentStep.value as keyof typeof stepActions] || stepActions.default;
    action();
};

const onNext = (value: boolean) => {
    if (value) {
        setTimeout(() => {
            routerQuery.replaceOneQuery("step", { step: 3 });
            currentStep.value = 3;
        }, 500);
    }
}

const isCleared = (event: any): void => {
    console.log(event);
};

const updateSelections = (e: any[]): void => {
    selections.value = e;
};

const onFileRemoved = (): void => {
    $toast.warn("Cover art removed, please upload a new one");
}

const onCoverPicker = (e: File[]): void => {
    coverArt.value = e[0];
}

const getSelectedSponsors = (s: Sponsor[]): void => {
    sponsors.value = s;
}

const onFormSubmit = async (): Promise<void> => {
    loading.value = true;
    start();
    try {
        const formData = new FormData();
        formData.append("title", title.value);
        formData.append("description", description.value);
        formData.append("category", String(selectedCategory?.value?.id));
        formData.append("location", getLocationOnBasis.value || "Remote");
        formData.append("visibility", selectedVisibility.value == "Public" ? "1" : "0");
        formData.append("type", selectedLocationType.value == "Public" ? "1" : "0");
        formData.append("dateRange", dateRange.value);
        formData.append("meeting_link", meetingLink.value);
        formData.append("meetingIcon", meetingIcon.value);
        formData.append("start_date", dayjs(dateRange.value[0]).format("YYYY-MM-DD HH:mm:ss"));
        formData.append("end_date", dayjs(dateRange.value[1]).format("YYYY-MM-DD HH:mm:ss"));
        formData.append("locationType", selectedLocationType.value);
        formData.append("latitude", location?.value?.latlong?.lat ?? "");
        formData.append("longitude", location?.value?.latlong?.lng ?? "");
        formData.append("cover_art", coverArt.value);

        const response: { message: string, event: EventItem } = await httpClient.post(ENDPOINTS.EVENTS.CREATE, formData, {
            onError: (error: any) => {
                $toast.error(error.message);
            }
        });
        if (response) {
            eventDetails.value = response.event;
            $toast.success(response.message);
            steps.value[0].completed = true;
            routerQuery.replaceOneQuery("event_id", { event_id: response.event.id });
            setTimeout(() => {
                currentStep.value = 2;
                routerQuery.replaceOneQuery("step", { step: 2 });
            }, 1000)
        }
    } catch (error: any) {
        if (error) {
            const errors = error.errors;
            Object.keys(errors).forEach((key) => {
                if (Array.isArray(errors[key])) {
                    errors[key].forEach((message: string) => {
                        $toast.error(message);
                    });
                } else if (typeof errors[key] === 'string') {
                    $toast.error(errors[key]);
                }
            });
        } else if (error.message) {
            $toast.error(error.message);
        } else {
            $toast.error('Something went wrong, please try again later');
        }
        finish();
    } finally {
        loading.value = false;
        finish();
    }
};

const onGenerateTickets = async (): Promise<void> => {
    loading.value = true;
    start();
    try {
        const formData = new FormData();
        formData.append("event_id", String(route.query.event_id));
        formData.append("tickets", JSON.stringify(selections.value));

        const response = await httpClient.post<any>(ENDPOINTS.TICKETS.GENERATE, formData);
        if (response) {
            generationResponse.value = response.pdf;
            steps.value[1].completed = true;
            $toast.success(response.message);
            if (ticketsFormRef.value) {
                ticketsFormRef.value.openGenerationModal();
            }
        }
    } catch (error: any) {
        if (error) {
            const errors = error.errors;
            Object.keys(errors).forEach((key) => {
                if (Array.isArray(errors[key])) {
                    errors[key].forEach((message: string) => {
                        $toast.error(message);
                    });
                } else if (typeof errors[key] === 'string') {
                    $toast.error(errors[key]);
                }
            });
        } else if (error.message) {
            $toast.error(error.message);
        } else {
            $toast.error('Something went wrong while publishing the event');
        }

    } finally {
        loading.value = false;
        finish();
    }
}

const onSponsorSubmit = async (): Promise<void> => {
    loading.value = true;
    start();
    try {
        const formData = new FormData();
        formData.append('sponsors[]', JSON.stringify(sponsors.value.map((sponsor: Sponsor) => sponsor.id)));
        formData.append('event_id', String(route.query.event_id));
        const response = await httpClient.post<any>(ENDPOINTS.EVENTS.SPONSOR, formData);
        if (response) {
            steps.value[2].completed = true;
            $toast.success(response.message);
            currentStep.value = 4;
            routerQuery.replaceOneQuery("step", { step: 4 });
        }
    } catch (error: any) {
        if (error) {
            const errors = error.errors;
            Object.keys(errors).forEach((key) => {
                if (Array.isArray(errors[key])) {
                    errors[key].forEach((message: string) => {
                        $toast.error(message);
                    });
                } else if (typeof errors[key] === 'string') {
                    $toast.error(errors[key]);
                }
            });
        } else if (error.message) {
            $toast.error(error.message);
        } else {
            $toast.error('Something went wrong while publishing the event');
        }
    } finally {
        loading.value = false;
        finish();
    }
}

const getEventDetails = async () => {
    loading.value = true;
    start();
    try {
        const eventId = route.query.event_id as string;
        const response = await httpClient.get<{ event: EventItem }>(`${ENDPOINTS.EVENTS.READ}/${eventId}`);
        if (response) {
            eventDetails.value = response.event;
        }
    } catch (error: any) {
        if (error) {
            const errors = error.errors;
            Object.keys(errors).forEach((key) => {
                if (Array.isArray(errors[key])) {
                    errors[key].forEach((message: string) => {
                        $toast.error(message);
                    });
                } else if (typeof errors[key] === 'string') {
                    $toast.error(errors[key]);
                }
            });
        } else if (error.message) {
            $toast.error(error.message);
        } else {
            $toast.error('Something went wrong while publishing the event');
        }

    } finally {
        loading.value = false;
        finish();
    }
}

const onPublishSubmit = async () => {
    loading.value = true;
    start();
    try {
        const formData = new FormData();
        formData.append('event_id', String(route.query.event_id));
        formData.append('publish_type', publishType.value);
        if (publishType.value === 'schedule') {
            formData.append('scheduled_at', dayjs(scheduleDate.value).format('YYYY-MM-DD HH:mm:ss'));
        }

        const response = await httpClient.post<any>(ENDPOINTS.EVENTS.PUBLISH, formData);
        if (response) {
            steps.value[3].completed = true;
            $toast.success(response.message);
            if (publishFormRef.value) {
                publishFormRef.value.openPublishModal();
            }
        }
    } catch (error: any) {
        if (error) {
            const errors = error.errors;
            Object.keys(errors).forEach((key) => {
                if (Array.isArray(errors[key])) {
                    errors[key].forEach((message: string) => {
                        $toast.error(message);
                    });
                } else if (typeof errors[key] === 'string') {
                    $toast.error(errors[key]);
                }
            });
        } else if (error.message) {
            $toast.error(error.message);
        } else {
            $toast.error('Something went wrong while publishing the event');
        }
    }
    finally {
        loading.value = false;
        finish();
    }
};

watch((meetingLink), (newMeetingLink) => {
    const url = newMeetingLink.toLowerCase();
    const iconMap = {
        'zoom.us': 'zoom',
        'teams.microsoft.com': 'teams',
        'meet.google.com': 'meet',
    };
    let icon = "globe";
    for (const [keyword, mappedIcon] of Object.entries(iconMap)) {
        if (url.includes(keyword)) {
            icon = mappedIcon;
        }
    }
    meetingIcon.value = icon;
});

onMounted(() => {
    if (route.query.step) {
        currentStep.value = Number(route.query.step);
    }
});

onMounted(() => {
    if (route.query.event_id) {
        getEventDetails();
    }
});
</script>

<style lang="css" scoped>
.bg-pattern {
    background-image: radial-gradient(circle at 1px 1px, rgb(226 232 240 / 0.5) 1px, transparent 0);
    background-size: 24px 24px;
}
</style>
