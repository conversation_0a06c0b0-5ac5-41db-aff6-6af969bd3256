<template>
  <div class="dashboard-bg-main min-h-screen">
    <div class="p-6">
      <!-- Header -->
      <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-8">
        <div class="mb-4 sm:mb-0">
          <h1 class="text-2xl font-bold dashboard-text-primary mb-2">Withdrawals</h1>
          <p class="dashboard-text-muted text-lg">Manage your earnings and withdrawal requests</p>
        </div>
        <div class="flex space-x-2">
          <button
            @click="fetchBalanceAndWithdrawals"
            class="p-2.5 flex items-center justify-center rounded-full bg-white dark:bg-zinc-800 border border-gray-300 dark:border-zinc-600 shadow-sm hover:bg-gray-50 dark:hover:bg-zinc-700 text-gray-700 dark:text-gray-200"
          >
            <Icon icon="famicons:refresh-outline" class="w-5 h-5" />
          </button>
          <button
            @click="exportWithdrawals"
            class="p-2.5 flex items-center justify-center rounded-full bg-white dark:bg-zinc-800 border border-gray-300 dark:border-zinc-600 shadow-sm hover:bg-gray-50 dark:hover:bg-zinc-700 text-gray-700 dark:text-gray-200"
          >
            <img
              src="@/assets/icons/excel.png"
              alt="excel-icon"
              class="w-5 h-5 object-cover"
            />
          </button>
          <button
            @click="openWithdrawalModal"
            :disabled="!canWithdraw"
            class="dashboard-bg-primary text-white px-6 py-3 flex items-center hover:dashboard-bg-primary-hover dashboard-transition shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Icon icon="heroicons:banknotes" class="h-5 w-5 mr-2" />
            Request Withdrawal
          </button>
        </div>
      </div>

      <!-- Balance Cards -->
      <div v-if="isLoading" class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div v-for="i in 3" :key="i" class="dashboard-bg-card p-6 dashboard-shadow animate-pulse">
          <div class="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/2 mb-2"></div>
          <div class="h-8 bg-gray-300 dark:bg-gray-600 rounded w-3/4 mb-2"></div>
          <div class="h-3 bg-gray-300 dark:bg-gray-600 rounded w-1/3"></div>
        </div>
      </div>

      <div v-else class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- Available Balance -->
        <div class="dashboard-bg-card p-6 dashboard-shadow">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-100 dark:bg-green-900/20 mr-4">
              <Icon icon="heroicons:banknotes" class="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <p class="text-sm dashboard-text-muted">Available Balance</p>
              <p class="text-2xl font-bold dashboard-text-primary">{{ formatCurrency(balance.available_balance) }}</p>
              <p class="text-xs dashboard-text-muted">Ready for withdrawal</p>
            </div>
          </div>
        </div>

        <!-- Pending Balance -->
        <div class="dashboard-bg-card p-6 dashboard-shadow">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900/20 mr-4">
              <Icon icon="heroicons:clock" class="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
            </div>
            <div>
              <p class="text-sm dashboard-text-muted">Pending Balance</p>
              <p class="text-2xl font-bold dashboard-text-primary">{{ formatCurrency(balance.pending_balance) }}</p>
              <p class="text-xs dashboard-text-muted">Processing withdrawals</p>
            </div>
          </div>
        </div>

        <!-- Total Earned -->
        <div class="dashboard-bg-card p-6 dashboard-shadow">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900/20 mr-4">
              <Icon icon="heroicons:chart-bar" class="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <p class="text-sm dashboard-text-muted">Total Earned</p>
              <p class="text-2xl font-bold dashboard-text-primary">{{ formatCurrency(balance.total_earned) }}</p>
              <p class="text-xs dashboard-text-muted">All time earnings</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Withdrawal History -->
      <div class="bg-white dark:bg-zinc-800 shadow">
        <div class="px-6 py-4 dashboard-border border-b flex justify-between items-center">
          <h3 class="text-lg font-medium dashboard-text-primary">Withdrawal History</h3>
          <div class="flex space-x-2">
            <select
              v-model="statusFilter"
              @change="() => fetchWithdrawals()"
              class="dashboard-bg-input dashboard-border dashboard-text-secondary text-sm px-3 py-2 focus:dashboard-focus-primary"
            >
              <option value="">All Status</option>
              <option value="pending">Pending</option>
              <option value="processing">Processing</option>
              <option value="completed">Completed</option>
              <option value="failed">Failed</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
        </div>

        <div v-if="isLoadingWithdrawals" class="flex justify-center items-center py-20">
          <CoreLoader />
        </div>

        <div v-else-if="withdrawals.data && withdrawals.data.length > 0">
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-zinc-700">
              <thead>
                <tr>
                  <th class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Reference
                  </th>
                  <th class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Amount
                  </th>
                  <th class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Fee
                  </th>
                  <th class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Net Amount
                  </th>
                  <th class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Method
                  </th>
                  <th class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Status
                  </th>
                  <th class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Date
                  </th>
                  <th class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-zinc-800 divide-y divide-gray-200 dark:divide-zinc-700">
                <tr v-for="withdrawal in withdrawals.data" :key="withdrawal.id" class="hover:bg-gray-50 dark:hover:bg-zinc-700">
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    {{ withdrawal.reference }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {{ formatCurrency(withdrawal.amount) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {{ formatCurrency(withdrawal.fee) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    {{ formatCurrency(withdrawal.net_amount) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    <div class="capitalize">{{ withdrawal.payment_method.replace('_', ' ') }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span
                      class="px-2 py-1 text-xs font-medium rounded-full"
                      :class="getStatusClass(withdrawal.status)"
                    >
                      {{ withdrawal.status.charAt(0).toUpperCase() + withdrawal.status.slice(1) }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    <div>{{ dayjs(withdrawal.created_at).format('MMM DD, YYYY') }}</div>
                    <div class="text-xs">{{ dayjs(withdrawal.created_at).format('h:mm A') }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex space-x-2">
                      <button
                        @click="viewWithdrawal(withdrawal)"
                        class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"
                      >
                        <Icon icon="hugeicons:eye" class="w-5 h-5" />
                      </button>
                      <button
                        v-if="withdrawal.status === 'pending'"
                        @click="cancelWithdrawal(withdrawal)"
                        class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300"
                      >
                        <Icon icon="heroicons:x-mark" class="w-5 h-5" />
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <div class="bg-white dark:bg-zinc-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-zinc-700 sm:px-6">
            <div class="flex-1 flex justify-between sm:hidden">
              <button
                @click="prevPage"
                :disabled="withdrawals.current_page === 1"
                class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-zinc-600 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-zinc-700 hover:bg-gray-50 dark:hover:bg-zinc-600"
              >
                Previous
              </button>
              <button
                @click="nextPage"
                :disabled="withdrawals.current_page === withdrawals.last_page"
                class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-zinc-600 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-zinc-700 hover:bg-gray-50 dark:hover:bg-zinc-600"
              >
                Next
              </button>
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p class="text-sm text-gray-700 dark:text-gray-300">
                  Showing
                  <span class="font-medium">{{ withdrawals.from }}</span> to
                  <span class="font-medium">{{ withdrawals.to }}</span> of
                  <span class="font-medium">{{ withdrawals.total }}</span> withdrawals
                </p>
              </div>
              <div>
                <nav class="relative z-0 inline-flex shadow-sm -space-x-px" aria-label="Pagination">
                  <button
                    @click="prevPage"
                    :disabled="withdrawals.current_page === 1"
                    class="relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-zinc-600"
                  >
                    <span class="sr-only">Previous</span>
                    <Icon icon="heroicons:chevron-left" class="h-5 w-5" />
                  </button>
                  <button
                    v-for="page in displayedPages"
                    :key="page"
                    @click="goToPage(page)"
                    class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-zinc-600 dark:bg-zinc-700 text-sm font-medium"
                    :class="
                      page === withdrawals.current_page
                        ? 'z-10 bg-red-600 text-red-100 dark:text-red-50'
                        : 'text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-zinc-600'
                    "
                  >
                    {{ page }}
                  </button>
                  <button
                    @click="nextPage"
                    :disabled="withdrawals.current_page === withdrawals.last_page"
                    class="relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-zinc-600"
                  >
                    <span class="sr-only">Next</span>
                    <Icon icon="heroicons:chevron-right" class="h-5 w-5" />
                  </button>
                </nav>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="flex flex-col items-center justify-center py-20">
          <Icon icon="heroicons:banknotes" class="h-16 w-16 dashboard-text-muted mb-4" />
          <h3 class="text-lg font-medium dashboard-text-primary mb-2">No Withdrawals Yet</h3>
          <p class="dashboard-text-muted text-center max-w-md">
            Once you start earning from ticket sales, you can request withdrawals here.
          </p>
        </div>
      </div>
    </div>

    <DashboardWithdrawalModal
      :is-open="showWithdrawalModal"
      :balance="balance"
      :minimum-withdrawal="minimumWithdrawal"
      :fee-percentage="feePercentage"
      @close="closeWithdrawalModal"
      @success="handleWithdrawalSuccess"
    />

    <DashboardWithdrawalDetailsModal
      v-if="selectedWithdrawal"
      :is-open="showDetailsModal"
      :withdrawal="selectedWithdrawal"
      @close="closeDetailsModal"
    />

    <UiConfirmationDialog
      :is-open="showCancelConfirmation"
      :loading="isCancelling"
      title="Cancel Withdrawal Request"
      message="Are you sure you want to cancel this withdrawal request? This action cannot be undone."
      confirm-text="Cancel Withdrawal"
      cancel-text="Keep Request"
      icon="heroicons:exclamation-triangle"
      variant="warning"
      @close="closeCancelConfirmation"
      @confirm="confirmCancelWithdrawal"
    >
      <template #details>
        <div v-if="withdrawalToCancel" class="mt-4 p-4 dashboard-bg-input dashboard-border space-y-2">
          <div class="flex justify-between">
            <span class="text-sm font-medium dashboard-text-muted">Reference:</span>
            <span class="text-sm dashboard-text-primary">{{ withdrawalToCancel.reference }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm font-medium dashboard-text-muted">Amount:</span>
            <span class="text-sm dashboard-text-primary">{{ formatCurrency(withdrawalToCancel.amount) }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm font-medium dashboard-text-muted">Net Amount:</span>
            <span class="text-sm dashboard-text-primary">{{ formatCurrency(withdrawalToCancel.net_amount) }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm font-medium dashboard-text-muted">Status:</span>
            <span class="text-sm dashboard-text-primary capitalize">{{ withdrawalToCancel.status }}</span>
          </div>
        </div>
      </template>
    </UiConfirmationDialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { useHttpClient } from '@/composables/useHttpClient';
import { ENDPOINTS } from '@/utils/api';
import dayjs from 'dayjs';
import type {
  Withdrawal,
  WithdrawalResponse,
  WithdrawalBalance,
  WithdrawalData,
  ApiResponse
} from '@/types/api';

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

useHead({
  title: 'Withdrawals | Dashboard',
});

const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const isLoading = ref<boolean>(true);
const isLoadingWithdrawals = ref<boolean>(false);
const showWithdrawalModal = ref<boolean>(false);
const showDetailsModal = ref<boolean>(false);
const showCancelConfirmation = ref<boolean>(false);
const selectedWithdrawal = ref<Withdrawal | null>(null);
const withdrawalToCancel = ref<Withdrawal | null>(null);
const statusFilter = ref<string>('');
const isCancelling = ref<boolean>(false);

const balance = reactive<WithdrawalBalance>({
  available_balance: 0,
  pending_balance: 0,
  total_earned: 0,
  total_withdrawn: 0,
  platform_fees_collected: 0,
  last_payout_at: null,
});

const withdrawals = reactive<WithdrawalResponse>({
  data: [],
  current_page: 1,
  last_page: 1,
  per_page: 15,
  total: 0,
  from: 0,
  to: 0,
});

const minimumWithdrawal = ref<number>(1000);
const feePercentage = ref<number>(1.5);

const canWithdraw = computed<boolean>(() => {
  return balance.available_balance >= minimumWithdrawal.value;
});

const displayedPages = computed(() => {
  const pages = [];
  const maxPagesToShow = 5;

  if (withdrawals.last_page <= maxPagesToShow) {
    for (let i = 1; i <= withdrawals.last_page; i++) {
      pages.push(i);
    }
  } else {
    const leftSide = Math.floor(maxPagesToShow / 2);
    const rightSide = maxPagesToShow - leftSide - 1;

    if (
      withdrawals.current_page > leftSide &&
      withdrawals.current_page < withdrawals.last_page - rightSide
    ) {
      for (
        let i = withdrawals.current_page - leftSide;
        i <= withdrawals.current_page + rightSide;
        i++
      ) {
        pages.push(i);
      }
    } else if (withdrawals.current_page <= leftSide) {
      for (let i = 1; i <= maxPagesToShow; i++) {
        pages.push(i);
      }
    } else {
      for (
        let i = withdrawals.last_page - maxPagesToShow + 1;
        i <= withdrawals.last_page;
        i++
      ) {
        pages.push(i);
      }
    }
  }

  return pages;
});

const formatCurrency = (amount: number): string => {
  return `MK ${Number(amount).toLocaleString()}`;
};

const getStatusClass = (status: string): string => {
  switch (status) {
    case 'completed':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
    case 'processing':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
    case 'pending':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
    case 'failed':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
    case 'cancelled':
      return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
  }
};

const fetchBalanceAndWithdrawals = async (): Promise<void> => {
  try {
    isLoading.value = true;
    const response = await httpClient.get<ApiResponse<WithdrawalData>>(ENDPOINTS.WITHDRAWALS.GET);

    Object.assign(balance, response.data.balance);
    Object.assign(withdrawals, response.data.withdrawals);
    minimumWithdrawal.value = response.data.minimum_withdrawal;
    feePercentage.value = response.data.withdrawal_fee_percentage;

  } catch (error: any) {
    console.error('Error fetching withdrawal data:', error);
    $toast.error('Failed to load withdrawal data');
  } finally {
    isLoading.value = false;
  }
};

const fetchWithdrawals = async (page: number = 1): Promise<void> => {
  try {
    isLoadingWithdrawals.value = true;
    const params = new URLSearchParams({
      page: page.toString(),
    });

    if (statusFilter.value) {
      params.append('status', statusFilter.value);
    }

    const response = await httpClient.get<ApiResponse<{ withdrawals: WithdrawalResponse }>>(
      `${ENDPOINTS.WITHDRAWALS.GET}?${params.toString()}`
    );
    Object.assign(withdrawals, response.data.withdrawals);

  } catch (error: any) {
    console.error('Error fetching withdrawals:', error);
    $toast.error('Failed to load withdrawal history');
  } finally {
    isLoadingWithdrawals.value = false;
  }
};

const changePage = (page: number): void => {
  fetchWithdrawals(page);
};

const prevPage = (): void => {
  if (withdrawals.current_page > 1) {
    fetchWithdrawals(withdrawals.current_page - 1);
  }
};

const nextPage = (): void => {
  if (withdrawals.current_page < withdrawals.last_page) {
    fetchWithdrawals(withdrawals.current_page + 1);
  }
};

const goToPage = (page: number): void => {
  fetchWithdrawals(page);
};

const exportWithdrawals = async (): Promise<void> => {
  try {
    const params = new URLSearchParams();

    // Specify format as excel to get a proper Excel file
    params.append('format', 'excel');

    if (statusFilter.value) {
      params.append('status', statusFilter.value);
    }

    const response = await httpClient.get(
      `${ENDPOINTS.WITHDRAWALS.EXPORT}?${params.toString()}`,
      {
        responseType: 'blob',
      }
    ) as Blob;

    // Create download link
    const url = window.URL.createObjectURL(response);
    const link = document.createElement('a');
    link.href = url;

    // Use .xlsx extension for Excel files
    link.setAttribute(
      'download',
      `withdrawals-${new Date().toISOString().split('T')[0]}.xlsx`
    );
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);

    $toast.success('Withdrawals exported successfully');
  } catch (error: any) {
    console.error('Error exporting withdrawals:', error);
    $toast.error('Failed to export withdrawals');
  }
};

const openWithdrawalModal = (): void => {
  showWithdrawalModal.value = true;
};

const closeWithdrawalModal = (): void => {
  showWithdrawalModal.value = false;
};

const handleWithdrawalSuccess = (): void => {
  closeWithdrawalModal();
  fetchBalanceAndWithdrawals();
  $toast.success('Withdrawal request submitted successfully');
};

const viewWithdrawal = (withdrawal: Withdrawal): void => {
  selectedWithdrawal.value = withdrawal;
  showDetailsModal.value = true;
};

const closeDetailsModal = (): void => {
  showDetailsModal.value = false;
  selectedWithdrawal.value = null;
};

const openCancelConfirmation = (withdrawal: Withdrawal): void => {
  withdrawalToCancel.value = withdrawal;
  showCancelConfirmation.value = true;
};

const closeCancelConfirmation = (): void => {
  showCancelConfirmation.value = false;
  withdrawalToCancel.value = null;
  isCancelling.value = false;
};

const confirmCancelWithdrawal = async (): Promise<void> => {
  if (!withdrawalToCancel.value) return;

  try {
    isCancelling.value = true;
    await httpClient.post(`${ENDPOINTS.WITHDRAWALS.CANCEL}/${withdrawalToCancel.value.id}/cancel`);
    $toast.success('Withdrawal request cancelled successfully');
    fetchBalanceAndWithdrawals();
    closeCancelConfirmation();
  } catch (error: any) {
    console.error('Error cancelling withdrawal:', error);
    $toast.error(error.response?.data?.message || 'Failed to cancel withdrawal');
    isCancelling.value = false;
  }
};

const cancelWithdrawal = (withdrawal: Withdrawal): void => {
  openCancelConfirmation(withdrawal);
};

onMounted(() => {
  fetchBalanceAndWithdrawals();
});
</script>
