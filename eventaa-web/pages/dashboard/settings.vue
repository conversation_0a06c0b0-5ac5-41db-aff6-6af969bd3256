<template>
  <div class="min-h-screen bg-gray-100 dark:bg-zinc-900 p-6">
    <div class="max-w-7xl mx-auto">
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
          Settings
        </h1>
        <p class="text-gray-500 dark:text-gray-400 mt-2">
          Manage your account and application settings
        </p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <div class="col-span-1">
          <div
            class="bg-white dark:bg-zinc-800 shadow border-gray-200 dark:border-zinc-700 p-6"
          >
            <div class="space-y-2">
              <button
                v-for="(tab, index) in tabs"
                :key="index"
                @click="activeTab = tab.id"
                :class="[
                  'w-full text-left px-4 py-1.5 transition-all duration-200 text-gray-700 dark:text-gray-200',
                  activeTab === tab.id
                    ? 'bg-red-50 text-red-600 dark:bg-red-900/20 dark:text-red-400 border-l-4 border-red-600 shadow-sm'
                    : 'hover:bg-gray-50 dark:hover:bg-zinc-700 hover:shadow-sm',
                ]"
              >
                <div class="flex items-center">
                  <Icon :icon="tab.icon" class="h-5 w-5 mr-3" />
                  <span class="font-medium">{{ tab.name }}</span>
                </div>
              </button>
            </div>
          </div>
        </div>

        <div class="col-span-1 lg:col-span-3">
          <ProfileTab
            v-if="activeTab === 'profile'"
            :profileForm="profileForm"
            :loading="loading"
            :saving="saving"
            :uploading="uploading"
            @update-profile="updateProfile"
            @fetch-profile="fetchUserProfile"
            @photo-change="handlePhotoChange"
          />

          <PreferencesTab
            v-if="activeTab === 'preferences'"
            :loading="loading"
            :saving="saving"
            @save-preferences="savePreferences"
          />

          <NotificationsTab
            v-if="activeTab === 'notifications'"
            :userSettings="userSettings"
            :loading="loading"
            :saving="saving"
            @toggle-setting="toggleSetting"
            @save-notification-settings="saveNotificationSettings"
          />

          <SecurityTab
            v-if="activeTab === 'security'"
            :securitySettings="securitySettings"
            :sessions="sessions"
            :loading="loading"
            :saving="saving"
            @toggle-two-factor="toggleTwoFactor"
            @toggle-login-notifications="
              securitySettings.login_notifications = $event
            "
            @fetch-sessions="fetchSessions"
            @revoke-session="revokeSession"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, watch } from "vue";
import type { User } from "@/types/user";
import ProfileTab from "@/components/settings/ProfileTab.vue";
import PreferencesTab from "@/components/settings/PreferencesTab.vue";
import NotificationsTab from "@/components/settings/NotificationsTab.vue";
import SecurityTab from "@/components/settings/SecurityTab.vue";
import type { GenericResponse } from "~/types/api";
import { useAuthStore } from "~/store/auth";

definePageMeta({
  layout: "dashboard",
});

useHead({
  title: "Settings | EventaHub Malawi",
  meta: [
    { name: "description", content: "Manage your account settings, preferences, notifications, and security options" }
  ]
});

const { $toast }: any = useNuxtApp();
const httpClient = useHttpClient();
const { refreshUser } = useAuthStore();
const loading = ref<boolean>(true);
const saving = ref<boolean>(false);
const uploading = ref<boolean>(false);
const activeTab = ref<string>("profile");

const profileForm = reactive({
  name: "",
  email: "",
  phone: "",
  avatar: "",
  twitter_url: "",
  facebook_url: "",
  interests: [] as number[],
});

const passwordForm = reactive({
  current_password: "",
  password: "",
  confirm_password: "",
});

const userSettings = ref<any[]>([]);
const settingsChanges = ref<{ [key: number]: boolean }>({});

const securitySettings = reactive({
  two_factor_enabled: false,
  login_notifications: true,
});

const sessions = ref<any[]>([]);

const fetchTwoFactorStatus = async () => {
  try {
    loading.value = true;
    const response = await httpClient.get<any>(`${ENDPOINTS.TWO_FACTOR.STATUS}`);
    console.log(response)
    if (response) {
      if (typeof response.enabled === 'boolean') {
        securitySettings.two_factor_enabled = response.enabled;
      } else if (response.status === true || response.status === false) {
        securitySettings.two_factor_enabled = response.status;
      } else if (response.two_factor_enabled === true || response.two_factor_enabled === false) {
        securitySettings.two_factor_enabled = response.two_factor_enabled;
      }
    }
  } catch (error: any) {
    console.error('Error fetching two-factor status:', error);
  } finally {
    loading.value = false;
  }
};
const tabs = [
  { id: "profile", name: "Profile", icon: "iconoir:user" },
  { id: "preferences", name: "Preferences", icon: "ph:gear-fine" },
  { id: "notifications", name: "Notifications", icon: "heroicons:bell" },
  { id: "security", name: "Security", icon: "heroicons:lock-closed" },
];


const fetchUserProfile = async (): Promise<void> => {
  try {
    loading.value = true;
    const response = await httpClient.get<User>(ENDPOINTS.PROFILE.USER);

    if (response && typeof response === "object") {
      const user = response;
      profileForm.name = user.name || "";
      profileForm.email = user.email || "";
      profileForm.phone = user.phone || "";
      profileForm.avatar = user.avatar || "";
      profileForm.twitter_url = user.twitter_url || "";
      profileForm.facebook_url = user.facebook_url || "";
      profileForm.interests = user.interests?.map((i) => i.id) || [];
    } else {
      console.warn("No user data received from API, response:", response);
      $toast.error("No user data available");
    }
  } catch (error: any) {
    console.error("Error fetching profile:", error);
    $toast.error("Failed to load profile data");
  } finally {
    loading.value = false;
  }
};

const updateProfile = async (data: any) => {
  try {
    saving.value = true;

    if (!data) {
      $toast.error("No data to update");
      return;
    }

    const payload = {
      name: data.text_2 || "",
      email: data.email_4 || "",
      phone: data.tel_3 || "",
      twitter_url: data.url_5 || "",
      facebook_url: data.url_6 || "",
      interests: data.interests || [],
    };

    await httpClient.post(ENDPOINTS.PROFILE.UPDATE, payload);
    $toast.success("Profile updated successfully");
  } catch (error: any) {
    console.error("Error updating profile:", error);
    handleErrorWithToast(error, $toast);
  } finally {
    saving.value = false;
  }
};

const handlePhotoChange = async (formData: FormData | Event) => {
  if (formData instanceof FormData) {
    try {
      uploading.value = true;

      const response = await httpClient.post<GenericResponse>(
        ENDPOINTS.PROFILE.UPDATE_PHOTO,
        formData
      );

      if (response?.message) {
        await refreshUser();
        await fetchUserProfile();
        $toast.success("Profile photo updated successfully");
      }
    } catch (error: any) {
      console.error("Error uploading photo:", error);
      $toast.error(error.response?.data?.message || "Failed to upload photo");
    } finally {
      uploading.value = false;
    }
    return;
  }

  const event = formData as Event;
  if (!event || !event.target) return;

  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];

  if (!file) return;

  try {
    uploading.value = true;
    const fileFormData = new FormData();
    fileFormData.append("photo", file);

    const response = await httpClient.post<{ user: User }>(
      ENDPOINTS.PROFILE.UPDATE_PHOTO,
      fileFormData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }
    );

    if (response?.user) {
      profileForm.avatar = response.user.avatar;
      $toast.success("Profile photo updated successfully");
    }
  } catch (error: any) {
    console.error("Error uploading photo:", error);
    $toast.error(error.response?.data?.message || "Failed to upload photo");
  } finally {
    uploading.value = false;
  }
};

const updatePassword = async (data: any) => {
  if (data.password !== data.confirm_password) {
    $toast.error("Passwords do not match");
    return;
  }

  try {
    saving.value = true;
    const payload = {
      password: data.password,
      confirm_password: data.confirm_password,
    };

    await httpClient.post(ENDPOINTS.AUTH.RESET_PASSWORD, payload);
    $toast.success("Password updated successfully");
    passwordForm.current_password = "";
    passwordForm.password = "";
    passwordForm.confirm_password = "";
  } catch (error: any) {
    console.error("Error updating password:", error);
    $toast.error(error.response?.data?.message || "Failed to update password");
  } finally {
    saving.value = false;
  }
};

const fetchUserSettings = async () => {
  try {
    const response = await httpClient.get<{ settings: any[] }>(
      ENDPOINTS.SETTINGS.GET
    );

    if (response?.settings) {
      userSettings.value = response.settings;
    }
  } catch (error: any) {
    console.error("Error fetching settings:", error);
  }
};

const toggleSetting = (settingId: number, enabled: boolean) => {
  settingsChanges.value[settingId] = enabled;
  const setting = userSettings.value.find((s) => s.id === settingId);
  if (setting) {
    setting.user_enabled = enabled;
  }
};

const saveNotificationSettings = async () => {
  try {
    saving.value = true;

    const settingsToUpdate = userSettings.value.map((setting) => ({
      id: setting.id,
      enabled: setting.user_enabled,
    }));

    await httpClient.post(ENDPOINTS.SETTINGS.CREATE, {
      settings: settingsToUpdate,
    });

    settingsChanges.value = {};
    $toast.success("Notification settings updated successfully");
  } catch (error: any) {
    console.error("Error updating settings:", error);
    $toast.error(error.response?.data?.message || "Failed to update settings");
  } finally {
    saving.value = false;
  }
};

const savePreferences = async (data: any) => {
  try {
    saving.value = true;

    const payload = {
      currency_id: data.currency_id,
      show_currency_symbol: data.show_currency_symbol,
      auto_convert_prices: data.auto_convert_prices
    };

    await httpClient.post(ENDPOINTS.PROFILE.UPDATE, payload);
    $toast.success("Preferences updated successfully");
  } catch (error: any) {
    console.error("Error updating preferences:", error);
    $toast.error(error.response?.data?.message || "Failed to update preferences");
  } finally {
    saving.value = false;
  }
};

const fetchSessions = async () => {
  try {
    const response = await httpClient.get<{ sessions: any[] }>(
      ENDPOINTS.SETTINGS.SESSIONS
    );

    if (response?.sessions) {
      sessions.value = response.sessions;
    }
  } catch (error: any) {
    console.error("Error fetching sessions:", error);
  }
};

const revokeSession = async (sessionId: string) => {
  try {
    saving.value = true;

    await httpClient.post(ENDPOINTS.SETTINGS.REVOKE_SESSION, {
      session_id: sessionId,
    });

    sessions.value = sessions.value.filter((s) => s.id !== sessionId);
    $toast.success("Session revoked successfully");
  } catch (error: any) {
    console.error("Error revoking session:", error);
    $toast.error(error.response?.data?.message || "Failed to revoke session");
  } finally {
    saving.value = false;
  }
};

const toggleTwoFactor = async () => {
  try {
    saving.value = true;

    if (securitySettings.two_factor_enabled) {
      const response = await httpClient.post<any>(ENDPOINTS.TWO_FACTOR.DISABLE);
      // Check the response but fetch fresh status regardless
      $toast.success("Two-factor authentication disabled");
    } else {
      const response = await httpClient.post<any>(ENDPOINTS.TWO_FACTOR.ENABLE);
      // Check the response but fetch fresh status regardless
      $toast.success("Two-factor authentication enabled");
    }

    // Always fetch the current status from the server to ensure UI is in sync
    await fetchTwoFactorStatus();

  } catch (error: any) {
    console.error("Error toggling 2FA:", error);
    $toast.error(
      error.response?.data?.message ||
        "Failed to update two-factor authentication"
    );

    // Even after an error, refresh the status to ensure the UI is correct
    await fetchTwoFactorStatus();
  } finally {
    saving.value = false;
  }
};


onMounted(() => {
  fetchUserProfile();
  fetchUserSettings();
  fetchSessions();
  fetchTwoFactorStatus(); // Fetch 2FA status on mount

  // Create a watcher for the activeTab to refresh security data when the tab is selected
  watch(() => activeTab.value, (newTab) => {
    if (newTab === 'security') {
      fetchTwoFactorStatus();
      fetchSessions();
    }
  });
});
</script>
