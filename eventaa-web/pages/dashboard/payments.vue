<template>
  <div class="h-screen p-6 bg-gray-100 dark:bg-zinc-900">
    <div class="mb-6 flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
          Payments
        </h1>
        <p class="text-gray-500 dark:text-gray-400">
          Manage event ticket payments and transactions
        </p>
      </div>
      <div class="flex space-x-2">
        <button
          @click="fetchPaymentStats"
          class="p-2.5 flex items-center justify-center rounded-full bg-white dark:bg-zinc-800 border border-gray-300 dark:border-zinc-600 shadow-sm hover:bg-gray-50 dark:hover:bg-zinc-700 text-gray-700 dark:text-gray-200"
        >
          <Icon icon="famicons:refresh-outline" class="w-5 h-5" />
        </button>
        <button
          @click="exportTransactions"
          class="p-2.5 flex items-center justify-center rounded-full bg-white dark:bg-zinc-800 border border-gray-300 dark:border-zinc-600 shadow-sm hover:bg-gray-50 dark:hover:bg-zinc-700 text-gray-700 dark:text-gray-200"
        >
          <img
            src="@/assets/icons/excel.png"
            alt="excel-icon"
            class="w-5 h-5 object-cover"
          />
        </button>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <CoreStatsCard
        title="Total Revenue"
        :value="formatCurrency(stats.totalRevenue)"
        icon="heroicons:currency-dollar"
        icon-color="green"
        :growth="stats.revenueGrowth"
      />
      <CoreStatsCard
        title="Transactions"
        :value="stats.totalTransactions"
        icon="heroicons:shopping-cart"
        icon-color="blue"
        :growth="stats.transactionsGrowth"
      />
      <CoreStatsCard
        title="Tickets Sold"
        :value="stats.ticketsSold"
        icon="heroicons:ticket"
        icon-color="yellow"
        :growth="stats.ticketsGrowth"
      />
      <CoreStatsCard
        title="Refunds"
        :value="formatCurrency(stats.totalRefunds)"
        icon="heroicons:arrow-path"
        icon-color="red"
        :growth="stats.refundsGrowth"
      />
    </div>

    <div class="bg-white dark:bg-zinc-800 p-4 shadow mb-6">
      <div
        class="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4"
      >
        <div class="flex-1">
          <CoreSearchBar
            v-model="searchQuery"
            placeholder="Search by transaction ID, customer, or event"
            :max-history-items="10"
          />
        </div>
        <CoreSelect :options="statuses" v-model="statusFilter" />
        <CoreSelect :options="eventsOptions" v-model="eventFilter" />
        <CoreSelect :options="dates" v-model="dateFilter" />
      </div>
    </div>

    <div class="bg-white dark:bg-zinc-800 shadow">
      <div v-if="loading" class="flex justify-center items-center py-20">
        <CoreLoader />
      </div>
      <div v-else>
        <div class="overflow-x-auto">
          <table
            class="min-w-full divide-y divide-gray-200 dark:divide-zinc-700"
          >
            <thead>
              <tr>
                <th
                  class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                >
                  Transaction ID
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                >
                  Customer
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                >
                  Amount
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                >
                  Date
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                >
                  Payment Method
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                >
                  Status
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                >
                  Actions
                </th>
              </tr>
            </thead>
            <tbody
              class="bg-white dark:bg-zinc-800 divide-y divide-gray-200 dark:divide-zinc-700"
            >
              <tr
                v-for="transaction in filteredTransactions"
                :key="transaction.id"
                class="hover:bg-gray-50 dark:hover:bg-zinc-700"
              >
                <td
                  class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white"
                >
                  {{ transaction.transaction_id || transaction.transactionId }}
                </td>

                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900 dark:text-white">
                    {{ transaction.user?.name }}
                  </div>
                  <div class="text-sm text-gray-500 dark:text-gray-400">
                    {{
                      transaction.user?.email
                    }}
                  </div>
                </td>
                <td
                  class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"
                >
                  {{ formatCurrency(parseFloat(transaction.amount), transaction.currency) }}
                </td>
                <td
                  class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"
                >
                  {{
                    formatDate(transaction.date || transaction.created_at || "")
                  }}
                </td>
                <td
                  class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"
                >
                  <div class="flex items-center" v-if="getPaymentMethodName(transaction).toString().toLowerCase() !== 'unknown'">
                    <img
                      :src="getPaymentMethodLogo(transaction)"
                      :alt="getPaymentMethodName(transaction).toString()"
                      class="w-5 h-5 mr-2 object-contain"
                    />
                    {{ getPaymentMethodName(transaction) }}
                  </div>
                  <div v-else class="text-red-600">
                    <Icon icon="material-symbols:error-outline-rounded" class="w-4 h-4"/>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    class="px-2 rounded-full inline-flex text-xs leading-5 font-semibold"
                    :class="{
                      'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200':
                        transaction.status === 'completed',
                      'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200':
                        transaction.status === 'pending',
                      'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200':
                        transaction.status === 'failed',
                      'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200':
                        transaction.status === 'refunded',
                    }"
                  >
                    {{ formatStatus(transaction.status) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <button
                      @click="viewTransactionDetails(transaction)"
                      class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"
                    >
                      <Icon icon="hugeicons:eye" class="w-5 h-5" />
                    </button>
                    <button
                      v-if="transaction.status === 'completed'"
                      @click="issueRefund(transaction)"
                      class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300"
                    >
                      <Icon icon="heroicons:arrow-path" class="w-5 h-5" />
                    </button>
                    <button
                      @click="downloadReceipt(transaction)"
                      class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300"
                    >
                      <Icon
                        icon="heroicons:document-arrow-down"
                        class="w-5 h-5"
                      />
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div
          class="bg-white dark:bg-zinc-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-zinc-700 sm:px-6"
        >
          <div class="flex-1 flex justify-between sm:hidden">
            <button
              @click="prevPage"
              :disabled="currentPage === 1"
              class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-zinc-600 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-zinc-700 hover:bg-gray-50 dark:hover:bg-zinc-600"
            >
              Previous
            </button>
            <button
              @click="nextPage"
              :disabled="currentPage === totalPages"
              class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-zinc-600 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-zinc-700 hover:bg-gray-50 dark:hover:bg-zinc-600"
            >
              Next
            </button>
          </div>
          <div
            class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"
          >
            <div>
              <p class="text-sm text-gray-700 dark:text-gray-300">
                Showing
                <span class="font-medium">{{ paginationStart }}</span> to
                <span class="font-medium">{{ paginationEnd }}</span> of
                <span class="font-medium">{{ totalItems }}</span> transactions
              </p>
            </div>
            <div>
              <nav
                class="relative z-0 inline-flex shadow-sm -space-x-px"
                aria-label="Pagination"
              >
                <button
                  @click="prevPage"
                  :disabled="currentPage === 1"
                  class="relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-zinc-600"
                >
                  <span class="sr-only">Previous</span>
                  <Icon icon="heroicons:chevron-left" class="h-5 w-5" />
                </button>
                <button
                  v-for="page in displayedPages"
                  :key="page"
                  @click="goToPage(page)"
                  class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-zinc-600 dark:bg-zinc-700 text-sm font-medium"
                  :class="
                    page === currentPage
                      ? 'z-10 bg-red-600 text-red-100 dark:text-red-50'
                      : 'text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-zinc-600'
                  "
                >
                  {{ page }}
                </button>
                <button
                  @click="nextPage"
                  :disabled="currentPage === totalPages"
                  class="relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-zinc-600"
                >
                  <span class="sr-only">Next</span>
                  <Icon icon="heroicons:chevron-right" class="h-5 w-5" />
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>

    <PaymentsTransactionDetailsDialog
      :transaction="selectedTransaction"
      :is-open="showTransactionDetails"
      @close="closeTransactionDetails"
      @refund="openRefundConfirmation"
    />

    <PaymentsRefundConfirmationDialog
      ref="refundDialog"
      :transaction="selectedTransaction"
      :is-open="showRefundConfirmation"
      @close="closeRefundConfirmation"
      @confirm="handleRefundConfirmation"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import { ENDPOINTS } from "@/utils/api";
import type { EventItem, Transaction } from "@/types";
import type { SelectOption } from "@/components/core/Select.vue";

definePageMeta({
  layout: "dashboard",
});

useHead({
  title: "Payments | EventaHub Malawi",
  meta: [
    {
      name: "description",
      content:
        "Manage ticket payments, refunds, and view transaction history for all your events",
    },
  ],
});

const loading = ref(true);
const searchQuery = ref("");
const statusFilter = ref("all");
const eventFilter = ref("all");
const dateFilter = ref("all");
const currentPage = ref(1);
const itemsPerPage = 10;
const totalItems = ref(0);
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

interface PaymentStats {
  totalRevenue: number;
  revenueGrowth: number;
  totalTransactions: number;
  transactionsGrowth: number;
  ticketsSold: number;
  ticketsGrowth: number;
  totalRefunds: number;
  refundsGrowth: number;
}

const stats = ref<PaymentStats>({
  totalRevenue: 0,
  revenueGrowth: 0,
  totalTransactions: 0,
  transactionsGrowth: 0,
  ticketsSold: 0,
  ticketsGrowth: 0,
  totalRefunds: 0,
  refundsGrowth: 0,
});

const transactions = ref<Transaction[]>([]);
const events = ref<EventItem[]>([]);
const selectedTransaction = ref<Transaction | null>(null);
const showTransactionDetails = ref<boolean>(false);
const showRefundConfirmation = ref<boolean>(false);
const refundDialog = ref();

const statuses: SelectOption[] = [
  { value: "all", label: "All Statuses" },
  { value: "completed", label: "Completed" },
  { value: "pending", label: "Pending" },
  { value: "failed", label: "Failed" },
  { value: "refunded", label: "Refunded" },
];

const dates: SelectOption[] = [
  { value: "all", label: "All Time" },
  { value: "today", label: "Today" },
  { value: "week", label: "This Week" },
  { value: "month", label: "This Month" },
  { value: "year", label: "This Year" },
];

const eventsOptions = computed(() => {
  const allOption: SelectOption = { value: "all", label: "All Events" };
  return [
    allOption,
    ...events.value.map((event: EventItem) => ({
      value: event.id,
      label: event.title,
    })),
  ];
});

const fetchPaymentStats = async () => {
  try {
    const response = await httpClient.get<PaymentStats>(
      ENDPOINTS.PAYMENTS.STATS
    );
    stats.value = response;
  } catch (error) {
    console.error("Error fetching payment stats:", error);
    $toast.error("Failed to load payment statistics");
  }
};

const fetchEvents = async () => {
  try {
    const response = await httpClient.get<{ events: { data: EventItem[] } }>(
      ENDPOINTS.EVENTS.USER
    );
    events.value = response.events.data;
  } catch (error) {
    console.error("Error fetching events:", error);
    $toast.error("Failed to load events");
  }
};

const fetchTransactions = async () => {
  try {
    loading.value = true;
    const params = new URLSearchParams({
      page: currentPage.value.toString(),
      limit: itemsPerPage.toString(),
      search: searchQuery.value,
      status: statusFilter.value !== "all" ? statusFilter.value : "",
      event: eventFilter.value !== "all" ? eventFilter.value : "",
      date_filter: dateFilter.value !== "all" ? dateFilter.value : "",
    });

    const response = await httpClient.get<{
      data: Transaction[];
      total: number;
      per_page: number;
      current_page: number;
      last_page: number;
    }>(`${ENDPOINTS.PAYMENTS.USER_RECEIVED_TRANSACTIONS}?${params.toString()}`);

    transactions.value = response.data;
    totalItems.value = response.total;
    currentPage.value = response.current_page;
  } catch (error) {
    console.error("Error fetching transactions:", error);
    $toast.error("Failed to load transactions");
  } finally {
    loading.value = false;
  }
};

const viewTransactionDetails = (transaction: Transaction) => {
  selectedTransaction.value = transaction;
  showTransactionDetails.value = true;
};

const closeTransactionDetails = () => {
  showTransactionDetails.value = false;
  selectedTransaction.value = null;
};

const openRefundConfirmation = (transaction: Transaction) => {
  selectedTransaction.value = transaction;
  showRefundConfirmation.value = true;
};

const closeRefundConfirmation = () => {
  showRefundConfirmation.value = false;
};

const handleRefundConfirmation = async (transaction: Transaction) => {
  try {
    await httpClient.post(`${ENDPOINTS.PAYMENTS.REFUND}/${transaction.id}`);
    $toast.success(
      `Refund initiated for transaction: ${
        transaction.transactionId || transaction.transaction_id
      }`
    );

    // Update transaction status locally
    const index = transactions.value.findIndex((t) => t.id === transaction.id);
    if (index !== -1) {
      transactions.value[index].status = "refunded";
    }

    // Refresh stats
    await fetchPaymentStats();

    // Close dialog
    refundDialog.value?.closeAfterRefund();
  } catch (error) {
    console.error("Error processing refund:", error);
    $toast.error("Failed to process refund");
    refundDialog.value?.setLoading(false);
  }
};



const exportTransactions = async () => {
  try {
    const response = (await httpClient.get(
      `${ENDPOINTS.PAYMENTS.USER_RECEIVED_TRANSACTIONS}/export`,
      {
        params: {
          search: searchQuery.value,
          status: statusFilter.value !== "all" ? statusFilter.value : "",
          event: eventFilter.value !== "all" ? eventFilter.value : "",
          date_filter: dateFilter.value !== "all" ? dateFilter.value : "",
        },
        responseType: "blob",
      }
    )) as Blob;

    // Create download link
    const url = window.URL.createObjectURL(response);
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute(
      "download",
      `transactions-${new Date().toISOString().split("T")[0]}.csv`
    );
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);

    $toast.success("Transactions exported successfully");
  } catch (error) {
    console.error("Error exporting transactions:", error);
    $toast.error("Failed to export transactions");
  }
};

const filteredTransactions = computed(() => {
  // Since filtering and pagination are now handled by the backend, just return the transactions as-is
  return transactions.value;
});

const totalPages = computed(() => Math.ceil(totalItems.value / itemsPerPage));

const paginationStart = computed(() => {
  if (totalItems.value === 0) return 0;
  return (currentPage.value - 1) * itemsPerPage + 1;
});

const paginationEnd = computed(() => {
  return Math.min(currentPage.value * itemsPerPage, totalItems.value);
});

const displayedPages = computed(() => {
  const pages = [];
  const maxPagesToShow = 5;

  if (totalPages.value <= maxPagesToShow) {
    for (let i = 1; i <= totalPages.value; i++) {
      pages.push(i);
    }
  } else {
    const leftSide = Math.floor(maxPagesToShow / 2);
    const rightSide = maxPagesToShow - leftSide - 1;

    if (
      currentPage.value > leftSide &&
      currentPage.value < totalPages.value - rightSide
    ) {
      for (
        let i = currentPage.value - leftSide;
        i <= currentPage.value + rightSide;
        i++
      ) {
        pages.push(i);
      }
    } else if (currentPage.value <= leftSide) {
      for (let i = 1; i <= maxPagesToShow; i++) {
        pages.push(i);
      }
    } else {
      for (
        let i = totalPages.value - maxPagesToShow + 1;
        i <= totalPages.value;
        i++
      ) {
        pages.push(i);
      }
    }
  }

  return pages;
});

function formatCurrency(amount: number, currency = "MWK") {
  const currencyCode = currency || "MWK";

  // Map currency codes to locales
  const localeMap: Record<string, string> = {
    "MWK": "en-MW",
    "USD": "en-US",
    "GBP": "en-GB",
    "EUR": "en-EU",
    "CAD": "en-CA",
    "ZAR": "en-ZA",
    "MK": "en-MW" // Handle PayChangu's MK code for Malawi Kwacha
  };

  // Default to en-US if no specific locale is found
  const locale = localeMap[currencyCode] || "en-US";

  // Convert MK to MWK for display (PayChangu uses MK)
  const displayCurrency = currencyCode === "MK" ? "MWK" : currencyCode;

  return new Intl.NumberFormat(locale, {
    style: "currency",
    currency: displayCurrency,
    minimumFractionDigits: ["MWK", "MK"].includes(currencyCode) ? 0 : 2,
  }).format(amount);
}

function formatDate(dateString: string | number | Date) {
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
}

function formatStatus(status: string) {
  return status.charAt(0).toUpperCase() + status.slice(1);
}

function getPaymentMethodLogo(transaction: Transaction) {
  if (!transaction.gateway_response?.data?.data?.mobile_money) {
    return "/assets/icons/credit-card.png";
  }

  const provider = transaction.gateway_response.data.data.mobile_money.name;

  switch (provider.toLowerCase()) {
    case "tnm mpamba":
      return "/assets/images/tnm.jpg";
    case "airtel money":
      return "/assets/images/airtel.png";
    default:
      return "/assets/icons/credit-card.png";
  }
}

function getPaymentMethodName(transaction: Transaction) {
  if (transaction.gateway_response?.data?.data?.mobile_money) {
    return transaction.gateway_response.data.data.mobile_money.name;
  }

  return transaction.payment_method || "Unknown";
}

function prevPage() {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
}

function nextPage() {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
}

function goToPage(page: number) {
  currentPage.value = page;
}

const issueRefund = (transaction: Transaction) => {
  selectedTransaction.value = transaction;
  showRefundConfirmation.value = true;
};

const downloadReceipt = async (transaction: Transaction) => {
  try {
    const response = (await httpClient.get(
      `${ENDPOINTS.PAYMENTS.RECEIPT}/${transaction.id}`,
      {
        responseType: "blob",
      }
    )) as Blob;

    const url = window.URL.createObjectURL(response);
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", `receipt-${transaction.id}.pdf`);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);

    $toast.success(
      `Receipt for transaction ${transaction.id} downloaded successfully`
    );
  } catch (error) {
    console.error("Error downloading receipt:", error);
    $toast.error("Failed to download receipt");
  }
};

watch([searchQuery, statusFilter, eventFilter, dateFilter], () => {
  currentPage.value = 1;
  fetchTransactions();
});

watch(currentPage, () => {
  fetchTransactions();
});

onMounted(async () => {
  await Promise.all([fetchPaymentStats(), fetchTransactions(), fetchEvents()]);
});
</script>
