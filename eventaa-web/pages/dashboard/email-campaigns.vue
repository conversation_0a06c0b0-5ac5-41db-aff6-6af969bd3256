<template>
  <div class="p-6 bg-gray-100">
    <div class="mb-6 flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold">Email Campaigns</h1>
        <p class="text-gray-500">Create and manage email marketing campaigns</p>
      </div>
      <div class="flex space-x-2">
        <button class="px-4 py-2 bg-white border border-gray-300 rounded shadow-sm hover:bg-gray-50 flex items-center">
          <Icon icon="heroicons:arrow-down-tray" class="w-5 h-5 text-gray-600 mr-1" />
          Export
        </button>
        <button class="px-4 py-2 bg-red-600 text-white rounded shadow-sm hover:bg-red-700 flex items-center">
          <Icon icon="heroicons:plus" class="w-5 h-5 mr-1" />
          New Campaign
        </button>
      </div>
    </div>

    <!-- Campaign Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <div class="bg-white p-4 shadow rounded">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-blue-100 mr-4">
            <Icon icon="heroicons:envelope" class="w-6 h-6 text-blue-600" />
          </div>
          <div>
            <p class="text-sm text-gray-500">Total Sent</p>
            <p class="text-2xl font-bold">{{ stats.totalSent }}</p>
            <div class="flex items-center text-sm">
              <span :class="stats.sentGrowth >= 0 ? 'text-green-600' : 'text-red-600'">
                <Icon :icon="stats.sentGrowth >= 0 ? 'heroicons:arrow-up' : 'heroicons:arrow-down'" class="w-4 h-4 inline" />
                {{ Math.abs(stats.sentGrowth) }}%
              </span>
              <span class="text-gray-500 ml-1">vs last month</span>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white p-4 shadow rounded">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-green-100 mr-4">
            <Icon icon="heroicons:envelope-open" class="w-6 h-6 text-green-600" />
          </div>
          <div>
            <p class="text-sm text-gray-500">Open Rate</p>
            <p class="text-2xl font-bold">{{ stats.openRate }}%</p>
            <div class="flex items-center text-sm">
              <span :class="stats.openRateGrowth >= 0 ? 'text-green-600' : 'text-red-600'">
                <Icon :icon="stats.openRateGrowth >= 0 ? 'heroicons:arrow-up' : 'heroicons:arrow-down'" class="w-4 h-4 inline" />
                {{ Math.abs(stats.openRateGrowth) }}%
              </span>
              <span class="text-gray-500 ml-1">vs last month</span>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white p-4 shadow rounded">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-purple-100 mr-4">
            <Icon icon="heroicons:cursor-arrow-ripple" class="w-6 h-6 text-purple-600" />
          </div>
          <div>
            <p class="text-sm text-gray-500">Click Rate</p>
            <p class="text-2xl font-bold">{{ stats.clickRate }}%</p>
            <div class="flex items-center text-sm">
              <span :class="stats.clickRateGrowth >= 0 ? 'text-green-600' : 'text-red-600'">
                <Icon :icon="stats.clickRateGrowth >= 0 ? 'heroicons:arrow-up' : 'heroicons:arrow-down'" class="w-4 h-4 inline" />
                {{ Math.abs(stats.clickRateGrowth) }}%
              </span>
              <span class="text-gray-500 ml-1">vs last month</span>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white p-4 shadow rounded">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-red-100 mr-4">
            <Icon icon="heroicons:x-circle" class="w-6 h-6 text-red-600" />
          </div>
          <div>
            <p class="text-sm text-gray-500">Unsubscribe Rate</p>
            <p class="text-2xl font-bold">{{ stats.unsubscribeRate }}%</p>
            <div class="flex items-center text-sm">
              <span :class="stats.unsubscribeRateGrowth <= 0 ? 'text-green-600' : 'text-red-600'">
                <Icon :icon="stats.unsubscribeRateGrowth <= 0 ? 'heroicons:arrow-down' : 'heroicons:arrow-up'" class="w-4 h-4 inline" />
                {{ Math.abs(stats.unsubscribeRateGrowth) }}%
              </span>
              <span class="text-gray-500 ml-1">vs last month</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white p-4 shadow mb-6">
      <div class="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
        <div class="flex-1">
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Icon icon="heroicons:magnifying-glass" class="h-5 w-5 text-gray-400" />
            </div>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search campaigns"
              class="block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
            />
          </div>
        </div>
        <div>
          <select 
            v-model="statusFilter"
            class="border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
          >
            <option value="all">All Statuses</option>
            <option value="draft">Draft</option>
            <option value="scheduled">Scheduled</option>
            <option value="sending">Sending</option>
            <option value="sent">Sent</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>
        <div>
          <select 
            v-model="dateFilter"
            class="border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
          >
            <option value="all">All Time</option>
            <option value="today">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="year">This Year</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Campaigns Table -->
    <div class="bg-white shadow">
      <div v-if="loading" class="flex justify-center items-center py-20">
        <CoreLoader />
      </div>
      <div v-else>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead>
              <tr>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Campaign</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Recipients</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Performance</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sent Date</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="campaign in filteredCampaigns" :key="campaign.id" class="hover:bg-gray-50">
                <td class="px-6 py-4">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10 bg-gray-100 rounded-full flex items-center justify-center">
                      <Icon icon="heroicons:envelope" class="h-5 w-5 text-gray-600" />
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900">{{ campaign.name }}</div>
                      <div class="text-sm text-gray-500">{{ campaign.subject }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ campaign.recipients.total }} recipients</div>
                  <div class="text-sm text-gray-500">{{ campaign.recipients.segment }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex flex-col space-y-1">
                    <div class="flex items-center text-sm">
                      <span class="text-gray-500 w-24">Open Rate:</span>
                      <div class="w-32 bg-gray-200 rounded-full h-2.5">
                        <div class="bg-green-600 h-2.5 rounded-full" :style="{ width: `${campaign.performance.openRate}%` }"></div>
                      </div>
                      <span class="ml-2 text-sm text-gray-700">{{ campaign.performance.openRate }}%</span>
                    </div>
                    <div class="flex items-center text-sm">
                      <span class="text-gray-500 w-24">Click Rate:</span>
                      <div class="w-32 bg-gray-200 rounded-full h-2.5">
                        <div class="bg-blue-600 h-2.5 rounded-full" :style="{ width: `${campaign.performance.clickRate}%` }"></div>
                      </div>
                      <span class="ml-2 text-sm text-gray-700">{{ campaign.performance.clickRate }}%</span>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(campaign.sentDate) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" 
                    :class="{
                      'bg-gray-100 text-gray-800': campaign.status === 'draft',
                      'bg-yellow-100 text-yellow-800': campaign.status === 'scheduled',
                      'bg-blue-100 text-blue-800': campaign.status === 'sending',
                      'bg-green-100 text-green-800': campaign.status === 'sent',
                      'bg-red-100 text-red-800': campaign.status === 'cancelled'
                    }">
                    {{ formatStatus(campaign.status) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <button @click="viewCampaign(campaign)" class="text-gray-600 hover:text-gray-900">
                      <Icon icon="heroicons:eye" class="w-5 h-5" />
                    </button>
                    <button 
                      v-if="campaign.status === 'draft'"
                      @click="editCampaign(campaign)" 
                      class="text-blue-600 hover:text-blue-900"
                    >
                      <Icon icon="heroicons:pencil-square" class="w-5 h-5" />
                    </button>
                    <button 
                      v-if="campaign.status === 'draft'"
                      @click="scheduleCampaign(campaign)" 
                      class="text-green-600 hover:text-green-900"
                    >
                      <Icon icon="heroicons:calendar" class="w-5 h-5" />
                    </button>
                    <button 
                      v-if="campaign.status === 'scheduled'"
                      @click="cancelCampaign(campaign)" 
                      class="text-red-600 hover:text-red-900"
                    >
                      <Icon icon="heroicons:x-circle" class="w-5 h-5" />
                    </button>
                    <button 
                      v-if="campaign.status === 'sent'"
                      @click="duplicateCampaign(campaign)" 
                      class="text-purple-600 hover:text-purple-900"
                    >
                      <Icon icon="heroicons:document-duplicate" class="w-5 h-5" />
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div class="flex-1 flex justify-between sm:hidden">
            <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              Previous
            </button>
            <button @click="nextPage" :disabled="currentPage === totalPages" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              Next
            </button>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700">
                Showing <span class="font-medium">{{ paginationStart }}</span> to <span class="font-medium">{{ paginationEnd }}</span> of <span class="font-medium">{{ totalItems }}</span> campaigns
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                  <span class="sr-only">Previous</span>
                  <Icon icon="heroicons:chevron-left" class="h-5 w-5" />
                </button>
                <button v-for="page in displayedPages" :key="page" @click="goToPage(page)" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium" :class="page === currentPage ? 'z-10 bg-red-50 border-red-500 text-red-600' : 'text-gray-500 hover:bg-gray-50'">
                  {{ page }}
                </button>
                <button @click="nextPage" :disabled="currentPage === totalPages" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                  <span class="sr-only">Next</span>
                  <Icon icon="heroicons:chevron-right" class="h-5 w-5" />
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { ENDPOINTS } from '@/utils/api';

definePageMeta({
  layout: "dashboard",
});

const loading = ref(true);
const searchQuery = ref('');
const statusFilter = ref('all');
const dateFilter = ref('all');
const currentPage = ref(1);
const itemsPerPage = 10;
const totalItems = ref(0);
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

// Mock data - would be replaced with API calls
const stats = ref({
  totalSent: 12500,
  sentGrowth: 15.3,
  openRate: 24.8,
  openRateGrowth: 2.5,
  clickRate: 3.6,
  clickRateGrowth: 1.2,
  unsubscribeRate: 0.5,
  unsubscribeRateGrowth: -0.2
});

const campaigns = ref([
  {
    id: 1,
    name: 'Summer Music Festival Announcement',
    subject: 'Get Your Tickets for Summer Music Festival 2024!',
    recipients: {
      total: 5000,
      segment: 'All Subscribers'
    },
    performance: {
      openRate: 28.5,
      clickRate: 4.2
    },
    sentDate: '2024-05-01',
    status: 'sent'
  },
  {
    id: 2,
    name: 'Tech Conference Early Bird',
    subject: 'Early Bird Tickets for Tech Conference 2024',
    recipients: {
      total: 3500,
      segment: 'Tech Enthusiasts'
    },
    performance: {
      openRate: 32.1,
      clickRate: 5.8
    },
    sentDate: '2024-05-10',
    status: 'sent'
  },
  {
    id: 3,
    name: 'Food & Wine Expo Reminder',
    subject: 'Last Chance: Food & Wine Expo This Weekend!',
    recipients: {
      total: 4200,
      segment: 'Food & Beverage Subscribers'
    },
    performance: {
      openRate: 25.7,
      clickRate: 3.9
    },
    sentDate: '2024-04-20',
    status: 'sent'
  },
  {
    id: 4,
    name: 'Monthly Newsletter - June',
    subject: 'Your June Events Newsletter',
    recipients: {
      total: 7500,
      segment: 'All Subscribers'
    },
    performance: {
      openRate: 0,
      clickRate: 0
    },
    sentDate: '2024-06-01',
    status: 'scheduled'
  },
  {
    id: 5,
    name: 'Summer Sale Promotion',
    subject: 'Summer Sale: 25% Off All Event Tickets',
    recipients: {
      total: 6000,
      segment: 'Previous Customers'
    },
    performance: {
      openRate: 0,
      clickRate: 0
    },
    sentDate: null,
    status: 'draft'
  },
  {
    id: 6,
    name: 'New Vendor Announcement',
    subject: 'Introducing Our New Premium Vendors',
    recipients: {
      total: 4800,
      segment: 'Event Organizers'
    },
    performance: {
      openRate: 0,
      clickRate: 0
    },
    sentDate: null,
    status: 'draft'
  },
  {
    id: 7,
    name: 'Feedback Survey',
    subject: 'We Value Your Feedback - Quick Survey',
    recipients: {
      total: 2500,
      segment: 'Recent Attendees'
    },
    performance: {
      openRate: 18.3,
      clickRate: 2.7
    },
    sentDate: '2024-05-15',
    status: 'sending'
  }
]);

const filteredCampaigns = computed(() => {
  let result = [...campaigns.value];
  
  // Apply search
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(c => 
      c.name.toLowerCase().includes(query) || 
      c.subject.toLowerCase().includes(query) ||
      c.recipients.segment.toLowerCase().includes(query)
    );
  }
  
  // Apply status filter
  if (statusFilter.value !== 'all') {
    result = result.filter(c => c.status === statusFilter.value);
  }
  
  // Apply date filter
  if (dateFilter.value !== 'all' && dateFilter.value !== 'custom') {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    
    switch (dateFilter.value) {
      case 'today':
        result = result.filter(c => c.sentDate && new Date(c.sentDate) >= today);
        break;
      case 'week':
        const weekStart = new Date(today);
        weekStart.setDate(today.getDate() - today.getDay());
        result = result.filter(c => c.sentDate && new Date(c.sentDate) >= weekStart);
        break;
      case 'month':
        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
        result = result.filter(c => c.sentDate && new Date(c.sentDate) >= monthStart);
        break;
      case 'year':
        const yearStart = new Date(today.getFullYear(), 0, 1);
        result = result.filter(c => c.sentDate && new Date(c.sentDate) >= yearStart);
        break;
    }
  }
  
  totalItems.value = result.length;
  
  // Apply pagination
  const start = (currentPage.value - 1) * itemsPerPage;
  const end = start + itemsPerPage;
  return result.slice(start, end);
});

const totalPages = computed(() => Math.ceil(totalItems.value / itemsPerPage));

const paginationStart = computed(() => {
  if (totalItems.value === 0) return 0;
  return (currentPage.value - 1) * itemsPerPage + 1;
});

const paginationEnd = computed(() => {
  return Math.min(currentPage.value * itemsPerPage, totalItems.value);
});

const displayedPages = computed(() => {
  const pages = [];
  const maxPagesToShow = 5;
  
  if (totalPages.value <= maxPagesToShow) {
    for (let i = 1; i <= totalPages.value; i++) {
      pages.push(i);
    }
  } else {
    const leftSide = Math.floor(maxPagesToShow / 2);
    const rightSide = maxPagesToShow - leftSide - 1;
    
    if (currentPage.value > leftSide && currentPage.value < totalPages.value - rightSide) {
      // Middle case
      for (let i = currentPage.value - leftSide; i <= currentPage.value + rightSide; i++) {
        pages.push(i);
      }
    } else if (currentPage.value <= leftSide) {
      // Near the start
      for (let i = 1; i <= maxPagesToShow; i++) {
        pages.push(i);
      }
    } else {
      // Near the end
      for (let i = totalPages.value - maxPagesToShow + 1; i <= totalPages.value; i++) {
        pages.push(i);
      }
    }
  }
  
  return pages;
});

function formatDate(dateString) {
  if (!dateString) return 'Not sent yet';
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}

function formatStatus(status) {
  return status.charAt(0).toUpperCase() + status.slice(1);
}

function prevPage() {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
}

function nextPage() {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
}

function goToPage(page) {
  currentPage.value = page;
}

function viewCampaign(campaign) {
  // Implement view campaign functionality
  console.log('View campaign:', campaign);
  $toast.info(`Viewing campaign: ${campaign.name}`);
}

function editCampaign(campaign) {
  // Implement edit campaign functionality
  console.log('Edit campaign:', campaign);
  $toast.info(`Editing campaign: ${campaign.name}`);
}

function scheduleCampaign(campaign) {
  // Implement schedule campaign functionality
  console.log('Schedule campaign:', campaign);
  campaign.status = 'scheduled';
  campaign.sentDate = '2024-06-15';
  $toast.success(`Campaign "${campaign.name}" has been scheduled for ${formatDate(campaign.sentDate)}`);
}

function cancelCampaign(campaign) {
  // Implement cancel campaign functionality
  console.log('Cancel campaign:', campaign);
  campaign.status = 'cancelled';
  $toast.success(`Campaign "${campaign.name}" has been cancelled`);
}

function duplicateCampaign(campaign) {
  // Implement duplicate campaign functionality
  console.log('Duplicate campaign:', campaign);
  
  const newCampaign = {
    id: campaigns.value.length + 1,
    name: `${campaign.name} (Copy)`,
    subject: campaign.subject,
    recipients: { ...campaign.recipients },
    performance: { openRate: 0, clickRate: 0 },
    sentDate: null,
    status: 'draft'
  };
  
  campaigns.value.push(newCampaign);
  totalItems.value = campaigns.value.length;
  
  $toast.success(`Campaign "${campaign.name}" has been duplicated`);
}

watch([searchQuery, statusFilter, dateFilter], () => {
  currentPage.value = 1; // Reset to first page when filters change
});

// Simulate API call
onMounted(() => {
  setTimeout(() => {
    loading.value = false;
  }, 1000);
});
</script>
