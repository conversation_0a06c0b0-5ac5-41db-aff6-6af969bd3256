<template>
  <div class="bg-gray-50 dark:bg-zinc-800 min-h-screen">
    <div class="p-4 sm:p-6">
      <div class="mb-6">
        <div
          class="flex flex-col md:flex-row md:items-center md:justify-between"
        >
          <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
              Reviews
            </h1>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Manage event and vendor reviews
            </p>
          </div>
          <div class="mt-4 md:mt-0 flex space-x-3">
            <button
              @click="exportReviews"
              :disabled="exportLoading"
              class="p-2.5 flex items-center justify-center rounded-full bg-white dark:bg-zinc-800 border border-gray-300 dark:border-zinc-600 shadow-sm hover:bg-gray-50 dark:hover:bg-zinc-700 text-gray-700 dark:text-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
              title="Export Reviews"
            >
              <CoreLoader v-if="exportLoading" height="20" width="20" />
              <Icon v-else icon="mdi:microsoft-excel" class="w-5 h-5 text-green-600" />
            </button>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <CoreStatsCard
          title="Average Rating"
          :value="stats.averageRating.toFixed(1)"
          icon="heroicons:star"
          icon-color="yellow"
          :growth="stats.ratingGrowth"
          growth-label="vs last month"
          :formatter="(value) => `${value} ⭐`"
        />

        <CoreStatsCard
          title="Total Reviews"
          :value="stats.totalReviews"
          icon="heroicons:chat-bubble-left-right"
          icon-color="blue"
          :growth="stats.reviewsGrowth"
          growth-label="vs last month"
        />

        <CoreStatsCard
          title="Positive Reviews"
          :value="stats.positiveReviews"
          icon="heroicons:check-circle"
          icon-color="green"
          :growth="stats.positiveGrowth"
          growth-label="vs last month"
          :formatter="(value) => `${value}%`"
        />

        <CoreStatsCard
          title="Flagged Reviews"
          :value="stats.flaggedReviews"
          icon="heroicons:flag"
          icon-color="red"
          :growth="stats.flaggedGrowth"
          growth-label="vs last month"
        />
      </div>

      <!-- Filters -->
      <div class="bg-white dark:bg-zinc-900 p-4 shadow-sm mb-6">
        <div
          class="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4"
        >
          <div class="flex-1">
            <CoreSearchBar
              v-model="searchQuery"
              placeholder="Search reviews..."
              :max-history-items="10"
            />
          </div>

          <CoreSelect
            v-model="typeFilter"
            :options="typeOptions"
            custom-class="w-32"
          />

          <CoreSelect
            v-model="ratingFilter"
            :options="ratingOptions"
            custom-class="w-32"
          />

          <CoreSelect
            v-model="statusFilter"
            :options="statusOptions"
            custom-class="w-32"
          />
        </div>
      </div>

      <!-- Reviews Table -->
      <div class="bg-white dark:bg-zinc-900 shadow-sm overflow-hidden">
        <div v-if="loading" class="flex justify-center items-center py-20">
          <CoreLoader />
        </div>
        <div v-else>
          <div class="overflow-x-auto">
            <table
              class="min-w-full divide-y divide-gray-200 dark:divide-zinc-700"
            >
              <thead class="bg-gray-50 dark:bg-zinc-800">
                <tr>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                  >
                    Reviewer
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                  >
                    Rating
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                  >
                    Review
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                  >
                    For
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                  >
                    Date
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                  >
                    Status
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                  >
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody
                class="bg-white dark:bg-zinc-900 divide-y divide-gray-200 dark:divide-zinc-700"
              >
                <tr
                  v-for="review in filteredReviews"
                  :key="review.id"
                  class="hover:bg-gray-50 hover:dark:bg-zinc-800"
                >
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-10 w-10">
                        <img
                          class="h-10 w-10 rounded-full object-cover"
                          :src="`${runtimeConfig.public.baseUrl}storage/avatars/${review.reviewer.avatar}`"
                          alt="review.reviewer.name.toLowerCase().replaceAll('', '-')"
                        />
                      </div>
                      <div class="ml-4">
                        <div
                          class="text-sm font-medium text-gray-900 dark:text-white"
                        >
                          {{ review.reviewer.name }}
                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">
                          {{ review.reviewer.email }}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex">
                      <Icon
                        v-for="i in 5"
                        :key="i"
                        :icon="
                          i <= review.rating
                            ? 'heroicons:star-solid'
                            : 'heroicons:star'
                        "
                        class="w-5 h-5"
                        :class="
                          i <= review.rating
                            ? 'text-yellow-500'
                            : 'text-gray-300 dark:text-gray-600'
                        "
                      />
                    </div>
                  </td>
                  <td class="px-6 py-4">
                    <div class="text-sm text-gray-900 dark:text-white truncate" v-html="review.content">
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900 dark:text-white">
                      {{ review.for.name }}
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                      {{ formatType(review.type) }}
                    </div>
                  </td>
                  <td
                    class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"
                  >
                    {{ formatDate(review.date) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span
                      class="px-2 inline-flex rounded-full text-xs leading-5 font-semibold"
                      :class="{
                        'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400':
                          review.status === 'published',
                        'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-400':
                          review.status === 'pending',
                        'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-400':
                          review.status === 'flagged',
                      }"
                    >
                      {{ formatStatus(review.status) }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex space-x-2">
                      <button
                        @click="viewReview(review)"
                        class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
                      >
                        <Icon icon="hugeicons:eye" class="w-5 h-5" />
                      </button>
                      <button
                        v-if="review.status === 'pending'"
                        @click="approveReview(review)"
                        class="text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300"
                      >
                        <Icon icon="heroicons:check-circle" class="w-5 h-5" />
                      </button>
                      <button
                        v-if="
                          review.status === 'pending' ||
                          review.status === 'flagged'
                        "
                        @click="rejectReview(review)"
                        class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300"
                      >
                        <Icon icon="heroicons:x-circle" class="w-5 h-5" />
                      </button>
                      <button
                        v-if="review.status === 'published'"
                        @click="flagReview(review)"
                        class="text-yellow-600 dark:text-yellow-400 hover:text-yellow-900 dark:hover:text-yellow-300"
                      >
                        <Icon icon="heroicons:flag" class="w-5 h-5" />
                      </button>
                      <button
                        @click="deleteReview(review.id)"
                        class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300"
                      >
                        <Icon icon="heroicons:trash" class="w-5 h-5" />
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <div
            class="bg-white dark:bg-zinc-900 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-zinc-700 sm:px-6"
          >
            <div class="flex-1 flex justify-between sm:hidden">
              <button
                @click="prevPage"
                :disabled="currentPage === 1"
                class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-zinc-600 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-800 hover:bg-gray-50 dark:hover:bg-zinc-700"
              >
                Previous
              </button>
              <button
                @click="nextPage"
                :disabled="currentPage === totalPages"
                class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-zinc-600 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-800 hover:bg-gray-50 dark:hover:bg-zinc-700"
              >
                Next
              </button>
            </div>
            <div
              class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"
            >
              <div>
                <p class="text-sm text-gray-700 dark:text-gray-300">
                  Showing
                  <span class="font-medium">{{ paginationStart }}</span> to
                  <span class="font-medium">{{ paginationEnd }}</span> of
                  <span class="font-medium">{{ totalItems }}</span> reviews
                </p>
              </div>
              <div>
                <CorePagination
                  :current-page="currentPage"
                  :total-pages="totalPages"
                  :total-items="totalItems"
                  :per-page="itemsPerPage"
                  @page-change="handlePageChange"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <TransitionRoot appear :show="showReviewModal" as="template">
    <Dialog as="div" @close="closeReviewModal" class="relative z-50">
      <TransitionChild
        as="template"
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-black bg-opacity-25" />
      </TransitionChild>

      <div class="fixed inset-0 overflow-y-auto">
        <div class="flex min-h-full items-center justify-center p-4 text-center">
          <TransitionChild
            as="template"
            enter="duration-300 ease-out"
            enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95"
          >
            <DialogPanel class="w-full max-w-md transform overflow-hidden bg-white dark:bg-zinc-900 p-6 text-left align-middle shadow-xl transition-all">
              <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900 dark:text-white">
                Review Details
              </DialogTitle>

              <div v-if="selectedReview" class="mt-4 space-y-4">
                <div class="flex items-center space-x-3">
                  <img
                    class="h-12 w-12 rounded-full object-cover"
                    :src="`${runtimeConfig.public.baseUrl}storage/avatars/${selectedReview.reviewer.avatar}`"
                    :alt="selectedReview.reviewer.name"
                    @error="handleImageError"
                  />
                  <div>
                    <p class="text-sm font-medium text-gray-900 dark:text-white">
                      {{ selectedReview.reviewer.name }}
                    </p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">
                      {{ selectedReview.reviewer.email }}
                    </p>
                  </div>
                </div>

                <!-- Rating -->
                <div class="flex items-center space-x-2">
                  <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Rating:</span>
                  <div class="flex">
                    <Icon
                      v-for="i in 5"
                      :key="i"
                      :icon="i <= selectedReview.rating ? 'heroicons:star-solid' : 'heroicons:star'"
                      class="w-5 h-5"
                      :class="i <= selectedReview.rating ? 'text-yellow-500' : 'text-gray-300 dark:text-gray-600'"
                    />
                  </div>
                  <span class="text-sm text-gray-600 dark:text-gray-400">({{ selectedReview.rating }}/5)</span>
                </div>

                <div>
                  <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Review:</span>
                  <div class="mt-1 p-3 bg-gray-50 dark:bg-zinc-800 rounded-md">
                    <p class="text-sm text-gray-900 dark:text-white" v-html="selectedReview.content"></p>
                  </div>
                </div>

                <div class="flex justify-between">
                  <div>
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">For:</span>
                    <p class="text-sm text-gray-900 dark:text-white">{{ selectedReview.for.name }}</p>
                    <p class="text-xs text-gray-500 dark:text-gray-400 capitalize">{{ selectedReview.type }}</p>
                  </div>
                  <div class="text-right">
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Date:</span>
                    <p class="text-sm text-gray-900 dark:text-white">{{ formatDate(selectedReview.date) }}</p>
                  </div>
                </div>

                <div>
                  <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Status:</span>
                  <span
                    class="ml-2 px-2 inline-flex rounded-full text-xs leading-5 font-semibold"
                    :class="{
                      'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400':
                        selectedReview.status === 'published',
                      'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-400':
                        selectedReview.status === 'pending',
                      'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-400':
                        selectedReview.status === 'flagged',
                    }"
                  >
                    {{ formatStatus(selectedReview.status) }}
                  </span>
                </div>
              </div>

              <!-- Actions -->
              <div class="mt-6 flex flex-wrap gap-2 justify-end">
                <button
                  v-if="selectedReview?.status === 'pending'"
                  @click="approveSelectedReview"
                  type="button"
                  class="inline-flex justify-center border border-transparent bg-green-600 px-4 py-2 text-sm font-medium text-white hover:bg-green-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-green-500 focus-visible:ring-offset-2"
                >
                  Approve
                </button>
                <button
                  v-if="selectedReview?.status === 'published'"
                  @click="flagSelectedReview"
                  type="button"
                  class="inline-flex justify-center border border-transparent bg-yellow-600 px-4 py-2 text-sm font-medium text-white hover:bg-yellow-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-yellow-500 focus-visible:ring-offset-2"
                >
                  Flag
                </button>
                <button
                  v-if="selectedReview?.status === 'pending' || selectedReview?.status === 'flagged'"
                  @click="rejectSelectedReview"
                  type="button"
                  class="inline-flex justify-center border border-transparent bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-red-500 focus-visible:ring-offset-2"
                >
                  Reject
                </button>
                <button
                  @click="closeReviewModal"
                  type="button"
                  class="inline-flex justify-center border border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-zinc-600 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"
                >
                  Close
                </button>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import { ENDPOINTS } from "~/utils/api";
import {
  Dialog,
  DialogPanel,
  DialogTitle,
  TransitionChild,
  TransitionRoot,
} from "@headlessui/vue";

definePageMeta({
  layout: "dashboard",
});

interface ReviewStats {
  averageRating: number;
  ratingGrowth: number;
  totalReviews: number;
  reviewsGrowth: number;
  positiveReviews: number;
  positiveGrowth: number;
  flaggedReviews: number;
  flaggedGrowth: number;
}

interface Reviewer {
  name: string;
  email: string;
  avatar?: string;
}

interface ReviewSubject {
  id: number;
  name: string;
}

interface Review {
  id: number;
  reviewer: Reviewer;
  rating: number;
  content: string;
  type: "event" | "vendor";
  for: ReviewSubject;
  date: string;
  status: "published" | "pending" | "flagged" | "hidden";
}



const loading = ref(true);
const exportLoading = ref(false);
const showReviewModal = ref(false);
const selectedReview = ref<Review | null>(null);
const searchQuery = ref("");
const typeFilter = ref("all");
const ratingFilter = ref("all");
const statusFilter = ref("all");
const currentPage = ref(1);
const itemsPerPage = 10;
const totalItems = ref(0);
const httpClient = useHttpClient();
const runtimeConfig = useRuntimeConfig();
const { $toast }: any = useNuxtApp();

const stats = ref<ReviewStats>({
  averageRating: 0,
  ratingGrowth: 0,
  totalReviews: 0,
  reviewsGrowth: 0,
  positiveReviews: 0,
  positiveGrowth: 0,
  flaggedReviews: 0,
  flaggedGrowth: 0,
});

const reviews = ref<Review[]>([]);

const typeOptions = [
  { value: "all", label: "All Types" },
  { value: "event", label: "Event" },
  { value: "vendor", label: "Vendor" },
];

const ratingOptions = [
  { value: "all", label: "All Ratings" },
  { value: "5", label: "5 Stars" },
  { value: "4", label: "4 Stars" },
  { value: "3", label: "3 Stars" },
  { value: "2", label: "2 Stars" },
  { value: "1", label: "1 Star" },
];

const statusOptions = [
  { value: "all", label: "All Status" },
  { value: "published", label: "Published" },
  { value: "pending", label: "Pending" },
  { value: "flagged", label: "Flagged" },
  { value: "rejected", label: "Rejected" },
];

async function fetchStats() {
  try {
    const response = await httpClient.get<any>(ENDPOINTS.REVIEWS.STATS);
    if (response) {
      stats.value = response;
    }
  } catch (error) {
    console.error("Error fetching review stats:", error);
    $toast.error("Failed to load review statistics");
  }
}

async function fetchReviews() {
  try {
    loading.value = true;
    const params = new URLSearchParams();
    params.append('page', currentPage.value.toString());
    params.append('per_page', itemsPerPage.toString());

    if (searchQuery.value) {
      params.append('search', searchQuery.value);
    }
    if (typeFilter.value !== "all") {
      params.append('type', typeFilter.value);
    }
    if (ratingFilter.value !== "all") {
      params.append('rating', ratingFilter.value);
    }
    if (statusFilter.value !== "all") {
      params.append('status', statusFilter.value);
    }

    const response = await httpClient.get<any>(
      `${ENDPOINTS.REVIEWS.GET}?${params.toString()}`
    );
    if (response && response.data) {
      reviews.value = response.data;
      totalItems.value = response.total || response.data.length;
    }
  } catch (error) {
    console.error("Error fetching reviews:", error);
    $toast.error("Failed to load reviews");
  } finally {
    loading.value = false;
  }
}

async function updateReviewStatus(reviewId: number, status: string) {
  try {
    await httpClient.put(`${ENDPOINTS.REVIEWS.UPDATE}/${reviewId}`, { status });
    await fetchReviews();
    $toast.success(`Review status updated successfully`);
  } catch (error) {
    console.error("Error updating review status:", error);
    $toast.error("Failed to update review status");
  }
}

async function deleteReview(reviewId: number) {
  try {
    await httpClient.delete(`${ENDPOINTS.REVIEWS.DELETE}/${reviewId}`);
    await fetchReviews();
    $toast.success("Review deleted successfully");
  } catch (error) {
    console.error("Error deleting review:", error);
    $toast.error("Failed to delete review");
  }
}

async function exportReviews() {
  try {
    exportLoading.value = true;
    $toast.info("Preparing export...");

    const params: any = {};

    if (searchQuery.value) {
      params.search = searchQuery.value;
    }
    if (typeFilter.value !== "all") {
      params.type = typeFilter.value;
    }
    if (ratingFilter.value !== "all") {
      params.rating = ratingFilter.value;
    }
    if (statusFilter.value !== "all") {
      params.status = statusFilter.value;
    }
    params.format = 'excel';

    const response = await httpClient.get(
      ENDPOINTS.REVIEWS.EXPORT,
      {
        params,
        responseType: 'blob',
      }
    ) as Blob;

    // Check if response is valid
    if (!response || response.size === 0) {
      throw new Error('No data available to export');
    }

    // Create download link
    const url = window.URL.createObjectURL(response);
    const link = document.createElement('a');
    link.href = url;

    const date = new Date().toISOString().split('T')[0];
    const filterSuffix = searchQuery.value ? `_${searchQuery.value.replace(/[^a-zA-Z0-9]/g, '_')}` : '';
    link.download = `reviews_export_${date}${filterSuffix}.xlsx`;

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    $toast.success("Reviews exported successfully!");
  } catch (error: any) {
    console.error("Error exporting reviews:", error);

    if (error.response?.status === 404) {
      $toast.error("Export feature not available");
    } else if (error.response?.status === 403) {
      $toast.error("You don't have permission to export reviews");
    } else if (error.response?.status === 422) {
      $toast.error("Invalid export parameters");
    } else if (error.message?.includes('No data available')) {
      $toast.error("No reviews available to export");
    } else {
      $toast.error("Failed to export reviews. Please try again.");
    }
  } finally {
    exportLoading.value = false;
  }
}

function handlePageChange(page: number) {
  currentPage.value = page;
  fetchReviews();
}

function approveReview(review: Review): void {
  updateReviewStatus(review.id, "published");
}

function rejectReview(review: Review): void {
  updateReviewStatus(review.id, "rejected");
}

function flagReview(review: Review): void {
  updateReviewStatus(review.id, "flagged");
}

watch([searchQuery, typeFilter, ratingFilter, statusFilter], () => {
  currentPage.value = 1;
  fetchReviews();
});

onMounted(async () => {
  await Promise.all([fetchStats(), fetchReviews()]);
});

const filteredReviews = computed(() => {
  // Since backend handles filtering, just return the reviews from API
  // and handle sorting if needed
  const result = [...reviews.value];

  // Sort by date (newest first) - backend should handle this but ensuring consistency
  result.sort(
    (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
  );

  return result;
});

const totalPages = computed(() => Math.ceil(totalItems.value / itemsPerPage));

const paginationStart = computed(() => {
  if (totalItems.value === 0) return 0;
  return (currentPage.value - 1) * itemsPerPage + 1;
});

const paginationEnd = computed(() => {
  return Math.min(currentPage.value * itemsPerPage, totalItems.value);
});


function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
}

function formatType(type: string) {
  return type.charAt(0).toUpperCase() + type.slice(1);
}

function formatStatus(status: string) {
  return status.charAt(0).toUpperCase() + status.slice(1);
}

function truncateText(text: string, maxLength: number) {
  if (!text) return "";
  return text.length > maxLength ? text.substring(0, maxLength) + "..." : text;
}

function prevPage() {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
}

function nextPage() {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
}


function viewReview(review: Review) {
  selectedReview.value = review;
  showReviewModal.value = true;
}

function closeReviewModal() {
  showReviewModal.value = false;
  selectedReview.value = null;
}

function handleImageError(event: Event) {
  const target = event.target as HTMLImageElement;
  if (target) {
    target.src = 'https://via.placeholder.com/48';
  }
}

function approveSelectedReview() {
  if (selectedReview.value) {
    updateReviewStatus(selectedReview.value.id, "published");
    closeReviewModal();
  }
}

function rejectSelectedReview() {
  if (selectedReview.value) {
    updateReviewStatus(selectedReview.value.id, "rejected");
    closeReviewModal();
  }
}

function flagSelectedReview() {
  if (selectedReview.value) {
    updateReviewStatus(selectedReview.value.id, "flagged");
    closeReviewModal();
  }
}
</script>
