<template>
  <div class="p-4 sm:p-6">
    <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
      <div>
        <h1 class="text-2xl font-semibold dashboard-text-primary">Permissions</h1>
        <p class="dashboard-text-secondary">Manage system permissions and access control</p>
      </div>
      <div class="mt-4 md:mt-0">
        <button
          @click="openCreatePermissionDialog"
          class="px-4 py-2 bg-red-600 text-white hover:bg-red-700 flex items-center dashboard-transition"
        >
          <Icon icon="heroicons:plus" class="h-5 w-5 mr-2" />
          Create Permission
        </button>
      </div>
    </div>

    <!-- Permissions by Category -->
    <div class="dashboard-bg-card shadow p-6 mb-6">
      <h2 class="text-lg font-semibold mb-4 dashboard-text-primary">Permissions by Category</h2>

      <div v-if="loading" class="py-8">
        <div class="animate-pulse space-y-4">
          <div class="h-10 dashboard-bg-hover"></div>
          <div class="h-10 dashboard-bg-hover"></div>
          <div class="h-10 dashboard-bg-hover"></div>
        </div>
      </div>

      <div v-else-if="Object.keys(permissionsByCategory).length === 0" class="py-8 text-center dashboard-text-secondary">
        No permissions found
      </div>

      <div v-else>
        <div v-for="(categoryPermissions, category) in permissionsByCategory" :key="category" class="mb-6">
          <div class="flex items-center mb-3">
            <h3 class="text-md font-medium dashboard-text-primary">{{ formatCategory(String(category)) }}</h3>
            <span class="ml-2 px-2 py-0.5 text-xs bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
              {{ categoryPermissions.length }}
            </span>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div
              v-for="permission in categoryPermissions"
              :key="permission.id"
              class="p-4 dashboard-bg-hover dashboard-border border"
            >
              <div class="flex justify-between">
                <div>
                  <h4 class="font-medium dashboard-text-primary">{{ permission.name }}</h4>
                  <p class="text-xs dashboard-text-secondary">{{ permission.description || permission.guard_name }}</p>
                </div>
                <div class="flex space-x-2">
                  <button
                    @click="editPermission(permission)"
                    class="p-1 hover:dashboard-bg-hover dashboard-transition"
                  >
                    <Icon icon="heroicons:pencil-square" class="h-5 w-5 dashboard-text-secondary" />
                  </button>
                  <button
                    @click="deletePermission(permission)"
                    class="p-1 hover:dashboard-bg-hover dashboard-transition"
                  >
                    <Icon icon="heroicons:trash" class="h-5 w-5 text-red-500" />
                  </button>
                </div>
              </div>
              <p v-if="permission.description" class="mt-2 text-sm dashboard-text-secondary">
                {{ permission.description }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Role Permissions -->
    <div class="dashboard-bg-card shadow p-6">
      <h2 class="text-lg font-semibold mb-4 dashboard-text-primary">Assign Permissions to Roles</h2>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label class="block text-sm font-medium dashboard-text-primary mb-2">Select Role</label>
          <select
            v-model="selectedRoleId"
            class="w-full px-3 py-2 dashboard-border border dashboard-bg-card dashboard-text-primary focus:outline-none focus:ring-red-500 focus:border-red-500"
            @change="fetchRolePermissions"
          >
            <option value="">Select a role</option>
            <option v-for="role in roles" :key="role.id" :value="role.id">{{ role.name }}</option>
          </select>
        </div>

        <div v-if="selectedRoleId">
          <label class="block text-sm font-medium dashboard-text-primary mb-2">Assigned Permissions</label>

          <div v-if="rolePermissionsLoading" class="py-4 text-center">
            <div class="inline-block animate-spin h-6 w-6 border-b-2 border-red-600"></div>
          </div>

          <div v-else-if="rolePermissions.length === 0" class="py-4 text-center dashboard-text-secondary">
            No permissions assigned to this role
          </div>

          <div v-else class="max-h-60 overflow-y-auto p-2 dashboard-bg-hover">
            <div
              v-for="permission in rolePermissions"
              :key="permission.id"
              class="flex items-center justify-between p-2 mb-1 dashboard-bg-card dashboard-border border"
            >
              <div>
                <p class="text-sm font-medium dashboard-text-primary">{{ permission.name }}</p>
                <p class="text-xs dashboard-text-secondary">{{ permission.description || permission.guard_name }}</p>
              </div>
              <button
                class="p-1 hover:dashboard-bg-hover dashboard-transition"
                @click="removePermissionFromRole(permission)"
              >
                <Icon icon="heroicons:x-mark" class="w-5 h-5 text-red-500" />
              </button>
            </div>
          </div>
        </div>
      </div>

      <div v-if="selectedRoleId" class="mt-6">
        <label class="block text-sm font-medium dashboard-text-primary mb-2">Add Permissions</label>
        <div class="flex space-x-2">
          <select
            v-model="permissionToAdd"
            class="flex-1 px-3 py-2 dashboard-border border dashboard-bg-card dashboard-text-primary focus:outline-none focus:ring-red-500 focus:border-red-500"
          >
            <option value="">Select a permission</option>
            <option
              v-for="permission in availablePermissions"
              :key="permission.id"
              :value="permission.name"
            >
              {{ permission.name }}
            </option>
          </select>
          <button
            class="px-4 py-2 bg-red-600 text-white hover:bg-red-700 dashboard-transition"
            :disabled="!permissionToAdd"
            @click="addPermissionToRole"
          >
            Add
          </button>
        </div>
      </div>
    </div>

    <!-- Create/Edit Permission Dialog -->
    <div v-if="showCreateDialog || showEditDialog" class="fixed inset-0 z-50 overflow-y-auto">
      <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
          <div class="absolute inset-0 bg-black opacity-50"></div>
        </div>

        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <div class="inline-block align-bottom dashboard-bg-card text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <form @submit.prevent="savePermission">
            <div class="dashboard-bg-card px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div class="sm:flex sm:items-start">
                <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                  <h3 class="text-lg leading-6 font-medium dashboard-text-primary" id="modal-title">
                    {{ showEditDialog ? 'Edit Permission' : 'Create New Permission' }}
                  </h3>
                  <div class="mt-4 space-y-4">
                    <div>
                      <label for="permission-name" class="block text-sm font-medium dashboard-text-primary">Permission Name</label>
                      <input
                        id="permission-name"
                        v-model="permissionForm.name"
                        type="text"
                        required
                        class="mt-1 block w-full dashboard-border border px-3 py-2 dashboard-bg-card dashboard-text-primary focus:outline-none focus:ring-red-500 focus:border-red-500"
                        placeholder="e.g., users.create"
                      />
                    </div>
                    <div>
                      <label for="permission-description" class="block text-sm font-medium dashboard-text-primary">Description (Optional)</label>
                      <textarea
                        id="permission-description"
                        v-model="permissionForm.description"
                        rows="3"
                        class="mt-1 block w-full dashboard-border border px-3 py-2 dashboard-bg-card dashboard-text-primary focus:outline-none focus:ring-red-500 focus:border-red-500"
                        placeholder="Enter permission description"
                      ></textarea>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="dashboard-bg-hover px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                type="submit"
                :disabled="saving"
                class="w-full inline-flex justify-center border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm dashboard-transition disabled:opacity-50"
              >
                {{ saving ? 'Saving...' : (showEditDialog ? 'Update Permission' : 'Create Permission') }}
              </button>
              <button
                type="button"
                class="mt-3 w-full inline-flex justify-center dashboard-border border shadow-sm px-4 py-2 dashboard-bg-card text-base font-medium dashboard-text-primary hover:dashboard-bg-hover focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dashboard-transition"
                @click="closePermissionDialog"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { ENDPOINTS } from '~/utils/api';

definePageMeta({
  layout: "dashboard",
});

interface Permission {
  id: number;
  name: string;
  guard_name: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

interface Role {
  id: number;
  name: string;
  guard_name: string;
  created_at: string;
  updated_at: string;
}

const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const loading = ref(true);
const roles = ref<Role[]>([]);
const permissions = ref<Permission[]>([]);
const selectedRoleId = ref('');
const rolePermissions = ref<Permission[]>([]);
const rolePermissionsLoading = ref(false);
const permissionToAdd = ref('');
const showCreateDialog = ref(false);
const showEditDialog = ref(false);
const saving = ref(false);
const permissionForm = ref({
  name: '',
  description: ''
});
const editingPermissionId = ref<number | null>(null);

const permissionsByCategory = computed(() => {
  const result: { [key: string]: Permission[] } = {};

  permissions.value.forEach(permission => {
    // Extract category from permission name (e.g., "users.create" -> "users")
    const parts = permission.name.split('.');
    const category = parts[0] || 'other';

    if (!result[category]) {
      result[category] = [];
    }
    result[category].push(permission);
  });

  return result;
});

const availablePermissions = computed(() => {
  return permissions.value.filter(permission =>
    !rolePermissions.value.some(rp => rp.id === permission.id)
  );
});

onMounted(async () => {
  await Promise.all([
    fetchPermissions(),
    fetchRoles()
  ]);
});

const fetchPermissions = async () => {
  loading.value = true;
  try {
    const response: any = await httpClient.get(ENDPOINTS.PERMISSIONS.GET);
    permissions.value = response.permissions || [];
  } catch (error) {
    console.error('Error fetching permissions:', error);
    $toast.error('Failed to fetch permissions');
    permissions.value = [];
  } finally {
    loading.value = false;
  }
};

const fetchRoles = async () => {
  try {
    const response: any = await httpClient.get(ENDPOINTS.ROLES.GET);
    roles.value = response.roles || [];
  } catch (error) {
    console.error('Error fetching roles:', error);
    $toast.error('Failed to fetch roles');
    roles.value = [];
  }
};

const fetchRolePermissions = async () => {
  if (!selectedRoleId.value) {
    rolePermissions.value = [];
    return;
  }

  rolePermissionsLoading.value = true;
  try {
    const response: any = await httpClient.get(`${ENDPOINTS.ROLES.SHOW}/${selectedRoleId.value}/permissions`);
    rolePermissions.value = response.permissions || [];
  } catch (error) {
    console.error('Error fetching role permissions:', error);
    $toast.error('Failed to fetch role permissions');
    rolePermissions.value = [];
  } finally {
    rolePermissionsLoading.value = false;
  }
};

const formatCategory = (category: string) => {
  return category
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

function openCreatePermissionDialog() {
  permissionForm.value = {
    name: '',
    description: ''
  };
  editingPermissionId.value = null;
  showCreateDialog.value = true;
}

function editPermission(permission: Permission) {
  permissionForm.value = {
    name: permission.name,
    description: permission.description || ''
  };
  editingPermissionId.value = permission.id;
  showEditDialog.value = true;
}

async function deletePermission(permission: Permission) {
  if (confirm(`Are you sure you want to delete the permission "${permission.name}"?`)) {
    try {
      await httpClient.delete(`${ENDPOINTS.PERMISSIONS.STORE}/${permission.id}`);
      await fetchPermissions();
      $toast.success(`Permission "${permission.name}" deleted successfully`);
    } catch (error) {
      console.error('Error deleting permission:', error);
      $toast.error('Failed to delete permission');
    }
  }
}

const addPermissionToRole = async () => {
  if (!selectedRoleId.value || !permissionToAdd.value) return;

  try {
    // Get current permissions and add the new one
    const currentPermissions = rolePermissions.value.map(p => p.name);
    const updatedPermissions = [...currentPermissions, permissionToAdd.value];

    await httpClient.post(`${ENDPOINTS.ROLES.ASSIGN_PERMISSIONS}/${selectedRoleId.value}/permissions`, {
      permissions: updatedPermissions
    });

    await fetchRolePermissions();
    $toast.success('Permission added to role');
    permissionToAdd.value = '';
  } catch (error) {
    console.error('Error adding permission to role:', error);
    $toast.error('Failed to add permission to role');
  }
};

const removePermissionFromRole = async (permission: Permission) => {
  try {
    const remainingPermissions = rolePermissions.value
      .filter(p => p.id !== permission.id)
      .map(p => p.name);

    await httpClient.post(`${ENDPOINTS.ROLES.ASSIGN_PERMISSIONS}/${selectedRoleId.value}/permissions`, {
      permissions: remainingPermissions
    });

    await fetchRolePermissions();
    $toast.success('Permission removed from role');
  } catch (error) {
    console.error('Error removing permission from role:', error);
    $toast.error('Failed to remove permission from role');
  }
};

function closePermissionDialog() {
  showCreateDialog.value = false;
  showEditDialog.value = false;
  permissionForm.value = {
    name: '',
    description: ''
  };
  editingPermissionId.value = null;
}

async function savePermission() {
  try {
    saving.value = true;

    const payload = {
      name: permissionForm.value.name,
      description: permissionForm.value.description || undefined
    };

    if (showEditDialog.value && editingPermissionId.value) {
      await httpClient.put(`${ENDPOINTS.PERMISSIONS.STORE}/${editingPermissionId.value}`, payload);
      $toast.success('Permission updated successfully');
    } else {
      await httpClient.post(ENDPOINTS.PERMISSIONS.STORE, payload);
      $toast.success('Permission created successfully');
    }

    await fetchPermissions();
    closePermissionDialog();
  } catch (error: any) {
    console.error('Error saving permission:', error);
    if (error.response?.data?.errors) {
      const errors = error.response.data.errors;
      Object.keys(errors).forEach(key => {
        errors[key].forEach((message: string) => {
          $toast.error(message);
        });
      });
    } else {
      $toast.error('Failed to save permission');
    }
  } finally {
    saving.value = false;
  }
}
</script>
