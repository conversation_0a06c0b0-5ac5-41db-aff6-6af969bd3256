<template>
  <div class="dashboard-bg-main h-screen">
    <div class="p-4 sm:p-6">
      <div class="mb-6">
        <div
          class="flex flex-col md:flex-row md:items-center md:justify-between"
        >
          <div>
            <h1 class="text-2xl font-bold dashboard-text-primary">Messages</h1>
            <p class="mt-1 text-sm dashboard-text-muted">
              Communicate with your clients and manage inquiries
            </p>
          </div>
          <div class="mt-4 md:mt-0">
            <CoreSearchBar
              v-model="searchQuery"
              placeholder="Search messages"
              :max-history-items="10"
            />
          </div>
        </div>
      </div>

      <div class="dashboard-bg-card dashboard-shadow overflow-hidden">
        <div class="flex h-[calc(100vh-180px)]">
          <div
            :class="[
              'dashboard-border border-r flex flex-col',
              showMobileChat ? 'hidden md:flex md:w-1/3' : 'w-full md:w-1/3',
            ]"
          >
            <div class="px-6 py-4 dashboard-border border-b">
              <h3 class="text-lg font-medium dashboard-text-primary">
                Conversations
              </h3>
            </div>
            <div class="overflow-y-auto flex-1">
              <div v-if="loading" class="animate-pulse">
                <div
                  v-for="i in 5"
                  :key="i"
                  class="px-6 py-4 dashboard-border-light border-b"
                >
                  <div class="flex items-center">
                    <div class="flex-shrink-0">
                      <div
                        class="h-10 w-10 dashboard-bg-primary-light rounded-full"
                      ></div>
                    </div>
                    <div class="ml-4 flex-1">
                      <div
                        class="h-4 dashboard-bg-primary-light w-1/3 mb-2"
                      ></div>
                      <div class="h-3 dashboard-bg-primary-light w-2/3"></div>
                    </div>
                  </div>
                </div>
              </div>

              <template v-else>
                <div
                  v-for="conversation in filteredConversations"
                  :key="conversation.id"
                  @click="selectConversation(conversation)"
                  class="px-6 py-4 dashboard-border-light border-b hover:dashboard-bg-hover cursor-pointer dashboard-transition"
                  :class="{
                    'bg-red-50 dark:bg-red-900/20':
                      selectedConversation?.id === conversation.id,
                  }"
                >
                  <div class="flex items-center">
                    <div class="flex-shrink-0 relative">
                      <img
                        class="h-10 w-10 rounded-full object-cover"
                        :src="conversation.avatar"
                        alt=""
                      />
                      <div
                        v-if="conversation.online"
                        class="absolute bottom-0 right-0 w-3 h-3 bg-green-400 border-2 dashboard-bg-card rounded-full"
                      ></div>
                    </div>
                    <div class="ml-4 flex-1">
                      <div class="flex items-center justify-between">
                        <h4 class="text-sm font-medium dashboard-text-primary">
                          {{ conversation.name }}
                        </h4>
                        <span class="text-xs dashboard-text-muted">{{
                          conversation.lastMessageTime
                        }}</span>
                      </div>
                      <p class="text-sm dashboard-text-muted truncate">
                        {{ conversation.lastMessage }}
                      </p>
                    </div>
                    <div v-if="conversation.unread" class="ml-2 flex-shrink-0">
                      <span
                        class="inline-flex items-center justify-center h-5 w-5 dashboard-bg-primary text-xs font-medium text-white rounded-full"
                      >
                        {{ conversation.unread }}
                      </span>
                    </div>
                  </div>
                </div>
                <div
                  v-if="filteredConversations.length === 0"
                  class="p-6 text-center dashboard-text-muted"
                >
                  No conversations found
                </div>
              </template>
            </div>
          </div>

          <div
            :class="[
              'flex-col flex-1',
              !showMobileChat ? 'hidden md:flex' : 'flex',
            ]"
          >
            <div v-if="selectedConversation" class="flex-1 flex flex-col">
              <div
                class="px-6 py-4 dashboard-border border-b flex items-center justify-between"
              >
                <div class="flex items-center">
                  <button
                    @click="showMobileChat = false"
                    class="md:hidden mr-2 p-1 dashboard-text-muted hover:dashboard-text-secondary dashboard-transition"
                  >
                    <Icon icon="heroicons:arrow-left" class="h-5 w-5" />
                  </button>
                  <img
                    class="h-10 w-10"
                    :src="selectedConversation.avatar"
                    alt=""
                  />
                  <div class="ml-3">
                    <h3 class="text-sm font-medium dashboard-text-primary">
                      {{ selectedConversation.name }}
                    </h3>
                    <p class="text-xs dashboard-text-muted">
                      {{
                        selectedConversation.online
                          ? "Online"
                          : "Last seen " + selectedConversation.lastSeen
                      }}
                    </p>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <button
                    class="p-1 dashboard-text-light hover:dashboard-text-secondary dashboard-transition"
                  >
                    <Icon icon="heroicons:phone" class="h-5 w-5" />
                  </button>
                  <button
                    class="p-1 dashboard-text-light hover:dashboard-text-secondary dashboard-transition"
                  >
                    <Icon icon="heroicons:video-camera" class="h-5 w-5" />
                  </button>
                  <button
                    class="p-1 dashboard-text-light hover:dashboard-text-secondary dashboard-transition"
                  >
                    <Icon icon="heroicons:information-circle" class="h-5 w-5" />
                  </button>
                </div>
              </div>

              <div ref="messagesContainer" class="flex-1 p-6 overflow-y-auto">
                <div v-if="loadingMessages" class="animate-pulse space-y-4">
                  <div v-for="i in 5" :key="i">
                    <div
                      class="flex items-start"
                      :class="i % 2 === 0 ? 'justify-end' : 'justify-start'"
                    >
                      <div
                        v-if="i % 2 !== 0"
                        class="h-8 w-8 mr-2 dashboard-bg-primary-light rounded-full"
                      ></div>
                      <div
                        :class="[
                          i % 2 === 0
                            ? 'dashboard-bg-primary-light'
                            : 'dashboard-bg-hover',
                          'px-4 py-2 max-w-xs sm:max-w-md rounded',
                        ]"
                      >
                        <div
                          class="h-4 w-32 dashboard-bg-primary-light mb-1"
                        ></div>
                        <div class="h-4 w-48 dashboard-bg-primary-light"></div>
                      </div>
                    </div>
                  </div>
                </div>

                <template v-else>
                  <div
                    v-for="(message, index) in selectedConversation.messages"
                    :key="index"
                    class="mb-4"
                  >
                    <div
                      class="flex items-start"
                      :class="
                        message.sender === 'me'
                          ? 'justify-end'
                          : 'justify-start'
                      "
                    >
                      <template v-if="message.sender !== 'me'">
                        <img
                          v-if="
                            shouldShowAvatar(
                              index,
                              selectedConversation.messages
                            )
                          "
                          class="h-8 w-8 mr-2 rounded-full object-cover"
                          :src="selectedConversation.avatar"
                          alt=""
                        />
                        <div v-else class="w-8 mr-2"></div>
                      </template>
                      <div
                        :class="[
                          message.sender === 'me'
                            ? 'bg-red-600 text-white rounded-tl-lg rounded-tr-lg rounded-bl-lg'
                            : 'dashboard-bg-hover dashboard-text-primary rounded-tl-lg rounded-tr-lg rounded-br-lg',
                          'px-4 py-2 max-w-xs sm:max-w-md shadow-sm',
                        ]"
                      >
                        <p class="text-sm">{{ message.text }}</p>
                      </div>
                    </div>
                    <div
                      :class="[
                        message.sender === 'me'
                          ? 'flex justify-end'
                          : 'flex justify-start ml-10',
                        'mt-1',
                      ]"
                    >
                      <p class="text-xs dashboard-text-muted">
                        {{ message.time }}
                      </p>
                    </div>
                  </div>

                  <div
                    v-if="selectedConversation.messages.length === 0"
                    class="flex items-center justify-center h-full"
                  >
                    <div class="text-center">
                      <Icon
                        icon="heroicons:chat-bubble-left-right"
                        class="h-12 w-12 dashboard-text-light mx-auto"
                      />
                      <h3
                        class="mt-2 text-sm font-medium dashboard-text-primary"
                      >
                        No messages yet
                      </h3>
                      <p class="mt-1 text-sm dashboard-text-muted">
                        Start the conversation by sending a message
                      </p>
                    </div>
                  </div>
                </template>
              </div>

              <div class="px-6 py-4 dashboard-border border-t">
                <div class="flex items-center">
                  <button
                    class="p-2 dashboard-text-light hover:dashboard-text-secondary dashboard-transition"
                  >
                    <Icon icon="heroicons:paper-clip" class="h-5 w-5" />
                  </button>
                  <div class="flex-1 mx-2">
                    <textarea
                      v-model="newMessage"
                      @keydown.enter.prevent="sendMessage"
                      placeholder="Type a message..."
                      rows="1"
                      class="block w-full px-3 py-2 dashboard-border dashboard-input dashboard-focus-primary sm:text-sm resize-none"
                    ></textarea>
                  </div>
                  <div class="flex space-x-2">
                    <button
                      class="p-2 dashboard-text-light hover:dashboard-text-secondary dashboard-transition"
                    >
                      <Icon icon="heroicons:face-smile" class="h-5 w-5" />
                    </button>
                    <CorePrimaryButton
                      @click="sendMessage"
                      :disabled="!newMessage.trim() || sendingMessage"
                      text=""
                      class="px-3 py-2"
                    >
                      <div
                        v-if="sendingMessage"
                        class="animate-spin rounded-full h-4 w-4 border-2 border-white"
                      ></div>
                      <Icon
                        v-else
                        icon="heroicons:paper-airplane"
                        class="h-5 w-5"
                      />
                    </CorePrimaryButton>
                  </div>
                </div>
              </div>
            </div>

            <div v-else class="flex-1 flex items-center justify-center">
              <div class="text-center">
                <Icon
                  icon="heroicons:chat-bubble-left-right"
                  class="h-12 w-12 dashboard-text-light mx-auto"
                />
                <h3 class="mt-2 text-sm font-medium dashboard-text-primary">
                  No conversation selected
                </h3>
                <p class="mt-1 text-sm dashboard-text-muted">
                  Choose a conversation from the list to start chatting
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from "vue";
import { useRoute } from "vue-router";
import { useAuthStore } from "@/store/auth";
import { useCountersStore } from "@/store/counters";
import { ENDPOINTS } from "@/utils/api";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";

dayjs.extend(relativeTime);

definePageMeta({
  layout: "dashboard",
});

interface ApiMessage {
  id: number;
  conversation_id: number;
  user_id: number;
  content: string;
  is_read: boolean;
  read_at: string | null;
  created_at: string;
  updated_at: string;
  sender?: {
    id: number;
    name: string;
    avatar: string;
  };
}

interface ApiConversation {
  id: number;
  user_id: number;
  vendor_id: number;
  last_message_at: string;
  created_at: string;
  updated_at: string;
  user: {
    id: number;
    name: string;
    email: string;
    avatar: string | null;
  };
  vendor?: {
    id: number;
    name: string;
    logo: string | null;
    user_id?: number;
    user?: {
      id: number;
      name: string;
      avatar: string | null;
    };
  };
  latest_message?: ApiMessage;
  unread_count?: number;
}

interface Message {
  sender: "me" | "them";
  text: string;
  time: string;
}

interface Conversation {
  id: number;
  name: string;
  avatar: string;
  online: boolean;
  lastSeen?: string;
  lastMessage: string;
  lastMessageTime: string;
  unread: number;
  messages: Message[];
}

const conversations = ref<Conversation[]>([]);
const apiConversations = ref<ApiConversation[]>([]);
const apiMessages = ref<ApiMessage[]>([]);

const selectedConversation = ref<Conversation | null>(null);
const searchQuery = ref("");
const showMobileChat = ref(false);
const newMessage = ref("");
const messagesContainer = ref<HTMLElement | null>(null);
const loading = ref<boolean>(true);
const loadingMessages = ref<boolean>(false);
const sendingMessage = ref<boolean>(false);
const route = useRoute();
const authStore = useAuthStore();
const countersStore = useCountersStore();
const httpClient = useHttpClient();
const runtimeConfig = useRuntimeConfig();
const nuxtApp = useNuxtApp();
const { $toast }: any = nuxtApp;
const $echo = nuxtApp.$echo as any;

const filteredConversations = computed(() => {
  if (!searchQuery.value) {
    return conversations.value;
  }

  const query = searchQuery.value.toLowerCase();
  return conversations.value.filter(
    (conversation) =>
      conversation.name.toLowerCase().includes(query) ||
      conversation.lastMessage.toLowerCase().includes(query)
  );
});

const fetchConversations = async (): Promise<void> => {
  try {
    loading.value = true;
    const response: any = await httpClient.get(
      `${ENDPOINTS.MESSAGES.GET_CONVERSATIONS}`
    );

    if (response && response.data) {
      console.log(response.data)
      apiConversations.value = response.data;

      conversations.value = apiConversations.value.map((conv) => {
        const isVendor = conv.vendor?.user_id === authStore.user?.id;

        let otherUser: {
          id?: number;
          name?: string;
          avatar?: string | null;
          logo?: string | null;
        };

        if (isVendor) {
          // Current user is the vendor, so the other party is the regular user
          otherUser = conv.user;
        } else if (conv.user.id === authStore.user?.id) {
          otherUser = {
            id: conv.vendor?.id,
            name: conv.vendor?.name || "Unknown Vendor",
            logo: conv.vendor?.logo,
          };
        } else {
          // Fallback (shouldn't happen in normal cases)
          otherUser =
            conv.user.id === authStore.user?.id
              ? {
                  id: conv.vendor?.id,
                  name: conv.vendor?.name,
                  logo: conv.vendor?.logo,
                }
              : conv.user;
        }

        return {
          id: conv.id,
          name: otherUser?.name || "Unknown User",
          avatar: otherUser?.avatar
            ? `${runtimeConfig.public.baseUrl}storage/avatars/${otherUser.avatar}`
            : otherUser?.logo
            ? `${runtimeConfig.public.baseUrl}storage/${otherUser.logo}`
            : `https://ui-avatars.com/api/?name=${encodeURIComponent(
                otherUser?.name || "User"
              )}&background=random`,
          online: false, // We'll update this with real-time data
          lastMessage: conv.latest_message?.content || "No messages yet",
          lastMessageTime: conv.latest_message
            ? formatMessageTime(conv.latest_message.created_at)
            : formatMessageTime(conv.created_at),
          unread: conv.unread_count || 0,
          messages: [],
        };
      });

      // Update unread message count in the counters store
      const totalUnread = conversations.value.reduce(
        (sum, conv) => sum + conv.unread,
        0
      );
      countersStore.unreadMessages = totalUnread;

      const conversationId = route.query.conversation
        ? parseInt(route.query.conversation as string)
        : null;
      if (conversationId) {
        const conversation = conversations.value.find(
          (c) => c.id === conversationId
        );
        if (conversation) {
          selectConversation(conversation);
        }
      } else if (window.innerWidth >= 768 && conversations.value.length > 0) {
        selectConversation(conversations.value[0]);
      }
    }
  } catch (error) {
    $toast.error("Failed to load conversations");
  } finally {
    loading.value = false;
  }
};

const fetchMessages = async (conversationId: number): Promise<void> => {
  if (!conversationId) return;

  try {
    loadingMessages.value = true;
    const response: any = await httpClient.get(
      `${ENDPOINTS.MESSAGES.GET_MESSAGES}/${conversationId}`
    );

    if (response && response.data) {
      const messagesData = response.data.data || response.data;
      apiMessages.value = Array.isArray(messagesData) ? messagesData : [];

      if (selectedConversation.value) {
        const mappedMessages = apiMessages.value.map((msg) => {
          const isMine = msg.user_id === authStore.user?.id;

          return {
            sender: isMine ? ("me" as const) : ("them" as const),
            text: msg.content,
            time: formatMessageTime(msg.created_at),
          };
        });

        selectedConversation.value.messages = mappedMessages.reverse();
        markConversationAsRead(conversationId);
      }
    }
  } catch (error) {
    $toast.error("Failed to load messages");
  } finally {
    loadingMessages.value = false;
    nextTick(() => {
      scrollToBottom();
    });
  }
};

const markConversationAsRead = async (
  conversationId: number
): Promise<void> => {
  try {
    const response: any = await httpClient.put(
      `${ENDPOINTS.MESSAGES.MARK_AS_READ}/${conversationId}`
    );
    if (response) {
      const conversation = conversations.value.find(
        (c) => c.id === conversationId
      );
      if (conversation && conversation.unread > 0) {
        for (let i = 0; i < conversation.unread; i++) {
          countersStore.decrementUnreadMessages();
        }
        conversation.unread = 0;
      }
    }
  } catch (error) {
    console.error("Error marking conversation as read:", error);
  }
};

const selectConversation = (conversation: Conversation) => {
  selectedConversation.value = conversation;
  showMobileChat.value = true;
  fetchMessages(conversation.id);
};

const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
  }
};

const shouldShowAvatar = (index: number, messages: Message[]): boolean => {
  // Show avatar only for the first message in a sequence from the same sender
  if (index === 0) return true;
  return messages[index].sender !== messages[index - 1].sender;
};

const formatMessageTime = (timestamp: string): string => {
  const date = dayjs(timestamp);
  const now = dayjs();

  if (now.diff(date, "day") < 1) {
    return date.format("h:mm A");
  } else if (now.diff(date, "week") < 1) {
    return date.format("ddd h:mm A");
  } else {
    return date.format("MMM D, YYYY");
  }
};

const sendMessage = async (): Promise<void> => {
  if (!newMessage.value.trim() || !selectedConversation.value) return;

  const messageContent = newMessage.value.trim();
  newMessage.value = "";
  sendingMessage.value = true;

  try {
    const response: any = await httpClient.post(`${ENDPOINTS.MESSAGES.SEND}`, {
      conversation_id: selectedConversation.value.id,
      content: messageContent,
    });

    if (response && response.data) {
      // Add the new message to the conversation
      selectedConversation.value.messages.push({
        sender: "me",
        text: messageContent,
        time: "Just now",
      });

      // Update last message info
      selectedConversation.value.lastMessage = messageContent;
      selectedConversation.value.lastMessageTime = "Just now";

      // Scroll to bottom after DOM update
      nextTick(() => {
        scrollToBottom();
      });
    }
  } catch (error) {
    $toast.error("Failed to send message");
    newMessage.value = messageContent; // Restore message if failed
  } finally {
    sendingMessage.value = false;
  }
};

watch(selectedConversation, () => {
  nextTick(() => {
    scrollToBottom();
  });
});

onMounted(() => {
  fetchConversations();

  if ($echo && authStore.user?.id) {
    try {
      apiConversations.value.forEach((conv) => {
        $echo
          .private(`conversation.${conv.id}`)
          .listen(".message.sent", (event: any) => {
            if (
              selectedConversation.value &&
              selectedConversation.value.id === conv.id
            ) {
              if (event.user.id !== authStore.user?.id) {
                selectedConversation.value.messages.push({
                  sender: "them",
                  text: event.message.content,
                  time: "Just now",
                });

                markConversationAsRead(conv.id);

                nextTick(() => {
                  scrollToBottom();
                });
              }
            } else {
              const conversation = conversations.value.find(
                (c) => c.id === conv.id
              );
              if (conversation) {
                conversation.lastMessage = event.message.content;
                conversation.lastMessageTime = "Just now";

                if (event.user.id !== authStore.user?.id) {
                  conversation.unread++;
                  countersStore.incrementUnreadMessages();
                }
              }
            }
          });
      });
    } catch (error) {
      $toast.error("Failed to subscribe to real-time messages");
    }
  }
});
</script>
