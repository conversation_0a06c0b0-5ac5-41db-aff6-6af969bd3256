<template>
  <div class="p-6 bg-gray-100 dark:bg-zinc-900">
    <div class="mb-6 flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Sponsors</h1>
        <p class="text-gray-500 dark:text-gray-400">Manage event sponsors and partnerships</p>
      </div>
      <div class="flex space-x-2">
        <button @click="exportSponsors" class="px-4 py-2 bg-white dark:bg-zinc-800 border border-gray-300 dark:border-zinc-600 shadow-sm hover:bg-gray-50 dark:hover:bg-zinc-700 flex items-center text-gray-700 dark:text-gray-200">
          <Icon icon="vscode-icons:file-type-excel" class="w-5 h-5 mr-1" />
          Export Excel
        </button>
        <SponsorAddDialog @sponsor-created="fetchSponsors" />
      </div>
    </div>

    <!-- Sponsor Filters -->
    <div class="bg-white dark:bg-zinc-800 p-4 shadow mb-6">
      <div class="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
        <div>
          <label for="filter-field" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Filter By</label>
          <select
            id="filter-field"
            v-model="filterField"
            class="border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500"
          >
            <option value="all">All Fields</option>
            <option value="name">Name</option>
            <option value="city">City</option>
            <option value="country">Country</option>
            <option value="address">Address</option>
          </select>
        </div>
        <div class="md:ml-auto">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Search</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Icon icon="heroicons:magnifying-glass" class="h-5 w-5 text-gray-400 dark:text-gray-500" />
            </div>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search sponsors"
              class="block w-full pl-10 border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500"
            />
          </div>
        </div>
      </div>

      <!-- Selected Sponsors -->
      <div v-if="selectedSponsors.length" class="mt-4 flex flex-wrap gap-2">
        <div
          v-for="sponsor in selectedSponsors"
          :key="sponsor.id"
          class="flex items-center bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200 px-3 py-1"
        >
          <span class="mr-2">{{ sponsor.name }}</span>
          <button
            @click="removeSponsor(Number(sponsor.id))"
            class="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200"
          >
            <Icon icon="heroicons:x-mark" class="w-4 h-4" />
          </button>
        </div>
        <button
          @click="clearAllSelected"
          class="text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 px-2 py-1"
        >
          Clear all
        </button>
      </div>
    </div>

    <!-- Sponsors Table -->
    <div class="bg-white dark:bg-zinc-800 shadow">
      <div v-if="loading" class="flex justify-center items-center py-20">
        <CoreLoader />
      </div>
      <div v-else>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-zinc-700">
            <thead>
              <tr>
                <th class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  <input
                    type="checkbox"
                    :checked="allSelected"
                    @change="toggleSelectAll"
                    class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 dark:border-zinc-600"
                  />
                </th>
                <th class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Sponsor</th>
                <th class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Location</th>
                <th class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Coordinates</th>
                <th class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Date Created</th>
                <th class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-zinc-800 divide-y divide-gray-200 dark:divide-zinc-700">
              <tr v-for="sponsor in filteredSponsors" :key="sponsor.id" class="hover:bg-gray-50 dark:hover:bg-zinc-700">
                <td class="px-6 py-4 whitespace-nowrap">
                  <input
                    type="checkbox"
                    :checked="isSelected(sponsor)"
                    @change="toggleSelect(sponsor)"
                    class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 dark:border-zinc-600"
                  />
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-12 w-12">
                      <img
                        :src="`${runtimeConfig.public.baseUrl}storage/sponsors/${sponsor.logo}`"
                        :alt="sponsor.name"
                        class="h-12 w-12 object-contain bg-gray-50 dark:bg-zinc-700 p-1"
                      />
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900 dark:text-white">{{ sponsor.name }}</div>
                      <div class="text-sm text-gray-500 dark:text-gray-400">{{ sponsor.address }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900 dark:text-white">{{ sponsor.city }}</div>
                  <div class="text-sm text-gray-500 dark:text-gray-400">{{ sponsor.country }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  {{ sponsor.latitude }}, {{ sponsor.longitude }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  {{ formatDate(sponsor.created_at) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <SponsorEditDialog
                      :sponsor="sponsor"
                      @sponsor-updated="fetchSponsors"
                    />
                    <SponsorDeleteDialog
                      :sponsor-id="sponsor.id"
                      @sponsor-deleted="fetchSponsors"
                    />
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div v-if="!filteredSponsors.length && !loading" class="text-center py-12">
          <Icon icon="heroicons:building-office-2" class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" />
          <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No sponsors found</h3>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {{ searchQuery ? 'Try adjusting your search criteria.' : 'Get started by adding a new sponsor.' }}
          </p>
          <div v-if="!searchQuery" class="mt-6 flex items-center justify-center">
            <SponsorAddDialog @sponsor-created="fetchSponsors" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import type { Sponsor } from "@/types/index";
import { ENDPOINTS } from '@/utils/api';

definePageMeta({
  layout: "dashboard",
});

const httpClient = useHttpClient();
const runtimeConfig = useRuntimeConfig();
const { $toast }: any = useNuxtApp();
const loading = ref(false);
const sponsors = ref<Sponsor[]>([]);
const searchQuery = ref("");
const filterField = ref("all");
const selectedSponsors = ref<Sponsor[]>([]);

// Computed properties
const allSelected = computed(() => {
  return filteredSponsors.value.length > 0 && selectedSponsors.value.length === filteredSponsors.value.length;
});

const filteredSponsors = computed(() => {
  if (!searchQuery.value.trim()) {
    return sponsors.value;
  }

  const query = searchQuery.value.toLowerCase().trim();

  return sponsors.value.filter((sponsor) => {
    if (filterField.value === "all") {
      return (
        sponsor.name.toLowerCase().includes(query) ||
        sponsor.city.toLowerCase().includes(query) ||
        sponsor.country.toLowerCase().includes(query) ||
        sponsor.address.toLowerCase().includes(query)
      );
    } else {
      return sponsor[filterField.value as keyof Sponsor]
        ?.toString()
        .toLowerCase()
        .includes(query);
    }
  });
});

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString();
};

// Selection functions
const isSelected = (sponsor: Sponsor): boolean => {
  return selectedSponsors.value.some(s => s.id === sponsor.id);
};

const toggleSelect = (sponsor: Sponsor): void => {
  if (isSelected(sponsor)) {
    selectedSponsors.value = selectedSponsors.value.filter(s => s.id !== sponsor.id);
  } else {
    selectedSponsors.value.push(sponsor);
  }
};

const toggleSelectAll = (): void => {
  if (allSelected.value) {
    selectedSponsors.value = [];
  } else {
    selectedSponsors.value = [...filteredSponsors.value];
  }
};

const clearAllSelected = (): void => {
  selectedSponsors.value = [];
};

const removeSponsor = (id: number) => {
  selectedSponsors.value = selectedSponsors.value.filter(
    (sponsor: Sponsor) => Number(sponsor.id) !== id
  );
};

// Export function
const exportSponsors = async (): Promise<void> => {
  try {
    const response = await httpClient.get(`${ENDPOINTS.SPONSORS.READ}/export`, {
      responseType: 'blob'
    }) as Blob;

    // Create download link
    const url = window.URL.createObjectURL(response);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `sponsors-${new Date().toISOString().split('T')[0]}.xlsx`);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);

    $toast.success('Sponsors exported successfully');
  } catch (error) {
    console.error('Error exporting sponsors:', error);
    $toast.error('Failed to export sponsors');
  }
};

const fetchSponsors = async () => {
  loading.value = true;
  try {
    const response = await httpClient.get<{ sponsors: Sponsor[] }>(
      ENDPOINTS.SPONSORS.READ
    );

    sponsors.value = response.sponsors.map((sponsor) => ({
      ...sponsor,
      coordinates: `${sponsor.latitude}, ${sponsor.longitude}`,
    }));
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchSponsors();
});
</script>


