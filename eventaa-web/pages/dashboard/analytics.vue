<template>
  <div class="p-6 dashboard-bg-main">
    <div class="mb-6">
      <h1 class="text-2xl font-bold dashboard-text-primary">Analytics</h1>
      <p class="dashboard-text-muted">
        Track your events performance and user engagement
      </p>
    </div>

    <div v-if="loading" class="flex justify-center items-center h-64">
      <CoreLoader />
    </div>
    <div v-else>
      <div class="dashboard-bg-card p-4 dashboard-shadow mb-6">
        <div
          class="flex flex-col md:flex-row md:items-center md:justify-between"
        >
          <div class="mb-4 md:mb-0">
            <h2 class="font-semibold dashboard-text-primary">Date Range</h2>
            <p class="text-sm dashboard-text-muted">Select a time period to analyze</p>
          </div>
          <div class="flex items-center space-x-2">
            <button
              v-for="period in periods"
              :key="period.value"
              @click="selectedPeriod = period.value"
              class="px-3 py-1 text-sm dashboard-transition"
              :class="
                selectedPeriod === period.value
                  ? 'bg-red-600 text-white'
                  : 'dashboard-bg-hover dashboard-text-secondary hover:dashboard-bg-active'
              "
            >
              {{ period.label }}
            </button>
            <button
              class="px-3 py-1 text-sm dashboard-bg-hover dashboard-text-secondary hover:dashboard-bg-active flex items-center dashboard-transition"
            >
              <Icon icon="heroicons:calendar" class="w-4 h-4 mr-1" />
              Custom
            </button>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <CoreStatsCard
          title="Total Tickets Sold"
          :value="stats.ticketsSold"
          icon="heroicons:ticket"
          icon-color="blue"
          :growth="stats.ticketsGrowth"
        />

        <CoreStatsCard
          title="Revenue"
          :value="stats.revenue"
          icon="heroicons:currency-dollar"
          icon-color="green"
          :growth="stats.revenueGrowth"
          :is-currency="true"
          currency-compact
        />

        <CoreStatsCard
          title="Total Attendees"
          :value="stats.attendees"
          icon="heroicons:users"
          icon-color="purple"
          :growth="stats.attendeesGrowth"
        />

        <CoreStatsCard
          title="Page Views"
          :value="stats.pageViews"
          icon="heroicons:eye"
          icon-color="yellow"
          :growth="stats.pageViewsGrowth"
        />
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div class="dashboard-bg-card p-4 dashboard-shadow">
          <h2 class="font-semibold mb-4 dashboard-text-primary">Revenue Analytics</h2>
          <div class="h-64">
            <Line
              v-if="revenueChartData"
              :data="revenueChartData"
              :options="lineChartOptions"
            />
            <div
              v-else
              class="flex items-center justify-center h-full dashboard-text-muted"
            >
              <p>Loading revenue data...</p>
            </div>
          </div>
        </div>

        <div class="dashboard-bg-card p-4 dashboard-shadow">
          <h2 class="font-semibold mb-4 dashboard-text-primary">Attendees Analytics</h2>
          <div class="h-64">
            <Bar
              v-if="attendeesChartData"
              :data="attendeesChartData"
              :options="barChartOptions"
            />
            <div
              v-else
              class="flex items-center justify-center h-full dashboard-text-muted"
            >
              <p>Loading attendees data...</p>
            </div>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div class="dashboard-bg-card p-4 dashboard-shadow">
          <h2 class="font-semibold mb-4 dashboard-text-primary">Events Performance</h2>
          <div class="h-64">
            <Doughnut
              v-if="eventsChartData"
              :data="eventsChartData"
              :options="doughnutChartOptions"
            />
            <div
              v-else
              class="flex items-center justify-center h-full dashboard-text-muted"
            >
              <p>Loading events data...</p>
            </div>
          </div>
        </div>

        <div class="dashboard-bg-card p-4 dashboard-shadow">
          <h2 class="font-semibold mb-4 dashboard-text-primary">Revenue Distribution</h2>
          <div class="h-64">
            <Pie
              v-if="distributionChartData"
              :data="distributionChartData"
              :options="pieChartOptions"
            />
            <div
              v-else
              class="flex items-center justify-center h-full dashboard-text-muted"
            >
              <p>Loading distribution data...</p>
            </div>
          </div>
        </div>
      </div>

      <div class="dashboard-bg-card p-4 dashboard-shadow mb-6">
        <h2 class="font-semibold mb-4 dashboard-text-primary">Top Performing Events</h2>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y dashboard-border">
            <thead>
              <tr>
                <th
                  class="px-6 py-3 text-left text-xs font-medium dashboard-text-muted uppercase tracking-wider"
                >
                  Event
                </th>
                <th
                  class="px-6 py-3 text-left text-xs font-medium dashboard-text-muted uppercase tracking-wider"
                >
                  Tickets Sold
                </th>
                <th
                  class="px-6 py-3 text-left text-xs font-medium dashboard-text-muted uppercase tracking-wider"
                >
                  Revenue
                </th>
                <th
                  class="px-6 py-3 text-left text-xs font-medium dashboard-text-muted uppercase tracking-wider"
                >
                  Conversion Rate
                </th>
                <th
                  class="px-6 py-3 text-left text-xs font-medium dashboard-text-muted uppercase tracking-wider"
                >
                  Status
                </th>
              </tr>
            </thead>
            <tbody class="dashboard-bg-card divide-y dashboard-border">
              <tr v-for="event in topEvents" :key="event.id">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                      <img
                        class="h-10 w-10 rounded-full"
                        :src="event.image"
                        alt=""
                      />
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium dashboard-text-primary">
                        {{ event.title }}
                      </div>
                      <div class="text-sm dashboard-text-muted">
                        {{ formatDate(event.start) }}
                      </div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm dashboard-text-primary">
                    {{ event.tickets_sold }}
                  </div>
                  <div class="text-sm dashboard-text-muted">
                    of {{ event.total_tickets }}
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm dashboard-text-primary">
                    {{ formatCurrency(event.revenue) }}
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm dashboard-text-primary">
                    {{ Math.round(event.conversion_rate) }}%
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                    :class="{
                      'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400': event.status === 'Active',
                      'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-400':
                        event.status === 'Upcoming',
                      'bg-zinc-100 dark:bg-zinc-800 text-zinc-800 dark:text-zinc-400': event.status === 'Ended',
                    }"
                  >
                    {{ event.status }}
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from "vue";
import { ENDPOINTS } from "@/utils/api";
import { Line, Bar, Doughnut, Pie } from "vue-chartjs";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from "chart.js";

definePageMeta({
  layout: "dashboard",
});

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const loading = ref(true);
const selectedPeriod = ref("7d");
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();
const { formatCurrency, formatCurrencyCompact } = useCurrency();

const periods = [
  { label: "7 Days", value: "7d" },
  { label: "30 Days", value: "30d" },
  { label: "90 Days", value: "90d" },
  { label: "This Year", value: "year" },
];

interface AnalyticsStats {
  ticketsSold: number;
  ticketsGrowth: number;
  revenue: number;
  revenueGrowth: number;
  attendees: number;
  attendeesGrowth: number;
  pageViews: number;
  pageViewsGrowth: number;
}

interface TopEvent {
  id: number;
  title: string;
  start: string;
  image?: string;
  tickets_sold: number;
  total_tickets: number;
  revenue: number;
  conversion_rate: number;
  status: string;
}

const stats = ref<AnalyticsStats>({
  ticketsSold: 0,
  ticketsGrowth: 0,
  revenue: 0,
  revenueGrowth: 0,
  attendees: 0,
  attendeesGrowth: 0,
  pageViews: 0,
  pageViewsGrowth: 0,
});

const topEvents = ref<TopEvent[]>([]);
const revenueData = ref<any>(null);
const attendeesData = ref<any>(null);

const revenueChartData = computed(() => {
  if (!revenueData.value?.chart_data) {
    const labels = [];
    const data = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      labels.push(date.toLocaleDateString());
      data.push(Math.floor(Math.random() * 1000) + 500);
    }
    return {
      labels,
      datasets: [
        {
          label: "Revenue",
          data,
          borderColor: "#10B981",
          backgroundColor: "rgba(16, 185, 129, 0.1)",
          tension: 0.4,
          fill: true,
        },
      ],
    };
  }
  return {
    labels: revenueData.value.chart_data.labels || [],
    datasets: [
      {
        label: "Revenue",
        data: revenueData.value.chart_data.values || [],
        borderColor: "#10B981",
        backgroundColor: "rgba(16, 185, 129, 0.1)",
        tension: 0.4,
        fill: true,
      },
    ],
  };
});

const attendeesChartData = computed(() => {
  if (!attendeesData.value?.chart_data) {
    const labels = [];
    const data = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      labels.push(date.toLocaleDateString());
      data.push(Math.floor(Math.random() * 50) + 10);
    }
    return {
      labels,
      datasets: [
        {
          label: "Attendees",
          data,
          backgroundColor: "#3B82F6",
          borderColor: "#2563EB",
          borderWidth: 1,
        },
      ],
    };
  }
  return {
    labels: attendeesData.value.chart_data.labels || [],
    datasets: [
      {
        label: "Attendees",
        data: attendeesData.value.chart_data.values || [],
        backgroundColor: "#3B82F6",
        borderColor: "#2563EB",
        borderWidth: 1,
      },
    ],
  };
});

const eventsChartData = computed(() => {
  if (!topEvents.value.length) {
    return {
      labels: [],
      datasets: [
        {
          data: [45, 32, 28, 19, 15],
          backgroundColor: [
            "#EF4444",
            "#F97316",
            "#EAB308",
            "#22C55E",
            "#3B82F6",
          ],
        },
      ],
    };
  }
  return {
    labels: topEvents.value.slice(0, 5).map((event) => event.title),
    datasets: [
      {
        data: topEvents.value.slice(0, 5).map((event) => event.tickets_sold),
        backgroundColor: [
          "#EF4444",
          "#F97316",
          "#EAB308",
          "#22C55E",
          "#3B82F6",
        ],
      },
    ],
  };
});

const distributionChartData = computed(() => {
  if (!topEvents.value.length) {
    return {
      labels: [],
      datasets: [
        {
          data: [2500, 1800, 1200, 900, 600],
          backgroundColor: [
            "#DC2626",
            "#EA580C",
            "#CA8A04",
            "#16A34A",
            "#2563EB",
          ],
        },
      ],
    };
  }
  return {
    labels: topEvents.value.slice(0, 5).map((event) => event.title),
    datasets: [
      {
        data: topEvents.value.slice(0, 5).map((event) => event.revenue),
        backgroundColor: [
          "#DC2626",
          "#EA580C",
          "#CA8A04",
          "#16A34A",
          "#2563EB",
        ],
      },
    ],
  };
});

const lineChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false,
    },
  },
  scales: {
    y: {
      beginAtZero: true,
      ticks: {
        callback: function (value: any) {
          return formatCurrencyCompact(value);
        },
      },
    },
  },
};

const barChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false,
    },
  },
  scales: {
    y: {
      beginAtZero: true,
    },
  },
};

const doughnutChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: "bottom" as const,
    },
  },
};

const pieChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: "bottom" as const,
    },
  },
};
const runtimeConfig = useRuntimeConfig();

const fetchAnalyticsStats = async (): Promise<void> => {
  try {
    const response = await httpClient.get<AnalyticsStats>(
      `${ENDPOINTS.ANALYTICS.STATS}?period=${selectedPeriod.value}`
    );
    stats.value = {
      ticketsSold: response.ticketsSold || 0,
      ticketsGrowth: response.ticketsGrowth || 0,
      revenue: Number(response.revenue) || 0,
      revenueGrowth: response.revenueGrowth || 0,
      attendees: response.attendees || 0,
      attendeesGrowth: response.attendeesGrowth || 0,
      pageViews: response.pageViews || 0,
      pageViewsGrowth: response.pageViewsGrowth || 0,
    };
  } catch (error) {
    console.error("Error fetching analytics stats:", error);
    $toast.error("Failed to load analytics statistics");
  }
};

const fetchTopEvents = async (): Promise<void> => {
  try {
    const response = await httpClient.get<any>(
      `${ENDPOINTS.ANALYTICS.EVENTS}?period=${selectedPeriod.value}`
    );
    if (response && response.data) {
      topEvents.value = response.data.map((event: any) => ({
        id: event.id,
        title: event.title,
        start: event.start,
        image: `${runtimeConfig.public.baseUrl}storage/events/${event.cover_art}`,
        tickets_sold: event.tickets_sold || 0,
        total_tickets: event.tickets_sold + 100,
        revenue: Number(event.revenue) || 0,
        conversion_rate: event.performance_score || 0,
        status:
          new Date(event.start) > new Date()
            ? "Upcoming"
            : new Date(event.end) < new Date()
            ? "Ended"
            : "Active",
      }));
    }
  } catch (error) {
    console.error("Error fetching top events:", error);
    $toast.error("Failed to load top events");
  }
};

const fetchRevenueData = async () => {
  try {
    const response = await httpClient.get<any>(
      `${ENDPOINTS.ANALYTICS.REVENUE}?period=${selectedPeriod.value}`
    );
    revenueData.value = response;
  } catch (error) {
    console.error("Error fetching revenue data:", error);
  }
};

const fetchAttendeesData = async () => {
  try {
    const response = await httpClient.get<any>(
      `${ENDPOINTS.ANALYTICS.ATTENDEES}?period=${selectedPeriod.value}`
    );
    attendeesData.value = response;
  } catch (error) {
    console.error("Error fetching attendees data:", error);
  }
};

const fetchAnalyticsData = async () => {
  loading.value = true;
  try {
    await Promise.all([
      fetchAnalyticsStats(),
      fetchTopEvents(),
      fetchRevenueData(),
      fetchAttendeesData(),
    ]);
  } catch (error) {
    console.error("Error fetching analytics data:", error);
  } finally {
    loading.value = false;
  }
};

watch(selectedPeriod, () => {
  fetchAnalyticsData();
});

onMounted(async () => {
  await fetchAnalyticsData();
});

const formatDate = (dateString: string): string => {
  if (!dateString) return "";
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};
</script>
