<template>
  <div class="p-6 bg-gray-100 dark:bg-zinc-900">
    <div class="mb-6 flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Reports</h1>
        <p class="text-gray-500 dark:text-gray-400">Generate and view financial and event reports</p>
      </div>
      <div class="flex space-x-2">
        <button @click="exportReports" class="px-4 py-2 bg-white dark:bg-zinc-800 border border-gray-300 dark:border-zinc-600 shadow-sm hover:bg-gray-50 dark:hover:bg-zinc-700 flex items-center text-gray-700 dark:text-gray-200">
          <Icon icon="vscode-icons:file-type-excel" class="w-5 h-5 mr-1" />
          Export Excel
        </button>
        <button @click="newReport" class="px-4 py-2 bg-red-600 text-white shadow-sm hover:bg-red-700 flex items-center">
          <Icon icon="heroicons:plus" class="w-5 h-5 mr-1" />
          New Report
        </button>
      </div>
    </div>

    <!-- Report Filters -->
    <div class="bg-white dark:bg-zinc-800 p-4 shadow mb-6">
      <div class="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
        <div>
          <label for="report-type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Report Type</label>
          <select
            id="report-type"
            v-model="reportType"
            class="border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500"
          >
            <option value="all">All Reports</option>
            <option value="financial">Financial</option>
            <option value="attendance">Attendance</option>
            <option value="sales">Sales</option>
            <option value="vendor">Vendor</option>
          </select>
        </div>
        <div>
          <label for="date-range" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Date Range</label>
          <select
            id="date-range"
            v-model="dateRange"
            class="border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500"
          >
            <option value="all">All Time</option>
            <option value="today">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="quarter">This Quarter</option>
            <option value="year">This Year</option>
            <option value="custom">Custom Range</option>
          </select>
        </div>
        <div v-if="dateRange === 'custom'" class="flex space-x-2">
          <div>
            <label for="start-date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Start Date</label>
            <input
              id="start-date"
              v-model="startDate"
              type="date"
              class="border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500"
            />
          </div>
          <div>
            <label for="end-date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">End Date</label>
            <input
              id="end-date"
              v-model="endDate"
              type="date"
              class="border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500"
            />
          </div>
        </div>
        <div class="md:ml-auto">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Search</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Icon icon="heroicons:magnifying-glass" class="h-5 w-5 text-gray-400 dark:text-gray-500" />
            </div>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search reports"
              class="block w-full pl-10 border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Reports Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <div class="bg-white dark:bg-zinc-800 p-4 shadow">
        <div class="flex items-center">
          <div class="p-3 bg-blue-100 dark:bg-blue-900 mr-4">
            <Icon icon="heroicons:currency-dollar" class="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <p class="text-sm text-gray-500 dark:text-gray-400">Total Revenue</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ formatCurrency(stats.totalRevenue) }}</p>
            <div class="flex items-center text-sm">
              <span :class="stats.revenueGrowth >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'">
                <Icon :icon="stats.revenueGrowth >= 0 ? 'heroicons:arrow-up' : 'heroicons:arrow-down'" class="w-4 h-4 inline" />
                {{ Math.abs(stats.revenueGrowth) }}%
              </span>
              <span class="text-gray-500 dark:text-gray-400 ml-1">vs previous period</span>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-zinc-800 p-4 shadow">
        <div class="flex items-center">
          <div class="p-3 bg-green-100 dark:bg-green-900 mr-4">
            <Icon icon="heroicons:ticket" class="w-6 h-6 text-green-600 dark:text-green-400" />
          </div>
          <div>
            <p class="text-sm text-gray-500 dark:text-gray-400">Tickets Sold</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.ticketsSold }}</p>
            <div class="flex items-center text-sm">
              <span :class="stats.ticketsGrowth >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'">
                <Icon :icon="stats.ticketsGrowth >= 0 ? 'heroicons:arrow-up' : 'heroicons:arrow-down'" class="w-4 h-4 inline" />
                {{ Math.abs(stats.ticketsGrowth) }}%
              </span>
              <span class="text-gray-500 dark:text-gray-400 ml-1">vs previous period</span>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-zinc-800 p-4 shadow">
        <div class="flex items-center">
          <div class="p-3 bg-purple-100 dark:bg-purple-900 mr-4">
            <Icon icon="heroicons:users" class="w-6 h-6 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <p class="text-sm text-gray-500 dark:text-gray-400">Total Attendees</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.totalAttendees }}</p>
            <div class="flex items-center text-sm">
              <span :class="stats.attendeesGrowth >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'">
                <Icon :icon="stats.attendeesGrowth >= 0 ? 'heroicons:arrow-up' : 'heroicons:arrow-down'" class="w-4 h-4 inline" />
                {{ Math.abs(stats.attendeesGrowth) }}%
              </span>
              <span class="text-gray-500 dark:text-gray-400 ml-1">vs previous period</span>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-zinc-800 p-4 shadow">
        <div class="flex items-center">
          <div class="p-3 bg-yellow-100 dark:bg-yellow-900 mr-4">
            <Icon icon="heroicons:building-storefront" class="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
          </div>
          <div>
            <p class="text-sm text-gray-500 dark:text-gray-400">Vendor Revenue</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ formatCurrency(stats.vendorRevenue) }}</p>
            <div class="flex items-center text-sm">
              <span :class="stats.vendorGrowth >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'">
                <Icon :icon="stats.vendorGrowth >= 0 ? 'heroicons:arrow-up' : 'heroicons:arrow-down'" class="w-4 h-4 inline" />
                {{ Math.abs(stats.vendorGrowth) }}%
              </span>
              <span class="text-gray-500 dark:text-gray-400 ml-1">vs previous period</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Reports Table -->
    <div class="bg-white dark:bg-zinc-800 shadow">
      <div v-if="loading" class="flex justify-center items-center py-20">
        <CoreLoader />
      </div>
      <div v-else>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-zinc-700">
            <thead>
              <tr>
                <th class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Report Name</th>
                <th class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Type</th>
                <th class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Period</th>
                <th class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Generated</th>
                <th class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-zinc-800 divide-y divide-gray-200 dark:divide-zinc-700">
              <tr v-for="report in filteredReports" :key="report.id" class="hover:bg-gray-50 dark:hover:bg-zinc-700">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10 bg-gray-100 dark:bg-zinc-700 flex items-center justify-center">
                      <Icon :icon="getReportIcon(report.type)" class="h-5 w-5 text-gray-600 dark:text-gray-400" />
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900 dark:text-white">{{ report.name }}</div>
                      <div class="text-sm text-gray-500 dark:text-gray-400">{{ report.description }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  {{ formatReportType(report.type) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  {{ report.period }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  {{ report.generated_at ? formatDate(report.generated_at) : 'Not generated' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold"
                    :class="{
                      'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200': report.status === 'completed',
                      'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200': report.status === 'processing',
                      'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200': report.status === 'failed',
                      'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200': report.status === 'scheduled'
                    }">
                    {{ formatStatus(report.status) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <button
                      v-if="report.status === 'completed'"
                      @click="viewReport(report)"
                      class="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200"
                    >
                      <Icon icon="heroicons:eye" class="w-5 h-5" />
                    </button>
                    <button
                      v-if="report.status === 'completed'"
                      @click="downloadReport(report)"
                      class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                    >
                      <Icon icon="heroicons:document-arrow-down" class="w-5 h-5" />
                    </button>
                    <button
                      v-if="report.status === 'scheduled'"
                      @click="cancelReport(report)"
                      class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                    >
                      <Icon icon="heroicons:x-circle" class="w-5 h-5" />
                    </button>
                    <button
                      v-if="report.status === 'failed'"
                      @click="regenerateReport(report)"
                      class="text-yellow-600 hover:text-yellow-900 dark:text-yellow-400 dark:hover:text-yellow-300"
                    >
                      <Icon icon="heroicons:arrow-path" class="w-5 h-5" />
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div class="bg-white dark:bg-zinc-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-zinc-700 sm:px-6">
          <div class="flex-1 flex justify-between sm:hidden">
            <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-zinc-600 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-zinc-700 hover:bg-gray-50 dark:hover:bg-zinc-600">
              Previous
            </button>
            <button @click="nextPage" :disabled="currentPage === totalPages" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-zinc-600 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-zinc-700 hover:bg-gray-50 dark:hover:bg-zinc-600">
              Next
            </button>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700 dark:text-gray-300">
                Showing <span class="font-medium">{{ paginationStart }}</span> to <span class="font-medium">{{ paginationEnd }}</span> of <span class="font-medium">{{ totalItems }}</span> reports
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex shadow-sm -space-x-px" aria-label="Pagination">
                <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-zinc-600">
                  <span class="sr-only">Previous</span>
                  <Icon icon="heroicons:chevron-left" class="h-5 w-5" />
                </button>
                <button v-for="page in displayedPages" :key="page" @click="goToPage(page)" class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-sm font-medium" :class="page === currentPage ? 'z-10 bg-red-50 dark:bg-red-900 border-red-500 dark:border-red-400 text-red-600 dark:text-red-200' : 'text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-zinc-600'">
                  {{ page }}
                </button>
                <button @click="nextPage" :disabled="currentPage === totalPages" class="relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-zinc-600">
                  <span class="sr-only">Next</span>
                  <Icon icon="heroicons:chevron-right" class="h-5 w-5" />
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- View Report Modal -->
    <TransitionRoot appear :show="isViewModalOpen" as="template">
      <Dialog as="div" @close="closeViewModal" class="relative z-50">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25" />
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex min-h-full items-center justify-center p-4 text-center">
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel class="w-full max-w-4xl transform overflow-hidden bg-white dark:bg-zinc-800 p-6 text-left align-middle shadow-xl transition-all">
                <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900 dark:text-white mb-4">
                  Report Details
                </DialogTitle>

                <div v-if="selectedReport" class="space-y-6">
                  <!-- Report Header -->
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Report Name</h4>
                      <p class="text-lg font-semibold text-gray-900 dark:text-white">{{ selectedReport?.name || 'Unknown Report' }}</p>
                    </div>
                    <div>
                      <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Status</h4>
                      <span :class="getStatusColor(selectedReport?.status)" class="inline-flex items-center px-2.5 py-0.5 text-xs font-medium">
                        {{ formatStatus(selectedReport?.status || 'unknown') }}
                      </span>
                    </div>
                  </div>

                  <!-- Report Info -->
                  <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Type</h4>
                      <p class="text-gray-900 dark:text-white">{{ formatType(selectedReport?.type || 'unknown') }}</p>
                    </div>
                    <div>
                      <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Period</h4>
                      <p class="text-gray-900 dark:text-white">{{ formatPeriod(selectedReport?.period || 'unknown') }}</p>
                    </div>
                    <div>
                      <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Generated By</h4>
                      <p class="text-gray-900 dark:text-white">{{ selectedReport?.user?.name || 'Unknown' }}</p>
                    </div>
                  </div>

                  <!-- Dates -->
                  <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Created Date</h4>
                      <p class="text-gray-900 dark:text-white">{{ selectedReport?.created_at ? formatDate(selectedReport.created_at) : 'Unknown' }}</p>
                    </div>
                    <div>
                      <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Generated Date</h4>
                      <p class="text-gray-900 dark:text-white">{{ selectedReport?.generated_at ? formatDate(selectedReport.generated_at) : 'Not generated' }}</p>
                    </div>
                    <div>
                      <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Date Range</h4>
                      <p class="text-gray-900 dark:text-white">
                        {{ selectedReport?.start_date ? formatDate(selectedReport.start_date) : 'N/A' }} - {{ selectedReport?.end_date ? formatDate(selectedReport.end_date) : 'N/A' }}
                      </p>
                    </div>
                  </div>

                  <!-- Description -->
                  <div v-if="selectedReport?.description">
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Description</h4>
                    <p class="text-gray-900 dark:text-white">{{ selectedReport.description }}</p>
                  </div>

                  <!-- Report Data -->
                  <div v-if="selectedReport?.data && selectedReport?.status === 'completed'" class="bg-gray-50 dark:bg-zinc-700 p-4">
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-3">Report Data</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      <div v-for="(value, key) in selectedReport.data" :key="key" class="bg-white dark:bg-zinc-800 p-3 shadow-sm">
                        <p class="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide">{{ formatDataKey(key) }}</p>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white">{{ formatDataValue(value, key) }}</p>
                      </div>
                    </div>
                  </div>

                  <!-- Actions -->
                  <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-zinc-600">
                    <button
                      @click="closeViewModal"
                      class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-700 border border-gray-300 dark:border-zinc-600 hover:bg-gray-50 dark:hover:bg-zinc-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                    >
                      Close
                    </button>
                    <button
                      v-if="selectedReport?.status === 'completed'"
                      @click="downloadReport(selectedReport)"
                      class="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                    >
                      <Icon icon="heroicons:arrow-down-tray" class="w-4 h-4 mr-1 inline" />
                      Download PDF
                    </button>
                  </div>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { ENDPOINTS } from '@/utils/api';
import {
  Dialog,
  DialogPanel,
  DialogTitle,
  TransitionChild,
  TransitionRoot
} from '@headlessui/vue';

definePageMeta({
  layout: "dashboard",
});

useHead({
  title: "Reports | EventaHub Malawi",
  meta: [
    { name: "description", content: "Generate and view financial, attendance, sales, and vendor reports for your events" }
  ]
});

const loading = ref(true);
const searchQuery = ref('');
const reportType = ref('all');
const dateRange = ref('all');
const startDate = ref('');
const endDate = ref('');
const currentPage = ref(1);
const itemsPerPage = 10;
const totalItems = ref(0);
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

interface ReportStats {
  totalRevenue: number;
  revenueGrowth: number;
  ticketsSold: number;
  ticketsGrowth: number;
  totalAttendees: number;
  attendeesGrowth: number;
  vendorRevenue: number;
  vendorGrowth: number;
  totalEvents: number;
  eventsGrowth: number;
}

interface Report {
  id: number;
  name: string;
  description: string;
  type: 'financial' | 'sales' | 'attendance' | 'vendor';
  period: string;
  status: 'completed' | 'processing' | 'failed' | 'scheduled' | 'cancelled';
  created_at: string;
  generated_at?: string;
  start_date: string;
  end_date: string;
  data?: Record<string, any>;
  user?: {
    id: number;
    name: string;
    email: string;
  };
}

const stats = ref<ReportStats>({
  totalRevenue: 0,
  revenueGrowth: 0,
  ticketsSold: 0,
  ticketsGrowth: 0,
  totalAttendees: 0,
  attendeesGrowth: 0,
  vendorRevenue: 0,
  vendorGrowth: 0,
  totalEvents: 0,
  eventsGrowth: 0
});

const fetchReportStats = async () => {
  try {
    const params = new URLSearchParams();
    if (dateRange.value !== 'all') {
      params.append('period', dateRange.value);
    }

    const response = await httpClient.get<ReportStats>(`${ENDPOINTS.REPORTS.STATS}?${params.toString()}`);
    stats.value = response;
  } catch (error) {
    console.error('Error fetching report stats:', error);
    $toast.error('Failed to load report statistics');
  }
};





const reports = ref<Report[]>([]);

const fetchReports = async () => {
  try {
    loading.value = true;
    const params = new URLSearchParams({
      page: currentPage.value.toString(),
      limit: itemsPerPage.toString(),
      search: searchQuery.value,
      type: reportType.value !== 'all' ? reportType.value : '',
      period: dateRange.value !== 'all' ? dateRange.value : '',
      start_date: startDate.value,
      end_date: endDate.value
    });

    const response = await httpClient.get<{ data: Report[], total: number }>(
      `${ENDPOINTS.REPORTS.BASE}?${params.toString()}`
    );

    reports.value = response.data;
    totalItems.value = response.total;
  } catch (error) {
    console.error('Error fetching reports:', error);
    $toast.error('Failed to load reports');
  } finally {
    loading.value = false;
  }
};

const newReport = async () => {
  try {
    const payload = {
      type: reportType.value !== 'all' ? reportType.value : 'financial',
      period: dateRange.value !== 'all' ? dateRange.value : 'month',
      start_date: startDate.value,
      end_date: endDate.value,
      format: 'pdf'
    };

    await httpClient.post(ENDPOINTS.REPORTS.GENERATE, payload);
    $toast.success('Report generation initiated');

    // Refresh reports list
    await fetchReports();
  } catch (error) {
    console.error('Error generating report:', error);
    $toast.error('Failed to generate report');
  }
};

const exportReports = async () => {
  try {
    const params = new URLSearchParams({
      search: searchQuery.value,
      type: reportType.value !== 'all' ? reportType.value : '',
      period: dateRange.value !== 'all' ? dateRange.value : '',
      start_date: startDate.value,
      end_date: endDate.value,
      format: 'excel'
    });

    const response = await httpClient.get(`${ENDPOINTS.REPORTS.EXPORT}?${params.toString()}`, {
      responseType: 'blob'
    }) as Blob;

    // Create download link
    const url = window.URL.createObjectURL(response);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `reports-${new Date().toISOString().split('T')[0]}.xlsx`);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);

    $toast.success('Reports exported to Excel successfully');
  } catch (error) {
    console.error('Error exporting reports:', error);
    $toast.error('Failed to export reports');
  }
};

const filteredReports = computed(() => {
  // Since filtering and pagination are handled by the backend,
  // we just return the fetched reports directly
  return reports.value;
});

const totalPages = computed(() => Math.ceil(totalItems.value / itemsPerPage));

const paginationStart = computed(() => {
  if (totalItems.value === 0) return 0;
  return (currentPage.value - 1) * itemsPerPage + 1;
});

const paginationEnd = computed(() => {
  return Math.min(currentPage.value * itemsPerPage, totalItems.value);
});

const displayedPages = computed(() => {
  const pages = [];
  const maxPagesToShow = 5;

  if (totalPages.value <= maxPagesToShow) {
    for (let i = 1; i <= totalPages.value; i++) {
      pages.push(i);
    }
  } else {
    const leftSide = Math.floor(maxPagesToShow / 2);
    const rightSide = maxPagesToShow - leftSide - 1;

    if (currentPage.value > leftSide && currentPage.value < totalPages.value - rightSide) {
      // Middle case
      for (let i = currentPage.value - leftSide; i <= currentPage.value + rightSide; i++) {
        pages.push(i);
      }
    } else if (currentPage.value <= leftSide) {
      // Near the start
      for (let i = 1; i <= maxPagesToShow; i++) {
        pages.push(i);
      }
    } else {
      // Near the end
      for (let i = totalPages.value - maxPagesToShow + 1; i <= totalPages.value; i++) {
        pages.push(i);
      }
    }
  }

  return pages;
});

const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);
};

function formatDate(dateString: string | number | Date): string {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}

function formatStatus(status: string): string {
  return status.charAt(0).toUpperCase() + status.slice(1);
}

function formatReportType(type: string): string {
  return type.charAt(0).toUpperCase() + type.slice(1);
}

function getReportIcon(type: string): string {
  switch (type) {
    case 'financial':
      return 'heroicons:currency-dollar';
    case 'sales':
      return 'heroicons:shopping-cart';
    case 'attendance':
      return 'heroicons:users';
    case 'vendor':
      return 'heroicons:building-storefront';
    default:
      return 'heroicons:document-text';
  }
}

function prevPage() {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
}

function nextPage() {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
}

function goToPage(page: number): void {
  currentPage.value = page;
}

const selectedReport = ref<Report | null>(null);
const isViewModalOpen = ref(false);

const viewReport = async (report: Report): Promise<void> => {
  try {
    console.log('Viewing report:', report);

    // First, set the current report data to show something immediately
    selectedReport.value = report;
    isViewModalOpen.value = true;

    // Then try to fetch detailed data from API
    try {
      const response = await httpClient.get(`${ENDPOINTS.REPORTS.BASE}/${report.id}`) as any;
      console.log('API response:', response);

      // Update with API data if available
      if (response && response.data) {
        selectedReport.value = response.data;
      }
    } catch (apiError) {
      console.warn('Failed to fetch detailed report data, using existing data:', apiError);
      // Modal is already open with the basic report data, so this is not critical
    }
  } catch (error) {
    console.error('Error viewing report:', error);
    $toast.error('Failed to load report details');
  }
};

const closeViewModal = (): void => {
  isViewModalOpen.value = false;
  selectedReport.value = null;
};



const formatDataKey = (key: string | number): string => {
  const keyStr = String(key);
  return keyStr.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
};

const formatDataValue = (value: any, key: string | number = ''): string => {
  const keyStr = String(key);
  if (typeof value === 'number') {
    if (keyStr.includes('revenue') || keyStr.includes('amount')) {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
      }).format(value);
    }
    return value.toLocaleString();
  }
  return String(value);
};

const getStatusColor = (status: string | undefined): string => {
  if (!status) return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';

  const colors = {
    completed: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    processing: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    failed: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    scheduled: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    cancelled: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
  };
  return colors[status as keyof typeof colors] || colors.cancelled;
};

const formatType = (type: string | undefined): string => {
  if (!type) return 'Unknown';

  const types = {
    financial: 'Financial',
    sales: 'Sales',
    attendance: 'Attendance',
    vendor: 'Vendor'
  };
  return types[type as keyof typeof types] || type;
};

const formatPeriod = (period: string | undefined): string => {
  if (!period) return 'Unknown';

  const periods = {
    day: 'Daily',
    week: 'Weekly',
    month: 'Monthly',
    quarter: 'Quarterly',
    year: 'Yearly'
  };
  return periods[period as keyof typeof periods] || period;
};

const downloadReport = async (report: Report): Promise<void> => {
  try {
    const response = await httpClient.get(`${ENDPOINTS.REPORTS.BASE}/${report.id}/download`, {
      responseType: 'blob'
    }) as Blob;

    // Create download link
    const url = window.URL.createObjectURL(response);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `${report.name.replace(/\s+/g, '-').toLowerCase()}.pdf`);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);

    $toast.success(`Report "${report.name}" downloaded successfully`);
  } catch (error) {
    console.error('Error downloading report:', error);
    $toast.error('Failed to download report');
  }
};

const cancelReport = async (report: Report): Promise<void> => {
  try {
    await httpClient.delete(`${ENDPOINTS.REPORTS.BASE}/${report.id}`);
    $toast.success(`Report "${report.name}" has been cancelled`);

    // Refresh reports list
    await fetchReports();
  } catch (error) {
    console.error('Error cancelling report:', error);
    $toast.error('Failed to cancel report');
  }
};

const regenerateReport = async (report: Report): Promise<void> => {
  try {
    await httpClient.post(`${ENDPOINTS.REPORTS.BASE}/${report.id}/regenerate`);
    $toast.success(`Report "${report.name}" is being regenerated`);

    // Refresh reports list
    await fetchReports();
  } catch (error) {
    console.error('Error regenerating report:', error);
    $toast.error('Failed to regenerate report');
  }
};

watch([searchQuery, reportType, dateRange, startDate, endDate], () => {
  currentPage.value = 1;
  fetchReports();
});

onMounted(async () => {
  await Promise.all([
    fetchReportStats(),
    fetchReports()
  ]);
});
</script>
