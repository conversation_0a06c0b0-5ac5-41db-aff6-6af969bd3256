<template>
  <div class="bg-gray-50 dark:bg-zinc-800 min-h-screen">
    <div class="p-4 sm:p-6">
      <div class="mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Notifications</h1>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Stay updated with important alerts and messages</p>
          </div>
          <div class="mt-4 md:mt-0 flex space-x-3">
            <NuxtLink to="/vendor/dashboard/settings/notifications" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 focus:outline-none">
              Notification Settings
            </NuxtLink>
          </div>
        </div>
      </div>
      <div class="mb-6">
        <div class="border-b border-gray-200 dark:border-gray-700">
          <nav class="-mb-px flex space-x-8">
            <button
              v-for="tab in tabs"
              :key="tab.name"
              @click="changeTab(tab)"
              :class="[
                tab.current
                  ? 'border-red-500 text-red-600 dark:text-red-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600',
                'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm'
              ]"
            >
              {{ tab.name }}
              <span
                v-if="tab.count > 0"
                :class="[
                  tab.current ? 'bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400' : 'bg-gray-100 dark:bg-zinc-700 text-gray-900 dark:text-gray-300',
                  'ml-2 py-0.5 px-2.5 text-xs font-medium'
                ]"
              >
                {{ tab.count }}
              </span>
            </button>
          </nav>
        </div>
      </div>

      <div class="mb-4 flex justify-end">
        <button
          @click="markAllAsRead"
          class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 dark:bg-zinc-700 dark:border-none dark:text-zinc-300 focus:outline-none"
        >
          Mark All as Read
        </button>
      </div>

      <div v-if="loading" class="bg-white shadow-sm overflow-hidden">
        <div class="divide-y divide-gray-200">
          <div v-for="i in 5" :key="i" class="p-6 animate-pulse">
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <div class="w-10 h-10 bg-gray-200"></div>
              </div>
              <div class="ml-4 flex-1">
                <div class="flex items-center justify-between">
                  <div class="h-4 bg-gray-200 w-1/3"></div>
                  <div class="h-4 bg-gray-200 w-1/4"></div>
                </div>
                <div class="mt-2 h-4 bg-gray-200 w-3/4"></div>
                <div class="mt-2 h-4 bg-gray-200 w-1/2"></div>
                <div class="mt-2 flex">
                  <div class="h-4 bg-gray-200 w-1/5"></div>
                  <div class="ml-4 h-4 bg-gray-200 w-1/5"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-else-if="notifications.length > 0" class="bg-white dark:bg-zinc-900 shadow-sm overflow-hidden">
        <div class="divide-y divide-gray-200 dark:divide-zinc-700">
          <div v-for="notification in notifications" :key="notification.id"
            class="p-6 hover:bg-gray-50 hover:dark:bg-zinc-800 transition-colors duration-150"
            :class="{ 'bg-red-50 dark:bg-red-900/20': !notification.read_at }">
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <div class="w-10 h-10 flex items-center justify-center"
                  :class="notificationTypeClasses[notification.data?.type || 'system']?.bg || 'bg-gray-100 dark:bg-zinc-700'">
                  <Icon
                    :icon="notificationTypeClasses[notification.data?.type || 'system']?.icon || 'heroicons:bell'"
                    :class="notificationTypeClasses[notification.data?.type || 'system']?.text || 'text-gray-600'"
                    class="h-5 w-5"
                  />
                </div>
              </div>
              <div class="ml-4 flex-1">
                <div class="flex items-center justify-between">
                  <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ notification.data?.title || 'Notification' }}</h4>
                  <div class="flex items-center">
                    <span v-if="!notification.read_at" class="inline-block h-2 w-2 bg-red-600 mr-2"></span>
                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ formatTime(notification.created_at) }}</p>
                  </div>
                </div>
                <p class="mt-1 text-sm text-gray-700 dark:text-gray-300">{{ notification.data?.message || notification.data?.line || 'No message' }}</p>
                <div class="mt-2 flex">
                  <button
                    v-if="notification.data?.action"
                    class="text-sm font-medium text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300"
                    @click="notificationAction(notification)"
                  >
                    {{ notification.data.action }}
                  </button>
                  <button
                    class="ml-4 text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                    @click="markAsRead(notification.id)"
                  >
                    {{ notification.read_at ? 'Mark as unread' : 'Mark as read' }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="px-6 py-3 bg-gray-50 dark:bg-zinc-800 flex items-center justify-between">
          <div class="flex-1 flex justify-between sm:hidden">
            <button
              @click="fetchNotifications(pagination.currentPage - 1, currentTab)"
              :disabled="!pagination.prevPageUrl"
              :class="[
                !pagination.prevPageUrl ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50 dark:hover:bg-gray-700',
                'relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-800'
              ]"
            >
              Previous
            </button>
            <button
              @click="fetchNotifications(pagination.currentPage + 1, currentTab)"
              :disabled="!pagination.nextPageUrl"
              :class="[
                !pagination.nextPageUrl ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50 dark:hover:bg-gray-700',
                'ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-800'
              ]"
            >
              Next
            </button>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700 dark:text-gray-300">
                Showing <span class="font-medium">{{ (pagination.currentPage - 1) * pagination.perPage + 1 }}</span> to
                <span class="font-medium">{{ Math.min(pagination.currentPage * pagination.perPage, pagination.total) }}</span> of
                <span class="font-medium">{{ pagination.total }}</span> results
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex shadow-sm -space-x-px" aria-label="Pagination">
                <button
                  @click="fetchNotifications(pagination.currentPage - 1, currentTab)"
                  :disabled="!pagination.prevPageUrl"
                  :class="[
                    !pagination.prevPageUrl ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50 dark:hover:bg-gray-700',
                    'relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-zinc-800 text-sm font-medium text-gray-500 dark:text-gray-400'
                  ]"
                >
                  <span class="sr-only">Previous</span>
                  <Icon icon="heroicons:chevron-left" class="h-5 w-5" />
                </button>

                <template v-for="page in pagination.lastPage" :key="page">
                  <button
                    v-if="page === 1 || page === pagination.lastPage || (page >= pagination.currentPage - 1 && page <= pagination.currentPage + 1)"
                    @click="fetchNotifications(page, currentTab)"
                    :class="[
                      pagination.currentPage === page ? 'bg-red-50 dark:bg-red-900/30 border-red-500 dark:border-red-700 text-red-600 dark:text-red-400' : 'bg-white dark:bg-zinc-800 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700',
                      'relative inline-flex items-center px-4 py-2 border text-sm font-medium'
                    ]"
                  >
                    {{ page }}
                  </button>
                  <span
                    v-else-if="page === 2 || page === pagination.lastPage - 1"
                    class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-zinc-800 text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    ...
                  </span>
                </template>

                <button
                  @click="fetchNotifications(pagination.currentPage + 1, currentTab)"
                  :disabled="!pagination.nextPageUrl"
                  :class="[
                    !pagination.nextPageUrl ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50 dark:hover:bg-gray-700',
                    'relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-zinc-800 text-sm font-medium text-gray-500 dark:text-gray-400'
                  ]"
                >
                  <span class="sr-only">Next</span>
                  <Icon icon="heroicons:chevron-right" class="h-5 w-5" />
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="bg-white dark:bg-zinc-800 shadow-sm overflow-hidden p-8 text-center">
        <div class="flex flex-col items-center justify-center">
          <Icon icon="heroicons:bell" class="h-16 w-16 text-gray-300 dark:text-gray-600 mb-4" />
          <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">No notifications</h3>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">You don't have any notifications at the moment.</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import type { Notification, NotificationsResponse } from '@/types/api';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

dayjs.extend(relativeTime);

definePageMeta({
  layout: 'dashboard'
});

const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const loading = ref<boolean>(true);
const notifications = ref<Notification[]>([]);
const currentTab = ref<string>('all');
const totalUnread = ref<number>(0);

const pagination = ref({
  total: 0,
  perPage: 10,
  currentPage: 1,
  lastPage: 1,
  prevPageUrl: null as string | null,
  nextPageUrl: null as string | null,
});

interface NotificationTypeClass {
  bg: string;
  text: string;
  icon: string;
}

interface NotificationTypeClasses {
  [key: string]: NotificationTypeClass;
}

const notificationTypeClasses: NotificationTypeClasses = {
  booking: {
    bg: 'bg-red-100',
    text: 'text-red-600',
    icon: 'heroicons:calendar'
  },
  message: {
    bg: 'bg-purple-100',
    text: 'text-purple-600',
    icon: 'heroicons:chat-bubble-left-right'
  },
  review: {
    bg: 'bg-yellow-100',
    text: 'text-yellow-600',
    icon: 'heroicons:star'
  },
  payment: {
    bg: 'bg-green-100',
    text: 'text-green-600',
    icon: 'heroicons:banknotes'
  },
  vendor_request_approved: {
    bg: 'bg-green-100',
    text: 'text-green-600',
    icon: 'heroicons:check-badge'
  },
  vendor_request: {
    bg: 'bg-red-100',
    text: 'text-red-600',
    icon: 'heroicons:building-storefront'
  },
  vendor_request_rejected: {
    bg: 'bg-red-100',
    text: 'text-red-600',
    icon: 'heroicons:x-circle'
  },
  system: {
    bg: 'bg-gray-100',
    text: 'text-gray-600',
    icon: 'heroicons:bell'
  }
};

const tabs = computed(() => [
  { name: 'All', key: 'all', current: currentTab.value === 'all', count: pagination.value.total },
  { name: 'Unread', key: 'unread', current: currentTab.value === 'unread', count: totalUnread.value },
  { name: 'Bookings', key: 'booking', current: currentTab.value === 'booking', count: countByType('booking') },
  { name: 'Messages', key: 'message', current: currentTab.value === 'message', count: countByType('message') },
  { name: 'Reviews', key: 'review', current: currentTab.value === 'review', count: countByType('review') },
  { name: 'System', key: 'system', current: currentTab.value === 'system', count: countByType('system') },
]);
function countByType(type: string): number {
  return notifications.value.filter(notification => {
    const notificationType = notification.data?.type || '';
    return notificationType.includes(type);
  }).length;
}

const fetchNotifications = async (page: number = 1, filter: string = 'all'): Promise<void> => {
  try {
    loading.value = true;
    let url = `${ENDPOINTS.NOTIFICATIONS.GET}?page=${page}`;

    if (filter !== 'all') {
      if (filter === 'unread') {
        url += '&unread=1';
      } else {
        url += `&type=${filter}`;
      }
    }

    const response = await httpClient.get<NotificationsResponse>(url);

    if (response) {
      notifications.value = response.data;
      pagination.value = {
        total: response.total,
        perPage: response.per_page,
        currentPage: response.current_page,
        lastPage: response.last_page,
        prevPageUrl: response.prev_page_url,
        nextPageUrl: response.next_page_url,
      };

      totalUnread.value = notifications.value.filter(n => !n.read_at).length;
    }
  } catch (error: any) {
    $toast.error(error.message || 'Failed to fetch notifications');
    console.error('Error fetching notifications:', error);
  } finally {
    loading.value = false;
  }
};
const markAllAsRead = async (): Promise<void> => {
  try {
    const response = await httpClient.get<{ message: string }>(ENDPOINTS.NOTIFICATIONS.READ_ALL);
    if (response) {
      $toast.success(response.message);
      fetchNotifications(pagination.value.currentPage, currentTab.value);
    }
  } catch (error: any) {
    $toast.error(error.message || 'Failed to mark notifications as read');
    console.error('Error marking notifications as read:', error);
  }
};

const markAsRead = async (id: number): Promise<void> => {
  try {
    const response = await httpClient.put<{ success: string }>(
      `${ENDPOINTS.NOTIFICATIONS.READ}/${id}`
    );
    if (response) {
      $toast.success(response.success);
      fetchNotifications(pagination.value.currentPage, currentTab.value);
    }
  } catch (error: any) {
    $toast.error(error.message || 'Failed to mark notification as read');
    console.error('Error marking notification as read:', error);
  }
};

const notificationAction = (notification: Notification): void => {
  if (notification.data?.actionURL) {
    const url = notification.data.actionURL;
    const link = document.createElement('a');
    link.href = url;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};

const changeTab = (tab: { key: string }): void => {
  currentTab.value = tab.key;
  fetchNotifications(1, tab.key);
};

const formatTime = (timestamp: string): string => {
  return dayjs(timestamp).fromNow();
};
onMounted(() => {
  fetchNotifications();
});
</script>
