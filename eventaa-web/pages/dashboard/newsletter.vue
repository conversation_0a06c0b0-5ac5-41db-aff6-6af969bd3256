<template>
  <div class="newsletter-dashboard">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Page Header -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-zinc-900 dark:text-zinc-100">
          Newsletter Dashboard
        </h1>
        <p class="mt-2 text-zinc-600 dark:text-zinc-400">
          Manage your newsletter campaigns and monitor subscriber engagement
        </p>
      </div>

      <!-- Newsletter Management Component -->
      <DashboardNewsletterManagement />

      <!-- Additional Features Section -->
      <div class="mt-12 grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Quick Actions -->
        <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-sm border border-zinc-200 dark:border-zinc-700 p-6">
          <h2 class="text-xl font-semibold text-zinc-900 dark:text-zinc-100 mb-4">
            Quick Actions
          </h2>
          <div class="space-y-4">
            <NuxtLink
              to="/newsletter/preferences"
              class="flex items-center p-4 bg-zinc-50 dark:bg-zinc-700 rounded-lg hover:bg-zinc-100 dark:hover:bg-zinc-600 transition-colors"
            >
              <Icon icon="heroicons:cog-6-tooth" class="w-6 h-6 text-red-500 mr-3" />
              <div>
                <h3 class="font-medium text-zinc-900 dark:text-zinc-100">
                  Manage Preferences
                </h3>
                <p class="text-sm text-zinc-600 dark:text-zinc-400">
                  Update subscription preferences
                </p>
              </div>
              <Icon icon="heroicons:arrow-right" class="w-5 h-5 text-zinc-400 ml-auto" />
            </NuxtLink>

            <button
              @click="downloadSubscribers"
              :disabled="downloading"
              class="w-full flex items-center p-4 bg-zinc-50 dark:bg-zinc-700 rounded-lg hover:bg-zinc-100 dark:hover:bg-zinc-600 transition-colors disabled:opacity-50"
            >
              <Icon icon="heroicons:arrow-down-tray" class="w-6 h-6 text-blue-500 mr-3" />
              <div class="text-left">
                <h3 class="font-medium text-zinc-900 dark:text-zinc-100">
                  Export Subscribers
                </h3>
                <p class="text-sm text-zinc-600 dark:text-zinc-400">
                  Download subscriber list as CSV
                </p>
              </div>
              <Icon
                :icon="downloading ? 'heroicons:arrow-path' : 'heroicons:arrow-right'"
                :class="[
                  'w-5 h-5 text-zinc-400 ml-auto',
                  downloading ? 'animate-spin' : ''
                ]"
              />
            </button>

            <button
              @click="showAnalytics = true"
              class="w-full flex items-center p-4 bg-zinc-50 dark:bg-zinc-700 rounded-lg hover:bg-zinc-100 dark:hover:bg-zinc-600 transition-colors"
            >
              <Icon icon="heroicons:chart-bar" class="w-6 h-6 text-green-500 mr-3" />
              <div class="text-left">
                <h3 class="font-medium text-zinc-900 dark:text-zinc-100">
                  View Analytics
                </h3>
                <p class="text-sm text-zinc-600 dark:text-zinc-400">
                  Detailed subscriber analytics
                </p>
              </div>
              <Icon icon="heroicons:arrow-right" class="w-5 h-5 text-zinc-400 ml-auto" />
            </button>
          </div>
        </div>

        <!-- Newsletter Widget Preview -->
        <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-sm border border-zinc-200 dark:border-zinc-700 p-6">
          <h2 class="text-xl font-semibold text-zinc-900 dark:text-zinc-100 mb-4">
            Subscription Widget
          </h2>
          <p class="text-zinc-600 dark:text-zinc-400 mb-4">
            Preview of the newsletter subscription widget
          </p>

          <UiNewsletterWidget
            variant="compact"
            title="Join Our Newsletter"
            description="Get event updates delivered to your inbox"
            :show-name-field="false"
            @subscribe="handleWidgetSubscribe"
            @success="handleWidgetSuccess"
          />
        </div>
      </div>

      <!-- Analytics Modal -->
      <CoreModal
        v-model="showAnalytics"
        title="Newsletter Analytics"
        max-width="4xl"
      >
        <div class="p-6">
          <div class="text-center py-12">
            <Icon icon="heroicons:chart-bar" class="w-16 h-16 text-zinc-400 mx-auto mb-4" />
            <h3 class="text-lg font-medium text-zinc-900 dark:text-zinc-100 mb-2">
              Analytics Dashboard
            </h3>
            <p class="text-zinc-600 dark:text-zinc-400">
              Detailed analytics feature coming soon. This will include open rates,
              click-through rates, subscriber growth over time, and more.
            </p>
          </div>
        </div>
      </CoreModal>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  middleware: ['auth'],
  layout: 'dashboard',
});

const { $toast }: any = useNuxtApp();

const showAnalytics = ref(false);
const downloading = ref(false);

const downloadSubscribers = async () => {
  downloading.value = true;

  // Simulate download process
  setTimeout(() => {
    downloading.value = false;
    $toast.info("Export feature coming soon!");
  }, 2000);
};

const handleWidgetSubscribe = (data: any) => {
  console.log("Widget subscription:", data);
};

const handleWidgetSuccess = () => {
  $toast.success("Newsletter widget subscription successful!");
};

// Set page title and meta
useHead({
  title: "Newsletter Dashboard - EventaHub Malawi",
  meta: [
    {
      name: "description",
      content: "Manage newsletter campaigns and monitor subscriber engagement",
    },
  ],
});
</script>

<style scoped>
.newsletter-dashboard {
  min-height: 100vh;
  background-color: #f9fafb;
}

.dark .newsletter-dashboard {
  background-color: #111827;
}
</style>
