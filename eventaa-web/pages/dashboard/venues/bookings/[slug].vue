<template>
    <div class="p-6">
        <div class="mb-6 flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold">Venue Bookings</h1>
                <p class="text-gray-500">Manage and track all bookings for your venues</p>
            </div>

            <div class="flex space-x-3">
                <CorePrimaryButton text="Create Booking" start-icon="mdi:calendar-plus"
                    @click="$router.push('/dashboard/venues/bookings/create')" />
            </div>
        </div>

        <div class="w-full flex items-center justify-between mb-4">
            <div class="flex items-center">
                <button @click="activeView = 'all'" class="flex items-center px-2 py-1.5 border"
                    :class="activeView === 'all' ? 'bg-red-600 text-white' : 'bg-white border-gray-300'">
                    <Icon icon="mdi:view-list" class="w-5 h-5 inline mr-1" />
                    All
                </button>
                <button @click="activeView = 'pending'" class="flex items-center px-2 py-1.5 border border-l-0"
                    :class="activeView === 'pending' ? 'bg-red-600 text-white' : 'bg-white border-gray-300'">
                    <Icon icon="hugeicons:hourglass" class="w-5 h-5 inline mr-1" />
                    Pending
                </button>
                <button @click="activeView = 'approved'" class="flex items-center px-2 py-1.5 border border-l-0"
                    :class="activeView === 'approved' ? 'bg-red-600 text-white' : 'bg-white border-gray-300'">
                    <Icon icon="mdi:check-circle-outline" class="w-5 h-5 inline mr-1" />
                    Approved
                </button>
                <button @click="activeView = 'rejected'" class="flex items-center px-2 py-1.5 border border-l-0"
                    :class="activeView === 'rejected' ? 'bg-red-600 text-white' : 'bg-white border-gray-300'">
                    <Icon icon="mdi:close-circle-outline" class="w-5 h-5 inline mr-1" />
                    Rejected
                </button>
            </div>

            <div class="flex items-center space-x-2">
                <div v-if="venueId" class="flex items-center space-x-2">
                    <CorePrimaryButton text="Back to Venues" start-icon="mdi:arrow-left"
                        @click="$router.push('/dashboard/venues')" />
                </div>
                <CoreSearch v-model="search" placeholder="Search bookings..." />
            </div>
        </div>

        <Datatable :server-options="serverOptions" :server-items-length="totalItems" :loading="loading"
            :headers="headers" :items="computedBookings" buttons-pagination :rows-items="[10, 25, 50, 100]"
            theme-color="#dc2626" @update:options="handleOptionsChange" alternating>
            <template #loading>
                <CoreLoader />
            </template>
            <template #item-status="item">
                <div class="py-1 px-3 text-sm rounded-full font-semibold text-center whitespace-nowrap" :class="{
                    'text-yellow-500': item.status === 'pending',
                    'text-green-600': item.status === 'approved',
                    'text-red-600': item.status === 'rejected'
                }">
                    {{ item.status }}
                </div>
            </template>
            <template #item-venue="item">
                <div class="whitespace-nowrap">
                    {{ item.venue?.name }}
                </div>
            </template>
            <template #item-booking_period="item">
                <div>
                    <div class="flex items-center space-x-1">
                        <Icon icon="mdi:calendar-start" class="w-4 h-4 text-gray-500" />
                        <span>{{ item.booking_from }}</span>
                    </div>
                    <div class="flex items-center space-x-1 mt-1">
                        <Icon icon="mdi:calendar-end" class="w-4 h-4 text-gray-500" />
                        <span>{{ item.booking_to }}</span>
                    </div>
                </div>
            </template>
            <template #item-user="item">
                <div class="whitespace-nowrap">
                    {{ item.user?.name }}
                </div>
            </template>
            <template #item-actions="item">
                <div class="flex space-x-2">
                    <button @click="viewBookingDetails(item)" class="p-1 hover:text-blue-600">
                        <Icon icon="solar:eye-broken" class="w-5 h-5" />
                    </button>
                    <button v-if="item.status === 'pending'" @click="openStatusDialog(item, 'approved')"
                        class="p-1 hover:text-green-600">
                        <Icon icon="mdi:check-circle" class="w-5 h-5" />
                    </button>
                    <button v-if="item.status === 'pending'" @click="openStatusDialog(item, 'rejected')"
                        class="p-1 hover:text-red-600">
                        <Icon icon="mdi:close-circle" class="w-5 h-5" />
                    </button>
                    <button @click="openDeleteDialog(item)" class="p-1 hover:text-red-600">
                        <Icon icon="mage:trash" class="w-5 h-5" />
                    </button>
                </div>
            </template>
        </Datatable>

        <TransitionRoot appear :show="isBookingDetailsOpen" as="template">
            <Dialog as="div" @close="closeBookingDetails" class="relative z-10">
                <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0"
                    enter-to="opacity-100" leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                    <div class="fixed inset-0 bg-black bg-opacity-25" />
                </TransitionChild>

                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                            enter-to="opacity-100 scale-100" leave="duration-200 ease-in"
                            leave-from="opacity-100 scale-100" leave-to="opacity-0 scale-95">
                            <DialogPanel
                                class="w-full max-w-2xl transform overflow-hidden bg-white text-left align-middle shadow-xl transition-all">
                                <DialogTitle as="h3"
                                    class="text-xl font-semibold p-2 leading-6 text-gray-900 border-b flex justify-between items-center">
                                    Booking Details
                                    <button @click="closeBookingDetails" class="p-1 hover:bg-gray-100 rounded-full">
                                        <Icon icon="mdi:close" class="w-5 h-5" />
                                    </button>
                                </DialogTitle>

                                <div v-if="selectedBooking" class="mt-4 px-5 py-2">
                                    <div class="flex justify-between items-start mb-4">
                                        <div>
                                            <h4 class="font-medium text-lg">{{ selectedBooking.venue?.name }}</h4>
                                            <p class="text-base text-gray-500">{{ selectedBooking.venue?.address }}, {{
                                                selectedBooking.venue?.city }}</p>
                                        </div>
                                        <div class="py-1 px-3 text-white text-center rounded-full texts-m font-semibold"
                                            :class="{
                                                'text-yellow-400': selectedBooking.status === 'pending',
                                                'text-green-400': selectedBooking.status === 'approved',
                                                'text-red-400': selectedBooking.status === 'rejected'
                                            }">
                                            {{ selectedBooking.status }}
                                        </div>
                                    </div>

                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                        <div>
                                            <h5 class="text-lg font-medium text-gray-700">Booking Period</h5>
                                            <div class="flex items-center space-x-1 mt-1">
                                                <Icon icon="mdi:stopwatch-start" class="w-5 h-5 text-gray-500" />
                                                <span>{{ formatDate(selectedBooking.booking_from) }}</span>
                                            </div>
                                            <div class="flex items-center space-x-1 mt-1">
                                                <Icon icon="mdi:stopwatch-stop" class="w-5 h-5 text-gray-500" />
                                                <span>{{ formatDate(selectedBooking.booking_to) }}</span>
                                            </div>
                                        </div>

                                        <div>
                                            <h5 class="text-lg font-medium text-gray-700">Request Details</h5>
                                            <div class="flex items-center space-x-1 mt-1">
                                                <Icon icon="solar:user-hand-up-bold-duotone"
                                                    class="w-5 h-5 text-gray-500" />
                                                <span class="text-base">{{ selectedBooking.user?.name }}</span>
                                            </div>
                                            <div class="flex items-center space-x-1 mt-1">
                                                <Icon icon="si:mail-duotone" class="w-5 h-5 text-gray-500" />
                                                <span class="text-base">{{ selectedBooking.user?.email }}</span>
                                            </div>
                                            <div class="flex items-center space-x-1 mt-1">
                                                <Icon icon="grommet-icons:group" class="w-5 h-5 text-gray-500" />
                                                <span class="text-base">{{ selectedBooking.number_of_guests }}
                                                    Guests</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-4">
                                        <h5 class="text-lg font-medium text-gray-700">Category</h5>
                                        <p class="text-lg">{{ selectedBooking.category?.name }}</p>
                                    </div>

                                    <div class="mb-4">
                                        <h5 class="text-lg font-medium text-gray-700">Message</h5>
                                        <p class="text-gray-700 whitespace-pre-line text-lg">{{ selectedBooking.message
                                        }}</p>
                                    </div>

                                    <div class="mb-4">
                                        <h5 class="text-lg font-medium text-gray-700">Price Option</h5>
                                        <div>
                                            <div>
                                                <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-1">{{ new
                                                    Intl.NumberFormat('en-US',
                                                        {
                                                            style: 'currency', currency:
                                                                selectedBooking?.venue_price?.currency.name
                                                        }).format(Number(selectedBooking?.venue_price?.price)) }}</h3>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="flex justify-end space-x-3 mt-6">
                                        <button type="button" @click="closeBookingDetails"
                                            class="px-4 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50">
                                            Close
                                        </button>
                                        <div v-if="selectedBooking.status === 'pending'" class="flex space-x-2">
                                            <button type="button" @click="openStatusDialog(selectedBooking, 'approved')"
                                                class="px-4 py-2 bg-green-600 text-white hover:bg-green-700">
                                                Approve
                                            </button>
                                            <button type="button" @click="openStatusDialog(selectedBooking, 'rejected')"
                                                class="px-4 py-2 bg-red-600 text-white hover:bg-red-700">
                                                Reject
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>

        <TransitionRoot appear :show="isStatusDialogOpen" as="template">
            <Dialog as="div" @close="closeStatusDialog" class="relative z-10">
                <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0"
                    enter-to="opacity-100" leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                    <div class="fixed inset-0 bg-black bg-opacity-25" />
                </TransitionChild>

                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                            enter-to="opacity-100 scale-100" leave="duration-200 ease-in"
                            leave-from="opacity-100 scale-100" leave-to="opacity-0 scale-95">
                            <DialogPanel
                                class="w-full max-w-md transform overflow-hidden bg-white p-6 text-left align-middle shadow-xl transition-all">
                                <DialogTitle as="h3" class="text-xl font-semibold leading-6 text-gray-900">
                                    {{ newStatus === 'approved' ? 'Approve' : 'Reject' }} Booking
                                </DialogTitle>
                                <div class="mt-4">
                                    <p class="text-gray-600">
                                        Are you sure you want to {{ newStatus === 'approved' ? 'approve' : 'reject' }}
                                        this
                                        booking for
                                        <strong>{{ bookingToUpdate?.venue?.name }}</strong>?
                                    </p>

                                    <div class="mt-6 flex justify-end space-x-3">
                                        <button type="button" @click="closeStatusDialog"
                                            class="px-4 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50">
                                            Cancel
                                        </button>
                                        <CorePrimaryButton
                                            :text="isSubmitting ? 'Processing...' : (newStatus === 'approved' ? 'Approve' : 'Reject')"
                                            :loading="isSubmitting" @click="updateBookingStatus"
                                            :class="newStatus === 'rejected' ? 'bg-red-600 hover:bg-red-700' : ''" />
                                    </div>
                                </div>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>

        <TransitionRoot appear :show="isDeleteDialogOpen" as="template">
            <Dialog as="div" @close="closeDeleteDialog" class="relative z-10">
                <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0"
                    enter-to="opacity-100" leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                    <div class="fixed inset-0 bg-black bg-opacity-25" />
                </TransitionChild>

                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                            enter-to="opacity-100 scale-100" leave="duration-200 ease-in"
                            leave-from="opacity-100 scale-100" leave-to="opacity-0 scale-95">
                            <DialogPanel
                                class="w-full max-w-md transform overflow-hidden bg-white p-6 text-left align-middle shadow-xl transition-all">
                                <DialogTitle as="h3" class="text-xl font-semibold leading-6 text-gray-900">
                                    Delete Booking
                                </DialogTitle>
                                <div class="mt-4">
                                    <p class="text-gray-600">
                                        Are you sure you want to delete this booking for
                                        <strong>{{ bookingToDelete?.venue?.name }}</strong>?
                                        This action cannot be undone.
                                    </p>

                                    <div class="mt-6 flex justify-end space-x-3">
                                        <button type="button" @click="closeDeleteDialog"
                                            class="px-4 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50">
                                            Cancel
                                        </button>
                                        <CorePrimaryButton :text="isSubmitting ? 'Deleting...' : 'Delete'"
                                            :loading="isSubmitting" @click="deleteBooking"
                                            class="bg-red-600 hover:bg-red-700" />
                                    </div>
                                </div>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>
    </div>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router';
import { Dialog, DialogPanel, DialogTitle, TransitionRoot, TransitionChild } from '@headlessui/vue';
import type { Venue } from '@/types';

definePageMeta({
    layout: "dashboard",
});

useHead({
    title: "Manage Bookings | EventaHub Malawi",
    meta: [
        {
            name: "description",
            content: "Manage all venue bookings on our platform."
        }
    ]
});

const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();
const route = useRoute();
const venueId = route.params.venueId;
const search = ref<string>('');
const activeView = ref<'all' | 'pending' | 'approved' | 'rejected'>('all');

interface Booking {
    id: number;
    venue_id: number;
    user_id: number;
    category_id: number;
    booking_from: string;
    booking_to: string;
    number_of_guests: number;
    message: string;
    status: 'pending' | 'approved' | 'rejected';
    venue?: Venue;
    user?: { id: number; name: string; email: string };
    category?: { id: number; name: string };
    venue_price?: { id: number; name: string; type: string; price: number, currency: { id: number; name: string; code: string; symbol: string } };
}

const bookings = ref<Booking[]>([]);
const serverOptions = ref({
    page: 1,
    rowsPerPage: 25
});
const totalItems = ref<number>(0);
const loading = ref<boolean>(false);
const isBookingDetailsOpen = ref<boolean>(false);
const selectedBooking = ref<Booking | null>(null);
const isStatusDialogOpen = ref<boolean>(false);
const bookingToUpdate = ref<Booking | null>(null);
const newStatus = ref<'approved' | 'rejected'>('approved');
const isDeleteDialogOpen = ref<boolean>(false);
const bookingToDelete = ref<Booking | null>(null);
const isSubmitting = ref<boolean>(false);

const headers = [
    { text: "VENUE NAME", value: "name" },
    { text: "BOOKING PERIOD", value: "booking_period" },
    { text: "GUESTS", value: "guests" },
    { text: "STATUS", value: "status" },
    { text: "REQUESTED BY", value: "user" },
    { text: "ACTIONS", value: "actions", sortable: false },
];

function formatDate(dateStr: string) {
    const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateStr).toLocaleDateString(undefined, options);
}

const computedBookings = computed(() => {
    return bookings.value.length > 0 ? bookings.value.map((booking: Booking) => {
        return {
            ...booking,
            name: booking.venue?.name,
            guests: booking.number_of_guests,
            status: booking.status,
            user: booking.user,
        }
    }) : []
});

async function fetchBookings(): Promise<void> {
    loading.value = true;
    try {
        const { page, rowsPerPage } = serverOptions.value;
        const params: any = {
            page,
            per_page: rowsPerPage,
            search: search.value,
            status: activeView.value !== 'all' ? activeView.value : undefined,
            venue_id: venueId || undefined,
        };
        const response: any = await httpClient.get(`${ENDPOINTS.VENUES.BOOKINGS}/${route.params.slug}`, { params });
        bookings.value = response.bookings.data;
        totalItems.value = response.bookings.total;
    } catch (error) {
        console.error(error);
        $toast.error("Failed to load bookings.");
    } finally {
        loading.value = false;
    }
}

function handleOptionsChange(newOptions: any) {
    serverOptions.value = newOptions;
}

function viewBookingDetails(booking: Booking): void {
    selectedBooking.value = booking;
    isBookingDetailsOpen.value = true;
}

function closeBookingDetails(): void {
    selectedBooking.value = null;
    isBookingDetailsOpen.value = false;
}

function openStatusDialog(booking: Booking, status: 'approved' | 'rejected'): void {
    bookingToUpdate.value = booking;
    newStatus.value = status;
    isStatusDialogOpen.value = true;
}

function closeStatusDialog(): void {
    bookingToUpdate.value = null;
    newStatus.value = 'approved';
    isStatusDialogOpen.value = false;
}

async function updateBookingStatus(): Promise<void> {
    if (!bookingToUpdate.value) return;
    isSubmitting.value = true;
    try {
        await httpClient.patch(`${ENDPOINTS.BOOKINGS.BASE}/${bookingToUpdate.value.id}/status`, {
            status: newStatus.value
        });
        $toast.success(`Booking ${newStatus.value} successfully.`);
        closeStatusDialog();
        closeBookingDetails();
        await fetchBookings();
    } catch (error) {
        console.error(error);
        $toast.error("Failed to update booking status.");
    } finally {
        isSubmitting.value = false;
    }
}

function openDeleteDialog(booking: Booking): void {
    bookingToDelete.value = booking;
    isDeleteDialogOpen.value = true;
}

function closeDeleteDialog(): void {
    bookingToDelete.value = null;
    isDeleteDialogOpen.value = false;
}

async function deleteBooking(): Promise<void> {
    if (!bookingToDelete.value) return;
    isSubmitting.value = true;
    try {
        await httpClient.delete(`${ENDPOINTS.BOOKINGS.DELETE}/${bookingToDelete.value.id}`);
        $toast.success("Booking deleted successfully.");
        closeDeleteDialog();
        closeBookingDetails();
        await fetchBookings();
    } catch (error) {
        console.error(error);
        $toast.error("Failed to delete booking.");
    } finally {
        isSubmitting.value = false;
    }
}

watch([activeView, search], async () => {
    await fetchBookings();
});

onMounted(async () => {
    await fetchBookings();
});
</script>
