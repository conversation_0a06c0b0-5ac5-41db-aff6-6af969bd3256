<template>
    <div class="m-5 bg-white dark:bg-zinc-800 shadow rounded-none">
        <div :class="[
            'flex items-center space-x-5 px-10 py-5 border-b dark:border-zinc-700',
        ]">
            <Icon icon="proicons:building-multiple" class="w-12 h-12 dark:text-gray-200" />
            <div class="flex flex-col">
                <h1 class="text-2xl font-bold text-black dark:text-zinc-100">Update Venue</h1>
                <p class="text-gray-500 dark:text-zinc-300">Edit all required fields including basic information,
                    location, pricing and media files
                </p>
            </div>
        </div>
        <CoreVenueStepper :venue="venue" :endpoint="`${ENDPOINTS.VENUES.UPDATE}/${$route.params.slug}`"
            v-if="!fetching" />
    </div>
</template>

<script setup lang="ts">
import type { VenueForm, VenuePrice } from '@/types';

definePageMeta({
    layout: "dashboard"
});

useHead({
    title: "Update Venue | EventaHub Malawi",
    meta: [
        {
            name: "description",
            content: "Create a new venue on our platform."
        }
    ]
});

const httpClient = useHttpClient();
const runtimeConfig = useRuntimeConfig();
const route = useRoute();
const fetching = ref(false);
const venue = ref<VenueForm>({
    name: '',
    address: '',
    city: '',
    state: '',
    zip: '',
    country: '',
    phone: '',
    email: '',
    website: null,
    description: '',
    logo: null,
    capacity: null,
    latitude: 40.7128,
    longitude: -74.0060,
    images: [],
    videos: [],
    prices: [],
    activities: [],
    slug: ''
});

const fetchVenue = async (): Promise<void> => {
    fetching.value = true;
    try {
        const response: any = await httpClient.get(`${ENDPOINTS.VENUES.READ}/${route.params.slug}`);
        if (response && response.venue) {
            venue.value = {
                name: response.venue.name || '',
                address: response.venue.address || '',
                city: response.venue.city || '',
                state: response.venue.state || '',
                slug: response.venue.slug || '',
                zip: response.venue.zip || '',
                country: response.venue.country || '',
                phone: response.venue.phone || '',
                email: response.venue.email || '',
                website: response.venue.website || null,
                description: response.venue.description || '',
                logo: `${runtimeConfig.public.baseUrl}storage/${response.venue.logo}` || "",
                capacity: response.venue.capacity || 0,
                latitude: response.venue.latitude || 40.7128,
                longitude: response.venue.longitude || -74.0060,
                images: response.venue.images.map((image: { image_path: string; }) => ({
                    image_path: `${runtimeConfig.public.baseUrl}storage/${image.image_path}`
                })).map((image: { image_path: string; }) => image.image_path) || [],
                videos: response.venue.videos.map((video: { video_path: string; }) => ({
                    video_path: `${runtimeConfig.public.baseUrl}storage/${video.video_path}`
                })).map((video: { video_path: string; }) => video.video_path) || [],
                activities: response.venue.activities.map(
                    (activity: { category: { name: string } }) => activity.category.name
                ) || [],
                prices: response.venue.prices.map((price: VenuePrice) => ({
                    amount: price.price,
                    currency: price.currency.id,
                    attributes: JSON.parse(price.ammenities),
                })) || []
            };
        }
    } catch (error) {
        console.log("Error fetching venue:", error);
    } finally {
        fetching.value = false;
    }
}

onMounted(() => {
    fetchVenue();
});
</script>

<style scoped></style>
