<template>
    <div class="m-5 bg-white dark:bg-black shadow rounded-none">
        <div :class="[
            'flex items-center space-x-5 px-10 py-5 border-b',
        ]">
            <Icon icon="fa6-solid:map-location-dot" class="w-auto h-10" />
            <div class="flex flex-col">
                <h1 class="text-2xl font-bold text-black">Create Venue</h1>
                <p class="text-gray-500">Enter all required fields including basic information, location, media files
                </p>
            </div>
        </div>
        <CoreVenueStepper :venue="venue" :endpoint="ENDPOINTS.VENUES.CREATE"/>
    </div>
</template>

<script setup lang="ts">
import type { VenueForm } from '@/types';

definePageMeta({
    layout: "dashboard"
});

useHead({
    title: "Create Venue | EventaHub Malawi",
    meta: [
        {
            name: "description",
            content: "Create a new venue on our platform."
        }
    ]
});

const venue: VenueForm = {
    name: '',
    address: '',
    city: '',
    state: '',
    zip: '',
    country: '',
    phone: '',
    email: '',
    website: null,
    description: '',
    logo: null,
    capacity: null,
    latitude: null,
    longitude: null,
    images: [],
    videos: [],
    prices: [],
    activities: [],
    slug: ''
}
</script>

<style scoped></style>