<template>
    <div class="p-6 h-screen dark:bg-zinc-900">
        <div class="mb-6 flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-zinc-900 dark:text-zinc-50">Venue Details</h1>
                <p class="text-gray-500 dark:text-zinc-400">View detailed information about this venue</p>
            </div>

            <div class="flex items-center">
                <CorePrimaryButton text="Edit" color="neutral" start-icon="majesticons:edit-pen-4-line"
                    @click="$router.push(`/dashboard/venues/update/${venue?.slug}`)" />
                <CorePrimaryButton text="View Bookings" start-icon="solar:calendar-mark-bold-duotone"
                    @click="$router.push(`/dashboard/venues/bookings/${venue?.id}`)" />
            </div>
        </div>

        <div v-if="loading" class="flex justify-center items-center h-64">
            <CoreLoader />
        </div>

        <div v-else-if="venue" class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-white dark:bg-zinc-800 shadow p-5 col-span-2">
                <div class="flex flex-col items-start mb-4">
                    <h2 class="text-xl font-semibold text-zinc-800 dark:text-zinc-100">{{ venue.name }}</h2>
                    <div class="mb-2">
                        <h3 class="text-lg font-semibold text-zinc-600 dark:text-zinc-200 mb-2">Description</h3>
                        <p class="text-gray-600 dark:text-zinc-400">{{ venue.description || 'No description available.'
                            }}</p>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div>
                        <h3 class="text-lg font-semibold text-zinc-600 dark:text-zinc-200 mb-2">Location</h3>
                        <div class="flex items-start space-x-2">
                            <Icon icon="fa6-solid:map-location-dot"
                                class="w-6 h-6 mt-1 text-zinc-600 dark:text-zinc-300" />
                            <div class="text-zinc-700 dark:text-zinc-100">
                                <p>{{ venue.address }}</p>
                                <p>{{ venue.city }}, {{ venue.country }}</p>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-zinc-600 dark:text-zinc-200 mb-2">Contact</h3>
                        <div class="flex items-center space-x-2 mb-2 text-zinc-700 dark:text-zinc-100">
                            <Icon icon="mdi:phone" class="w-6 h-6" />
                            <p>{{ venue.phone || 'Not provided' }}</p>
                        </div>
                        <div class="flex items-center space-x-2 text-zinc-700 dark:text-zinc-100">
                            <Icon icon="mdi:email" class="w-6 h-6" />
                            <p>{{ venue.email || 'Not provided' }}</p>
                        </div>
                    </div>
                </div>

                <div>
                    <h3 class="text-lg font-semibold text-zinc-600 dark:text-zinc-200 mb-2">Amenities</h3>
                    <div class="flex flex-wrap gap-2">
                        <div v-for="(amenity, index) in venueAmenities" :key="index"
                            class="flex items-center border border-zinc-300 dark:border-zinc-700 px-3 py-1 text-zinc-700 dark:text-zinc-100">
                            <Icon icon="mdi:check-circle" class="w-4 h-4 text-green-600 mr-2" />
                            <span>{{ amenity }}</span>
                        </div>
                        <div v-if="venueAmenities.length === 0" class="text-gray-500 dark:text-zinc-400">
                            No amenities listed
                        </div>
                    </div>
                </div>
            </div>

            <div class="space-y-6">
                <div class="bg-white dark:bg-zinc-800 shadow">
                    <div class="px-4 py-2 border-b border-zinc-200 dark:border-zinc-700">
                        <h3 class="font-semibold text-xl text-gray-700 dark:text-zinc-100">Location Map</h3>
                    </div>
                    <div>
                        <div v-if="venue.latitude && venue.longitude" class="h-64">
                            <GoogleMap :api-key="runtimeConfig.public.googleMapsApiKey" class="h-full w-full"
                                :center="{ lat: Number(venue.latitude), lng: Number(venue.longitude) }" :zoom="15"
                                mapTypeId="roadmap">
                                <CustomMarker :options="{
                                    position: { lat: Number(venue.latitude), lng: Number(venue.longitude) },
                                    anchorPoint: 'BOTTOM_CENTER',
                                }">
                                    <div style="text-align: center">
                                        <div class="relative shadow-xl shadow-black">
                                            <button
                                                class="relative inline-flex items-center justify-center p-2 cursor-pointer bg-red-600 text-white">
                                                <Icon icon="fa6-solid:map-location-dot" class="w-6 h-6" />
                                            </button>
                                            <span
                                                class="absolute left-1/2 -bottom-1 transform -translate-x-1/2 w-0 h-0 border-l-[6px] border-l-transparent border-r-[6px] border-r-transparent border-t-[6px] border-t-red-600"></span>
                                        </div>
                                    </div>
                                </CustomMarker>
                            </GoogleMap>
                        </div>
                        <div v-if="venue.latitude == null" class="h-64 bg-gray-100 dark:bg-zinc-900 flex items-center justify-center">
                            <div class="text-center text-gray-500 dark:text-zinc-400">
                                <Icon icon="mdi:map-marker-off" class="w-10 h-10 mx-auto mb-2" />
                                <p>Location coordinates not available</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-zinc-800 shadow">
                    <div class="px-4 py-2 border-b border-zinc-200 dark:border-zinc-700">
                        <h3 class="font-semibold text-xl text-gray-700 dark:text-zinc-100">Pricing Options</h3>
                    </div>
                    <div class="p-5">
                        <div v-if="venue.prices && venue.prices.length > 0">
                            <div class="space-y-6">
                                <div v-for="price in venue.prices"
                                    class="border-b border-gray-200 dark:border-zinc-700 pb-4">
                                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-1">
                                        {{
                                            new Intl.NumberFormat('en-US', {
                                                style: 'currency',
                                                currency: price.currency.name,
                                        }).format(Number(price.price))
                                        }}
                                    </h3>
                                    <div>
                                        <ul class="space-y-2">
                                            <li v-for="item in JSON.parse(String(price.ammenities))"
                                                class="flex items-center space-x-2 text-zinc-700 dark:text-zinc-100">
                                                <div
                                                    class="w-10 h-10 rounded bg-zinc-200 dark:bg-zinc-700 flex items-center justify-center p-2">
                                                    <Icon icon="solar:bag-check-bold-duotone"
                                                        class="h-6 w-6 text-zinc-600 dark:text-zinc-200" />
                                                </div>
                                                <span>{{ item }}</span>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-else class="text-center py-4 text-gray-500 dark:text-zinc-400">
                            <Icon icon="mdi:currency-usd-off" class="w-8 h-8 mx-auto mb-2" />
                            <p>No pricing information available</p>
                        </div>
                    </div>

                    <div class="px-4 py-2 border-b border-zinc-200 dark:border-zinc-700">
                        <h3 class="font-semibold text-xl text-gray-700 dark:text-zinc-100">Activities</h3>
                    </div>
                    <div class="p-5">
                        <div>
                            <div v-for="activity in venue.activities as any" :key="activity?.id"
                                class="flex items-center space-x-2 mb-4">
                                <div class="flex items-center justify-center p-2">
                                    <img :src="`${runtimeConfig.public.baseUrl}storage/categories/${activity.category.icon}`"
                                        :alt="activity.category.name" class="w-auto h-10 object-contain" />
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                        {{ activity.category.name }}
                                    </h3>
                                    <p class="text-gray-500 dark:text-zinc-400">{{ activity.category.description }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div v-else-if="error" class="bg-red-50 dark:bg-red-950 border border-red-200 dark:border-red-600 p-4 rounded">
            <div class="flex items-center space-x-2">
                <Icon icon="mdi:alert-circle" class="w-5 h-5 text-red-500 dark:text-red-400" />
                <p class="text-red-600 dark:text-red-300">{{ error }}</p>
            </div>
            <button @click="fetchVenue" class="mt-3 text-red-600 hover:text-red-800 dark:hover:text-red-400">
                Try again
            </button>
        </div>
    </div>
</template>


<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRoute } from 'vue-router';
import { GoogleMap } from 'vue3-google-map';
import type { Venue } from '@/types';

definePageMeta({
    layout: "dashboard"
});

useHead({
    title: "Venue Details | EventaHub Malawi",
    meta: [
        {
            name: "description",
            content: "View detailed information about this venue."
        }
    ]
});

const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();
const route = useRoute();
const runtimeConfig = useRuntimeConfig();
const venue = ref<Venue | null>(null);
const loading = ref<boolean>(true);
const error = ref<string>('');

const venueAmenities = computed<string[]>(() => {
    return Array.isArray(venue.value?.amenities) ? venue.value.amenities : [];
});

onMounted(() => {
    fetchVenue();
});

const fetchVenue = async (): Promise<void> => {
    loading.value = true;
    error.value = '';

    try {
        const response: any = await httpClient.get(`${ENDPOINTS.VENUES.READ}/${route.params.slug}`);
        venue.value = response.venue as Venue;
    } catch (err) {
        console.error('Error fetching venue:', err);
        error.value = 'Failed to load venue details. Please try again later.';
        $toast.error('Failed to load venue details');
    } finally {
        loading.value = false;
    }
};
</script>
