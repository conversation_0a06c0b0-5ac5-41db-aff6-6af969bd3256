<template>
    <div class="p-6 bg-zinc-100 dark:bg-zinc-900 min-h-screen">
        <div class="mb-6 flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Venues</h1>
                <p class="text-gray-500 dark:text-gray-400">Manage places where your events takes place as well as bookings</p>
            </div>

            <div class="flex space-x-2">
                <button
                    @click="refreshData"
                    :disabled="loading"
                    class="p-2.5 flex items-center justify-center rounded-full bg-white dark:bg-zinc-800 border border-gray-300 dark:border-zinc-600 shadow-sm hover:bg-gray-50 dark:hover:bg-zinc-700 text-gray-700 dark:text-gray-200 disabled:opacity-50"
                    title="Refresh"
                >
                    <Icon icon="famicons:refresh-outline" class="w-5 h-5" />
                </button>
                <button
                    @click="$router.push('/dashboard/venues/create')"
                    class="p-2.5 flex items-center justify-center rounded-full bg-red-600 text-white shadow-sm hover:bg-red-700"
                    title="Add Venue"
                    style="border-radius: 300px;"
                >
                    <Icon icon="mdi:plus" class="w-5 h-5" />
                </button>
            </div>
        </div>

        <div class="bg-white dark:bg-zinc-800 p-4 shadow mb-6">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                <div class="flex items-center space-x-4">
                    <button @click="activeView = 'table'" class="flex items-center px-3 py-2 border"
                        :class="activeView === 'table' ? 'bg-red-600 text-white border-red-600' : 'border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-zinc-600'">
                        <Icon icon="ri:table-fill" class="w-5 h-5 mr-2" />
                        Table
                    </button>
                    <button @click="activeView = 'map'" class="flex items-center px-3 py-2 border"
                        :class="activeView === 'map' ? 'bg-red-600 text-white border-red-600' : 'border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-zinc-600'">
                        <Icon icon="fa6-solid:map-location-dot" class="w-5 h-5 mr-2" />
                        Map
                    </button>
                </div>
                <div class="flex items-center space-x-4">
                    <div v-if="activeView === 'map'" class="flex items-center space-x-2">
                        <CoreSelect
                            v-model="countryFilter"
                            :options="countryOptions"
                            :disabled="loading"
                        />
                        <CoreSelect
                            v-model="cityFilter"
                            :options="cityOptions"
                            :disabled="loading || !countryFilter"
                        />
                    </div>
                </div>
            </div>
        </div>

        <div v-if="activeView === 'table'" class="bg-white dark:bg-zinc-800 p-4 shadow mb-6">
            <div class="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
                <div class="flex-1">
                    <CoreSearchBar
                        v-model="search"
                        placeholder="Search venues by name, address, or city..."
                        :max-history-items="10"
                    />
                </div>
                <CoreSelect
                    v-model="sortBy"
                    :options="sortOptions"
                />
                <CoreSelect
                    v-model="sortOrder"
                    :options="sortOrderOptions"
                />
            </div>
        </div>

        <!-- Stats Cards for Table View -->
        <div v-if="activeView === 'table'" class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div class="bg-white dark:bg-zinc-800 p-4 shadow">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900 mr-4">
                        <Icon icon="heroicons:building-office" class="w-6 h-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Total Venues</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ venues.length }}</p>
                    </div>
                </div>
            </div>
            <div class="bg-white dark:bg-zinc-800 p-4 shadow">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 dark:bg-green-900 mr-4">
                        <Icon icon="heroicons:map-pin" class="w-6 h-6 text-green-600 dark:text-green-400" />
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">With Coordinates</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ venuesWithCoordinates }}</p>
                    </div>
                </div>
            </div>
            <div class="bg-white dark:bg-zinc-800 p-4 shadow">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900 mr-4">
                        <Icon icon="heroicons:globe-alt" class="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Countries</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ uniqueCountries.length }}</p>
                    </div>
                </div>
            </div>
            <div class="bg-white dark:bg-zinc-800 p-4 shadow">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100 dark:bg-purple-900 mr-4">
                        <Icon icon="heroicons:building-office-2" class="w-6 h-6 text-purple-600 dark:text-purple-400" />
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Cities</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ uniqueCities.length }}</p>
                    </div>
                </div>
            </div>
        </div>

        <div v-if="activeView === 'table'" class="bg-white dark:bg-zinc-800 shadow">
            <div v-if="loading" class="flex justify-center items-center py-20">
                <CoreLoader />
            </div>
            <div v-else-if="venues.length === 0" class="flex flex-col justify-center items-center py-20">
                <Icon icon="heroicons:building-office" class="w-16 h-16 text-gray-400 dark:text-gray-600 mb-4" />
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No venues found</h3>
                <p class="text-gray-500 dark:text-gray-400 text-center max-w-md mb-4">
                    No venues found matching your search criteria.
                </p>
                <button
                    @click="$router.push('/dashboard/venues/create')"
                    class="px-4 py-2 bg-red-600 text-white shadow-sm hover:bg-red-700"
                >
                    Add Your First Venue
                </button>
            </div>
            <div v-else>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead>
                            <tr>
                                <th class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                    Name
                                </th>
                                <th class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                    Address
                                </th>
                                <th class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                    Location
                                </th>
                                <th class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                    Created
                                </th>
                                <th class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-zinc-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <tr v-for="venue in paginatedVenues" :key="venue.id" class="hover:bg-gray-50 dark:hover:bg-zinc-700">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                                                <Icon icon="heroicons:building-office" class="h-5 w-5 text-red-600 dark:text-red-400" />
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900 dark:text-white">{{ venue.name }}</div>
                                            <div v-if="venue.slug" class="text-sm text-gray-500 dark:text-gray-400">{{ venue.slug }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900 dark:text-white">{{ venue.address }}</div>
                                    <div v-if="venue.street" class="text-sm text-gray-500 dark:text-gray-400">{{ venue.street }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900 dark:text-white">{{ venue.city }}</div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">{{ venue.country }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                    {{ formatDate(venue.created_at || '') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button @click="$router.push(`/dashboard/venues/${venue.slug}`)" class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-200" title="View">
                                            <Icon icon="solar:eye-broken" class="w-5 h-5" />
                                        </button>
                                        <button @click="$router.push(`/dashboard/venues/update/${venue.slug}`)" class="text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-200" title="Edit">
                                            <Icon icon="fluent:edit-28-regular" class="w-5 h-5" />
                                        </button>
                                        <button @click="openDeleteDialog(venue)" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-200" title="Delete">
                                            <Icon icon="mage:trash" class="w-5 h-5" />
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="bg-white dark:bg-zinc-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 sm:px-6">
                    <div class="flex-1 flex justify-between sm:hidden">
                        <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-zinc-600 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-700 hover:bg-gray-50 dark:hover:bg-zinc-600 disabled:opacity-50">
                            Previous
                        </button>
                        <button @click="nextPage" :disabled="currentPage === totalPages" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-zinc-600 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-700 hover:bg-gray-50 dark:hover:bg-zinc-600 disabled:opacity-50">
                            Next
                        </button>
                    </div>
                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700 dark:text-gray-300">
                                Showing <span class="font-medium">{{ paginationStart }}</span> to <span class="font-medium">{{ paginationEnd }}</span> of <span class="font-medium">{{ filteredVenues.length }}</span> venues
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex shadow-sm -space-x-px" aria-label="Pagination">
                                <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-zinc-600 disabled:opacity-50">
                                    <span class="sr-only">Previous</span>
                                    <Icon icon="heroicons:chevron-left" class="h-5 w-5" />
                                </button>
                                <button v-for="page in displayedPages" :key="page" @click="goToPage(page)" class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-sm font-medium" :class="page === currentPage ? 'z-10 bg-red-600 text-red-100 dark:text-red-50' : 'text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-zinc-600'">
                                    {{ page }}
                                </button>
                                <button @click="nextPage" :disabled="currentPage === totalPages" class="relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-zinc-600 disabled:opacity-50">
                                    <span class="sr-only">Next</span>
                                    <Icon icon="heroicons:chevron-right" class="h-5 w-5" />
                                </button>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div v-else-if="activeView === 'map'" class="bg-gray-100">
            <div v-if="isMapLoading" class="flex justify-center items-center h-96">
                <CoreLoader />
            </div>
            <div v-else>
                <GoogleMap :api-key="runtimeConfig.public.googleMapsApiKey" class="h-96 w-full" :center="mapCenter"
                    :zoom="mapZoom" mapTypeId="terrain" @map-clicked="handleMapClick">
                    <CustomMarker v-for="venue in filteredVenues" :key="venue.id" :options="{
                        position: { lat: Number(venue.latitude), lng: Number(venue.longitude) },
                        anchorPoint: 'BOTTOM_CENTER',
                    }">
                        <div style="text-align: center">
                            <div class="relative shadow-xl shadow-black"
                                :class="{ 'animate-bounce': selectedVenue?.id === venue.id }"
                                @mouseenter="hoveredVenue = venue" @mouseleave="hoveredVenue = null"
                                @click="toggleSelectedVenue(venue)">
                                <button
                                    class="relative inline-flex items-center justify-center p-2 cursor-pointer bg-red-600 text-white hover:bg-red-700 transition-colors">
                                    <Icon icon="fa6-solid:map-location-dot" class="w-6 h-6" />
                                </button>
                                <span
                                    class="absolute left-1/2 -bottom-1 transform -translate-x-1/2 w-0 h-0 border-l-[6px] border-l-transparent border-r-[6px] border-r-transparent border-t-[6px] border-t-red-600"></span>
                            </div>

                            <div v-if="hoveredVenue?.id === venue.id && selectedVenue?.id !== venue.id"
                                class="absolute mt-2 -translate-x-1/2 left-1/2 bg-white p-2 shadow-md border border-gray-200 z-10 min-w-48">
                                <div class="font-semibold">{{ venue.name }}</div>
                            </div>

                            <div v-if="selectedVenue?.id === venue.id"
                                class="absolute mt-2 -translate-x-1/2 left-1/2 bg-white p-3 shadow-lg border border-gray-300 z-20 min-w-64">
                                <div class="flex justify-between items-center mb-1">
                                    <h3 class="font-bold text-lg">{{ venue.name }}</h3>
                                    <button @click.stop="selectedVenue = null" class="p-1 hover:bg-gray-100">
                                        <Icon icon="mdi:close" class="w-4 h-4" />
                                    </button>
                                </div>
                                <div class="text-left">
                                    <p class="text-gray-700">{{ venue.address }}</p>
                                    <p class="text-gray-700">{{ venue.city }}, {{ venue.country }}</p>
                                    <div class="flex space-x-2 mt-2 justify-end">
                                        <button class="px-2 py-1" @click="$router.push(`/dashboard/venues/${venue.slug}`)">
                                            <Icon icon="solar:eye-broken" class="w-5 h-5" />
                                        </button>
                                        <button class="px-2 py-1"
                                            @click.stop="$router.push(`/dashboard/venues/update/${venue.slug}`)">
                                            <Icon icon="fluent:edit-28-regular" class="w-5 h-5" />
                                        </button>
                                        <button class="px-2 py-1" @click.stop="openDeleteDialog(venue)">
                                            <Icon icon="mage:trash" class="w-5 h-5" />
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </CustomMarker>
                </GoogleMap>
                <div class="p-4 text-base text-gray-500">
                    <p>Showing {{ filteredVenues.length }} venues.</p>
                    <p v-if="filteredVenues.length === 0">No venues with location data found for the current
                        filters.</p>
                    <p v-else-if="filteredVenues.length < venues.length">Some venues may not appear on the map due to
                        missing location data.</p>
                </div>
            </div>
        </div>

        <TransitionRoot appear :show="isDeleteDialogOpen" as="template">
            <Dialog as="div" @close="closeDeleteDialog" class="relative z-10">
                <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0"
                    enter-to="opacity-100" leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                    <div class="fixed inset-0 bg-black bg-opacity-25" />
                </TransitionChild>

                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                            enter-to="opacity-100 scale-100" leave="duration-200 ease-in"
                            leave-from="opacity-100 scale-100" leave-to="opacity-0 scale-95">
                            <DialogPanel
                                class="w-full max-w-md transform overflow-hidden bg-white p-6 text-left align-middle shadow-xl transition-all">
                                <DialogTitle as="h3" class="text-xl font-semibold leading-6 text-gray-900">
                                    Delete Venue
                                </DialogTitle>
                                <div class="mt-4">
                                    <p class="text-gray-600">
                                        Are you sure you want to delete "<strong>{{ venueToDelete?.name }}</strong>"?
                                        This action cannot be undone.
                                    </p>

                                    <div class="mt-6 flex justify-end space-x-3">
                                        <button type="button" @click="closeDeleteDialog"
                                            class="px-4 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50">
                                            Cancel
                                        </button>
                                        <CorePrimaryButton :text="isSubmitting ? 'Deleting...' : 'Delete'"
                                            :loading="isSubmitting" @click="deleteVenue" />
                                    </div>
                                </div>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>
    </div>
</template>

<script setup lang="ts">
import { Dialog, DialogPanel, DialogTitle, TransitionRoot, TransitionChild } from '@headlessui/vue';
import { GoogleMap } from 'vue3-google-map';
import type { SelectOption } from '~/components/core/Select.vue';

definePageMeta({
    layout: "dashboard"
});

useHead({
    title: "Venues | EventaHub Malawi",
    meta: [
        {
            name: "description",
            content: "Manage venues on our platform."
        }
    ]
});

const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();
const search = ref<string>('');
const hoveredVenue = ref<Venue | null>(null);
const activeView = ref<'table' | 'map'>('table');
const isMapLoading = ref<boolean>(true);
const runtimeConfig = useRuntimeConfig();
const mapCenter = ref({ lat: -13.962612, lng: 33.774119 });
const mapZoom = ref(6);
const selectedVenue = ref<Venue | null>(null);
const countryFilter = ref<string>('');
const cityFilter = ref<string>('');

interface Venue {
    id: number;
    name: string;
    address: string;
    street: string | null;
    city: string;
    country: string;
    latitude: number | null;
    longitude: number | null;
    slug?: string;
    created_at?: string;
    updated_at?: string;
}

const venues = ref<Venue[]>([]);
const loading = ref<boolean>(false);
const totalItems = ref<number>(0);
const currentPage = ref<number>(1);
const itemsPerPage = ref<number>(10);
const sortBy = ref<string>('name');
const sortOrder = ref<string>('asc');

const sortOptions: SelectOption[] = [
    { value: 'name', label: 'Name' },
    { value: 'city', label: 'City' },
    { value: 'country', label: 'Country' },
    { value: 'created_at', label: 'Created Date' },
];

const sortOrderOptions: SelectOption[] = [
    { value: 'asc', label: 'Ascending' },
    { value: 'desc', label: 'Descending' },
];

const countryOptions = computed(() => {
    const allOption: SelectOption = { value: '', label: 'All Countries' };
    return [
        allOption,
        ...uniqueCountries.value.map((country: string) => ({
            value: country,
            label: country,
        })),
    ];
});

const cityOptions = computed(() => {
    const allOption: SelectOption = { value: '', label: 'All Cities' };
    return [
        allOption,
        ...uniqueCities.value.map((city: string) => ({
            value: city,
            label: city,
        })),
    ];
});

const toggleSelectedVenue = (venue: Venue) => {
    selectedVenue.value = selectedVenue.value?.id === venue.id ? null : venue;
};

const isDeleteDialogOpen = ref<boolean>(false);
const isSubmitting = ref<boolean>(false);
const venueToDelete = ref<Venue | null>(null);

const uniqueCountries = computed(() => {
    const countries = venues.value.map(venue => venue.country);
    return [...new Set(countries)].sort();
});

const uniqueCities = computed(() => {
    let cities;
    if (countryFilter.value) {
        cities = venues.value
            .filter(venue => venue.country === countryFilter.value)
            .map(venue => venue.city);
    } else {
        cities = venues.value.map(venue => venue.city);
    }
    return [...new Set(cities)].sort();
});

const venuesWithCoordinates = computed(() => {
    return venues.value.filter(venue =>
        venue.latitude !== null && venue.longitude !== null
    ).length;
});

const filteredVenues = computed(() => {
    let filtered = venues.value;

    // Apply search filter
    if (search.value) {
        const searchTerm = search.value.toLowerCase();
        filtered = filtered.filter(venue =>
            venue.name.toLowerCase().includes(searchTerm) ||
            venue.address.toLowerCase().includes(searchTerm) ||
            venue.city.toLowerCase().includes(searchTerm) ||
            venue.country.toLowerCase().includes(searchTerm)
        );
    }

    // Apply country filter for map view
    if (countryFilter.value && activeView.value === 'map') {
        filtered = filtered.filter(venue => venue.country === countryFilter.value);
    }

    // Apply city filter for map view
    if (cityFilter.value && activeView.value === 'map') {
        filtered = filtered.filter(venue => venue.city === cityFilter.value);
    }

    // For map view, only show venues with coordinates
    if (activeView.value === 'map') {
        filtered = filtered.filter(venue =>
            venue.latitude !== null && venue.longitude !== null
        );
    }

    // Apply sorting for table view
    if (activeView.value === 'table') {
        filtered.sort((a, b) => {
            let aValue: any = a[sortBy.value as keyof Venue];
            let bValue: any = b[sortBy.value as keyof Venue];

            if (sortBy.value === 'created_at') {
                aValue = new Date(aValue || 0);
                bValue = new Date(bValue || 0);
            } else if (typeof aValue === 'string') {
                aValue = aValue.toLowerCase();
                bValue = bValue.toLowerCase();
            }

            if (sortOrder.value === 'desc') {
                return aValue < bValue ? 1 : -1;
            } else {
                return aValue > bValue ? 1 : -1;
            }
        });
    }

    return filtered;
});

const totalPages = computed(() => Math.ceil(filteredVenues.value.length / itemsPerPage.value));

const paginatedVenues = computed(() => {
    if (activeView.value === 'map') return filteredVenues.value;

    const start = (currentPage.value - 1) * itemsPerPage.value;
    const end = start + itemsPerPage.value;
    return filteredVenues.value.slice(start, end);
});

const paginationStart = computed(() => {
    if (filteredVenues.value.length === 0) return 0;
    return (currentPage.value - 1) * itemsPerPage.value + 1;
});

const paginationEnd = computed(() => {
    return Math.min(currentPage.value * itemsPerPage.value, filteredVenues.value.length);
});

const displayedPages = computed(() => {
    const pages = [];
    const maxPagesToShow = 5;

    if (totalPages.value <= maxPagesToShow) {
        for (let i = 1; i <= totalPages.value; i++) {
            pages.push(i);
        }
    } else {
        const leftSide = Math.floor(maxPagesToShow / 2);
        const rightSide = maxPagesToShow - leftSide - 1;

        if (currentPage.value > leftSide && currentPage.value < totalPages.value - rightSide) {
            for (let i = currentPage.value - leftSide; i <= currentPage.value + rightSide; i++) {
                pages.push(i);
            }
        } else if (currentPage.value <= leftSide) {
            for (let i = 1; i <= maxPagesToShow; i++) {
                pages.push(i);
            }
        } else {
            for (let i = totalPages.value - maxPagesToShow + 1; i <= totalPages.value; i++) {
                pages.push(i);
            }
        }
    }

    return pages;
});

watch(countryFilter, () => {
    cityFilter.value = '';
});

watch(activeView, (newView) => {
    if (newView === 'map') {
        isMapLoading.value = true;
        setTimeout(() => {
            isMapLoading.value = false;
            adjustMapView();
        }, 500);
    }
    currentPage.value = 1;
});

watch([search, sortBy, sortOrder], () => {
    currentPage.value = 1;
});

let searchTimeout: NodeJS.Timeout;
watch(search, () => {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        currentPage.value = 1;
    }, 300);
});

watch([countryFilter, cityFilter], () => {
    if (activeView.value === 'map') {
        adjustMapView();
    }
});

onMounted(() => {
    fetchVenues();
});

const fetchVenues = async (): Promise<void> => {
    loading.value = true;
    try {
        const response: any = await httpClient.get(ENDPOINTS.VENUES.USER);
        venues.value = response.data || response;
        totalItems.value = response.total || venues.value.length;

        if (activeView.value === 'map') {
            adjustMapView();
        }
    } catch (error) {
        console.error('Error fetching venues:', error);
        $toast.error('Failed to load venues');
    } finally {
        loading.value = false;
    }
};

const refreshData = (): void => {
    fetchVenues();
};

const formatDate = (dateString: string): string => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
};

const prevPage = (): void => {
    if (currentPage.value > 1) {
        currentPage.value--;
    }
};

const nextPage = (): void => {
    if (currentPage.value < totalPages.value) {
        currentPage.value++;
    }
};

const goToPage = (page: number): void => {
    currentPage.value = page;
};

const openDeleteDialog = (venue: Venue): void => {
    venueToDelete.value = venue;
    isDeleteDialogOpen.value = true;
};

const closeDeleteDialog = (): void => {
    isDeleteDialogOpen.value = false;
    venueToDelete.value = null;
};

const deleteVenue = async (): Promise<void> => {
    if (!venueToDelete.value) return;

    isSubmitting.value = true;
    try {
        const response: any = await httpClient.delete(`${ENDPOINTS.VENUES.DELETE}/${venueToDelete.value.id}`);
        if (response) {
            closeDeleteDialog();
            fetchVenues();
            $toast.success(response.message);
        }
    } catch (error) {
        console.error('Error deleting venue:', error);
    } finally {
        isSubmitting.value = false;
    }
};

const handleMapClick = (): void => {
    selectedVenue.value = null;
};

const openInfoWindow = (venue: Venue): void => {
    selectedVenue.value = venue;
};

const adjustMapView = (): void => {
    const venuesWithCoords = filteredVenues.value;

    if (venuesWithCoords.length === 0) {
        mapCenter.value = { lat: -13.962612, lng: 33.774119 };
        mapZoom.value = 6;
        return;
    }

    if (venuesWithCoords.length === 1) {
        mapCenter.value = {
            lat: Number(venuesWithCoords[0].latitude),
            lng: Number(venuesWithCoords[0].longitude)
        };
        mapZoom.value = 12;
        return;
    }

    let minLat = Infinity;
    let maxLat = -Infinity;
    let minLng = Infinity;
    let maxLng = -Infinity;

    venuesWithCoords.forEach(venue => {
        const lat = Number(venue.latitude);
        const lng = Number(venue.longitude);

        minLat = Math.min(minLat, lat);
        maxLat = Math.max(maxLat, lat);
        minLng = Math.min(minLng, lng);
        maxLng = Math.max(maxLng, lng);
    });

    mapCenter.value = {
        lat: (minLat + maxLat) / 2,
        lng: (minLng + maxLng) / 2
    };

    const latDiff = maxLat - minLat;
    const lngDiff = maxLng - minLng;
    const maxDiff = Math.max(latDiff, lngDiff);

    if (maxDiff > 10) mapZoom.value = 5;
    else if (maxDiff > 5) mapZoom.value = 6;
    else if (maxDiff > 2) mapZoom.value = 7;
    else if (maxDiff > 1) mapZoom.value = 8;
    else if (maxDiff > 0.5) mapZoom.value = 9;
    else if (maxDiff > 0.2) mapZoom.value = 10;
    else if (maxDiff > 0.1) mapZoom.value = 11;
    else mapZoom.value = 12;
};
</script>

<style scoped>
button,
.rounded-none,
[class*="rounded"] {
    border-radius: 0 !important;
}

.ListboxButton,
.ListboxOptions,
.ListboxOption {
    border-radius: 0 !important;
}
</style>
