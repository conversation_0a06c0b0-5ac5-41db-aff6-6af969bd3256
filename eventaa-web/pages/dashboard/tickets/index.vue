<template>
  <div class="h-screen p-6 bg-gray-100 dark:bg-zinc-900">
    <div class="mb-6 flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
          Tickets
        </h1>
        <p class="text-gray-500 dark:text-gray-400">
          Manage event tickets and scan attendees
        </p>
      </div>
      <div class="flex space-x-2">
        <button
          @click="openScanModal"
          class="p-2.5 flex items-center rounded-full justify-center bg-white dark:bg-zinc-800 border border-gray-300 dark:border-zinc-600 shadow-sm hover:bg-gray-50 dark:hover:bg-zinc-700 text-gray-700 dark:text-gray-200"
        >
          <Icon icon="heroicons:qr-code" class="w-5 h-5" />
        </button>
        <button
          @click="loadTickets"
          class="p-2.5 flex items-center rounded-full justify-center bg-white dark:bg-zinc-800 border border-gray-300 dark:border-zinc-600 shadow-sm hover:bg-gray-50 dark:hover:bg-zinc-700 text-gray-700 dark:text-gray-200"
        >
          <Icon icon="famicons:refresh-outline" class="w-5 h-5" />
        </button>
        <button
          @click="downloadPdf"
          class="p-2.5 flex items-center rounded-full justify-center bg-white dark:bg-zinc-800 border border-gray-300 dark:border-zinc-600 shadow-sm hover:bg-gray-50 dark:hover:bg-zinc-700 text-gray-700 dark:text-gray-200"
        >
          <img
            src="@/assets/icons/excel.png"
            alt="excel-icon"
            class="w-5 h-5 object-cover"
          />
        </button>
      </div>
    </div>

    <div class="bg-white dark:bg-zinc-800 p-4 shadow mb-6">
      <div
        class="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4"
      >
        <div class="flex-1">
          <CoreSearchBar
            v-model="searchQuery"
            placeholder="Search by ticket UUID, customer, or email"
            :max-history-items="10"
          />
        </div>
        <CoreSelect :options="statusOptions" v-model="statusFilter" />
        <CoreSelect :options="purchaseOptions" v-model="purchaseFilter" />
        <CoreSelect :options="eventsOptions" v-model="eventFilter" />
        <CoreSelect :options="dateOptions" v-model="dateFilter" />
      </div>
    </div>

    <div class="bg-white dark:bg-zinc-800 shadow">
      <div v-if="loading" class="flex justify-center items-center py-20">
        <CoreLoader />
      </div>
      <div v-else>
        <div class="overflow-x-auto">
          <table
            class="min-w-full divide-y divide-gray-200 dark:divide-zinc-700"
          >
            <thead>
              <tr>
                <th
                  class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                >
                  Ticket UUID
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                >
                  Customer
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                >
                  Event
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                >
                  Purchase Date
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                >
                  Scan Status
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                >
                  Purchase Status
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                >
                  Actions
                </th>
              </tr>
            </thead>
            <tbody
              class="bg-white dark:bg-zinc-800 divide-y divide-gray-200 dark:divide-zinc-700"
            >
              <tr
                v-for="ticket in filteredTickets"
                :key="ticket.id"
                class="hover:bg-gray-50 dark:hover:bg-zinc-700"
              >
                <td
                  class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white"
                >
                  <button
                    @click="viewTicketDetails(ticket)"
                    class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 font-mono"
                  >
                    {{ ticket.uuid }}
                  </button>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900 dark:text-white">
                    {{ ticket.customer }}
                  </div>
                  <div class="text-sm text-gray-500 dark:text-gray-400">
                    {{ ticket.email }}
                  </div>
                </td>
                <td
                  class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"
                >
                  {{ ticket.event_title }}
                </td>
                <td
                  class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"
                >
                  {{ ticket.purchaseDate }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    class="px-2 inline-flex rounded-full text-xs leading-5 font-semibold"
                    :class="{
                      'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200':
                        ticket.status === 'scanned',
                      'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200':
                        ticket.status === 'unscanned',
                    }"
                  >
                    {{ ticket.status === "scanned" ? "Scanned" : "Unscanned" }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    class="px-2 inline-flex rounded-full text-xs leading-5 font-semibold"
                    :class="{
                      'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200':
                        ticket.is_bought,
                      'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200':
                        !ticket.is_bought,
                    }"
                  >
                    {{ ticket.is_bought ? "Purchased" : "Available" }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <button
                      @click="viewTicketDetails(ticket)"
                      class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"
                    >
                      <Icon icon="hugeicons:eye" class="w-5 h-5" />
                    </button>
                    <button
                      @click="scanTicket(ticket)"
                      :disabled="ticket.status === 'scanned'"
                      :class="[
                        ticket.status === 'scanned'
                          ? 'text-gray-400 dark:text-gray-600 cursor-not-allowed'
                          : 'text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300',
                      ]"
                    >
                      <Icon icon="heroicons:qr-code" class="w-5 h-5" />
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div
          class="bg-white dark:bg-zinc-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-zinc-700 sm:px-6"
        >
          <div class="flex-1 flex justify-between sm:hidden">
            <button
              @click="prevPage"
              :disabled="currentPage === 1"
              class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-zinc-600 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-zinc-700 hover:bg-gray-50 dark:hover:bg-zinc-600"
            >
              Previous
            </button>
            <button
              @click="nextPage"
              :disabled="currentPage === totalPages"
              class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-zinc-600 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-zinc-700 hover:bg-gray-50 dark:hover:bg-zinc-600"
            >
              Next
            </button>
          </div>
          <div
            class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"
          >
            <div>
              <p class="text-sm text-gray-700 dark:text-gray-300">
                Showing
                <span class="font-medium">{{ paginationStart }}</span> to
                <span class="font-medium">{{ paginationEnd }}</span> of
                <span class="font-medium">{{ totalItems }}</span> tickets
                <span v-if="searchQuery || statusFilter !== 'all' || purchaseFilter !== 'all' || dateFilter !== 'all'"
                      class="text-gray-500 dark:text-gray-400">
                  (filtered)
                </span>
              </p>
            </div>
            <div>
              <nav
                class="relative z-0 inline-flex shadow-sm -space-x-px"
                aria-label="Pagination"
              >
                <button
                  @click="prevPage"
                  :disabled="currentPage === 1"
                  class="relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-zinc-600"
                >
                  <span class="sr-only">Previous</span>
                  <Icon icon="heroicons:chevron-left" class="h-5 w-5" />
                </button>
                <button
                  v-for="page in displayedPages"
                  :key="page"
                  @click="goToPage(page)"
                  class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-zinc-600 dark:bg-zinc-700 text-sm font-medium"
                  :class="
                    page === currentPage
                      ? 'z-10 bg-red-600 text-red-100 dark:text-red-50'
                      : 'text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-zinc-600'
                  "
                >
                  {{ page }}
                </button>
                <button
                  @click="nextPage"
                  :disabled="currentPage === totalPages"
                  class="relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-zinc-600"
                >
                  <span class="sr-only">Next</span>
                  <Icon icon="heroicons:chevron-right" class="h-5 w-5" />
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Ticket Details Modal -->
    <TicketsDetailsModal
      :ticket="selectedTicket"
      :event="selectedEvent"
      :is-open="showTicketDetails"
      @close="closeTicketDetails"
      @scan="handleScan"
    />

    <TicketsScanModal
      :is-open="showScanModal"
      :scan-result="scanResult"
      @close="closeScanModal"
      @scan="handleQRScan"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import type { EventItem, Transaction } from "@/types";
import type { SelectOption } from "@/components/core/Select.vue";
import { ENDPOINTS } from "@/utils/api";
import dayjs from "dayjs";
import { DATE_FORMAT } from "@/utils/dateformat";
import jsPDF from "jspdf";
import autoTable from "jspdf-autotable";
import type { GenericResponse } from "~/types/api";

definePageMeta({
  middleware: ["auth"],
  layout: "dashboard",
});

useHead({
  title: "Tickets | EventaHub Malawi",
  meta: [
    {
      name: "description",
      content: "Manage event tickets and scan attendees for all your events",
    },
  ],
});

interface TicketResponse {
  id: number;
  uuid: string;
  name: string;
  created_at: string;
  scanned: boolean;
  scanned_at?: string;
  status: 'scanned' | 'unscanned';
  is_bought: boolean;
  purchase_count: number;
  tier?: {
    id: number;
    name: string;
    price: number;
  };
  // Purchase information (if bought)
  purchase?: any; // Full purchase object if exists
  purchase_reference?: string;
  customer: string;
  email: string;
  purchaseDate?: string;
  event_title: string;
  // Keep original structure for compatibility
  ticket?: any;
  user?: any;
}

interface Ticket {
  id: number;
  uuid: string;
  name?: string;
  email: string;
  purchaseDate?: string;
  scanned: number | boolean;
  created_at: string;
  event_title?: string;
  customer?: string;
  status?: string;
  is_bought?: boolean;
  purchase_count?: number;
  tier?: any;
  ticket?: any;
  // Additional fields from ticket purchase
  purchase_reference?: string;
  attendee_name?: string | null;
  attendee_email?: string | null;
  user?: any;
}

interface PaginationData {
  current_page: number;
  from: number;
  last_page: number;
  per_page: number;
  to: number;
  total: number;
  links: Array<{
    url: string | null;
    label: string;
    active: boolean;
  }>;
}

interface TicketListResponse {
  data: TicketResponse[];
  current_page: number;
  from: number;
  last_page: number;
  per_page: number;
  to: number;
  total: number;
  links: Array<{
    url: string | null;
    label: string;
    active: boolean;
  }>;
}

interface Ticket {
  id: number;
  uuid: string;
  name?: string;
  email: string;
  purchaseDate?: string;
  scanned: number | boolean;
  created_at: string;
  event_title?: string;
  customer?: string;
  status?: string;
  is_bought?: boolean;
  purchase_count?: number;
  tier?: any;
  ticket?: any;
  // Additional fields from ticket purchase
  purchase_reference?: string;
  attendee_name?: string | null;
  attendee_email?: string | null;
  user?: any;
}

interface PaginationData {
  current_page: number;
  from: number;
  last_page: number;
  per_page: number;
  to: number;
  total: number;
  links: Array<{
    url: string | null;
    label: string;
    active: boolean;
  }>;
}

const loading = ref(true);
const searchQuery = ref<string>("");
const statusFilter = ref<string>("all");
const purchaseFilter = ref<string>("all");
const eventFilter = ref<string>("all");
const dateFilter = ref<string>("all");
const currentPage = ref<number>(1);
const itemsPerPage: number = 10;
const totalItems = ref<number>(0);
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();
const events = ref<EventItem[]>([]);
const tickets = ref<Ticket[]>([]);
const selectedEvent = ref<EventItem | null>(null);
const selectedTicket = ref<Ticket | null>(null);
const showTicketDetails = ref<boolean>(false);
const showScanModal = ref<boolean>(false);
const scanResult = ref<{ success: boolean; message: string } | null>(null);

const pagination = ref<PaginationData>({
  current_page: 1,
  from: 0,
  last_page: 1,
  per_page: 50,
  to: 0,
  total: 0,
  links: [],
});

const statusOptions: SelectOption[] = [
  { value: "all", label: "All Statuses" },
  { value: "scanned", label: "Scanned" },
  { value: "unscanned", label: "Unscanned" },
];

const purchaseOptions: SelectOption[] = [
  { value: "all", label: "All Tickets" },
  { value: "bought", label: "Bought" },
  { value: "not_bought", label: "Not Bought" },
];

const dateOptions: SelectOption[] = [
  { value: "all", label: "All Time" },
  { value: "today", label: "Today" },
  { value: "week", label: "This Week" },
  { value: "month", label: "This Month" },
  { value: "year", label: "This Year" },
];

const eventsOptions = computed(() => {
  const allOption: SelectOption = { value: "all", label: "All Events" };
  return [
    allOption,
    ...events.value.map((event: EventItem) => ({
      value: event.id,
      label: event.title,
    })),
  ];
});

const filteredTickets = computed(() => {
  let filtered = [...tickets.value];
  if (statusFilter.value && statusFilter.value !== "all") {
    const beforeCount = filtered.length;
    filtered = filtered.filter(ticket => {
      let ticketStatus = ticket.status;
      if (!ticketStatus) {
        const isScanned = String(ticket.scanned) === "1" ||
                         String(ticket.scanned) === "true" ||
                         String(ticket.ticket?.scanned) === "1" ||
                         String(ticket.ticket?.scanned) === "true";
        ticketStatus = isScanned ? "scanned" : "unscanned";
      }
      return ticketStatus === statusFilter.value;
    });
  }

  if (purchaseFilter.value && purchaseFilter.value !== "all") {
    const beforeCount = filtered.length;
    filtered = filtered.filter(ticket => {
      const isBought = ticket.is_bought ||
                      (ticket.customer !== "Not assigned" &&
                       ticket.email !== "Not assigned" &&
                       ticket.customer &&
                       ticket.email);

      if (purchaseFilter.value === "bought") {
        return isBought;
      } else if (purchaseFilter.value === "not_bought") {
        return !isBought;
      }
      return true;
    });
  }

  return filtered;
});

const totalPages = computed(() => {
  const count = filteredTickets.value.length || totalItems.value;
  return Math.ceil(count / itemsPerPage);
});

const paginationStart = computed(() => {
  const count = filteredTickets.value.length || totalItems.value;
  if (count === 0) return 0;
  return (currentPage.value - 1) * itemsPerPage + 1;
});

const paginationEnd = computed(() => {
  const count = filteredTickets.value.length || totalItems.value;
  return Math.min(currentPage.value * itemsPerPage, count);
});

const displayedPages = computed(() => {
  const pages = [];
  const maxPages = 5;
  let start = Math.max(1, currentPage.value - Math.floor(maxPages / 2));
  let end = Math.min(totalPages.value, start + maxPages - 1);

  if (end - start < maxPages - 1) {
    start = Math.max(1, end - maxPages + 1);
  }

  for (let i = start; i <= end; i++) {
    pages.push(i);
  }

  return pages;
});

const fetchEvents = async (): Promise<void> => {
  try {
    const response = await httpClient.get<any>(
      `${ENDPOINTS.EVENTS.USER}?per_page=100&page=1`
    );

    if (response && response.events) {
      events.value = response.events.data || [];
      if (events.value.length > 0 && !selectedEvent.value) {
        selectedEvent.value = events.value[0];
      }
    } else {
      events.value = [];
      $toast.warning("No events found. Please create an event first.");
    }
  } catch (error) {
    events.value = [];
    $toast.error(
      "An error occurred while fetching events, please try again later."
    );
    console.error("Error fetching events:", error);
  }
};

const fetchTicketsForEvent = async (
  eventId: number,
  page = 1,
  searchQuery = "",
  filterParams = {}
) => {
  if (!eventId) {
    $toast.error("No event selected");
    return [];
  }

  try {
    let url = `${ENDPOINTS.EVENTS.TICKETS}/${eventId}?page=${page}&per_page=${pagination.value.per_page}`;

    if (searchQuery) {
      url += `&search=${encodeURIComponent(searchQuery)}`;
    }

    if (statusFilter.value && statusFilter.value !== "all") {
      url += `&status=${statusFilter.value}`;
      url += `&scan_status=${statusFilter.value}`;
    }

    if (purchaseFilter.value && purchaseFilter.value !== "all") {
      url += `&purchase_status=${purchaseFilter.value}`;
    }

    if (dateFilter.value && dateFilter.value !== "all") {
      const now = new Date();
      let dateFrom, dateTo;

      switch (dateFilter.value) {
        case "today":
          dateFrom = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          dateTo = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
          break;
        case "week":
          dateFrom = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          dateTo = now;
          break;
        case "month":
          dateFrom = new Date(now.getFullYear(), now.getMonth(), 1);
          dateTo = now;
          break;
        case "year":
          dateFrom = new Date(now.getFullYear(), 0, 1);
          dateTo = now;
          break;
      }

      if (dateFrom && dateTo) {
        url += `&date_from=${dateFrom.toISOString()}&date_to=${dateTo.toISOString()}`;
      }
    }

    if (filterParams) {
      Object.entries(filterParams).forEach(([key, value]) => {
        if (value) {
          url += `&${key}=${encodeURIComponent(String(value))}`;
        }
      });
    }

    const response = await httpClient.get<TicketListResponse>(url);

    if (response && response.data) {
      const transformedTickets = response.data.map((ticketData: TicketResponse) => {
        return {
          ...ticketData,
          purchaseDate: ticketData.purchaseDate || (ticketData.created_at ? dayjs(ticketData.created_at).format(DATE_FORMAT.FULL) : undefined),
          scanned: ticketData.scanned ? 1 : 0,
        } as Ticket;
      });

      tickets.value = transformedTickets;

      Object.assign(pagination.value, {
        current_page: response.current_page || 1,
        from: response.from || 0,
        last_page: response.last_page || 1,
        per_page: response.per_page || pagination.value.per_page,
        to: response.to || 0,
        total: response.total || 0,
        links: response.links || [],
      });

      totalItems.value = response.total || 0;
      return transformedTickets;
    } else {
      tickets.value = [];
      return [];
    }
  } catch (error) {
    console.error("Error fetching tickets:", error);
    $toast.error("Failed to load tickets. Please try again.");
    tickets.value = [];
    return [];
  }
};

const loadTickets = async () => {
  if (!selectedEvent.value) {
    tickets.value = [];
    return;
  }

  try {
    loading.value = true;

    await fetchTicketsForEvent(
      selectedEvent.value.id,
      currentPage.value,
      debouncedSearch.value
    );
  } catch (error) {
    console.error("Error loading tickets:", error);
    $toast.error("Failed to load tickets. Please try again.");
    tickets.value = [];
  } finally {
    loading.value = false;
  }
};

const scanTicket = async (ticket: Ticket) => {
  if (ticket.status === "scanned") return;

  try {
    loading.value = true;
    const response = await httpClient.post<GenericResponse>(ENDPOINTS.TICKETS.SCAN, {
      ticketUUID: ticket.uuid,
    });

    if (response) {
      ticket.status = "scanned";
      ticket.scanned = 1;
      $toast.success(response.message);
    }
  } catch (error) {
    console.error("Error scanning ticket:", error);
    $toast.error("Failed to scan ticket. Please try again.");
  } finally {
    loading.value = false;
  }
};

const handleQRScan = async (qrData: string) => {
  try {
    loading.value = true;
    scanResult.value = null;

    if (!qrData || qrData.length < 10) {
      scanResult.value = {
        success: false,
        message: "Invalid QR code format. Please scan a valid ticket QR code.",
      };
      return;
    }

    await httpClient.post(ENDPOINTS.TICKETS.SCAN, {
      ticketUUID: qrData,
    });

    const ticketIndex = tickets.value.findIndex((t) => t.uuid === qrData);
    if (ticketIndex !== -1) {
      tickets.value[ticketIndex].status = "scanned";
      tickets.value[ticketIndex].scanned = 1;
    }

    scanResult.value = {
      success: true,
      message: `Ticket ${qrData.substring(0, 8)}... scanned successfully!`,
    };

    setTimeout(() => {
      closeScanModal();
    }, 2000);
  } catch (error: any) {
    console.error("Error scanning QR code:", error);
    const message =
      error?.response?.data?.message ||
      "Failed to scan QR code. Please try again.";

    scanResult.value = {
      success: false,
      message: message,
    };
  } finally {
    loading.value = false;
  }
};

const handleScan = (ticket: Ticket) => {
  scanTicket(ticket);
};

const viewTicketDetails = (ticket: Ticket) => {
  selectedTicket.value = ticket;
  showTicketDetails.value = true;
};

const closeTicketDetails = () => {
  selectedTicket.value = null;
  showTicketDetails.value = false;
};

const openScanModal = () => {
  showScanModal.value = true;
};

const closeScanModal = () => {
  showScanModal.value = false;
  scanResult.value = null; // Clear scan results when closing
};

// Pagination methods
const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
    loadTickets();
  }
};

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
    loadTickets();
  }
};

const goToPage = (page: number) => {
  currentPage.value = page;
  loadTickets();
};

const downloadPdf = async () => {
  if (totalItems.value === 0) {
    $toast.error("No tickets available to export");
    return;
  }

  try {
    $toast.info("Generating PDF...");

    const doc = new jsPDF();
    const tableColumn = [
      "Ticket UUID",
      "Customer",
      "Email",
      "Purchase Date",
      "Status",
    ];
    const tableRows: any[] = [];

    filteredTickets.value.forEach((ticket) => {
      const ticketData = [
        ticket.uuid,
        ticket.customer,
        ticket.email,
        ticket.purchaseDate,
        ticket.status === "scanned" ? "Scanned" : "Unscanned",
      ];
      tableRows.push(ticketData);
    });

    doc.setFontSize(18);
    doc.setTextColor(220, 38, 38);
    doc.text("EventaHub", 14, 15);

    doc.setFontSize(15);
    doc.setTextColor(0, 0, 0);
    doc.text(`Tickets Report`, 14, 25);

    doc.setFontSize(11);
    doc.setTextColor(100, 100, 100);
    doc.text(
      `Generated on: ${dayjs().format("MMMM D, YYYY")} at ${dayjs().format(
        "h:mm A"
      )}`,
      14,
      32
    );
    doc.text(`Total Tickets: ${totalItems.value}`, 14, 38);

    autoTable(doc, {
      head: [tableColumn],
      body: tableRows,
      startY: 50,
      theme: "grid",
      styles: {
        fontSize: 10,
        cellPadding: 3,
        overflow: "linebreak",
      },
      headStyles: {
        fillColor: [220, 38, 38],
        textColor: 255,
        fontStyle: "bold",
      },
      alternateRowStyles: {
        fillColor: [245, 245, 245],
      },
    });

    doc.save(`tickets-report-${dayjs().format("YYYY-MM-DD")}.pdf`);
    $toast.success("PDF downloaded successfully!");
  } catch (error) {
    console.error("Error downloading PDF:", error);
    $toast.error("Failed to download PDF. Please try again.");
  }
};

// Watchers with debounce for search
const debouncedSearch = ref<string>("");
let searchTimeout: NodeJS.Timeout | null = null;

watch(searchQuery, (newVal) => {
  if (searchTimeout) {
    clearTimeout(searchTimeout);
  }
  searchTimeout = setTimeout(() => {
    debouncedSearch.value = newVal;
  }, 300); // 300ms debounce
});

watch([statusFilter, purchaseFilter, dateFilter, debouncedSearch], () => {
  currentPage.value = 1;
  loadTickets();
});

watch(eventFilter, (newEventId) => {
  if (newEventId && newEventId !== "all") {
    const event = events.value.find((e) => e.id === Number(newEventId));
    if (event) {
      selectedEvent.value = event;
    }
  } else if (newEventId === "all" && events.value.length > 0) {
    selectedEvent.value = events.value[0];
  }
});

watch(selectedEvent, async () => {
  if (selectedEvent.value) {
    currentPage.value = 1;
    await loadTickets();
  }
});

onMounted(async () => {
  try {
    await fetchEvents();
    if (selectedEvent.value) {
      // Initialize debounced search
      debouncedSearch.value = searchQuery.value;
      await loadTickets();
    }
  } catch (error) {
    console.error("Error initializing tickets page:", error);
    $toast.error(
      "Failed to initialize the tickets page. Please refresh and try again."
    );
  }
});
</script>

<style scoped></style>

<style lang="css" scoped></style>
