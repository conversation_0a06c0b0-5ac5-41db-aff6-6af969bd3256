<template>
  <div class="min-h-screen bg-gray-100 dark:bg-zinc-900 py-8">
    <div class="container mx-auto px-4">
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
          PayChangu Payment Integration Demo
        </h1>
        <p class="text-gray-600 dark:text-gray-400 mt-2">
          Real-time payment verification using WebSocket and PayChangu API
        </p>
      </div>

      <!-- Demo Component -->
      <PaymentsPaymentVerificationDemo />

      <!-- Documentation -->
      <div class="mt-12 max-w-4xl mx-auto">
        <div class="bg-white dark:bg-zinc-800 shadow-lg p-6">
          <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            How It Works
          </h2>
          
          <div class="space-y-6 text-gray-700 dark:text-gray-300">
            <div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                1. Direct Charge Verification
              </h3>
              <p>
                Enter a charge ID to verify the status of a PayChangu direct charge payment. 
                This calls the <code class="bg-gray-100 dark:bg-zinc-700 px-2 py-1 text-sm">/payments/verify-direct-charge</code> endpoint.
              </p>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                2. Real-time Updates
              </h3>
              <p>
                Enter a transaction reference to listen for real-time payment status updates via WebSocket. 
                The system uses Laravel Echo with Reverb to broadcast payment status changes.
              </p>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                3. WebSocket Channels
              </h3>
              <p>
                Payment updates are broadcast on private channels:
              </p>
              <ul class="list-disc list-inside mt-2 space-y-1">
                <li><code class="bg-gray-100 dark:bg-zinc-700 px-2 py-1 text-sm">payment.{userId}</code> - User-specific payment updates</li>
                <li><code class="bg-gray-100 dark:bg-zinc-700 px-2 py-1 text-sm">payment.transaction.{transactionId}</code> - Transaction-specific updates</li>
              </ul>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                4. Background Processing
              </h3>
              <p>
                The system uses background jobs to verify payments:
              </p>
              <ul class="list-disc list-inside mt-2 space-y-1">
                <li><strong>VerifyPaymentJob</strong> - Automatically verifies payments after 2 minutes</li>
                <li><strong>HandlePaymentTimeoutJob</strong> - Handles payment timeouts after 30 minutes</li>
                <li><strong>PaymentStatusUpdated Event</strong> - Broadcasts status changes via WebSocket</li>
              </ul>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                5. API Endpoints
              </h3>
              <div class="space-y-2">
                <div class="bg-gray-50 dark:bg-zinc-700 p-3">
                  <code class="text-sm">POST /api/payments/verify-direct-charge</code>
                  <p class="text-sm mt-1">Verify a direct charge using charge_id</p>
                </div>
                <div class="bg-gray-50 dark:bg-zinc-700 p-3">
                  <code class="text-sm">GET /api/payments/verify</code>
                  <p class="text-sm mt-1">Verify a payment using transaction reference</p>
                </div>
                <div class="bg-gray-50 dark:bg-zinc-700 p-3">
                  <code class="text-sm">POST /api/payments/webhook</code>
                  <p class="text-sm mt-1">PayChangu webhook endpoint for payment notifications</p>
                </div>
              </div>
            </div>

            <div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                6. Testing
              </h3>
              <p>
                To test the integration:
              </p>
              <ol class="list-decimal list-inside mt-2 space-y-1">
                <li>Initialize a payment using the ticket purchase or subscription flow</li>
                <li>Copy the charge_id from the payment response</li>
                <li>Use this demo page to verify the charge and listen for updates</li>
                <li>Complete the payment on your mobile device</li>
                <li>Watch the real-time status updates appear</li>
              </ol>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: 'default',
  middleware: 'auth'
})

useHead({
  title: 'PayChangu Payment Demo - EventaHub',
  meta: [
    {
      name: 'description',
      content: 'Real-time payment verification demo using PayChangu API and WebSocket'
    }
  ]
})
</script>
