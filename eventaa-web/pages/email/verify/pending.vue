<template>
  <div
    class="min-h-screen bg-gray-50 dark:bg-zinc-900 flex flex-col justify-center py-12 sm:px-6 lg:px-8"
  >
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <div class="flex justify-center">
        <div
          class="w-20 h-20 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center"
        >
          <Icon
            icon="streamline-ultimate:send-email-fly"
            class="w-10 h-10 text-red-600 dark:text-red-400"
          />
        </div>
      </div>
      <h2
        class="mt-6 text-center text-3xl font-bold tracking-tight text-gray-900 dark:text-zinc-100"
      >
        Check your email
      </h2>
      <p class="mt-2 text-center  text-gray-600 dark:text-zinc-400">
        We've sent a verification link to your email address
      </p>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
      <div class="bg-white dark:bg-zinc-800 py-8 px-4 shadow sm:px-10">
        <div class="space-y-6">
          <div class="text-center">
            <p class=" text-gray-600 dark:text-zinc-200">
              Please check your email and click the verification link to
              activate your account.
            </p>
            <p class="mt-2  text-gray-600 dark:text-zinc-200">
              The verification link will expire in 60 minutes.
            </p>
          </div>

          <div class="space-y-4">
            <button
              @click="resendEmail"
              :disabled="loading || cooldown > 0"
              class="w-full flex justify-center py-2 px-4 border border-transparent  font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <CoreLoader v-if="loading" :height="20" :width="20" color="white" class="w-4 h-4 mr-2" />
              {{
                cooldown > 0
                  ? `Resend in ${cooldown}s`
                  : "Resend verification email"
              }}
            </button>

            <NuxtLink
              to="/"
              class="w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-zinc-600  font-medium text-gray-700 dark:text-zinc-300 bg-white dark:bg-zinc-700 hover:bg-gray-50 dark:hover:bg-zinc-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              Back to home
            </NuxtLink>
          </div>

          <div class="text-center">
            <p class="text-xs text-gray-500 dark:text-zinc-200">
              Didn't receive the email? Check your spam folder or try resending.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { GenericResponse } from "@/types/api";
import { ENDPOINTS } from "@/utils/api";

const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();
const loading = ref<boolean>(false);
const cooldown = ref<number>(0);

let cooldownInterval: NodeJS.Timeout | null = null;

const resendEmail = async (): Promise<void> => {
  if (loading.value || cooldown.value > 0) return;

  const email = localStorage.getItem("pending_verification_email");
  if (!email) {
    $toast.error("Email address not found. Please register again.");
    return;
  }

  loading.value = true;
  try {
    const response = await httpClient.post<GenericResponse>(
      ENDPOINTS.AUTH.RESEND_VERIFICATION,
      {
        email: email,
      }
    );

    if (response) {
      $toast.success(response.message);
      startCooldown();
    }
  } catch (error: any) {
    if (error.message?.message) {
      $toast.error(error.message.message);
    } else {
      $toast.error("Failed to resend verification email. Please try again.");
    }
  } finally {
    loading.value = false;
  }
};

const startCooldown = (): void => {
  cooldown.value = 60;
  cooldownInterval = setInterval(() => {
    cooldown.value--;
    if (cooldown.value <= 0) {
      clearInterval(cooldownInterval!);
      cooldownInterval = null;
    }
  }, 1000);
};

onUnmounted(() => {
  if (cooldownInterval) {
    clearInterval(cooldownInterval);
  }
});

useSeoMeta({
  title: "Email Verification - EventaHub",
  description:
    "Verify your email address to complete your EventaHub registration",
});
</script>
