<template>
  <div
    class="min-h-screen bg-gray-50 dark:bg-zinc-900 flex flex-col justify-center py-12 sm:px-6 lg:px-8"
  >
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <div class="flex justify-center">
        <div
          class="w-16 h-16 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center"
        >
          <Icon
            icon="streamline-ultimate:send-email-fly"
            class="w-8 h-8 text-blue-600 dark:text-blue-400"
          />
        </div>
      </div>
      <h2
        class="mt-6 text-center text-3xl font-bold tracking-tight text-gray-900 dark:text-zinc-100"
      >
        Email already verified
      </h2>
      <p class="mt-2 text-center text-sm text-gray-600 dark:text-zinc-400">
        Your account is already active
      </p>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
      <div class="bg-white dark:bg-zinc-800 py-8 px-4 shadow sm:px-10">
        <div class="space-y-6">
          <div class="text-center">
            <p class="text-sm text-gray-600 dark:text-zinc-400">
              Your email address has already been verified. Your account is
              active and ready to use.
            </p>
            <p class="mt-2 text-sm text-gray-600 dark:text-zinc-400">
              You can sign in and start exploring events on EventaHub.
            </p>
          </div>

          <div class="space-y-4">
            <NuxtLink
              to="/"
              class="w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              Continue to EventaHub
            </NuxtLink>
          </div>

          <div class="text-center">
            <p class="text-xs text-gray-500 dark:text-zinc-500">
              Need help? Contact our support team for assistance.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
useSeoMeta({
  title: "Email Already Verified - EventaHub",
  description: "Your email address has already been verified.",
});
</script>
