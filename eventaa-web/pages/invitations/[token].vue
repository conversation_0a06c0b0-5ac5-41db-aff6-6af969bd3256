<template>
  <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <div class="flex justify-center">
        <img class="h-12 w-auto" src="/icon.png" alt="EventaHub" />
      </div>
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
        Event Invitation
      </h2>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-lg">
      <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
        <div v-if="isLoading" class="text-center">
          <CoreLoader :width="48" :height="48" />
          <p class="mt-4 text-gray-600">Loading invitation details...</p>
        </div>

        <div v-else-if="error" class="text-center">
          <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
            <Icon icon="heroicons:exclamation-triangle" class="h-6 w-6 text-red-600" />
          </div>
          <h3 class="mt-2 text-lg font-medium text-gray-900">Error</h3>
          <p class="mt-1 text-sm text-gray-500">{{ error }}</p>
          <div class="mt-6">
            <NuxtLink to="/" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700">
              Go to Homepage
            </NuxtLink>
          </div>
        </div>

        <div v-else-if="invitation && event">
          <div class="text-center mb-6">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
              <Icon icon="heroicons:calendar-days" class="h-6 w-6 text-green-600" />
            </div>
            <h3 class="mt-2 text-lg font-medium text-gray-900">{{ event.title }}</h3>
            <p class="mt-1 text-sm text-gray-500">
              You've been invited by {{ invitation.invited_by_name || 'Event Organizer' }}
            </p>
          </div>

          <div class="space-y-4 mb-6">
            <div class="flex items-center text-sm text-gray-600">
              <Icon icon="heroicons:calendar" class="h-5 w-5 mr-2" />
              <span>{{ formatDate(event.start) }}</span>
            </div>
            <div class="flex items-center text-sm text-gray-600">
              <Icon icon="heroicons:clock" class="h-5 w-5 mr-2" />
              <span>{{ formatTime(event.start) }}</span>
            </div>
            <div class="flex items-center text-sm text-gray-600">
              <Icon icon="heroicons:map-pin" class="h-5 w-5 mr-2" />
              <span>{{ event.location }}</span>
            </div>
          </div>

          <div v-if="invitation.message" class="mb-6 p-4 bg-gray-50 rounded-lg">
            <p class="text-sm text-gray-700">
              <span class="font-medium">Personal message:</span><br>
              "{{ invitation.message }}"
            </p>
          </div>

          <div v-if="invitation.status === 'pending' || invitation.status === 'sent'" class="space-y-3">
            <button
              @click="handleResponse('accept')"
              :disabled="isResponding"
              class="w-full flex justify-center items-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50">
              <CoreLoader v-if="isResponding && responseType === 'accept'" :width="16" :height="16" color="#ffffff" class="mr-2" />
              <Icon v-else icon="heroicons:check" class="h-4 w-4 mr-2" />
              Accept Invitation
            </button>

            <button
              @click="handleResponse('decline')"
              :disabled="isResponding"
              class="w-full flex justify-center items-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50">
              <CoreLoader v-if="isResponding && responseType === 'decline'" :width="16" :height="16" class="mr-2" />
              <Icon v-else icon="heroicons:x-mark" class="h-4 w-4 mr-2" />
              Decline Invitation
            </button>
          </div>

          <div v-else-if="invitation.status === 'accepted'" class="text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
              <Icon icon="heroicons:check-circle" class="h-6 w-6 text-green-600" />
            </div>
            <h3 class="mt-2 text-lg font-medium text-gray-900">Invitation Accepted</h3>
            <p class="mt-1 text-sm text-gray-500">
              You have already accepted this invitation.
            </p>
            <div class="mt-6">
              <NuxtLink :to="`/events/${event.slug}`" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700">
                View Event
              </NuxtLink>
            </div>
          </div>

          <div v-else-if="invitation.status === 'declined'" class="text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
              <Icon icon="heroicons:x-circle" class="h-6 w-6 text-red-600" />
            </div>
            <h3 class="mt-2 text-lg font-medium text-gray-900">Invitation Declined</h3>
            <p class="mt-1 text-sm text-gray-500">
              You have declined this invitation.
            </p>
            <div class="mt-6">
              <NuxtLink to="/" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-gray-600 hover:bg-gray-700">
                Go to Homepage
              </NuxtLink>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useHttpClient } from '@/composables/useHttpClient'
import { ENDPOINTS } from '@/utils/api'
import type { EventItem } from '@/types'
import type { EventInvitation } from '@/types/invitation'

const route = useRoute()
const router = useRouter()
const httpClient = useHttpClient()
const { $toast }: any = useNuxtApp()

const token = route.params.token as string

const isLoading = ref(true)
const isResponding = ref(false)
const responseType = ref<'accept' | 'decline' | null>(null)
const error = ref<string>('')
const invitation = ref<EventInvitation | null>(null)
const event = ref<EventItem | null>(null)

const fetchInvitationDetails = async () => {
  try {
    isLoading.value = true
    const response = await httpClient.get(`/invitations/${token}`) as any

    if (response.data) {
      invitation.value = response.data.invitation
      event.value = response.data.event
    }
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to load invitation details'
  } finally {
    isLoading.value = false
  }
}

const handleResponse = async (type: 'accept' | 'decline') => {
  try {
    isResponding.value = true
    responseType.value = type

    const endpoint = type === 'accept'
      ? `/invitations/accept/${token}`
      : `/invitations/decline/${token}`

    const response = await httpClient.post(endpoint) as any

    if (response.data) {
      invitation.value = response.data

      if (type === 'accept') {
        $toast.success('Invitation accepted! Welcome to the event.')
        setTimeout(() => {
          if (event.value?.slug) {
            router.push(`/events/${event.value.slug}`)
          }
        }, 2000)
      } else {
        $toast.success('Invitation declined.')
      }
    }
  } catch (err: any) {
    error.value = err.response?.data?.message || `Failed to ${type} invitation`
    $toast.error(`Failed to ${type} invitation`)
  } finally {
    isResponding.value = false
    responseType.value = null
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const formatTime = (dateString: string) => {
  return new Date(dateString).toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  })
}

useHead({
  title: 'Event Invitation - EventaHub'
})

onMounted(fetchInvitationDetails)
</script>
