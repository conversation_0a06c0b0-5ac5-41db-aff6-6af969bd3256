<template>
    <div class="flex flex-col items-center justify-center py-10 sm:py-20 px-4 sm:px-0">
        <img src="/assets/svgs/to-the-stars.svg"
            class="w-48 sm:w-auto h-48 sm:h-72 object-contain sm:object-cover mb-6 sm:mb-0" alt="to-the-stars-svg">
        <h3 class="text-xl sm:text-2xl font-semibold tracking-wider sm:tracking-widest text-center">
            Welcome to EventaHub Malawi
        </h3>
        <p class="text-base sm:text-lg tracking-wide mt-2">Getting started</p>
        <p class="text-sm sm:text-base text-gray-500 text-center px-4">
            Select event categories you are interested in (select up to 5)
        </p>
        <div class="mt-6 sm:mt-10 w-full max-w-xl px-4">
            <div class="flex flex-wrap justify-center gap-2">
                <button type="button" @click="modifySelectedCategory(category)"
                    class="flex items-center bg-gray-100 font-light hover:bg-red-600 hover:text-white transition duration-150 rounded-full px-2.5 py-1.5 mb-2"
                    v-for="category in $categories" :key="category.name"
                    :class="selectedCategory.find((cat) => category.name == cat.name) ? 'bg-red-600 text-white' : ''">
                    <img :src="`${runtimeConfig.public.baseUrl}storage/categories/${category.icon}`"
                        class="w-5 h-5 sm:w-6 sm:h-6 mr-1.5 sm:mr-2" />
                    {{ category.name }}
                </button>
            </div>
            <div class="w-full flex items-center justify-center mt-6 sm:mt-10">
                <button v-if="!loading" @click="saveInterests" class="text-sky-500 font-medium text-sm sm:text-base">
                    Continue &rarr;
                </button>
                <div v-else class="text-sky-500 flex items-center text-sm sm:text-base">
                    <CoreLoader color="rgb(14 165 233 / var(--tw-text-opacity))" width="24" height="24"
                        class="mr-2 sm:mr-3" />
                    Saving, please wait...
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { Category } from '@/types';
import type { GenericResponse } from '@/types/api';

definePageMeta({
    layout: "default",
    layoutTransition: {
        name: 'fade',
        mode: 'out-in',
    },
    middleware: ['auth']
})

useHead({
    title: 'Get Started with EventaHub Malawi - Choose Your Interests',
    meta: [
        {
            name: 'description',
            content: 'Welcome to EventaHub Malawi! Get started by selecting your event interests and discover personalized events recommendations for conferences, concerts, workshops and more.',
        },
        {
            name: 'keywords',
            content: 'get started EventaHub, event interests, personalized events, event categories Malawi, event preferences'
        },
        {
            property: 'og:title',
            content: 'Get Started with EventaHub Malawi - Choose Your Interests'
        },
        {
            property: 'og:description',
            content: 'Welcome to EventaHub Malawi! Get started by selecting your event interests and discover personalized events recommendations.'
        },
        {
            property: 'og:type',
            content: 'website'
        },
        {
            name: 'twitter:card',
            content: 'summary_large_image'
        },
        {
            name: 'twitter:title',
            content: 'Get Started with EventaHub Malawi - Choose Your Interests'
        },
        {
            name: 'twitter:description',
            content: 'Welcome to EventaHub Malawi! Get started by selecting your event interests and discover personalized events.'
        },
        {
            name: 'robots',
            content: 'noindex, nofollow'
        }
    ],
})

const runtimeConfig = useRuntimeConfig();
const httpClient = useHttpClient();
const loading = ref<boolean>(false);
const selectedCategory = ref<Category[]>([]);
const { $toast, $categories }: any = useNuxtApp();

const modifySelectedCategory = (category: Category): void => {
    if (selectedCategory.value.length == 5) {
        $toast.error('You can only select up to 5 categories');
        return;
    }

    if (selectedCategory.value.find(c => c.name === category.name)) {
        selectedCategory.value = selectedCategory.value.filter(
            (category: Category) => category.name !== category.name
        );
    } else {
        selectedCategory.value.push(category);
    }
};

const saveInterests = async (): Promise<void> => {
    if (selectedCategory.value.length < 3) {
        $toast.error('Please select at least 3 categories');
        return;
    }
    try {
        loading.value = true;
        const formData: FormData = new FormData();
        formData.append('category_id', JSON.stringify(selectedCategory.value.map((category: Category) => category.id)));
        const response = await httpClient.post<GenericResponse>(ENDPOINTS.INTERESTS.CREATE, formData);

        if (response) {
            $toast.success(response.message);
            setTimeout(() => {
                navigateTo('/');
            }, 1500)
        }
    } catch (error: any) {
        const errors = error.errors;
        Object.keys(errors).forEach((key) => {
            if (Array.isArray(errors[key])) {
                errors[key].forEach((message: string) => {
                    $toast.error(message);
                });
            } else if (typeof errors[key] === 'string') {
                $toast.error(errors[key]);
            }
        });
    } finally {
        loading.value = false;
    }
};
</script>

<style lang="scss" scoped></style>
