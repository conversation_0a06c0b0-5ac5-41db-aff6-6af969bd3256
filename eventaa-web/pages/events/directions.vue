<template>
    <div class="relative w-full h-full">
        <div class="px-4 py-2">
            <button @click="router.back()" class="flex items-center gap-2 text-gray-600 font-medium py-2">
                <Icon icon="material-symbols:arrow-back" class="w-6 h-6" />
                Continue browsing
            </button>
            <h3 class="text-2xl font-semibold mb-2">Directions to '{{ route.query.location }} Venue'</h3>
        </div>
        <GoogleMap 
            :api-key="runtimeConfig.public.googleMapsApiKey" 
            :zoom="zoom" 
            :center="center" 
            map-type-id="terrain" 
            class="w-full h-screen"
        >
            <CustomMarker 
                :options="{
                    position: currentLocation,
                    anchorPoint: 'BOTTOM_CENTER'
                }"
            >
                <div class="relative">
                    <button 
                        @click="startNavigation" 
                        class="relative inline-flex items-center justify-center p-2 rounded-none cursor-pointer bg-red-600 text-white shadow-2xl shadow-black animate-bounce"
                    >
                        <Icon icon="fa6-solid:map-location-dot" class="w-6 h-6" />
                    </button>
                    <span 
                        class="absolute left-1/2 -bottom-1 transform -translate-x-1/2 w-0 h-0 border-l-[6px] border-l-transparent border-r-[6px] border-r-transparent border-t-[6px] border-t-red-600"
                    ></span>
                </div>
            </CustomMarker>

            <Polyline
                v-if="polylinePath && polylinePath.length > 0"
                :path="polylinePath"
                :options="{
                    strokeColor: '#dc2626',
                    strokeOpacity: 0.8,
                    strokeWeight: 4
                }"
            />

            
            <CustomMarker 
                :options="{
                    position: destinationMarker,
                    anchorPoint: 'BOTTOM_CENTER'
                }"
            >
                <div class="relative">
                    <button 
                        class="relative inline-flex items-center justify-center p-2 rounded-none cursor-pointer bg-red-600 text-white shadow-2xl shadow-black animate-bounce"
                    >
                        <Icon icon="bxs:map-pin" class="w-6 h-6" />
                    </button>
                    <span 
                        class="absolute left-1/2 -bottom-1 transform -translate-x-1/2 w-0 h-0 border-l-[6px] border-l-transparent border-r-[6px] border-r-transparent border-t-[6px] border-t-red-600"
                    ></span>
                </div>
            </CustomMarker>
        </GoogleMap>

        <div 
            v-if="showDirections" 
            class="absolute top-0 right-0 bg-white rounded-lg shadow-2xl max-w-xs w-full"
        >
            <h3 class="text-lg font-bold px-4 py-2 border-b pb-2">Navigation Details</h3>
            <div v-if="directions" class="space-y-2 p-4">
                <div 
                    v-for="(step, index) in directions.routes[0].legs[0].steps" 
                    :key="index" 
                    class="flex items-center space-x-2"
                >
                    <Icon icon="game-icons:path-distance" class="w-5 h-5 text-red-600"/>
                    <div v-html="step.instructions" class="text-sm text-gray-700"></div>
                </div>
            </div>
            <button 
                @click="clearNavigation" 
                class="mt-4 w-full bg-red-600 text-white py-2 hover:bg-red-600 transition"
            >
                Cancel Navigation
            </button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { GoogleMap, CustomMarker, Polyline } from 'vue3-google-map'
import { Icon } from '@iconify/vue'

interface DirectionsResult {
    routes: Array<{
        legs: Array<{
            steps: Array<{
                instructions: string
                distance: { text: string }
                duration: { text: string }
            }>
        }>
    }>
}

const route = useRoute();
const zoom = ref(15);
const center = ref({ lat: Number(route.query.lat), lng: Number(route.query.lng) })
const runtimeConfig = useRuntimeConfig()
const currentLocation = ref<any>(null)
const destinationMarker = ref<any>(null)
const { $toast }: any = useNuxtApp();
const router = useRouter();
const directions = ref<DirectionsResult | null>(null)
const showDirections = ref(false)
const polylinePath = ref<any[]>([])

const startNavigation = async (): Promise<void> => {
    try {
        const position = await getCurrentPosition();
        currentLocation.value = {
            lat: position.coords.latitude,
            lng: position.coords.longitude,
        };

        destinationMarker.value = center.value;
        const directionsService = new google.maps.DirectionsService();
        const directionsRequest = {
            origin: currentLocation.value,
            destination: destinationMarker.value,
            travelMode: google.maps.TravelMode.DRIVING,
        };

        const result = await directionsService.route(directionsRequest);
        const route = result.routes?.[0];
        if (!route || !route.overview_path) {
            console.error('No polyline data available in the response.');
            $toast.warn('No route data available. Please try again.');
            return;
        }

        polylinePath.value = route.overview_path.map((latLng) => ({
            lat: latLng.lat(),
            lng: latLng.lng(),
        }));

        directions.value = result;
        showDirections.value = true;
    } catch (error) {
        console.error('Failed to start navigation:', error);
        $toast.error('Unable to start navigation. Please check your location settings and try again.');
    }
};



const getCurrentPosition = (): Promise<GeolocationPosition> => {
    return new Promise((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject)
    })
}

const clearNavigation = () => {
    directions.value = null
    showDirections.value = false
    currentLocation.value = null
    destinationMarker.value = null
    polylinePath.value = [];
    $toast.warn("Navigation ended!");
    router.back();
}

onMounted(() => {
    startNavigation()
})
</script>