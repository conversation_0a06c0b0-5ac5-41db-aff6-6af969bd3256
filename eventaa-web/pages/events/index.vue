<template>
  <div>
    <LandingHomeHero @onQuery="onQuery" @tags="onSearchTags" />
    <div v-if="activeFilters.length > 0" class="w-full px-5 mt-3">
      <div class="shadow flex items-center justify-between bg-white dark:bg-zinc-800 p-4">
        <div class="flex flex-wrap items-center gap-3">
          <span class="font-medium text-gray-700 dark:text-zinc-300">Active Filters:</span>
          <div v-for="filter in activeFilters" :key="filter.key"
            class="flex items-center bg-zinc-100 dark:bg-zinc-900/20 rounded-full border border-zinc-50 dark:border-zinc-800 px-3 py-1">
            <span class="text-zinc-700 font-medium dark:text-zinc-300">{{ filter.label }}</span>
            <button
              @click="removeFilter(filter.key as 'location' | 'search' | 'categories' | 'dateRange' | 'trending' | 'recommended')"
              class="ml-2 text-zinc-500 font-normal hover:text-zinc-700 dark:text-zinc-400 dark:hover:text-zinc-200 transition">
              <Icon icon="zondicons:close-solid" class="w-4 h-4" />
            </button>
          </div>
        </div>
        <button @click="clearAllFilters"
          class="text-sm font-medium text-gray-500 dark:text-zinc-400 hover:text-red-600 dark:hover:text-red-400 transition">
          Clear All
        </button>
      </div>
    </div>
    <div class="w-full mt-5 px-5" v-if="loading">
      <EventsSkeletonLoader v-if="loading" :is-grid="toggleGrid" :items-to-show="itemsPerPage" />
    </div>

    <div class="w-full mt-5 px-5" v-else>
      <div class="w-full flex justify-between">
        <h3 class="text-lg sm:text-xl font-semibold dark:text-zinc-100">
          Showing {{ events.length }} events
        </h3>
        <div class="flex items-center space-x-2">
          <EventsFilters @apply-filters="onFiltersApply" />
          <div class="relative flex">
            <div class="relative">
              <button @click="changeView('grid')" :class="[
                toggleGrid
                  ? 'bg-red-600 text-white'
                  : 'bg-gray-200 text-gray-500 dark:bg-zinc-900 dark:text-zinc-50',
                'p-1.5 shiny',
              ]">
                <Icon icon="ep:grid" class="w-6 h-6" />
              </button>
              <span v-if="toggleGrid"
                class="absolute left-1/2 -bottom-1 transform -translate-x-1/2 w-0 h-0 border-l-[6px] border-l-transparent border-r-[6px] border-r-transparent border-t-[6px] border-t-red-600"></span>
            </div>

            <div class="relative">
              <button @click="changeView('list')" :class="[
                toggleGrid
                  ? 'bg-gray-200 text-gray-500 dark:bg-zinc-900 dark:text-zinc-50'
                  : 'bg-red-600 text-white',
                'p-1.5 shiny',
              ]">
                <Icon icon="ph:list-bold" class="w-6 h-6" />
              </button>
              <span v-if="!toggleGrid"
                class="absolute left-1/2 -bottom-1 transform -translate-x-1/2 w-0 h-0 border-l-[6px] border-l-transparent border-r-[6px] border-r-transparent border-t-[6px] border-t-red-600"></span>
            </div>
          </div>
        </div>
      </div>

      <div>
        <div v-if="toggleGrid" class="w-full sm:grid sm:grid-cols-3 gap-4 mt-1 space-y-4 sm:space-y-0">
          <EventsCard v-for="event in events" :key="event.id" :event="event" />
        </div>

        <div class="mt-1 w-full flex flex-col space-y-2.5" v-else>
          <EventsListview v-for="event in events" :key="event.id" :event="event" />
        </div>
      </div>

      <div class="flex flex-col items-center mt-4 dark:text-zinc-100" v-if="totalEvents > 0">
        <span class="">
          Showing
          <span class="font-semibold">{{ startIndex + 1 }}</span>
          to
          <span class="font-semibold">{{ endIndex }}</span>
          of
          <span class="font-semibold">{{ totalEvents }}</span>
          Entries
        </span>
        <div class="inline-flex mt-2 xs:mt-0">
          <button @click="prevPage" :disabled="currentPage === 1"
            class="flex items-center dark:text-zinc-100 justify-center px-4 h-10 text-base font-medium bg-gray-200 dark:bg-zinc-900 hover:bg-gray-300 transition duration-150 disabled:opacity-50 disabled:cursor-not-allowed">
            <Icon icon="grommet-icons:link-previous" class="w-5 h-5 mr-2" />
            Prev
          </button>
          <button @click="nextPage" :disabled="currentPage === totalPages"
            class="flex items-center justify-center px-4 h-10 text-base font-medium transition duration-150 text-white bg-red-600 border-0 hover:bg-red-700 disabled:cursor-not-allowed">
            Next
            <Icon icon="streamline:next" class="w-5 h-5 ml-2" />
          </button>
        </div>
      </div>

      <div v-if="totalEvents == 0" class="w-full flex flex-col space-y-2.5 items-center justify-center py-20">
        <img src="@/assets/illustrations/lighthouse.svg" class="w-auto h-72" alt="no-events-story-illustration" />
        <h3 class="font-semibold text-lg">Could not load events, please try again later...</h3>
        <button @click="fetchEvents"
          class="inline-flex items-center text-sky-500 transition-all hover:text-sky-400 duration-150">
          <Icon icon="system-uicons:refresh" class="w-5 h-5 mr-2" /> Refresh
        </button>
      </div>
    </div>

    <div class="mt-10">
      <div
        class="flex items-center max-md:flex-col gap-6 bg-gradient-to-tr from-red-700 to-pink-500 text-white px-6 py-3.5">
        <p class="text-base flex-1 max-md:text-center">
          <span class="text-xl font-semibold flex items-center">
            <Icon icon="mage:megaphone-a" class="h-6 w-6 mr-2" /> Did you know?
          </span>
          You can host your own virtual and venue events here on EventaHub? Sign up
          today and start hosting your own events.
        </p>

        <div>
          <button @click="router.push('/become-host')" type="button" class="bg-white text-black py-2.5 px-5">
            Become Host
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Category, EventItem } from '@/types';
import type { EventsResponse } from '@/types/api';
import dayjs from 'dayjs';
import type { FilterComponents } from '@/types/filters';

definePageMeta({
  layout: "default",
});

useHead({
  title: "Discover Events in Malawi - Browse Conferences, Concerts & More | EventaHub",
  meta: [
    {
      name: "description",
      content: "Explore and discover amazing events happening in Malawi. Browse conferences, concerts, workshops, seminars, and cultural events. Find your next experience on EventaHub Malawi.",
    },
    {
      name: 'keywords',
      content: 'events Malawi, browse events, conferences Malawi, concerts, workshops, seminars, cultural events, event discovery'
    },
    {
      property: 'og:title',
      content: 'Discover Events in Malawi - Browse Conferences, Concerts & More | EventaHub'
    },
    {
      property: 'og:description',
      content: 'Explore and discover amazing events happening in Malawi. Browse conferences, concerts, workshops, and cultural events.'
    },
    {
      property: 'og:type',
      content: 'website'
    },
    {
      name: 'twitter:card',
      content: 'summary_large_image'
    },
    {
      name: 'twitter:title',
      content: 'Discover Events in Malawi - Browse Conferences, Concerts & More'
    },
    {
      name: 'twitter:description',
      content: 'Explore and discover amazing events happening in Malawi. Find your next experience on EventaHub.'
    },
    {
      name: 'robots',
      content: 'index, follow'
    }
  ],
});

const httpClient = useHttpClient();
const toggleGrid = ref<boolean>(true);
const currentPage = ref<number>(1);
const itemsPerPage = ref<number>(6);
const totalEvents = ref<number>(0);
const events = ref<EventItem[]>([]);
const loading = ref<boolean>(false);
const dateRange = ref<string>('');
const searchQuery = ref<string>('');
const categories = ref<number[]>([]);
const lat = ref<number>(0);
const lng = ref<number>(0);
const radius = ref<number>(10990);
const address = ref<string>('');
const routerQuery = useRouterQuery();
const router = useRouter();
const { $toast }: any = useNuxtApp();

const totalPages = computed(() =>
  Math.ceil(totalEvents.value / itemsPerPage.value)
);

const startIndex = computed(() =>
  (currentPage.value - 1) * itemsPerPage.value
);

const endIndex = computed(() =>
  Math.min(startIndex.value + itemsPerPage.value, totalEvents.value)
);

const prevPage = (): void => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

const nextPage = (): void => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};

const changeView = (type: "grid" | "list"): void => {
  toggleGrid.value = type === "grid";
  routerQuery.replaceOneQuery("view", { view: type });
}

interface ActiveFilter {
  key: string;
  label: string;
}

const activeFilters = ref<ActiveFilter[]>([]);

const filterResetActions = {
  search: () => {
    searchQuery.value = '';
  },
  categories: () => {
    categories.value = [];
  },
  dateRange: () => {
    dateRange.value = '';
  },
  location: () => {
    lat.value = 0;
    lng.value = 0;
    radius.value = 100;
    address.value = '';
  },
  trending: () => {
    activeFilters.value.map((filter) => {
      if (filter.key == "trending") {
        activeFilters.value.splice(activeFilters.value.indexOf(filter), 1);
        routerQuery.removeAllQueries()
      }
    });
  },
  recommended: () => {
    activeFilters.value.map((filter) => {
      if (filter.key == "recommended") {
        activeFilters.value.splice(activeFilters.value.indexOf(filter), 1);
      }
    });
  },
  nearby: () => {
    activeFilters.value.map((filter) => {
      if (filter.key == "nearby") {
        activeFilters.value.splice(activeFilters.value.indexOf(filter), 1);
      }
    });
  }
};

const removeFilter = (key: keyof typeof filterResetActions) => {
  const resetAction = filterResetActions[key];
  if (resetAction) {
    resetAction();
    if (routerQuery && typeof routerQuery.removeQuery === 'function') {
      const obj: Record<string, any> = {};
      obj[key] = undefined;
      routerQuery.removeQuery(obj);
    } else if (router && router.currentRoute && router.replace) {
      const currentQuery = { ...router.currentRoute.value.query };
      delete currentQuery[key];
      router.replace({ query: currentQuery });
    }
  }
};

const clearAllFilters = () => {
  searchQuery.value = '';
  categories.value = [];
  dateRange.value = '';
  lat.value = 0;
  lng.value = 0;
  radius.value = 100;
  address.value = '';
  if (routerQuery && typeof routerQuery.removeAllQueries === 'function') {
    routerQuery.removeAllQueries();
  } else if (router && router.replace) {
    router.replace({ query: {} });
  }
};

const updateActiveFilters = () => {
  activeFilters.value = [];

  if (searchQuery.value) {
    activeFilters.value.push({
      key: 'search',
      label: `Search: ${searchQuery.value}`
    });
  }

  if (categories.value.length > 0) {
    activeFilters.value.push({
      key: 'categories',
      label: `Categories: ${categories.value.length} selected`
    });
  }

  if (dateRange.value) {
    const [start, end] = dateRange.value.split(',');
    activeFilters.value.push({
      key: 'dateRange',
      label: `Date: ${dayjs(start).format('MMM D')} - ${dayjs(end).format('MMM D')}`
    });
  }

  if (address.value) {
    activeFilters.value.push({
      key: 'nearby',
      label: `Nearby: ${address.value}`
    });
  }
};

const onFiltersApply = (components: FilterComponents): void => {
  if (components) {
    const queryParams: Record<string, string> = {};
    if (components.dateRange && components.dateRange.length === 2) {
      dateRange.value = `${dayjs(components.dateRange[0]).format(DATE_FORMAT.FULL)},${dayjs(components.dateRange[1]).format(DATE_FORMAT.FULL)}`;
    }

    if (components.categories && components.categories.length > 0) {
      categories.value = components.categories.map((category: Category) => {
        return category.id;
      });
    }

    if (components.location) {
      if (components.location.center?.lat) {
        lat.value = Number(components.location.center.lat);
      }
      if (components.location.center?.lng) {
        lng.value = Number(components.location.center.lng);
      }
      if (components.location.radius) {
        radius.value = Number(components.location.radius);
      }
      if (components.location.address) {
        address.value = components.location.address;
      }
    }

    updateActiveFilters();

    if (Object.keys(queryParams).length > 0) {
      routerQuery.removeAllQueries();
      routerQuery.replaceQuery(queryParams);
    }
  }
};

const onQuery = async(items: { search: string; category: string | number; location: any; }): Promise<void> => {
  searchQuery.value = items.search;
  categories.value = [];
  if (items.category && typeof items.category === 'number') {
    categories.value.push(items.category);
  }
  if(items.location){
    address.value = items.location.city;
    lat.value = items.location.latitude;
    lng.value = items.location.longitude;
  }
};

const onSearchTags = (newTag: "Trending" | "Recommended" | "Nearby Me"): void => {
  const isTagAlreadyActive = activeFilters.value.some(
    filter => filter.key === newTag.toString().toLowerCase()
  );

  if (newTag?.toString().toLowerCase() === "trending") {
    fetchTrendingEvents();
  }

  if (newTag?.toString().toLowerCase() === "recommended") {
    fetchRecommendedEvents();
  }

  if (newTag?.toString().toLowerCase() === "nearby") {
    fetchNearbyEvents();
  }

  if (!isTagAlreadyActive) {
    activeFilters.value.push({
      key: newTag.toString().toLowerCase(),
      label: newTag.charAt(0).toUpperCase() + newTag.slice(1),
    });
  }
}

const fetchEvents = async (): Promise<void> => {
  try {
    loading.value = true;
    const response = await httpClient.get<EventsResponse>(
      `${ENDPOINTS.EVENTS.GET}?per_page=${itemsPerPage.value}&page=${currentPage.value}`
    );
    if (response) {
      events.value = response.events.data;
      totalEvents.value = response.events.total;
    }
  } catch (error) {
    $toast.error('An error occurred while fetching events, please try again later.');
    console.error('Error fetching events:', error);
  } finally {
    loading.value = false;
  }
};

const searchEvents = async (query: string): Promise<void> => {
  try {
    loading.value = true;
    const response = await httpClient.get<EventsResponse>(
      `${ENDPOINTS.EVENTS.SEARCH}?per_page=${itemsPerPage.value}&page=${currentPage.value}&${query}`
    );
    if (response) {
      events.value = response.events.data;
      totalEvents.value = response.events.total;
      response.events.data.length == 0 && $toast.warn('No events found with the specified filters, please try again.');
    }
  } catch (error) {
    $toast.error('An error occurred while fetching events, please try again later.');
    console.error('Error fetching events:', error);
  } finally {
    loading.value = false;
  }
};

const fetchTrendingEvents = async (): Promise<void> => {
  try {
    loading.value = true;
    const response = await httpClient.get<EventsResponse>(
      `${ENDPOINTS.EVENTS.TRENDING}?per_page=${itemsPerPage.value}&page=${currentPage.value}`
    );
    if (response) {
      events.value = response.events.data;
      totalEvents.value = response.events.total;
    }
  } catch (error) {
    $toast.error('An error occurred while fetching events, please try again later.');
    console.error('Error fetching events:', error);
  } finally {
    loading.value = false;
  }
}

const fetchRecommendedEvents = async (): Promise<void> => {
  try {
    loading.value = true;
    const response = await httpClient.get<EventsResponse>(
      `${ENDPOINTS.EVENTS.RECOMMENDED}?per_page=${itemsPerPage.value}&page=${currentPage.value}`
    );
    if (response) {
      events.value = response.events.data;
      totalEvents.value = response.events.total;
    }
  } catch (error) {
    $toast.error('An error occurred while fetching events, please try again later.');
    console.error('Error fetching events:', error);
  } finally {
    loading.value = false;
  }
}

const fetchNearbyEvents = async (): Promise<void> => {
  try {
    $toast.success("Searching events 100KM around you...");
    loading.value = true;
    const response = await httpClient.post<EventsResponse>(
      `${ENDPOINTS.EVENTS.NEARBY}?per_page=${itemsPerPage.value}&page=${currentPage.value}&latitude=${lat.value}&longitude=${lng.value}&radius=${radius.value}&city=${address.value}`
    );
    if (response) {
      events.value = response.events.data;
      totalEvents.value = response.events.total;
    }
  } catch (error) {
    $toast.error('An error occurred while fetching events, please try again later.');
    console.error('Error fetching events:', error);
  } finally {
    loading.value = false;
  }
}

onMounted(() => {
  const params = router.currentRoute.value.query;
  if (params.search) {
    searchQuery.value = params.search as string;
  }
  if (params.category) {
    if (Array.isArray(params.category)) {
      categories.value = params.category.map(Number);
    } else {
      categories.value = [Number(params.category)];
    }
  }
  if (params.location) {
    let loc: any = params.location;
    if (typeof loc === 'string') {
      try {
        loc = JSON.parse(loc);
      } catch {}
    }
    if (loc && typeof loc === 'object') {
      if (loc.city) address.value = loc.city;
      if (loc.latitude) lat.value = Number(loc.latitude);
      if (loc.longitude) lng.value = Number(loc.longitude);
    }
  }
})

watch(currentPage, (newPage: number, oldPage: number) => {
  if (newPage !== oldPage) {
    fetchEvents();
  }
});

watch(
  [() => searchQuery.value, () => categories.value, () => dateRange.value, address.value],
  ([newQuery, newCategories, newDateRange, newAddress]) => {
    if (newQuery || newCategories.length > 0 || newDateRange.length > 1) {
      searchEvents(`title=${newQuery}&categories=${JSON.stringify(newCategories)}&dateRange=${newDateRange || ''}`);
    } else if(newAddress !== null && lat.value !== 0 && lng.value !== 0) {
      fetchNearbyEvents();
    } else {
      fetchEvents();
    }
  },
  { deep: true }
);

onMounted(() => {
  fetchEvents();
});
</script>

<style lang="scss" scoped>
.moving-background {
  position: relative;
  background-image: url("/assets/images/hero.png");
  background-size: cover;
  background-position: center;
}

.shiny {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-1px);
  }
}
</style>
