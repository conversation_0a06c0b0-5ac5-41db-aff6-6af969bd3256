<template>
    <div>
        <BlogHero />
        <div class="w-full grid grid-cols-6 gap-5 px-5 py-5">
            <div class="col-span-4 space-y-3">
                <div>
                    <h3 class="text-lg font-semibold">Showing {{ articles.length }} of {{ total }} posts</h3>
                </div>
                <div v-if="isLoading" class="space-y-3">
                    <div v-for="i in 4" :key="i" class="p-6 border animate-pulse bg-gray-100">
                        <div class="h-16 bg-gray-300 mb-2 rounded"></div>
                        <div class="h-10 bg-gray-200 mb-1 rounded w-3/4"></div>
                        <div class="h-6 bg-gray-200 mb-1 rounded w-1/2"></div>
                    </div>
                </div>
                <div v-else>
                    <BlogArticle v-for="article in articles" :key="article.id" :article="article" />
                </div>
                <div class="flex justify-center mt-5 space-x-2">
                    <button class="px-4 py-2 bg-gray-200 hover:bg-gray-300"
                        :disabled="currentPage === 1" @click="changePage(currentPage - 1)">
                        Previous
                    </button>
                    <span>Page {{ currentPage }} of {{ lastPage }}</span>
                    <button class="px-4 py-2 bg-gray-200 hover:bg-gray-300"
                        :disabled="currentPage === lastPage" @click="changePage(currentPage + 1)">
                        Next
                    </button>
                </div>
            </div>
            <div class="col-span-2">
                <BlogSidebar />
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import type { Article } from '@/types';

useHead({
    title: 'Blog - Eventahub Malawi',
    meta: [
        {
            name: 'description',
            content: 'Blog articles and hashtags for eventahub malawi',
        },
    ],
})

const httpClient = useHttpClient();
const articles = ref<Article[]>([]);
const total = ref<number>(0);
const currentPage = ref<number>(1);
const lastPage = ref<number>(1);
const isLoading = ref<boolean>(false);

const fetchArticles = async (page = 1): Promise<void> => {
    isLoading.value = true;
    try {
        const response = await httpClient.get<any>(`${ENDPOINTS.BLOG.READ}?page=${page}`);
        if (response) {
            const data = response.data;
            articles.value = response.data;
            total.value = data.total;
            currentPage.value = data.current_page;
            lastPage.value = data.last_page;
        }
    } catch (error) {
        console.error(error);
    } finally {
        isLoading.value = false;
    }
};

const changePage = (page: number): void => {
    if (page >= 1 && page <= lastPage.value) {
        fetchArticles(page);
    }
};

onMounted(() => {
    fetchArticles();
});
</script>
