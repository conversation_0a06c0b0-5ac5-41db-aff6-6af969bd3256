<template>
    <div>
        <div class="relative w-full py-20">
            <div class="absolute inset-0 bg-black/50">
                <img src="@/assets/images/hero.png" alt="hero-background-image" class="w-full h-full object-cover" />
            </div>

            <div class="relative h-full flex flex-col items-center justify-center text-center px-4 md:px-8">
                <div class="transform transition-all duration-600"
                    :class="isAnimating ? 'opacity-0 -translate-y-8' : 'opacity-100 translate-y-0'">
                    <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold text-white max-w-5xl mx-auto">
                        {{ slides[currentSlide].heading }}
                        <span class="text-red-500 text-4xl md:text-5xl lg:text-6xl">
                            {{ slides[currentSlide].highlight }}
                        </span>
                        <br class="hidden sm:block" />
                    </h1>

                    <p class="text-gray-100 text-lg md:text-xl mt-6 max-w-4xl mx-auto">
                        {{ slides[currentSlide].subheading }}
                    </p>
                </div>
            </div>

            <div class="absolute bottom-6 left-0 right-0 flex justify-between items-center px-4">
                <button @click="prevSlide"
                    class="bg-black bg-opacity-25 p-2 rounded-full transition-colors duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                    </svg>
                </button>

                <div class="flex gap-2">
                    <button v-for="(_, index) in slides" :key="index"
                        class="h-3 w-3 rounded-full transition-all duration-300"
                        :class="currentSlide === index ? 'bg-white scale-125' : 'bg-white/50'"
                        @click="goToSlide(index)"></button>
                </div>

                <button @click="nextSlide"
                    class="bg-black bg-opacity-25 p-2 rounded-full transition-colors duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </button>
            </div>
        </div>
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-6">Venues</h1>
            <div class="mt-5">
                <div class="w-full grid grid-cols-5 items-center md:flex-row gap-4">
                    <div class="flex-1">
                        <Listbox v-model="selectedCategory">
                            <ListboxLabel class="block text-sm font-thin mb-2 dark:text-gray-300">SELECT YOUR ACTIVITY
                            </ListboxLabel>
                            <div class="relative mt-1">
                                <ListboxButton
                                    class="relative w-full cursor-default bg-white dark:bg-zinc-800 dark:border-zinc-700 py-2 pl-10 pr-10 text-left border focus:outline-none focus-visible:ring-offset-2 focus-visible:ring-offset-orange-300 sm:text-sm">
                                    <span
                                        class="absolute inset-y-0 left-0 flex items-center pl-3 text-black dark:text-zinc-300">
                                        <Icon icon="mdi:category-plus-outline" class="h-5 w-5" aria-hidden="true" />
                                    </span>
                                    <span class="block truncate dark:text-gray-200">{{ selectedCategory.name }}</span>
                                    <span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                                        <ChevronUpDownIcon class="h-5 w-5 text-gray-400 dark:text-gray-300"
                                            aria-hidden="true" />
                                    </span>
                                </ListboxButton>

                                <transition leave-active-class="transition duration-100 ease-in"
                                    leave-from-class="opacity-100" leave-to-class="opacity-0">
                                    <ListboxOptions
                                        class="absolute z-50 mt-1 max-h-60 w-full overflow-auto bg-white dark:bg-zinc-900 py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm">
                                        <ListboxOption v-slot="{ active, selected }" v-for="category in $categories.map((category: any) => {
                                            return {
                                                name: category.name,
                                                value: category.id,
                                                icon: category.icon
                                            }
                                        })" :key="category.name" :value="category" as="template">
                                            <li :class="[
                                                active ? 'bg-green-100 text-green-900 dark:bg-green-200 dark:text-green-800' : 'text-gray-900 dark:text-gray-100',
                                                'relative cursor-default select-none py-2 pl-3 pr-4',
                                            ]">
                                                <span :class="[
                                                    selected ? 'font-medium' : 'font-normal',
                                                    'flex items-center truncate',
                                                ]">
                                                    <img :src="`${runtimeConfig.public.baseUrl}storage/categories/${category.icon}`"
                                                        :alt="category.name" class="w-5 h-5 mr-5" />
                                                    {{ category.name }}
                                                </span>
                                                <span v-if="selected"
                                                    class="absolute inset-y-0 left-0 flex items-center pl-3 text-green-600 dark:text-green-500">
                                                    <CheckIcon class="h-5 w-5" aria-hidden="true" />
                                                </span>
                                            </li>
                                        </ListboxOption>
                                    </ListboxOptions>
                                </transition>
                            </div>
                        </Listbox>
                    </div>

                    <div class="flex-1">
                        <Listbox v-model="selectedLocation">
                            <ListboxLabel class="block text-sm font-thin mb-2 dark:text-gray-300">SELECT YOUR LOCATION
                            </ListboxLabel>
                            <div class="relative mt-1">
                                <ListboxButton
                                    class="relative w-full cursor-default bg-white dark:bg-zinc-800 dark:border-zinc-700 py-2 pl-10 pr-10 text-left border focus:outline-none focus-visible:ring-offset-2 focus-visible:ring-offset-orange-300 sm:text-sm">

                                    <span
                                        class="absolute inset-y-0 left-0 flex items-center pl-3 text-black dark:text-zinc-300">
                                        <Icon icon="fluent-mdl2:city-next" class="h-5 w-5" aria-hidden="true" />
                                    </span>
                                    <span class="block truncate dark:text-gray-200">{{ selectedLocation.name }}</span>
                                    <span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                                        <ChevronUpDownIcon class="h-5 w-5 text-gray-400 dark:text-gray-300"
                                            aria-hidden="true" />
                                    </span>
                                </ListboxButton>

                                <transition leave-active-class="transition duration-100 ease-in"
                                    leave-from-class="opacity-100" leave-to-class="opacity-0">
                                    <ListboxOptions
                                        class="absolute z-50 mt-1 max-h-60 w-full overflow-auto bg-white dark:bg-zinc-900 py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm">
                                        <ListboxOption v-slot="{ active, selected }" v-for="location in locations"
                                            :key="location.name" :value="location" as="template">
                                            <li :class="[
                                                active ? 'bg-green-100 text-green-900 dark:bg-green-200 dark:text-green-800' : 'text-gray-900 dark:text-gray-100',
                                                'relative cursor-default select-none py-2 pl-10 pr-4',
                                            ]">
                                                <span :class="[
                                                    selected ? 'font-medium' : 'font-normal',
                                                    'block truncate',
                                                ]">{{ location.name }}</span>
                                                <span v-if="selected"
                                                    class="absolute inset-y-0 left-0 flex items-center pl-3 text-green-600 dark:text-green-500">
                                                    <Icon icon="tabler:map-check" class="h-5 w-5" aria-hidden="true" />
                                                </span>
                                            </li>
                                        </ListboxOption>
                                    </ListboxOptions>
                                </transition>
                            </div>
                        </Listbox>
                    </div>
                    <div>
                        <button type="button" class="flex items-center dark:text-zinc-100 bg-zinc-100 dark:bg-zinc-700 mt-5 pl-2" @click="setNearby()">
                            Show nearby
                            <div class="w-8 h-8 flex items-center justify-center p-2 ml-1"
                                :class="isNearbyActive ? 'bg-red-600 text-red-100' : 'bg-zinc-400 text-zinc-100 dark:bg-zinc-900'">
                                <Icon icon="fluent-mdl2:search-nearby" class="w-5 h-5" />
                            </div>
                        </button>
                    </div>

                </div>
            </div>

            <div v-if="error" class="mt-6 bg-red-50 dark:bg-red-950 border border-red-200 dark:border-red-800 p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400 dark:text-red-300" xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd"
                                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800 dark:text-red-300">Network error occurred</h3>
                        <div class="mt-2 text-sm text-red-700 dark:text-red-400">
                            <p>{{ errorMessage }}</p>
                        </div>
                        <div class="mt-4">
                            <button @click="refreshData"
                                class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                Retry
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div v-if="loading" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-5">
                <div v-for="i in 6" :key="i" class="bg-white dark:bg-zinc-800 shadow-md overflow-hidden animate-pulse">
                    <div class="bg-zinc-200 dark:bg-zinc-700 h-48 w-full"></div>
                    <div class="p-4">
                        <div class="h-6 bg-zinc-200 dark:bg-zinc-600 w-3/4 mb-4"></div>
                        <div class="h-4 bg-zinc-200 dark:bg-zinc-600 w-full mb-2"></div>
                        <div class="h-4 bg-zinc-200 dark:bg-zinc-600 w-5/6 mb-4"></div>
                        <div class="flex justify-between items-center mt-4">
                            <div class="h-8 bg-zinc-200 dark:bg-zinc-600 w-24"></div>
                            <div class="h-8 bg-zinc-200 dark:bg-zinc-600 w-24"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div v-else-if="!error" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-5">
                <VenuesCard v-for="venue in venues" :key="venue.id" :venue="venue" />
            </div>

            <div v-if="venues.length == 0 && !loading"
                class="flex flex-col items-center justify-center p-10 mt-5">
                <img src="@/assets/illustrations/lighthouse.svg" alt="lighthouse-illustration" class="w-auto h-72 mb-4" />
                <h3 class="text-lg font-medium text-gray-900 mb-2 dark:text-zinc-50">No venues found</h3>
                <p class="text-zinc-700 text-center mb-4 dark:text-zinc-100">There are currently no venues available that match your
                    criteria</p>
                <CorePrimaryButton text="Refresh" @click="resetFilters" />
            </div>

            <div v-if="!loading && !error && pagination && venues.length > 0" class="mt-8 flex flex-col items-center">
                <span class="text-base text-gray-700 dark:text-gray-300">
                    Showing
                    <span class="font-semibold text-gray-900 dark:text-white">{{ pagination.from || 0 }}</span>
                    to
                    <span class="font-semibold text-gray-900 dark:text-white">{{ pagination.to || 0 }}</span>
                    of
                    <span class="font-semibold text-gray-900 dark:text-white">{{ pagination.total }}</span>
                    Entries
                </span>

                <div class="inline-flex mt-2 xs:mt-0 gap-1">
                    <button @click="goToPage(pagination.current_page - 1)" :disabled="!pagination.prev_page_url" :class="[
                        'flex items-center justify-center px-4 h-10 text-base font-medium dark:text-white',
                        pagination.prev_page_url
                            ? 'bg-red-600 hover:bg-red-700 cursor-pointer'
                            : 'bg-zinc-300 dark:bg-zinc-700 cursor-not-allowed'
                    ]">
                        Prev
                    </button>

                    <div class="flex gap-1">
                        <button v-for="page in getPageNumbers()" :key="page" @click="goToPage(page)" :class="[
                            'flex items-center justify-center px-4 h-10 text-base font-medium',
                            page === pagination.current_page
                                ? 'bg-red-600 text-white'
                                : 'bg-zinc-100 text-gray-700 hover:bg-gray-200 dark:bg-zinc-800 dark:text-gray-200 dark:hover:bg-zinc-700'
                        ]">
                            {{ page }}
                        </button>
                    </div>

                    <button @click="goToPage(pagination.current_page + 1)" :disabled="!pagination.next_page_url" :class="[
                        'flex items-center justify-center px-4 h-10 text-base font-medium dark:text-white',
                        pagination.next_page_url
                            ? 'bg-red-600 hover:bg-red-700 cursor-pointer'
                            : 'bg-gray-300 dark:bg-zinc-700 cursor-not-allowed'
                    ]">
                        Next
                    </button>
                </div>
            </div>
        </div>

    </div>
</template>

<script setup lang="ts">
import { Listbox, ListboxButton, ListboxLabel, ListboxOption, ListboxOptions } from '@headlessui/vue';
import { CheckIcon, ChevronUpDownIcon } from '@heroicons/vue/24/outline';
import type { VenueForm, VenuePrice } from '@/types';

definePageMeta({
    layout: 'default'
});

useHead({
    title: 'Event Venues in Malawi - Wedding Halls, Conference Centers & Party Venues | EventaHub',
    meta: [
        {
            name: 'description',
            content: 'Discover and book the perfect event venues in Malawi. Find wedding halls, conference centers, party venues, meeting rooms, and outdoor spaces for your next event.'
        },
        {
            name: 'keywords',
            content: 'event venues Malawi, wedding venues, conference centers, party halls, meeting rooms, outdoor venues, event spaces Lilongwe, venues Blantyre'
        },
        {
            property: 'og:title',
            content: 'Event Venues in Malawi - Wedding Halls, Conference Centers & Party Venues | EventaHub'
        },
        {
            property: 'og:description',
            content: 'Discover and book the perfect event venues in Malawi. Find wedding halls, conference centers, party venues, and more.'
        },
        {
            property: 'og:type',
            content: 'website'
        },
        {
            name: 'twitter:card',
            content: 'summary_large_image'
        },
        {
            name: 'twitter:title',
            content: 'Event Venues in Malawi | EventaHub'
        },
        {
            name: 'twitter:description',
            content: 'Discover and book the perfect event venues in Malawi. Find wedding halls, conference centers, and party venues.'
        },
        {
            name: 'robots',
            content: 'index, follow'
        }
    ]
});

interface Category {
    name: string;
    value: string;
}

interface Location {
    name: string;
    value: string;
}

interface Venue {
    id: number;
    title: string;
    slug: string;
    description: string;
    images: string[];
    status: string;
    rating: string;
    street: string;
    address: string;
    tags: { name: string, icon: string; }[];
    prices: VenuePrice[];
    replies: number;
    timeAgo: string;
}

interface PaginationMeta {
    current_page: number;
    from: number | null;
    to: number | null;
    total: number;
    last_page: number;
    prev_page_url: string | null;
    next_page_url: string | null;
}

interface ApiResponse {
    current_page: number;
    data: VenueForm[];
    first_page_url: string;
    from: number | null;
    last_page: number;
    last_page_url: string;
    links: Array<{ url: string | null; label: string; active: boolean }>;
    next_page_url: string | null;
    path: string;
    per_page: number;
    prev_page_url: string | null;
    to: number | null;
    total: number;
}

const { $categories, $toast }: any = useNuxtApp();
const loading = ref<boolean>(true);
const error = ref<boolean>(false);
const isNearbyActive = ref<boolean>(false);
const radius = ref<number>(0);
const currentSlide = ref<number>(0);
const isAnimating = ref<boolean>(false);
let autoplayInterval: string | number | NodeJS.Timeout | null | undefined = null;
const errorMessage = ref<string>('');
const httpClient = useHttpClient();
const center = ref<{ lat: number; lng: number }>({
  lat: -13.963280001090016,
  lng: 33.792221716425516,
});
const selectedCategory = ref<Category>({
    name: "All Categories",
    value: "all"
});

const locations = ref<Location[]>([]);
const selectedLocation = ref<Location>({
    name: "All Locations",
    value: "all"
});

const venues = ref<Venue[]>([]);
const pagination = ref<PaginationMeta | null>(null);
const currentPage = ref<number>(1);
const runtimeConfig = useRuntimeConfig();

const slides = [
    {
        heading: "FIND YOUR PERFECT",
        highlight: "EVENT SPACE",
        subheading: "Discover, compare, and book the best event spaces and top-rated vendors for your special occasion"
    },
    {
        heading: "PLAN UNFORGETTABLE",
        highlight: "CELEBRATIONS",
        subheading: "From intimate gatherings to grand ceremonies, explore venues and trusted vendors to suit your celebration perfectly"
    },
    {
        heading: "ENJOY EASY",
        highlight: "EVENT PLANNING",
        subheading: "Our platform simplifies the planning process with trusted options, helpful tools, and a smooth booking experience"
    },
    {
        heading: "EXPLORE EXCLUSIVE",
        highlight: "VENUE OPTIONS",
        subheading: "Discover hidden gems and premium locations that elevate your event with elegance, style, and unforgettable moments"
    }
];

const nextSlide = (): void => {
    isAnimating.value = true;
    setTimeout(() => {
        currentSlide.value = currentSlide.value === slides.length - 1 ? 0 : currentSlide.value + 1;
        isAnimating.value = false;
    }, 600);
};

const prevSlide = (): void => {
    isAnimating.value = true;
    setTimeout(() => {
        currentSlide.value = currentSlide.value === 0 ? slides.length - 1 : currentSlide.value - 1;
        isAnimating.value = false;
    }, 600);
};

const goToSlide = (index: number): void => {
    isAnimating.value = true;
    setTimeout(() => {
        currentSlide.value = index;
        isAnimating.value = false;
    }, 600);
};

const startAutoplay = () => {
    autoplayInterval = setInterval(() => {
        nextSlide();
    }, 5000);
};

const getPageNumbers = (): number[] => {
    if (!pagination.value) return [];

    const currentPage = pagination.value.current_page;
    const lastPage = pagination.value.last_page;

    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(lastPage, startPage + 4);

    if (endPage - startPage < 4) {
        startPage = Math.max(1, endPage - 4);
    }

    const pages: number[] = [];
    for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
    }

    return pages;
};

const goToPage = (page: number): void => {
    if (!pagination.value || page < 1 || page > pagination.value.last_page) return;

    currentPage.value = page;
    fetchVenues();
};

const setNearby = (): void => {
    isNearbyActive.value = !isNearbyActive.value;
    radius.value = isNearbyActive.value ? 50 : 0;
    isNearbyActive.value ? $toast.info('Searching for events near you...')
        : $toast.info('Nearby search disabled');
};

const refreshData = (): void => {
    error.value = false;
    errorMessage.value = '';
    loading.value = true;
    fetchVenues();
};

const fetchVenues = async (): Promise<void> => {
    loading.value = true;
    error.value = false;
    errorMessage.value = '';
    try {
        const categoryParam = selectedCategory.value.value !== 'all' ? `&category_id=${selectedCategory.value?.value}` : '';
        const locationParam = selectedLocation.value.value !== 'all' ? `&city=${selectedLocation.value.value}` : '';
        const radiusParam = isNearbyActive.value ? `&radius=${radius.value}&latitude=${center.value.lat}&longitude=${center.value.lng}` : '';

        const response = await httpClient.get<ApiResponse>(`${ENDPOINTS.VENUES.GET_ALL}?page=${currentPage.value}${categoryParam}${locationParam}${radiusParam}`);

        if (response) {
            venues.value = response.data.map((venue: any): Venue => ({
                id: Number(venue.id),
                title: venue.name,
                slug: venue.slug,
                description: venue.description,
                rating: venue.ratings_avg_rating,
                images: venue.images.map((image: { image_path: string; }) => ({
                    image_path: `${runtimeConfig.public.baseUrl}storage/${image.image_path}`
                })).map((image: { image_path: string; }) => image.image_path) || [],
                status: Number(venue?.capacity) > 200 ? "Large" : "Standard",
                street: venue.address,
                address: `${venue.city}, ${venue.state}`,
                tags: venue.activities.map((activity: { category: { name: string; icon: string } }) => ({
                    name: activity.category.name,
                    icon: `${runtimeConfig.public.baseUrl}storage/categories/${activity.category.icon}`
                })) || [],
                prices: venue.prices,
                replies: Math.floor(Math.random() * 30),
                timeAgo: "Available now"
            }));

            pagination.value = {
                current_page: response.current_page,
                from: response.from,
                to: response.to,
                total: response.total,
                last_page: response.last_page,
                prev_page_url: response.prev_page_url,
                next_page_url: response.next_page_url
            };
        }
    } catch (error: any) {
        console.error(error);
        error.value = true;
        errorMessage.value = "Failed to load venues. Please check your connection and try again.";
    } finally {
        loading.value = false;
    }
};

const fetchLocations = async (): Promise<void> => {
    try {
        const response = await httpClient.get<any>(ENDPOINTS.VENUES.LOCATIONS);
        if (response) {
            locations.value = response.map((location: { city: any; }) => ({
                value: location.city,
                name: location.city
            }));
            locations.value.unshift({
                value: 'all',
                name: 'All Locations'
            });
        }
    } catch (error) {
        console.error(error);
    } finally {

    }
}

const getCurrentLocation = async () => {
    try {
      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject)
      });
      const { latitude, longitude } = position.coords;
      center.value = {
        lat: latitude,
        lng: longitude,
      };
    } catch (error) {
      console.error("Error getting current location:", error);
    } finally {
      console.info("location fetched")
    }
};

const resetFilters = (): void => {
    selectedCategory.value = { value: 'all', name: 'All Categories' };
    selectedLocation.value = { value: 'all', name: 'All Locations' };
    radius.value = 0;
    isNearbyActive.value = false;
    fetchVenues();
}

watch([selectedCategory, selectedLocation, radius], () => {
    currentPage.value = 1;
    fetchVenues();
});

onMounted(() => {
    fetchVenues();
    fetchLocations();
    getCurrentLocation();
});

onMounted(() => {
    startAutoplay();
});

onBeforeUnmount(() => {
    if (autoplayInterval) {
        clearInterval(autoplayInterval);
    }
});
</script>

<style scoped>
.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: .5;
    }
}
</style>
