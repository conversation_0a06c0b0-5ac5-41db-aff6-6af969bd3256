<template>
  <div>
    <div class="mx-auto px-4 py-5 sm:px-6 lg:px-8">
      <NuxtLink to="/venues" class="inline-flex items-center text-sky-600 hover:text-sky-800 mb-6">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24"
          stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
        </svg>
        Back to Venues
      </NuxtLink>

      <div v-if="isLoading" class="py-12">
        <div class="space-y-6">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-gray-200 dark:bg-zinc-700 animate-pulse"></div>
            <div class="h-8 bg-gray-200 dark:bg-zinc-700 w-3/4 animate-pulse"></div>
          </div>

          <div class="flex flex-wrap gap-2">
            <div v-for="i in 4" :key="i" class="h-6 w-16 bg-gray-200 dark:bg-zinc-700 rounded-full animate-pulse"></div>
          </div>

          <div>
            <div class="h-6 bg-gray-200 dark:bg-zinc-700 w-1/4 mb-3 animate-pulse"></div>
            <div class="h-20 bg-gray-200 dark:bg-zinc-700 w-full animate-pulse"></div>
          </div>

          <div class="grid grid-cols-2 gap-2 md:grid-cols-4">
            <div v-for="i in 4" :key="i" class="h-48 bg-gray-200 dark:bg-zinc-700 animate-pulse"></div>
          </div>
        </div>
      </div>

      <div v-else-if="venue !== null" class="bg-white dark:bg-zinc-800 overflow-hidden">
        <div class="p-3">
          <div class="flex flex-col md:flex-row justify-between md:items-start gap-2">
            <div class="w-full flex items-center space-x-3">
              <Icon icon="fa6-solid:map-location-dot" class="w-8 h-8 dark:text-white" />
              <h1 class="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white">{{ venue.name }}</h1>
            </div>
          </div>

          <div class="flex flex-wrap gap-2 my-4">
            <span v-for="tag in venue.activities.category" :key="tag.name"
              class="bg-gray-100 dark:bg-zinc-700 text-gray-800 dark:text-zinc-50 text-sm px-3 py-1 rounded-full">
              {{ tag.name }}
            </span>
          </div>

          <div class="my-6">
            <h2 class="text-xl font-semibold text-gray-800 dark:text-zinc-50 mb-3">Description</h2>
            <p class="text-gray-700 dark:text-zinc-100">{{ venue.description }}</p>
          </div>

          <div class="relative">
            <div class="overflow-hidden relative">
              <div class="flex transition-transform duration-300 ease-in-out"
                :style="{ transform: `translateX(-${currentPage * 100}%)` }">
                <div v-for="pageIndex in Math.ceil(venue?.images.length / 4)" :key="`page-${pageIndex}`"
                  class="w-full flex-shrink-0 grid grid-cols-1 sm:grid-cols-2 gap-2 md:grid-cols-4">
                  <div v-for="i in 4" :key="`img-${(pageIndex - 1) * 4 + i - 1}`" class="h-48">
                    <img v-if="venue?.images[(pageIndex - 1) * 4 + i - 1]"
                      :src="`${runtimeConfig.public.baseUrl}storage/${venue?.images[(pageIndex - 1) * 4 + i - 1].image_path}`"
                      :alt="venue.slug" class="w-full h-full object-cover">
                  </div>
                </div>
              </div>

              <button v-if="totalPages > 1" @click="prevPage"
                class="absolute left-2 top-1/2 -translate-y-1/2 bg-white/70 p-2 rounded-full shadow hover:bg-white">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                  stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
              </button>

              <button v-if="totalPages > 1" @click="nextPage"
                class="absolute right-2 top-1/2 -translate-y-1/2 bg-white/70 p-2 rounded-full shadow hover:bg-white">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                  stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </button>

              <div v-if="totalPages > 1" class="absolute bottom-2 left-1/2 -translate-x-1/2 flex space-x-2">
                <button v-for="index in totalPages" :key="`page-dot-${index}`" @click="goToPage(index - 1)" :class="[
                  'w-2 h-2 rounded-full transition-colors',
                  currentPage === index - 1 ? 'bg-white' : 'bg-white/50'
                ]">
                </button>
              </div>
            </div>
          </div>

          <div class="my-6">
            <h2 class="text-xl font-semibold text-gray-800 dark:text-zinc-50 mb-3">Availability</h2>
            <VenuesCalendar v-model="selectedDate" :availability-data="availabilityData" />
          </div>

          <FormKit id="createBookingForm" @submit="onCreateBooking" type="form" submit-label="Update" :actions="false"
            #default="{ }">
            <div v-if="selectedDate" class="my-6 bg-red-50 p-4">
              <div class="w-full flex items-center justify-between">
                <h3 class="font-semibold text-red-500 mb-2 text-xl">Book for {{ formatSelectedDate }}</h3>
                <button>
                  <Icon icon="fa6-solid:xmark" class="w-6 h-6" @click="selectedDate = null" />
                </button>
              </div>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div class="flex flex-col space-y-2">
                  <label class="text-base font-medium">Start & end date time</label>
                  <datepicker @cleared="isCleared" required position="left" placeholder="select start & end date"
                    :range="true" input-class-name="datepicker bg-red-500" format="dd/MM/yyyy HH:mm"
                    v-model="booking.dateTimeRange" />
                </div>
                <div>
                  <FormKit type="number" label="Number of guests"
                    help="Estimate the number of events your event anticipates" v-model="booking.guests" />
                </div>

              </div>

              <div>
                <div class="flex flex-col space-y-2">
                  <label for="venue_price_id" class="text-lg font-semibold">Select Price Package:</label>
                  <select id="venue_price_id" name="venue_price_id" v-model="booking.price_id" class="px-2 py-2 border"
                    required>
                    <option value="" selected disabled>Select a price package</option>
                    <option v-for="price in venue.prices" :key="price.id" :value="price.id">
                      {{ new Intl.NumberFormat('en-US', {
                        style: 'currency', currency: price.currency.name
                      }).format(Number(price.price)) }}
                    </option>
                  </select>
                </div>

                <div v-if="booking.price_id" class="mt-3 p-3 border">
                  <div v-if="booking.price_id">
                    <p><strong>Price:</strong>
                      {{ new Intl.NumberFormat('en-US', {
                        style: 'currency', currency: selectedPrice?.currency.name
                      }).format(Number(selectedPrice?.price)) }}</p>
                    <p><strong>Attributes:</strong> {{ JSON.parse(String(selectedPrice?.ammenities)).join(', ') }}</p>
                  </div>
                </div>
              </div>

              <div class="w-full flex flex-col space-y-2 mt-2">
                <label class="text-lg font-semibold">What best describes your event?</label>
                <div class="flex flex-wrap space-x-2">
                  <button type="button" @click="booking.category = category.id"
                    class="flex items-center bg-gray-100 dark:bg-zinc-700 font-light hover:bg-red-600 hover:text-white transition duration-150 rounded-full px-1.5 py-1.5 mb-1.5"
                    v-for="category in $categories"
                    :class="booking.category === category.id ? 'bg-red-600 text-white' : ''">
                    <img :src="`${runtimeConfig.public.baseUrl}storage/categories/${category.icon}`"
                      class="w-6 h-6 mr-2 dark:text-zinc-200" />
                    {{ category.name }}
                  </button>
                </div>
              </div>

              <div class="w-full mt-1.5">
                <FormKit type="textarea" label="Message" v-model="booking.message" validation="required" />
              </div>

              <CoreSubmitButton :loading="isBooking" class="my-2" />
            </div>
          </FormKit>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 my-6">
            <div>
              <h2 class="text-xl font-semibold text-gray-800 dark:text-zinc-50 mb-3">Details</h2>
              <ul class="space-y-2">
                <li class="flex items-center">
                  <Icon icon="icon-park-twotone:building-one" class="w-6 h-6 mr-2 dark:text-zinc-200" />
                  <p class="text-lg text-black font-medium dark:text-zinc-200">Place: <span>{{ venue.name }}</span></p>
                </li>
                <li class="flex items-center">
                  <Icon icon="lets-icons:date-today-duotone" class="w-6 h-6 mr-2 dark:text-zinc-200" />
                  <p class="text-lg text-black font-medium dark:text-zinc-200">Added on: <span>{{
                    dayjs(venue.created_at).format(DATE_FORMAT.FULL) }}</span></p>
                </li>
                <li class="flex items-center">
                  <Icon icon="pepicons-print:people" class="w-6 h-6 mr-2 dark:text-zinc-200" />
                  <p class="text-lg text-black font-medium dark:text-zinc-200">Capacity: <span>{{ venue.capacity
                      }}</span></p>
                </li>
              </ul>
              <div class="bg-zinc-100 dark:bg-zinc-900 p-2 mt-4">
                <h3 class="text-xl font-bold text-gray-800 dark:text-zinc-50 mb-4">Pricing</h3>
                <div class="space-y-6">
                  <div v-for="price in venue.prices" class="border-b border-dotted border-gray-200 dark:border-zinc-700 pb-4">
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-1">{{ new Intl.NumberFormat('en-US',
                      {
                        style: 'currency', currency: price.currency.name
                      }).format(Number(price.price)) }}</h3>
                    <div>
                      <ul class="space-y-2">
                        <li v-for="item in JSON.parse(String(price.ammenities))" class="flex items-center">
                          <Icon icon="solar:bag-check-bold-duotone" class="h-5 w-5 text-green-500 mr-2" />
                          <span class="text-gray-700 dark:text-zinc-100">{{ item }}</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

            </div>

            <div>
              <h2 class="text-xl font-semibold text-gray-800 dark:text-zinc-50">Contact</h2>
              <div class="w-full flex items-center space-x-3 py-3">
                <div class="p-2 bg-zinc-100 dark:bg-zinc-900">
                  <Icon icon="zondicons:phone" class="w-5 h-5 dark:text-zinc-300" />
                </div>
                <div>
                  <p class="text-sm text-gray-500 dark:text-zinc-100">Phone</p>
                  <a :href="`tel:${venue.phone}`"
                    class="text-base text-black font-medium dark:text-zinc-200 hover:underline hover:text-sky-500 transition-all duration-150">
                    {{ venue.phone }}
                  </a>
                </div>
              </div>

              <div class="w-full flex items-center space-x-3 py-3">
                <div class="p-2 bg-zinc-100 dark:bg-zinc-900">
                  <Icon icon="weui:email-filled" class="w-5 h-5 dark:text-zinc-300" />
                </div>
                <div>
                  <p class="text-sm text-gray-500 dark:text-zinc-100">Email</p>
                  <a :href="`mailto:${venue.email}`"
                    class="text-base text-black font-medium dark:text-zinc-200 hover:underline hover:text-sky-500 transition-all duration-150">
                    {{ venue.email }}
                  </a>
                </div>
              </div>

              <div class="h-64 sm:h-72 mt-3">
                <GoogleMap :api-key="runtimeConfig.public.googleMapsApiKey" mapId="nearby-events-map"
                  class="w-full h-full" :zoom="15" :center="center" mapTypeId="terrain">
                  <CustomMarker :options="{
                    position: center,
                    anchorPoint: 'BOTTOM_CENTER',
                  }">
                    <div style="text-align: center">
                      <div class="relative shadow-2xl shadow-black animate-bounce">
                        <button @click="toDirections"
                          class="relative inline-flex items-center justify-center p-2 rounded-none cursor-pointer bg-red-600 text-white">
                          <Icon icon="fa6-solid:map-location-dot" class="w-6 h-6" />
                        </button>
                        <span
                          class="absolute left-1/2 -bottom-1 transform -translate-x-1/2 w-0 h-0 border-l-[6px] border-l-transparent border-r-[6px] border-r-transparent border-t-[6px] border-t-red-600"></span>
                      </div>
                    </div>
                  </CustomMarker>
                </GoogleMap>
              </div>
              <button class="w-full bg-sky-600 hover:bg-sky-700 text-white font-medium py-2 px-4 mb-2 mt-3">
                Message Host
              </button>
              <button class="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4">
                Request Information
              </button>
            </div>
          </div>

          <div class="border-t border-gray-200 dark:border-zinc-700 pt-6 mt-6">
            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 sm:gap-2">
              <h2 class="text-xl font-semibold text-gray-800 dark:text-zinc-50">Reviews ({{ venue.ratings.length ?? 0
                }})</h2>
              <div class="flex flex-col sm:items-center space-x-3">
                <div class="flex">
                  <template v-for="i in 5" :key="i">
                    <svg xmlns="http://www.w3.org/2000/svg" :class="[
                      'h-5 w-5',
                      i <= Math.round(Number(venue.ratings_avg_rating ?? 0)) ? 'text-orange-400' : 'text-gray-300 dark:text-zinc-200'
                    ]" viewBox="0 0 20 20" fill="currentColor">
                      <path
                        d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  </template>
                </div>
                <h3 class="text-2xl font-bold text-orange-400">{{ Number(venue.ratings_avg_rating == null ? 0 :
                  venue.ratings_avg_rating).toFixed(1) }}</h3>
              </div>

            </div>

            <div class="bg-gray-50 dark:bg-zinc-900 p-4 mb-6">
              <div class="mb-3">
                <label class="block text-base font-medium text-gray-700 dark:text-zinc-100 mb-1">Your Rating</label>
                <div class="flex">
                  <template v-for="i in 5" :key="i">
                    <svg @click="newReviewRating = i" xmlns="http://www.w3.org/2000/svg" :class="[
                      'h-6 w-6 cursor-pointer',
                      i <= newReviewRating ? 'text-orange-400' : 'text-gray-300 dark:text-zinc-200'
                    ]" viewBox="0 0 20 20" fill="currentColor">
                      <path
                        d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  </template>
                </div>
              </div>
              <FormKit type="textarea" rows="3"
                class="w-full px-3 py-2 text-gray-700 dark:text-zinc-100 border focus:outline-none focus:ring-2 focus:ring-sky-500"
                placeholder="Write your review..." v-model="newReviewText"></FormKit>
              <div class="mt-3 flex justify-end">
                <CorePrimaryButton text="Post Review" @click="submitReview" :loading="isRating" />
              </div>
            </div>

            <div v-for="(ratings, index) in venue.ratings" :key="index"
              class="mb-6 pb-6 border-b border-gray-200 last:border-0">
              <div class="flex items-start">
                <div class="flex-shrink-0 mr-3">
                  <div class="w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center text-gray-600">
                    {{ ratings.user?.name?.substring(0, 1) }}
                  </div>
                </div>
                <div class="flex-grow">
                  <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-1 gap-1">
                    <h3 class="text-md font-medium text-gray-800 dark:text-zinc-50">{{ ratings?.user?.name }}</h3>
                    <span class="text-sm text-gray-500 dark:text-zinc-100">{{ dayjs(ratings.created_at).fromNow()
                      }}</span>
                  </div>
                  <div class="flex items-center mb-2">
                    <div class="flex">
                      <template v-for="i in 5" :key="i">
                        <svg xmlns="http://www.w3.org/2000/svg" :class="[
                          'h-4 w-4',
                          i <= ratings.rating ? 'text-orange-400' : 'text-gray-300'
                        ]" viewBox="0 0 20 20" fill="currentColor">
                          <path
                            d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                      </template>
                    </div>
                  </div>
                  <p class="text-gray-700 dark:text-zinc-100">{{ ratings.comment }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="flex flex-col items-center justify-center py-12 bg-white p-6 rounded-lg shadow-sm">
        <Icon icon="fa6-solid:circle-exclamation" class="w-12 h-12 text-red-500 mb-4" />
        <h2 class="text-xl font-semibold text-gray-800 dark:text-zinc-50 mb-2">Venue Not Found</h2>
        <p class="text-gray-600 mb-4">The venue you're looking for doesn't exist or has been removed.</p>
        <NuxtLink to="/venues" class="bg-sky-600 hover:bg-sky-700 text-white font-medium py-2 px-4">
          Browse Venues
        </NuxtLink>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { GeoCoordinate, Venue } from '@/types';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime'

interface CalendarDay {
  day: number;
  date: Date;
  available: boolean;
  isToday: boolean;
}

interface Booking {
  dateTimeRange: string[],
  category: number,
  guests: string,
  message: string,
  price_id: number;
}

dayjs.extend(relativeTime)

const route = useRoute();
const httpClient = useHttpClient();
const runtimeConfig = useRuntimeConfig();
const isLoading = ref<boolean>(true);
const isRating = ref<boolean>(false);
const hasError = ref<boolean>(false);
const isBooking = ref<boolean>(false);
const { $toast, $categories }: any = useNuxtApp();
const venue = ref<Venue | null>(null);
const selectedDate = ref<CalendarDay | null>(null);
const newReviewRating = ref<number>(0);
const newReviewText = ref<string>('');
const slug = computed<string>(() => route.params.slug as string);

const booking = ref<Booking>({
  dateTimeRange: [],
  category: 0,
  guests: "",
  message: "",
  price_id: 0
});

const center = ref<GeoCoordinate>({
  lat: -13.991627856237,
  lng: 33.768879386812806,
});

const availabilityData = computed(() => {
  if (!venue.value) return {};

  const unavailableDates: { [key: string]: boolean } = {};

  venue.value.bookings.forEach(booking => {
    const startDate = new Date(booking.booking_from);
    const endDate = new Date(booking.booking_to);

    let currentDate = startDate;
    while (currentDate <= endDate) {
      const dateKey = `${currentDate.getFullYear()}-${currentDate.getMonth() + 1}-${currentDate.getDate()}`;
      unavailableDates[dateKey] = false;
      currentDate.setDate(currentDate.getDate() + 1);
    }
  });

  return unavailableDates;
});

const currentPage = ref(0);

const totalPages = computed(() => Math.ceil((venue.value?.images?.length ?? 0) / 4));

function nextPage() {
  currentPage.value = (currentPage.value + 1) % totalPages.value;
}

function prevPage() {
  currentPage.value = currentPage.value === 0
    ? totalPages.value - 1
    : currentPage.value - 1;
}

function goToPage(index: number) {
  currentPage.value = index;
}

const selectedPrice = computed(() => {
  if (!venue.value) return null;
  const selected = venue.value.prices.find(price => price.id === booking.value.price_id);
  return selected || null;
});

watch(() => venue.value, (newVenue) => {
  if (newVenue) {
    useHead({
      title: `${newVenue.name} | Venues`,
      meta: [
        { name: 'description', content: '' }
      ]
    });
  }
}, { immediate: true });

const formatSelectedDate = computed<string>(() => {
  if (!selectedDate.value) return '';
  const date = new Date(selectedDate.value.date);
  return date.toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' });
});

const isCleared = (): void => {
  selectedDate.value = null;
};

const toDirections = (): void => {
  navigateTo(`/events/directions?location=${venue?.value?.address}&lat=${venue.value?.latitude}&lng=${venue.value?.longitude}`)
}

const submitReview = async (): Promise<void> => {
  if (!venue.value || newReviewText.value.trim() === '' || newReviewRating.value === 0) return;
  isRating.value = true;
  try {
    const response = await httpClient.post(`${ENDPOINTS.VENUES.RATE}`, {
      venue_id: venue.value.id,
      rating: newReviewRating.value,
      review: newReviewText.value
    });

    if (response) {
      $toast.success('Review submitted successfully');
      fetchVenue();
    }
  } catch (error) {
    console.error("Error submitting review:", error);
    $toast.error('Failed to submit review');
  } finally {
    isRating.value = false;
    newReviewText.value = '';
    newReviewRating.value = 0;
  }
};

const fetchVenue = async (): Promise<void> => {
  if (!slug.value) return;

  isLoading.value = true;
  hasError.value = false;

  try {
    const response: any = await httpClient.get<Venue>(`${ENDPOINTS.VENUES.READ}/${slug.value}`);
    venue.value = response.venue;
    center.value = {
      lat: Number(venue.value?.latitude) || -13.991627856237,
      lng: Number(venue.value?.longitude) || 33.768879386812806
    };
  } catch (error) {
    console.error("Error fetching venue:", error);
    hasError.value = true;
    venue.value = null;
  } finally {
    isLoading.value = false;
  }
};

const onCreateBooking = async (): Promise<void> => {
  if (!booking.value.dateTimeRange.length || !booking.value.category || !booking.value.guests) {
    $toast.error('Please select a start and end date, time, and category');
    return;
  }
  isBooking.value = true;
  try {
    const response = await httpClient.post(`${ENDPOINTS.BOOKINGS.BASE}`, {
      venue_id: venue.value?.id,
      booking_from: booking.value.dateTimeRange[0],
      booking_to: booking.value.dateTimeRange[1],
      category_id: booking.value.category,
      number_of_guests: booking.value.guests,
      message: booking.value.message,
      venue_price_id: booking.value.price_id
    });
    if (response) {
      $toast.success('Booking created successfully');
      booking.value = {
        dateTimeRange: [],
        category: 0,
        guests: "",
        message: "",
        price_id: 0
      }
      selectedDate.value = null;
      fetchVenue();
    }
  } catch (error) {
    console.error("Error creating booking:", error);
    handleErrorWithToast(error, $toast);
  } finally {
    isBooking.value = false;
  }
}

onMounted(() => {
  fetchVenue();
});
</script>

<style scoped>
.aspect-w-16 {
  position: relative;
  padding-bottom: 56.25%;
}

.aspect-w-16>* {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style>
