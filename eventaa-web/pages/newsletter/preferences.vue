<template>
  <div class="min-h-screen bg-zinc-50 dark:bg-zinc-900 py-12">
    <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Header -->
      <div class="text-center mb-8">
        <Icon
          icon="heroicons:cog-6-tooth"
          class="mx-auto h-16 w-16 text-red-500"
        />
        <h1 class="mt-4 text-3xl font-extrabold text-zinc-900 dark:text-zinc-100">
          Newsletter Preferences
        </h1>
        <p class="mt-2 text-zinc-600 dark:text-zinc-400">
          Manage your newsletter subscription and preferences
        </p>
      </div>

      <!-- Get Preferences Section -->
      <div v-if="!subscription" class="bg-white dark:bg-zinc-800  shadow-sm border border-zinc-200 dark:border-zinc-700 p-6 mb-6">
        <h2 class="text-xl font-semibold text-zinc-900 dark:text-zinc-100 mb-4">
          Find Your Subscription
        </h2>
        <p class="text-zinc-600 dark:text-zinc-400 mb-6">
          Enter your email address to view and update your newsletter preferences.
        </p>

        <FormKit
          type="form"
          :actions="false"
          @submit="handleGetPreferences"
          :disabled="loading"
        >
          <div class="space-y-4">
            <FormKit
              type="email"
              name="email"
              v-model="email"
              required
              placeholder="Enter your email address"
              label="Email Address"
              :classes="{
                input: 'w-full px-3 py-2 border border-zinc-300 dark:border-zinc-600  bg-white dark:bg-zinc-700 text-zinc-900 dark:text-zinc-100'
              }"
            />

            <button
              type="submit"
              :disabled="loading || !email"
              class="w-full sm:w-auto px-6 py-2 border border-transparent text-sm font-medium  text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span v-if="loading" class="flex items-center justify-center">
                <CoreLoader :width="16" :height="16" color="#ffffff" />
                <span class="ml-2">Loading...</span>
              </span>
              <span v-else>Find My Preferences</span>
            </button>
          </div>
        </FormKit>

        <div v-if="error" class="mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 ">
          <div class="flex items-center">
            <Icon
              icon="heroicons:exclamation-triangle"
              class="w-5 h-5 text-red-600 dark:text-red-400 mr-2"
            />
            <p class="text-red-800 dark:text-red-200 text-sm">{{ error }}</p>
          </div>
        </div>
      </div>

      <div v-if="subscription" class="space-y-6">
        <div class="bg-white dark:bg-zinc-800  shadow-sm border border-zinc-200 dark:border-zinc-700 p-6">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-semibold text-zinc-900 dark:text-zinc-100">
              Subscription Status
            </h2>
            <div class="flex items-center">
              <div
                :class="[
                  'w-3 h-3 rounded-full mr-2',
                  subscription.is_active ? 'bg-green-500' : 'bg-red-500'
                ]"
              ></div>
              <span
                :class="[
                  'text-sm font-medium',
                  subscription.is_active ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                ]"
              >
                {{ subscription.is_active ? 'Active' : 'Inactive' }}
              </span>
            </div>
          </div>

          <div class="space-y-2 text-sm text-zinc-600 dark:text-zinc-400">
            <p><strong>Email:</strong> {{ subscription.email }}</p>
            <p v-if="subscription.name"><strong>Name:</strong> {{ subscription.name }}</p>
            <p><strong>Subscribed:</strong> {{ formatDate(subscription.subscribed_at) }}</p>
            <p v-if="!subscription.is_active && subscription.unsubscribed_at">
              <strong>Unsubscribed:</strong> {{ formatDate(subscription.unsubscribed_at) }}
            </p>
          </div>

          <div v-if="!subscription.is_active" class="mt-4">
            <button
              @click="handleReactivate"
              :disabled="reactivating"
              class="px-4 py-2 border border-transparent text-sm font-medium  text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50"
            >
              <span v-if="reactivating" class="flex items-center">
                <CoreLoader :width="16" :height="16" color="#ffffff" />
                <span class="ml-2">Reactivating...</span>
              </span>
              <span v-else>Reactivate Subscription</span>
            </button>
          </div>
        </div>

        <!-- Preferences Management -->
        <div v-if="subscription.is_active" class="bg-white dark:bg-zinc-800  shadow-sm border border-zinc-200 dark:border-zinc-700 p-6">
          <h2 class="text-xl font-semibold text-zinc-900 dark:text-zinc-100 mb-4">
            Email Preferences
          </h2>
          <p class="text-zinc-600 dark:text-zinc-400 mb-6">
            Choose what type of emails you'd like to receive from EventaHub Malawi.
          </p>

          <FormKit
            type="form"
            :actions="false"
            @submit="handleUpdatePreferences"
            :disabled="updating"
          >
            <div class="space-y-4">
              <div class="space-y-3">
                <label
                  v-for="preference in availablePreferences"
                  :key="preference.key"
                  class="flex items-start"
                >
                  <input
                    type="checkbox"
                    :value="preference.key"
                    v-model="selectedPreferences"
                    class="w-4 h-4 text-red-600 bg-zinc-100 dark:bg-zinc-700 border-zinc-300 dark:border-zinc-600 rounded focus:ring-red-500 mt-1"
                  />
                  <div class="ml-3">
                    <span class="text-sm font-medium text-zinc-900 dark:text-zinc-100">
                      {{ preference.label }}
                    </span>
                    <p class="text-xs text-zinc-500 dark:text-zinc-400">
                      {{ preference.description }}
                    </p>
                  </div>
                </label>
              </div>

              <div class="flex space-x-3 pt-4">
                <button
                  type="submit"
                  :disabled="updating || selectedPreferences.length === 0"
                  class="px-6 py-2 border border-transparent text-sm font-medium  text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span v-if="updating" class="flex items-center">
                    <CoreLoader :width="16" :height="16" color="#ffffff" />
                    <span class="ml-2">Updating...</span>
                  </span>
                  <span v-else>Update Preferences</span>
                </button>

                <button
                  type="button"
                  @click="resetPreferences"
                  class="px-6 py-2 border border-zinc-300 dark:border-zinc-600 text-sm font-medium  text-zinc-700 dark:text-zinc-300 hover:bg-zinc-50 dark:hover:bg-zinc-700 focus:outline-none focus:ring-2 focus:ring-red-500"
                >
                  Reset
                </button>
              </div>

              <p v-if="selectedPreferences.length === 0" class="text-xs text-red-600 dark:text-red-400">
                You must select at least one preference to continue receiving emails.
              </p>
            </div>
          </FormKit>
        </div>

        <!-- Unsubscribe Section -->
        <div v-if="subscription.is_active" class="bg-white dark:bg-zinc-800  shadow-sm border border-zinc-200 dark:border-zinc-700 p-6">
          <h2 class="text-xl font-semibold text-zinc-900 dark:text-zinc-100 mb-4">
            Unsubscribe
          </h2>
          <p class="text-zinc-600 dark:text-zinc-400 mb-4">
            No longer want to receive emails from us? You can unsubscribe at any time.
          </p>

          <button
            @click="showUnsubscribeConfirm = true"
            class="px-4 py-2 border border-red-300 text-sm font-medium  text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 focus:outline-none focus:ring-2 focus:ring-red-500"
          >
            Unsubscribe from All Emails
          </button>
        </div>
      </div>

      <!-- Unsubscribe Confirmation Modal -->
      <CoreModal
        v-model="showUnsubscribeConfirm"
        title="Confirm Unsubscribe"
        max-width="sm"
      >
        <div class="p-6">
          <div class="flex items-center mb-4">
            <Icon
              icon="heroicons:exclamation-triangle"
              class="h-6 w-6 text-red-600 mr-2"
            />
            <h3 class="text-lg font-medium text-zinc-900 dark:text-zinc-100">
              Are you sure?
            </h3>
          </div>
          <p class="text-zinc-600 dark:text-zinc-400 mb-6">
            You will no longer receive any newsletter emails from EventaHub Malawi.
            You can always resubscribe later if you change your mind.
          </p>

          <div class="flex space-x-3">
            <button
              @click="showUnsubscribeConfirm = false"
              class="flex-1 px-4 py-2 border border-zinc-300 dark:border-zinc-600 text-sm font-medium  text-zinc-700 dark:text-zinc-300 hover:bg-zinc-50 dark:hover:bg-zinc-700 focus:outline-none focus:ring-2 focus:ring-red-500"
            >
              Cancel
            </button>
            <button
              @click="handleUnsubscribe"
              :disabled="unsubscribing"
              class="flex-1 px-4 py-2 border border-transparent text-sm font-medium  text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50"
            >
              <span v-if="unsubscribing" class="flex items-center justify-center">
                <CoreLoader :width="16" :height="16" color="#ffffff" />
                <span class="ml-2">Unsubscribing...</span>
              </span>
              <span v-else>Yes, Unsubscribe</span>
            </button>
          </div>
        </div>
      </CoreModal>

      <!-- Newsletter Subscription Section (for non-subscribers) -->
      <div v-if="subscription && !subscription.is_active" class="bg-white dark:bg-zinc-800  shadow-sm border border-zinc-200 dark:border-zinc-700 p-6">
        <h2 class="text-xl font-semibold text-zinc-900 dark:text-zinc-100 mb-4">
          Subscribe to Newsletter
        </h2>
        <p class="text-zinc-600 dark:text-zinc-400 mb-6">
          Stay updated with the latest events and news from EventaHub Malawi.
        </p>

        <LandingHomeNewsletter />
      </div>

      <!-- Footer -->
      <div class="text-center mt-8">
        <div class="space-x-4 text-sm">
          <NuxtLink
            to="/"
            class="text-red-600 dark:text-red-400 hover:text-red-500 dark:hover:text-red-300"
          >
            Return to Homepage
          </NuxtLink>
          <span class="text-zinc-400">•</span>
          <a
            href="mailto:<EMAIL>"
            class="text-red-600 dark:text-red-400 hover:text-red-500 dark:hover:text-red-300"
          >
            Contact Support
          </a>
        </div>
        <p class="mt-4 text-xs text-zinc-500 dark:text-zinc-400">
          © {{ new Date().getFullYear() }} EventaHub Malawi. All rights reserved.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ENDPOINTS } from "@/utils/api";

const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const email = ref("");
const subscription = ref<any>(null);
const loading = ref(false);
const updating = ref(false);
const reactivating = ref(false);
const unsubscribing = ref(false);
const error = ref("");
const showUnsubscribeConfirm = ref(false);
const selectedPreferences = ref<string[]>([]);

const availablePreferences = [
  {
    key: "latest_events",
    label: "Latest Events",
    description: "Get notified about new events published on our platform"
  },
  {
    key: "recommended_events",
    label: "Recommended Events",
    description: "Receive personalized event recommendations based on your interests"
  },
  {
    key: "new_venues",
    label: "New Venues",
    description: "Be the first to know about new venues added to our platform"
  },
  {
    key: "event_updates",
    label: "Event Updates",
    description: "Get updates about events you're interested in or attending"
  },
];

const handleGetPreferences = async () => {
  if (!email.value) {
    error.value = "Please enter your email address";
    return;
  }

  loading.value = true;
  error.value = "";

  try {
    const response = await httpClient.post(ENDPOINTS.NEWSLETTER.PREFERENCES, {
      email: email.value,
    }) as any;

    if (response?.data) {
      subscription.value = response.data;
      selectedPreferences.value = response.data.preferences || [];
      $toast.success("Subscription found!");
    }
  } catch (err: any) {
    console.error("Get preferences error:", err);

    if (err.response?.status === 404) {
      error.value = "No newsletter subscription found for this email address.";
    } else if (err.response?.data?.message) {
      error.value = err.response.data.message;
    } else {
      error.value = "An error occurred while fetching your preferences. Please try again.";
    }
  } finally {
    loading.value = false;
  }
};

const handleUpdatePreferences = async () => {
  if (!subscription.value || selectedPreferences.value.length === 0) {
    $toast.error("Please select at least one preference");
    return;
  }

  updating.value = true;

  try {
    const response = await httpClient.put(ENDPOINTS.NEWSLETTER.UPDATE_PREFERENCES, {
      email: subscription.value.email,
      preferences: selectedPreferences.value,
    });

    if (response) {
      subscription.value.preferences = selectedPreferences.value;
      $toast.success("Preferences updated successfully!");
    }
  } catch (err: any) {
    console.error("Update preferences error:", err);

    if (err.response?.data?.message) {
      $toast.error(err.response.data.message);
    } else {
      $toast.error("Failed to update preferences. Please try again.");
    }
  } finally {
    updating.value = false;
  }
};

const handleReactivate = async () => {
  if (!subscription.value) return;

  reactivating.value = true;

  try {
    // Reactivate using the new reactivate endpoint
    const response = await httpClient.post(ENDPOINTS.NEWSLETTER.REACTIVATE, {
      email: subscription.value.email,
      preferences: subscription.value.preferences || ["latest_events", "recommended_events"],
    });

    if (response) {
      subscription.value.is_active = true;
      subscription.value.unsubscribed_at = null;
      selectedPreferences.value = subscription.value.preferences || [];
      $toast.success("Subscription reactivated successfully!");
    }
  } catch (err: any) {
    console.error("Reactivate error:", err);

    if (err.response?.data?.message) {
      $toast.error(err.response.data.message);
    } else {
      $toast.error("Failed to reactivate subscription. Please try again.");
    }
  } finally {
    reactivating.value = false;
  }
};

const handleUnsubscribe = async () => {
  if (!subscription.value?.unsubscribe_token) {
    $toast.error("Unable to unsubscribe. Please try again later.");
    return;
  }

  unsubscribing.value = true;

  try {
    const response = await httpClient.get(`${ENDPOINTS.NEWSLETTER.UNSUBSCRIBE}/${subscription.value.unsubscribe_token}`);

    if (response) {
      subscription.value.is_active = false;
      subscription.value.unsubscribed_at = new Date().toISOString();
      showUnsubscribeConfirm.value = false;
      $toast.success("Successfully unsubscribed from newsletter");
    }
  } catch (err: any) {
    console.error("Unsubscribe error:", err);

    if (err.response?.data?.message) {
      $toast.error(err.response.data.message);
    } else {
      $toast.error("Failed to unsubscribe. Please try again.");
    }
  } finally {
    unsubscribing.value = false;
  }
};

const resetPreferences = () => {
  selectedPreferences.value = subscription.value?.preferences || [];
};

const formatDate = (dateString: string) => {
  if (!dateString) return "";
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
};

// Set page title and meta
useHead({
  title: "Newsletter Preferences - EventaHub Malawi",
  meta: [
    {
      name: "description",
      content: "Manage your EventaHub Malawi newsletter subscription and email preferences",
    },
  ],
});
</script>
