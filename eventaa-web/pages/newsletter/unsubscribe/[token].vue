<template>
  <div class="min-h-screen bg-zinc-50 dark:bg-zinc-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div class="text-center">
        <Icon
          icon="heroicons:envelope"
          class="mx-auto h-16 w-16 text-red-500"
        />
        <h2 class="mt-6 text-3xl font-extrabold text-zinc-900 dark:text-zinc-100">
          Newsletter Unsubscribe
        </h2>
      </div>

      <div v-if="loading" class="text-center py-8">
        <CoreLoader :width="32" :height="32" color="#ef4444" />
        <p class="mt-4 text-zinc-600 dark:text-zinc-400">Processing your request...</p>
      </div>

      <div v-else-if="unsubscribed" class="text-center py-8">
        <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 dark:bg-green-900/20">
          <Icon icon="heroicons:check" class="h-8 w-8 text-green-600 dark:text-green-400" />
        </div>
        <h3 class="mt-4 text-lg font-medium text-zinc-900 dark:text-zinc-100">
          Successfully Unsubscribed
        </h3>
        <p class="mt-2 text-zinc-600 dark:text-zinc-400 text-sm">
          You have been unsubscribed from our newsletter. You will no longer receive marketing emails from EventaHub Malawi.
        </p>

        <div class="mt-6 space-y-3">
          <p class="text-sm text-zinc-500 dark:text-zinc-400">
            Changed your mind?
          </p>
          <button
            @click="showResubscribeForm = true"
            v-if="!showResubscribeForm"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium  text-red-600 dark:text-red-400 hover:text-red-500 dark:hover:text-red-300 focus:outline-none focus:ring-2 focus:ring-red-500"
          >
            Subscribe Again
          </button>
        </div>

        <div v-if="showResubscribeForm" class="mt-6 p-4 bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700">
          <h4 class="text-lg font-medium text-zinc-900 dark:text-zinc-100 mb-4">
            Resubscribe to Newsletter
          </h4>
          <FormKit
            type="form"
            :actions="false"
            @submit="handleResubscribe"
            :disabled="resubscribing"
          >
            <FormKit
              type="email"
              name="email"
              v-model="resubscribeEmail"
              required
              placeholder="Enter your email address"
              label="Email Address"
              :classes="{
                input: 'w-full px-3 py-2 border border-zinc-300 dark:border-zinc-600  bg-white dark:bg-zinc-700 text-zinc-900 dark:text-zinc-100'
              }"
            />

            <div class="mt-4">
              <h5 class="text-sm font-medium text-zinc-900 dark:text-zinc-100 mb-2">
                What would you like to receive?
              </h5>
              <div class="space-y-2">
                <label
                  v-for="preference in availablePreferences"
                  :key="preference.key"
                  class="flex items-center"
                >
                  <input
                    type="checkbox"
                    :value="preference.key"
                    v-model="selectedPreferences"
                    class="w-4 h-4 text-red-600 bg-zinc-100 dark:bg-zinc-700 border-zinc-300 dark:border-zinc-600 rounded focus:ring-red-500"
                  />
                  <span class="ml-2 text-sm text-zinc-700 dark:text-zinc-300">
                    {{ preference.label }}
                  </span>
                </label>
              </div>
            </div>

            <div class="mt-6 flex space-x-3">
              <button
                type="button"
                @click="showResubscribeForm = false"
                class="flex-1 py-2 px-4 border border-zinc-300 dark:border-zinc-600  text-sm font-medium text-zinc-700 dark:text-zinc-300 hover:bg-zinc-50 dark:hover:bg-zinc-700 focus:outline-none focus:ring-2 focus:ring-red-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                :disabled="resubscribing || !resubscribeEmail || selectedPreferences.length === 0"
                class="flex-1 py-2 px-4 border border-transparent  text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span v-if="resubscribing" class="flex items-center justify-center">
                  <CoreLoader :width="16" :height="16" color="#ffffff" />
                  <span class="ml-2">Subscribing...</span>
                </span>
                <span v-else>Subscribe</span>
              </button>
            </div>
          </FormKit>
        </div>
      </div>

      <div v-else-if="error" class="text-center py-8">
        <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 dark:bg-red-900/20">
          <Icon icon="heroicons:exclamation-triangle" class="h-8 w-8 text-red-600 dark:text-red-400" />
        </div>
        <h3 class="mt-4 text-lg font-medium text-zinc-900 dark:text-zinc-100">
          Unsubscribe Failed
        </h3>
        <p class="mt-2 text-zinc-600 dark:text-zinc-400 text-sm">
          {{ error }}
        </p>
        <div class="mt-6">
          <button
            @click="handleUnsubscribe"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium  text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
          >
            Try Again
          </button>
        </div>
      </div>

      <div class="text-center mt-8">
        <div class="space-x-4 text-sm">
          <NuxtLink
            to="/"
            class="text-red-600 dark:text-red-400 hover:text-red-500 dark:hover:text-red-300"
          >
            Return to Homepage
          </NuxtLink>
          <span class="text-zinc-400">•</span>
          <NuxtLink
            to="/newsletter/preferences"
            class="text-red-600 dark:text-red-400 hover:text-red-500 dark:hover:text-red-300"
          >
            Manage Preferences
          </NuxtLink>
        </div>
        <p class="mt-4 text-xs text-zinc-500 dark:text-zinc-400">
          © {{ new Date().getFullYear() }} EventaHub Malawi. All rights reserved.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ENDPOINTS } from "@/utils/api";

definePageMeta({
  layout: false
});

const route = useRoute();
const router = useRouter();
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const token = route.params.token as string;
const loading = ref(true);
const unsubscribed = ref(false);
const error = ref("");
const showResubscribeForm = ref(false);
const resubscribing = ref(false);
const resubscribeEmail = ref("");
const selectedPreferences = ref(["latest_events", "recommended_events"]);

const availablePreferences = [
  { key: "latest_events", label: "Latest Events" },
  { key: "recommended_events", label: "Recommended Events" },
  { key: "new_venues", label: "New Venues" },
  { key: "event_updates", label: "Event Updates" },
];

const handleUnsubscribe = async () => {
  if (!token) {
    error.value = "Invalid unsubscribe link. Please check your email or contact support.";
    loading.value = false;
    return;
  }

  loading.value = true;
  error.value = "";

  try {
    const response = await httpClient.get(`${ENDPOINTS.NEWSLETTER.UNSUBSCRIBE}/${token}`);

    if (response) {
      unsubscribed.value = true;
      $toast.success("Successfully unsubscribed from newsletter");
    }
  } catch (err: any) {
    console.error("Unsubscribe error:", err);

    if (err.response?.status === 404) {
      error.value = "Invalid or expired unsubscribe link. The token may have already been used or is invalid.";
    } else if (err.response?.data?.message) {
      error.value = err.response.data.message;
    } else {
      error.value = "An error occurred while unsubscribing. Please try again or contact support.";
    }
  } finally {
    loading.value = false;
  }
};

const handleResubscribe = async () => {
  if (!resubscribeEmail.value || selectedPreferences.value.length === 0) {
    $toast.error("Please enter your email and select at least one preference");
    return;
  }

  resubscribing.value = true;

  try {
    const response = await httpClient.post(ENDPOINTS.NEWSLETTER.SUBSCRIBE, {
      email: resubscribeEmail.value,
      preferences: selectedPreferences.value,
    });

    if (response) {
      $toast.success("Successfully resubscribed to newsletter!");
      showResubscribeForm.value = false;
      setTimeout(() => {
        router.push("/");
      }, 2000);
    }
  } catch (err: any) {
    console.error("Resubscribe error:", err);

    if (err.response?.data?.message) {
      $toast.error(err.response.data.message);
    } else if (err.response?.data?.errors?.email) {
      $toast.error(err.response.data.errors.email[0]);
    } else {
      $toast.error("Failed to resubscribe. Please try again.");
    }
  } finally {
    resubscribing.value = false;
  }
};

useHead({
  title: "Unsubscribe from Newsletter - EventaHub Malawi",
  meta: [
    {
      name: "description",
      content: "Unsubscribe from EventaHub Malawi newsletter updates",
    },
    {
      name: "robots",
      content: "noindex, nofollow",
    },
  ],
});

onMounted(() => {
  handleUnsubscribe();
});
</script>
