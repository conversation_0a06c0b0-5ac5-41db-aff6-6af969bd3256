<template>
    <div class="min-h-screen bg-white dark:bg-zinc-900 transition-colors duration-200">
        <LandingHomeHero @on-query="onQuery"/>
        <LandingHomeStats/>
        <LandingHomeHowItWorks/>
        <LandingHomeCategorySlider/>
        <LandingHomeBecomeHost/>
        <LandingHomeTwitterFeed/>
        <LandingHomeNewsletter/>
    </div>
</template>

<script setup lang="ts">
useHead({
    title: 'EventaHub Malawi - Discover & Book Amazing Events in Malawi',
    meta: [
        {
            name: 'description',
            content: 'Discover, book and attend amazing events in Malawi. EventaHub connects event organizers with attendees for conferences, concerts, workshops and more. Join thousands of event-goers in Malawi.'
        },
        {
            name: 'keywords',
            content: 'events Malawi, event booking, event tickets, conferences Malawi, concerts Malawi, workshops, event organizers, Lilongwe events, Blantyre events'
        },
        {
            property: 'og:title',
            content: 'EventaHub Malawi - Discover & Book Amazing Events in Malawi'
        },
        {
            property: 'og:description',
            content: 'Discover, book and attend amazing events in Malawi. EventaHub connects event organizers with attendees for conferences, concerts, workshops and more.'
        },
        {
            property: 'og:type',
            content: 'website'
        },
        {
            property: 'og:site_name',
            content: 'EventaHub Malawi'
        },
        {
            name: 'twitter:card',
            content: 'summary_large_image'
        },
        {
            name: 'twitter:title',
            content: 'EventaHub Malawi - Discover & Book Amazing Events in Malawi'
        },
        {
            name: 'twitter:description',
            content: 'Discover, book and attend amazing events in Malawi. EventaHub connects event organizers with attendees for conferences, concerts, workshops and more.'
        },
        {
            name: 'author',
            content: 'EventaHub Malawi'
        },
        {
            name: 'robots',
            content: 'index, follow'
        }
    ],
    script: [
        {
            type: 'application/ld+json',
            children: JSON.stringify({
                '@context': 'https://schema.org',
                '@graph': [
                    {
                        '@type': 'Organization',
                        '@id': 'https://eventahub.com/#organization',
                        name: 'EventaHub Malawi',
                        url: 'https://eventahub.com',
                        logo: 'https://eventahub.com/icon.png',
                        description: 'EventaHub Malawi connects event organizers with attendees for conferences, concerts, workshops and more across Malawi.',
                        address: {
                            '@type': 'PostalAddress',
                            addressCountry: 'MW',
                            addressLocality: 'Lilongwe'
                        }
                    },
                    {
                        '@type': 'WebSite',
                        '@id': 'https://eventahub.com/#website',
                        url: 'https://eventahub.com',
                        name: 'EventaHub Malawi',
                        description: 'Discover, book and attend amazing events in Malawi',
                        publisher: {
                            '@id': 'https://eventahub.com/#organization'
                        },
                        potentialAction: {
                            '@type': 'SearchAction',
                            target: 'https://eventahub.com/events?search={search_term_string}',
                            'query-input': 'required name=search_term_string'
                        }
                    }
                ]
            })
        }
    ]
});

interface SearchQuery {
    search?: string;
    category?: number;
    location?: string;
}

const router = useRouter();
const onQuery = (searchQuery: SearchQuery): void => {
    router.push(
        `/events?search=${searchQuery.search || ''}
        &category=${searchQuery.category || 0}
        &location=${searchQuery.location || ''}`
    );
};
</script>

<style lang="css">
</style>
