<template>
  <div class="w-full max-w-4xl mx-auto py-10 px-10 mt-10 mb-10">
    <div class="flex flex-col items-center mb-2">
      <h1 class="text-2xl font-bold mb-2 text-center">Vendor Application Request</h1>
      <p class="text-zinc-600">
        Please fill out the form below to request a vendor account.
      </p>
    </div>
    <div class="flex mb-4 mt-2 text-sm font-medium text-gray-700 space-x-4">
      <div v-for="(step, index) in steps" :key="index" class="flex-1 flex flex-col items-center">
        <div class="flex items-center justify-center w-8 h-8 mb-2" :class="{
          'bg-red-600 text-white': currentStep === index,
          'bg-gray-200 text-gray-600': currentStep !== index && !stepValidation[index],
          'bg-green-600 text-white': currentStep !== index && stepValidation[index],
          'bg-red-500 text-white': stepErrors[index]
        }">
          <span v-if="stepErrors[index]">!</span>
          <span v-else>{{ index + 1 }}</span>
        </div>
        <span :class="{
          'text-red-600 font-bold': currentStep === index,
          'text-gray-400': currentStep !== index && !stepValidation[index],
          'text-green-600': currentStep !== index && stepValidation[index],
          'text-red-500': stepErrors[index]
        }">
          {{ step.label }}
        </span>
      </div>
    </div>
    <FormKit type="form" id="vendorForm" @submit="handleSubmit" :actions="false" #default="{ }">
      <div v-if="formError" class="bg-red-100 text-red-700 p-4 mb-4">
        {{ formError }}
      </div>

      <div class="space-y-2" v-if="currentStep === 0">
        <FormKit type="text" name="name" id="name" label="Business Name" validation="required"
          validation-visibility="live" :validation-messages="{ required: 'Business name is required' }" :classes="{
            input: 'w-full text-red-500 py-1.5 border border-gray-300 rounded-none focus:ring-0 focus:outline-none',
          }" v-model="businessInfo.name" />

        <CoreLogoPicker v-model="businessInfo.logo" />
        <FormKit type="text" name="location" id="location" label="Location" validation="required"
          validation-visibility="live" :validation-messages="{ required: 'Location is required' }" :classes="{
            input: 'w-full text-red-500 py-1.5 border border-gray-300 rounded-none focus:ring-0 focus:outline-none',
          }" v-model="businessInfo.location" help="" />
        <FormKit type="textarea" name="bio" id="bio" label="Tell us about your business" validation="required"
          validation-visibility="live" :validation-messages="{ required: 'Bio is required' }"
          v-model="businessInfo.bio" />
        <FormKit type="text" name="languages" id="languages" label="Languages Spoken" validation="required"
          validation-visibility="live" :validation-messages="{ required: 'Languages are required' }"
          help="Separate languages with commas" v-model="businessInfo.languages" />
      </div>

      <div v-else-if="currentStep === 1">
        <FormKit type="text" name="phone" id="phone" label="Phone Number" validation="required"
          validation-visibility="live" prefixIcon="phone"
          :validation-messages="{ required: 'Phone number is required' }" :classes="{
            input: 'w-full text-red-500 py-1.5 border border-gray-300 rounded-none focus:ring-0 focus:outline-none',
          }" v-model="contacts.phone" />

        <FormKit type="email" name="business_email" id="business_email" label="Business Email"
          validation="required|email" prefixIcon="email" validation-visibility="live" :validation-messages="{
            required: 'Email is required',
            email: 'Please enter a valid email address'
          }" :classes="{
            input: 'w-full text-red-500 py-1.5 border border-gray-300 rounded-none focus:ring-0 focus:outline-none',
          }" v-model="contacts.business_email" />
        <FormKit type="url" name="website" id="website" label="Website" v-model="contacts.website" />
        <FormKit type="text" name="facebook" id="facebook" label="Facebook" :classes="{
          input: 'w-full text-red-500 py-1.5 border border-gray-300 rounded-none focus:ring-0 focus:outline-none',
        }" v-model="contacts.facebook" />
        <FormKit type="text" name="instagram" id="instagram" label="Instagram" :classes="{
          input: 'w-full text-red-500 py-1.5 border border-gray-300 rounded-none focus:ring-0 focus:outline-none',
        }" v-model="contacts.instagram" />
        <FormKit type="text" name="twitter" id="twitter" label="Twitter" :classes="{
          input: 'w-full text-red-500 py-1.5 border border-gray-300 rounded-none focus:ring-0 focus:outline-none',
        }" v-model="contacts.twitter" />
      </div>

      <div v-else-if="currentStep === 2">
        <FormKit type="select" name="services" id="services" label="Services Offered" multiple :options="serviceOptions"
          validation="required" validation-visibility="live"
          :validation-messages="{ required: 'Please select at least one service' }" v-model="services.services as any"
          help="Select one or more services you offer" />

        <div class="mt-4 mb-2">
          <label class="block mb-1 text-lg font-bold">Pricing</label>
          <div v-for="(price, index) in prices" :key="index" class="mb-4 space-y-2 p-4 border border-gray-200">
            <div class="flex justify-between mb-2">
              <h4 class="font-medium"></h4>
              <button type="button" @click="removePrice(index)" class="text-red-500 text-sm">Remove</button>
            </div>
            <FormKit type="number" :id="`price_amount_${index}`" :name="`price_amount_${index}`" label="Price"
              validation="required" validation-visibility="live"
              :validation-messages="{ required: 'Price is required' }" v-model="prices[index].price" />
            <CoreDropdown icon="emojione:money-bag" :items="currencyOptions" v-model="prices[index].currency_id"
              label="Currency" />
            <FormKit type="textarea" :id="`price_description_${index}`" :name="`price_description_${index}`"
              label="Description (optional)" v-model="prices[index].description" />
          </div>
          <button type="button" @click="addPrice"
            class="px-4 flex items-center justify-center py-2 bg-gray-200 text-gray-800 w-full">
            <Icon icon="heroicons:plus" class="inline-block w-5 h-5 mr-2" />
            Add Price Item
          </button>
        </div>

        <FormKit type="checkbox" name="is_available" id="is_available" label="Available for Bookings?"
          validation="required" validation-visibility="live"
          :validation-messages="{ required: 'Please indicate your availability' }" v-model="services.is_available" />
      </div>

      <div v-else-if="currentStep === 3">
        <div class="mt-4 mb-2">
          <label class="block mb-1 font-bold">Previous Work Gallery</label>
          <div v-for="(item, index) in mediaItems" :key="index" class="mb-4 p-4 border border-gray-200">
            <div class="flex justify-between mb-2">
              <h4 class="font-medium"></h4>
              <button type="button" @click="removeMediaItem(index)" class="text-red-500">Remove</button>
            </div>
            <FormKit type="text" :id="`media_title_${index}`" :name="`media_title_${index}`" label="Title"
              validation="required" validation-visibility="live"
              :validation-messages="{ required: 'Title is required' }" v-model="mediaItems[index].title" />

            <div class="mb-4">
              <label class="block mb-1 font-medium">Media Type</label>
              <Listbox v-model="mediaItems[index].type" as="div" class="relative">
                <ListboxButton
                  class="relative w-full py-2 pl-3 pr-10 text-left bg-white border border-gray-300 cursor-default">
                  <span class="block truncate">{{ mediaItems[index].type ? mediaItems[index].type === 'image' ? 'Image'
                    : 'Video' : 'Select type' }}</span>
                  <span class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                    <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd"
                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                        clip-rule="evenodd" />
                    </svg>
                  </span>
                </ListboxButton>
                <transition leave-active-class="transition duration-100 ease-in" leave-from-class="opacity-100"
                  leave-to-class="opacity-0">
                  <ListboxOptions
                    class="absolute w-full py-1 mt-1 overflow-auto text-base bg-white max-h-60 border border-gray-300 z-10">
                    <ListboxOption v-slot="{ active, selected }" :value="'image'" as="template">
                      <li
                        :class="[active ? 'bg-red-600 text-white' : 'text-gray-900', 'cursor-pointer select-none relative py-2 pl-10 pr-4']">
                        <span :class="[selected ? 'font-medium' : 'font-normal', 'block truncate']">Image</span>
                        <span v-if="selected" class="absolute inset-y-0 left-0 flex items-center pl-3 text-red-600">
                          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                              clip-rule="evenodd" />
                          </svg>
                        </span>
                      </li>
                    </ListboxOption>
                    <ListboxOption v-slot="{ active, selected }" :value="'video'" as="template">
                      <li
                        :class="[active ? 'bg-red-600 text-white' : 'text-gray-900', 'cursor-pointer select-none relative py-2 pl-10 pr-4']">
                        <span :class="[selected ? 'font-medium' : 'font-normal', 'block truncate']">Video</span>
                        <span v-if="selected" class="absolute inset-y-0 left-0 flex items-center pl-3 text-red-600">
                          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                              clip-rule="evenodd" />
                          </svg>
                        </span>
                      </li>
                    </ListboxOption>
                  </ListboxOptions>
                </transition>
              </Listbox>
            </div>

            <div v-if="mediaItems[index].type === 'image'" class="mb-4">
              <label class="block mb-1 font-medium">Upload Image</label>
              <div
                class="border-2 border-dashed border-gray-300  p-4 flex flex-col items-center relative transition-all"
                :class="{
                  'border-red-500 bg-red-50': dragOverIndex === index,
                  'border-green-500': mediaItems[index].preview
                }" @dragover.prevent="handleDragOver(index)" @dragleave="handleDragLeave"
                @drop.prevent="handleDrop($event, index)">
                <div v-if="mediaItems[index].preview" class="relative group w-full flex justify-center">
                  <img :src="mediaItems[index].preview" alt="Preview" class="max-h-48  object-contain mb-2" />
                  <div class="absolute top-2 right-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    <button @click.stop="openFilePicker(index)" class="p-1 bg-white/80 rounded-full hover:bg-white"
                      title="Edit image">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-700" viewBox="0 0 20 20"
                        fill="currentColor">
                        <path
                          d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                      </svg>
                    </button>
                    <button @click.stop="removeMedia(index)" class="p-1 bg-white/80 rounded-full hover:bg-white"
                      title="Remove image">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-600" viewBox="0 0 20 20"
                        fill="currentColor">
                        <path fill-rule="evenodd"
                          d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                          clip-rule="evenodd" />
                      </svg>
                    </button>
                  </div>
                </div>

                <div v-else class="text-center py-4">
                  <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z">
                    </path>
                  </svg>
                  <p class="mt-2 text-sm text-gray-600">
                    <span class="font-medium text-red-600">Drag & drop</span> your image here or
                  </p>
                  <label
                    class="mt-3 cursor-pointer inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium  shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none">
                    Browse Files
                    <input type="file" accept="image/*" @change="(e) => handleMediaUpload(e, index)" class="hidden"
                      :ref="el => fileInputs[index] = el as HTMLInputElement" />
                  </label>
                  <p class="text-xs text-gray-500 mt-2">Supports: JPG, PNG, GIF (Max 5MB)</p>
                </div>
              </div>
            </div>

            <div v-else-if="mediaItems[index].type === 'video'" class="mb-4">
              <label class="block mb-1 font-medium">Upload Video</label>
              <div
                class="border-2 border-dashed border-gray-300  p-4 flex flex-col items-center relative transition-all"
                :class="{
                  'border-red-500 bg-red-50': dragOverIndex === index,
                  'border-green-500': mediaItems[index].preview
                }" @dragover.prevent="handleDragOver(index)" @dragleave="handleDragLeave"
                @drop.prevent="handleDrop($event, index)">
                <div v-if="mediaItems[index].preview" class="relative group w-full">
                  <video :src="mediaItems[index].preview" controls class="max-h-48 w-full  mb-2"></video>
                  <div class="absolute top-2 right-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    <button @click.stop="openFilePicker(index)" class="p-1 bg-white/80 rounded-full hover:bg-white"
                      title="Edit video">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-700" viewBox="0 0 20 20"
                        fill="currentColor">
                        <path
                          d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                      </svg>
                    </button>
                    <button @click.stop="removeMedia(index)" class="p-1 bg-white/80 rounded-full hover:bg-white"
                      title="Remove video">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-600" viewBox="0 0 20 20"
                        fill="currentColor">
                        <path fill-rule="evenodd"
                          d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                          clip-rule="evenodd" />
                      </svg>
                    </button>
                  </div>
                </div>

                <div v-else class="text-center py-4">
                  <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z">
                    </path>
                  </svg>
                  <p class="mt-2 text-sm text-gray-600">
                    <span class="font-medium text-red-600">Drag & drop</span> your video here or
                  </p>
                  <label
                    class="mt-3 cursor-pointer inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium  shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none">
                    Browse Files
                    <input type="file" accept="video/*" @change="(e) => handleMediaUpload(e, index)" class="hidden"
                      :ref="el => fileInputs[index] = el as HTMLInputElement" />
                  </label>
                  <p class="text-xs text-gray-500 mt-2">Supports: MP4, MOV (Max 25MB)</p>
                </div>
              </div>
            </div>

            <div v-if="!mediaItems[index].path && mediaItems[index].type" class="text-red-500 text-sm mt-1">
              Please upload a {{ mediaItems[index].type }} file
            </div>
          </div>
          <button type="button" @click="addMediaItem" class="px-4 py-2 bg-gray-200 text-gray-800 w-full">
            Add Media Item
          </button>
        </div>
      </div>

      <div class="mt-4 flex justify-between">
        <button type="button" class="px-4 py-2 bg-gray-300" @click="prevStep" v-if="currentStep !== 0">Back</button>
        <button type="button" @click="nextStep" v-if="currentStep < steps.length - 1"
          class="px-4 py-2 bg-red-600 text-white">Next</button>
        <CoreSubmitButton v-else :loading="submitting" class="px-4 py-2 bg-red-600 text-white" />
      </div>
    </FormKit>
  </div>
</template>

<script lang="ts" setup>
import { useFormKitNodeById } from '@formkit/vue';
import { Listbox, ListboxButton, ListboxOptions, ListboxOption } from '@headlessui/vue';
import type { GenericResponse } from '@/types/api';

useHead({
  title: 'Vendor Request Application | EventaHub Malawi',
  meta: [
    {
      name: 'description',
      content: 'Request to become a vendor on EventaHub Malawi, sell your services and products at events.',
    }
  ]
})

interface Step {
  label: string;
}

interface ServiceOption {
  value: number;
  label: string;
}

interface PriceItem {
  service_id: number;
  price: string;
  currency_id: { id: number; name: string };
  description: string;
}

interface MediaItem {
  title?: string;
  type: 'image' | 'video'
  preview: string | null
  path: string | null
  file: File | null
}

const formNode = useFormKitNodeById('vendorForm');
const formError = ref<string>('');
const httpClient = useHttpClient();
const currentStep = ref<number>(0);
const submitting = ref<boolean>(false);
const { $toast }: any = useNuxtApp();
const stepValidation = reactive<{ [key: number]: boolean }>({
  0: false,
  1: false,
  2: false,
  3: false
});
const stepErrors = reactive<{ [key: number]: boolean }>({
  0: false,
  1: false,
  2: false,
  3: false
});

const steps: Step[] = [
  { label: 'Business Info' },
  { label: 'Contacts & Socials' },
  { label: 'Services & Pricing' },
  { label: 'Work Gallery' }
];

const serviceOptions = ref<ServiceOption[]>([])

const businessInfo = reactive({
  name: '',
  logo: null,
  location: '',
  bio: '',
  languages: ''
});

const contacts = reactive({
  phone: '',
  business_email: '',
  website: '',
  facebook: '',
  instagram: '',
  twitter: ''
});

const services = reactive({
  is_available: false,
  services: []
});

const currencyOptions = ref<{ id: number; name: string }[]>([]);

const prices = ref<PriceItem[]>([
  { service_id: 0, price: "", currency_id: { id: 0, name: 'select currency' }, description: '' }
]);

const mediaItems = ref<MediaItem[]>([
  { type: 'image', preview: null, path: null, file: null }
])

const dragOverIndex = ref<number | null>(null)
const fileInputs = ref<Record<number, HTMLInputElement>>({})

const imageCount = computed((): number => {
  return mediaItems.value.filter(item => item.type === 'image' && item.preview).length
})

const handleDragOver = (index: number) => {
  dragOverIndex.value = index
}

const handleDragLeave = () => {
  dragOverIndex.value = null
}

const handleDrop = (event: DragEvent, index: number) => {
  dragOverIndex.value = null
  if (!event.dataTransfer) return

  const files = event.dataTransfer.files
  if (files.length > 0) {
    processFile(files[0], index)
  }
}

const openFilePicker = (index: number) => {
  fileInputs.value[index]?.click()
}

const removeMedia = (index: number) => {
  if (mediaItems.value[index].type === 'video' && mediaItems.value[index].preview) {
    URL.revokeObjectURL(mediaItems.value[index].preview!)
  }

  mediaItems.value[index] = {
    ...mediaItems.value[index],
    preview: null,
    path: null
  }
}

const handleMediaUpload = (event: Event, index: number) => {
  const input = event.target as HTMLInputElement
  if (input.files && input.files[0]) {
    processFile(input.files[0], index)
  }
}

const processFile = (file: File, index: number) => {
  const type = mediaItems.value[index].type

  if ((type === 'image' && !file.type.startsWith('image/')) ||
    (type === 'video' && !file.type.startsWith('video/'))) {
    $toast.warning(`Please upload a ${type} file`)
    return
  }

  const maxSize = type === 'image' ? 5 : 25;
  if (file.size > maxSize * 1024 * 1024) {
    $toast.warning(`File is too large. Maximum size is ${maxSize}MB.`)
    return
  }

  if (type === 'image' && imageCount.value >= 5 && !mediaItems.value[index].preview) {
    $toast.warning('Maximum 5 images allowed')
    return
  }

  if (type === 'image') {
    const reader = new FileReader()
    reader.onload = (e) => {
      mediaItems.value[index] = {
        ...mediaItems.value[index],
        preview: e.target?.result as string,
        path: file.name,
        file: file
      }
    }
    reader.readAsDataURL(file)
  } else {
    const videoUrl = URL.createObjectURL(file)
    mediaItems.value[index] = {
      ...mediaItems.value[index],
      preview: videoUrl,
      path: file.name,
      file: file
    }
  }
}

const addPrice = (): void => {
  prices.value.push({ service_id: 0, price: "1", currency_id: { id: 0, name: 'select currency' }, description: '' });
};

const removePrice = (index: number): void => {
  if (prices.value.length > 1) {
    prices.value.splice(index, 1);
  }
};

const addMediaItem = (): void => {
  mediaItems.value.push({ type: 'image', preview: null, path: null, file: null });
};

const removeMediaItem = (index: number): void => {
  if (mediaItems.value.length > 1) {
    mediaItems.value.splice(index, 1);
  }
};

const validateField = async (fieldId: string) => {
  const field = useFormKitNodeById(fieldId);
  if (field.value) await field.value.value;
  return field.value?.context?.state?.valid !== false;
};

const validatePriceFields = async () => {
  const fieldTypes = ['price_service', 'price_amount', 'price_currency'];
  const validationResults = await Promise.all(
    prices.value.flatMap((_, index) =>
      fieldTypes.map(type => validateField(`${type}_${index}`))
    )
  );
  return validationResults.every(Boolean);
};

const validateCurrentStep = async (): Promise<boolean> => {
  if (!formNode.value) return false;

  let isValid = true;

  await formNode.value.value;

  if (currentStep.value === 0) {
    const fields = ['name', 'location', 'bio', 'languages'];
    for (const field of fields) {
      const fieldNode = useFormKitNodeById(field);
      if (fieldNode.value) {
        await fieldNode.value.value;

        if (fieldNode.value.context?.state?.valid === false) {
          isValid = false;
        }
      }
    }

    if (!businessInfo.logo) {
      isValid = false;
      formError.value = 'Logo is required';
    }
  }
  else if (currentStep.value === 1) {
    const fields = ['phone', 'business_email'];
    for (const field of fields) {
      const fieldNode = useFormKitNodeById(field);
      if (fieldNode.value) {
        await fieldNode.value.value;

        if (fieldNode.value.context?.state?.valid === false) {
          isValid = false;
        }
      }
    }
  }
  else if (currentStep.value === 2) {
    const fields = ['is_available', 'services'];
    for (const field of fields) {
      const fieldNode = useFormKitNodeById(field);
      if (fieldNode.value) {
        await fieldNode.value.value;

        if (fieldNode.value.context?.state?.valid === false) {
          isValid = false;
        }
      }
    }
    const fieldsValid = await Promise.all([
      validateField('is_available'),
      validateField('services'),
      validatePriceFields()
    ]);

    isValid = fieldsValid.every(Boolean);
  }
  else if (currentStep.value === 3) {
    for (let i = 0; i < mediaItems.value.length; i++) {
      const titleField = useFormKitNodeById(`media_title_${i}`);
      if (titleField.value) {
        await titleField.value.value;
        if (titleField.value.context?.state?.valid === false) {
          isValid = false;
        }
      }

      if (!mediaItems.value[i].type) {
        isValid = false;
        formError.value = 'Media type is required for all items';
      }

      if (!mediaItems.value[i].path && mediaItems.value[i].type) {
        isValid = false;
        formError.value = `Please upload a ${mediaItems.value[i].type} file for item ${i + 1}`;
      }
    }
  }

  return isValid;
};

const nextStep = async (): Promise<void> => {
  formError.value = '';
  const isValid = await validateCurrentStep();

  if (isValid) {
    stepValidation[currentStep.value] = true;
    stepErrors[currentStep.value] = false;

    if (currentStep.value < steps.length - 1) {
      currentStep.value++;
    }
  } else {
    stepErrors[currentStep.value] = true;
    stepValidation[currentStep.value] = false;

    if (!formError.value) {
      formError.value = `Please correct the errors in the "${steps[currentStep.value].label}" section.`;
    }

    setTimeout(() => {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }, 100);
  }
};

const prevStep = (): void => {
  if (currentStep.value > 0) {
    currentStep.value--;
  }
  formError.value = '';
};

const buildVendorFormData = (): FormData => {
  const formData = new FormData();

  formData.append('name', businessInfo.name);
  if (businessInfo.logo) {
    formData.append('logo', businessInfo.logo);
  }
  formData.append('location', businessInfo.location);
  formData.append('bio', businessInfo.bio);
  formData.append('languages', businessInfo.languages);

  formData.append('phone', contacts.phone);
  formData.append('business_email', contacts.business_email);
  formData.append('website', contacts.website);
  formData.append('facebook', contacts.facebook);
  formData.append('instagram', contacts.instagram);
  formData.append('twitter', contacts.twitter);

  formData.append('is_available', String(services.is_available));
  formData.append('services', JSON.stringify(services.services));

  formData.append('prices', JSON.stringify(prices.value));

  const mediaMetadata = mediaItems.value.map((item, index) => {
    return {
      title: item.title,
      type: item.type,
      path: item.path,
      index: index
    };
  });

  formData.append('mediaMetadata', JSON.stringify(mediaMetadata));
  mediaItems.value.forEach((item, index) => {
    if (item.file) {
      formData.append(`mediaFile_${index}`, item.file);
    }
  });

  return formData;
};

const handleSubmit = async (): Promise<void> => {
  formError.value = '';
  const isValid = await validateCurrentStep();
  if (isValid) {
    stepValidation[currentStep.value] = true;
    stepErrors[currentStep.value] = false;
    try {
      submitting.value = true;
      const formData = <FormData>buildVendorFormData();
      const response = await httpClient.post<GenericResponse>(ENDPOINTS.VENDORS.CREATE, formData);
      if (response) {
        $toast.success(response.message);
        setTimeout(() => {
          navigateTo('/vendors');
        }, 2000);
      }
    } catch (error: any) {
      handleErrorWithToast(error, $toast);
    } finally {
      submitting.value = false;
    }
  } else {
    stepErrors[currentStep.value] = true;
    stepValidation[currentStep.value] = false;

    if (!formError.value) {
      formError.value = `Please correct the errors in the "${steps[currentStep.value].label}" section.`;
    }

    setTimeout(() => {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }, 100);
  }
};

const fetchCurrencyOptions = async (): Promise<void> => {
  try {
    const response: any = await httpClient.get(ENDPOINTS.CURRENCIES.BASE);
    if (response) {
      currencyOptions.value = response.currencies.map((currency: any) => ({
        id: currency.id,
        name: currency.name
      }));
      currencyOptions.value.unshift({ id: 0, name: 'select currency' });
    }
  } catch (error) {
    console.error('Error fetching currency options:', error);
  }
}

const fetchServices = async (): Promise<void> => {
  try {
    const response: any = await httpClient.get(ENDPOINTS.SERVICES.BASE);
    if (response) {
      serviceOptions.value = response.data.map((service: any) => {
        return {
          value: service.id,
          label: service.name
        }
      });
    }
  } catch (error) {
    console.error('Error fetching services:', error);
  }
}

onMounted(() => {
  fetchCurrencyOptions();
  fetchServices();
});
</script>

<style scoped>
button {
  border-radius: 0;
}
</style>