<template>
  <div class="min-h-screen bg-white dark:bg-zinc-800 flex items-center justify-center px-4">
    <div class="max-w-lg mx-auto text-center">
      <div class="mb-8">
        <img
          src="/icon.png"
          alt="EventaHub Logo"
          class="h-16 w-16 mx-auto shadow-lg"
        >
      </div>

      <div class="mb-8">
        <div class="text-9xl font-bold text-purple-600 dark:text-purple-500 mb-4">
          429
        </div>
        <div class="w-32 h-32 mx-auto mb-6">
          <svg class="w-full h-full text-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
          </svg>
        </div>
      </div>

      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          Too Many Requests
        </h1>
        <p class="text-gray-600 dark:text-gray-400 text-lg mb-6">
          You've made too many requests in a short period. Please wait a moment before trying again to help us maintain a smooth experience for everyone.
        </p>
      </div>

      <div class="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 p-6 mb-8">
        <div class="flex items-center justify-center mb-4">
          <svg class="w-6 h-6 text-purple-600 dark:text-purple-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
          </svg>
          <span class="text-purple-800 dark:text-purple-200 font-medium">Rate Limited</span>
        </div>
        <p class="text-purple-700 dark:text-purple-300 text-sm">
          Please wait {{ waitTime }} seconds before making another request.
        </p>
      </div>

      <div class="bg-gray-50 dark:bg-gray-700 p-6 mb-8">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Please wait:
        </h3>
        <div class="text-4xl font-bold text-purple-600 dark:text-purple-500 mb-4">
          {{ countdown }}s
        </div>
        <div class="w-full bg-gray-200 dark:bg-gray-600 h-2">
          <div
            class="bg-purple-600 h-2 transition-all duration-1000 ease-linear"
            :style="{ width: `${progressPercentage}%` }"
          ></div>
        </div>
      </div>

      <div class="bg-gray-50 dark:bg-gray-700 p-6 mb-8">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Why rate limiting?
        </h3>
        <ul class="text-left text-gray-600 dark:text-gray-400 space-y-2">
          <li class="flex items-center">
            <svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
            Protects server performance
          </li>
          <li class="flex items-center">
            <svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
            Ensures fair usage for all users
          </li>
          <li class="flex items-center">
            <svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
            Prevents abuse and spam
          </li>
          <li class="flex items-center">
            <svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
            Maintains service stability
          </li>
        </ul>
      </div>

      <div class="space-y-4">
        <button
          @click="retryAfterCountdown"
          :disabled="countdown > 0"
          :class="[
            'inline-block font-semibold py-3 px-8 transition-all duration-200 shadow-lg',
            countdown > 0
              ? 'bg-gray-400 text-gray-700 cursor-not-allowed'
              : 'bg-purple-600 hover:bg-purple-700 text-white transform hover:scale-105'
          ]"
        >
          {{ countdown > 0 ? `Please wait ${countdown}s` : 'Try Again' }}
        </button>

        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <NuxtLink
            to="/"
            class="inline-block bg-gray-600 hover:bg-gray-700 text-white font-semibold py-2 px-6 transition-all duration-200 text-sm shadow-md"
          >
            Back to Home
          </NuxtLink>
          <NuxtLink
            to="/contact-us"
            class="inline-block bg-gray-600 hover:bg-gray-700 text-white font-semibold py-2 px-6 transition-all duration-200 text-sm shadow-md"
          >
            Contact Support
          </NuxtLink>
        </div>
      </div>

      <div class="mt-12 text-center text-sm text-gray-500 dark:text-gray-400">
        <p>&copy; {{ new Date().getFullYear() }} EventaHub Malawi. All rights reserved.</p>
        <p class="mt-1">Powered by Brainy Technologies</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

const waitTime = ref(60)
const countdown = ref(waitTime.value)
const progressPercentage = ref(100)

let countdownTimer: NodeJS.Timeout

onMounted(() => {

  startCountdown()
})

onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
  }
})

const startCountdown = () => {
  countdownTimer = setInterval(() => {
    if (countdown.value > 0) {
      countdown.value--
      progressPercentage.value = (countdown.value / waitTime.value) * 100
    } else {
      clearInterval(countdownTimer)
    }
  }, 1000)
}

const retryAfterCountdown = () => {
  if (countdown.value === 0) {
    window.location.reload()
  }
}


useHead({
  title: '429 - Too Many Requests | EventaHub',
  meta: [
    { name: 'description', content: 'You have made too many requests. Please wait before trying again.' },
    { name: 'robots', content: 'noindex, nofollow' }
  ]
})


definePageMeta({
  layout: false
})
</script>
