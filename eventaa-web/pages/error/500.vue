<template>
  <div class="min-h-screen bg-white dark:bg-zinc-800 flex items-center justify-center px-4">
    <div class="max-w-lg mx-auto text-center">
      <div class="mb-8">
        <img
          src="/icon.png"
          alt="EventaHub Logo"
          class="h-16 w-16 mx-auto shadow-lg"
        >
      </div>
      <div class="mb-8">
        <div class="text-9xl font-bold text-red-600 dark:text-red-500 mb-4">
          500
        </div>
        <div class="w-32 h-32 mx-auto mb-6">
          <svg class="w-full h-full text-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
        </div>
      </div>


      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          Internal Server Error
        </h1>
        <p class="text-gray-600 dark:text-gray-400 text-lg mb-6">
          We're experiencing some technical difficulties on our end. Our team has been automatically notified and is working to resolve this issue as quickly as possible.
        </p>
      </div>


      <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 p-6 mb-8">
        <div class="flex items-center justify-center mb-4">
          <svg class="w-6 h-6 text-red-600 dark:text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
          <span class="text-red-800 dark:text-red-200 font-medium">Service Temporarily Unavailable</span>
        </div>
        <p class="text-red-700 dark:text-red-300 text-sm">
          Error ID: {{ errorId }} • {{ new Date().toLocaleString() }}
        </p>
      </div>


      <div class="bg-gray-50 dark:bg-gray-700 p-6 mb-8">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          What you can do:
        </h3>
        <ul class="text-left text-gray-600 dark:text-gray-400 space-y-2">
          <li class="flex items-center">
            <svg class="w-5 h-5 text-blue-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
            </svg>
            Wait a few minutes and try again
          </li>
          <li class="flex items-center">
            <svg class="w-5 h-5 text-blue-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
            </svg>
            Check our status page for updates
          </li>
          <li class="flex items-center">
            <svg class="w-5 h-5 text-blue-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
            </svg>
            Contact support if the issue persists
          </li>
        </ul>
      </div>


      <div class="space-y-4">
        <button
          @click="refreshPage"
          class="inline-block bg-red-600 hover:bg-red-700 text-white font-semibold py-3 px-8 transition-all duration-200 transform hover:scale-105 shadow-lg"
        >
          Try Again
        </button>

        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <NuxtLink
            to="/"
            class="inline-block bg-gray-600 hover:bg-gray-700 text-white font-semibold py-2 px-6 transition-all duration-200 text-sm shadow-md"
          >
            Back to Home
          </NuxtLink>
          <NuxtLink
            to="/contact-us"
            class="inline-block bg-gray-600 hover:bg-gray-700 text-white font-semibold py-2 px-6 transition-all duration-200 text-sm shadow-md"
          >
            Contact Support
          </NuxtLink>
        </div>
      </div>


      <div class="mt-12 text-center text-sm text-gray-500 dark:text-gray-400">
        <p>&copy; {{ new Date().getFullYear() }} EventaHub Malawi. All rights reserved.</p>
        <p class="mt-1">Powered by Brainy Technologies</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const errorId = ref(Math.random().toString(36).substring(2, 11).toUpperCase())

const refreshPage = () => {
  window.location.reload()
}


useHead({
  title: '500 - Internal Server Error | EventaHub',
  meta: [
    { name: 'description', content: 'EventaHub is experiencing technical difficulties. Our team is working to resolve this issue.' },
    { name: 'robots', content: 'noindex, nofollow' }
  ]
})


definePageMeta({
  layout: false
})
</script>
