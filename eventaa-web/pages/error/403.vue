<template>
  <div class="min-h-screen bg-white dark:bg-zinc-800 flex items-center justify-center px-4">
    <div class="max-w-lg mx-auto text-center">
      <div class="mb-8">
        <img
          src="/icon.png"
          alt="EventaHub Logo"
          class="h-16 w-16 mx-auto shadow-lg"
        >
      </div>

      <div class="mb-8">
        <div class="text-9xl font-bold text-orange-600 dark:text-orange-500 mb-4">
          403
        </div>
        <div class="w-32 h-32 mx-auto mb-6">
          <svg class="w-full h-full text-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
          </svg>
        </div>
      </div>

      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          Access Forbidden
        </h1>
        <p class="text-gray-600 dark:text-gray-400 text-lg mb-6">
          You don't have permission to access this resource. This could be due to insufficient privileges or the content may be restricted.
        </p>
      </div>

      <div class="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 p-6 mb-8">
        <div class="flex items-center justify-center mb-4">
          <svg class="w-6 h-6 text-orange-600 dark:text-orange-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
          </svg>
          <span class="text-orange-800 dark:text-orange-200 font-medium">Restricted Access</span>
        </div>
        <p class="text-orange-700 dark:text-orange-300 text-sm">
          This area requires special permissions or authentication.
        </p>
      </div>

      <div class="bg-gray-50 dark:bg-gray-700 p-6 mb-8">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Possible reasons:
        </h3>
        <ul class="text-left text-gray-600 dark:text-gray-400 space-y-2">
          <li class="flex items-center">
            <svg class="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 1.944A11.954 11.954 0 012.166 5C2.056 5.649 2 6.319 2 7c0 5.225 3.34 9.67 8 11.317C14.66 16.67 18 12.225 18 7c0-.682-.057-1.35-.166-2.001A11.954 11.954 0 0110 1.944zM11 14a1 1 0 11-2 0 1 1 0 012 0zm0-7a1 1 0 10-2 0v3a1 1 0 102 0V7z" clip-rule="evenodd" />
            </svg>
            You need to be logged in
          </li>
          <li class="flex items-center">
            <svg class="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 1.944A11.954 11.954 0 012.166 5C2.056 5.649 2 6.319 2 7c0 5.225 3.34 9.67 8 11.317C14.66 16.67 18 12.225 18 7c0-.682-.057-1.35-.166-2.001A11.954 11.954 0 0110 1.944zM11 14a1 1 0 11-2 0 1 1 0 012 0zm0-7a1 1 0 10-2 0v3a1 1 0 102 0V7z" clip-rule="evenodd" />
            </svg>
            Your account lacks required permissions
          </li>
          <li class="flex items-center">
            <svg class="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 1.944A11.954 11.954 0 012.166 5C2.056 5.649 2 6.319 2 7c0 5.225 3.34 9.67 8 11.317C14.66 16.67 18 12.225 18 7c0-.682-.057-1.35-.166-2.001A11.954 11.954 0 0110 1.944zM11 14a1 1 0 11-2 0 1 1 0 012 0zm0-7a1 1 0 10-2 0v3a1 1 0 102 0V7z" clip-rule="evenodd" />
            </svg>
            Content is restricted to certain users
          </li>
          <li class="flex items-center">
            <svg class="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 1.944A11.954 11.954 0 012.166 5C2.056 5.649 2 6.319 2 7c0 5.225 3.34 9.67 8 11.317C14.66 16.67 18 12.225 18 7c0-.682-.057-1.35-.166-2.001A11.954 11.954 0 0110 1.944zM11 14a1 1 0 11-2 0 1 1 0 012 0zm0-7a1 1 0 10-2 0v3a1 1 0 102 0V7z" clip-rule="evenodd" />
            </svg>
            Geographic or IP restrictions
          </li>
        </ul>
      </div>

      <div class="space-y-4">
        <NuxtLink
          to="/get-started"
          class="inline-block bg-orange-600 hover:bg-orange-700 text-white font-semibold py-3 px-8 transition-all duration-200 transform hover:scale-105 shadow-lg"
        >
          Sign In / Register
        </NuxtLink>

        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <NuxtLink
            to="/"
            class="inline-block bg-gray-600 hover:bg-gray-700 text-white font-semibold py-2 px-6 transition-all duration-200 text-sm shadow-md"
          >
            Back to Home
          </NuxtLink>
          <NuxtLink
            to="/contact-us"
            class="inline-block bg-gray-600 hover:bg-gray-700 text-white font-semibold py-2 px-6 transition-all duration-200 text-sm shadow-md"
          >
            Contact Support
          </NuxtLink>
        </div>
      </div>

      <div class="mt-8 text-sm text-gray-500 dark:text-gray-400">
        <p>Need help? Our support team is here to assist you.</p>
        <p class="mt-1">Reference ID: {{ referenceId }}</p>
      </div>

      <div class="mt-12 text-center text-sm text-gray-500 dark:text-gray-400">
        <p>&copy; {{ new Date().getFullYear() }} EventaHub Malawi. All rights reserved.</p>
        <p class="mt-1">Powered by Brainy Technologies</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

const referenceId = ref(Math.random().toString(36).substring(2, 11).toUpperCase())


useHead({
  title: '403 - Access Forbidden | EventaHub',
  meta: [
    { name: 'description', content: 'Access to this resource is forbidden. You may need to log in or have insufficient permissions.' },
    { name: 'robots', content: 'noindex, nofollow' }
  ]
})


definePageMeta({
  layout: false
})
</script>
