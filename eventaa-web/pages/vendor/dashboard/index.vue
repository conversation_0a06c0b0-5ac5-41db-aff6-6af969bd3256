<template>
  <div class="dashboard-bg-main min-h-screen">
    <div v-if="isLoading" class="flex justify-center items-center h-64">
      <div class="flex flex-col items-center">
        <CoreLoader color="#ef4444" />
        <p class="mt-4 text-gray-600 dark:text-gray-300">Loading vendor dashboard...</p>
      </div>
    </div>
    <div v-else class="p-4 sm:p-6">
      <div class="dashboard-bg-card dashboard-shadow overflow-hidden mb-6">
        <div class="bg-gradient-to-r from-gray-800 to-gray-900 px-6 py-8">
          <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <h1 class="text-2xl font-bold text-white">Welcome back, {{ vendorName }}</h1>
              <p class="mt-1 text-gray-300">Manage your services and bookings efficiently.</p>
            </div>
            <div class="mt-4 md:mt-0">
              <NuxtLink :to="`/vendors/${vendorSlug}`" class="bg-white text-gray-900 px-4 py-2 font-medium hover:bg-gray-100 dashboard-transition">
                View Public Profile
              </NuxtLink>
            </div>
          </div>
        </div>
        <div class="px-6 py-5 dashboard-border border-b">
          <div class="flex items-center justify-between">
            <h2 class="text-lg font-medium dashboard-text-primary">Account Overview</h2>
            <span
              class="px-3 py-1 text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
              Active
            </span>
          </div>
          <p class="mt-1 dashboard-text-secondary">
            Your account is in good standing. Keep up the great work!
          </p>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div class="dashboard-bg-card dashboard-shadow p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0 p-3 bg-blue-100 dark:bg-blue-900">
              <Icon icon="heroicons:calendar" class="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div class="ml-5">
              <p class="font-medium dashboard-text-muted">Bookings</p>
              <div class="flex items-baseline">
                <p class="text-2xl font-semibold dashboard-text-primary">{{ bookingsTotal }}</p>
                <p class="ml-2 font-medium" :class="bookingsGrowth >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'">
                  {{ bookingsGrowth >= 0 ? '+' : '' }}{{ bookingsGrowth }}%
                </p>
              </div>
            </div>
          </div>
          <div class="mt-4">
            <div class="flex items-center justify-between">
              <p class="dashboard-text-muted">{{ bookingsPending }} pending approval</p>
              <NuxtLink to="/vendor/dashboard/bookings" class="font-medium dashboard-text-brand hover:text-red-800 dashboard-transition">View all</NuxtLink>
            </div>
          </div>
        </div>

        <div class="dashboard-bg-card dashboard-shadow p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0 p-3 bg-green-100 dark:bg-green-900">
              <Icon icon="heroicons:banknotes" class="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
            <div class="ml-5">
              <p class="font-medium dashboard-text-muted">Revenue</p>
              <div class="flex items-baseline">
                <p class="text-2xl font-semibold dashboard-text-primary">{{ formatCurrency(revenueTotal) }}</p>
                <p class="ml-2 font-medium" :class="revenueGrowth >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'">
                  {{ revenueGrowth >= 0 ? '+' : '' }}{{ revenueGrowth }}%
                </p>
              </div>
            </div>
          </div>
          <div class="mt-4">
            <div class="flex items-center justify-between">
              <p class="dashboard-text-muted">{{ formatCurrency(revenuePending) }} pending</p>
              <a href="#" class="font-medium dashboard-text-brand hover:text-red-800 dashboard-transition">Details</a>
            </div>
          </div>
        </div>

        <div class="dashboard-bg-card dashboard-shadow p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0 p-3 bg-yellow-100 dark:bg-yellow-900">
              <Icon icon="heroicons:star" class="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
            </div>
            <div class="ml-5">
              <p class="font-medium dashboard-text-muted">Rating</p>
              <div class="flex items-baseline">
                <p class="text-2xl font-semibold dashboard-text-primary">{{ ratingAverage }}/5</p>
                <p class="ml-2 font-medium" :class="ratingGrowth >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'">
                  {{ ratingGrowth >= 0 ? '+' : '' }}{{ ratingGrowth }}
                </p>
              </div>
            </div>
          </div>
          <div class="mt-4">
            <div class="flex items-center justify-between">
              <p class="dashboard-text-muted">Based on {{ reviewsTotal }} reviews</p>
              <a href="#" class="font-medium dashboard-text-brand hover:text-red-800 dashboard-transition">View all</a>
            </div>
          </div>
        </div>

        <div class="dashboard-bg-card dashboard-shadow p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0 p-3 bg-purple-100 dark:bg-purple-900">
              <Icon icon="heroicons:eye" class="h-6 w-6 text-purple-600 dark:text-purple-400" />
            </div>
            <div class="ml-5">
              <p class="font-medium dashboard-text-muted">Profile Views</p>
              <div class="flex items-baseline">
                <p class="text-2xl font-semibold dashboard-text-primary">{{ profileViews }}</p>
                <p class="ml-2 font-medium" :class="profileViewsGrowth >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'">
                  {{ profileViewsGrowth >= 0 ? '+' : '' }}{{ profileViewsGrowth }}%
                </p>
              </div>
            </div>
          </div>
          <div class="mt-4">
            <div class="flex items-center justify-between">
              <p class="dashboard-text-muted">{{ profileViewsPeriod }}</p>
              <a href="#" class="font-medium dashboard-text-brand hover:text-red-800 dashboard-transition">Analytics</a>
            </div>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div class="dashboard-bg-card dashboard-shadow overflow-hidden">
          <div class="px-6 py-4 border-b border-zinc-200 dark:border-zinc-700">
            <h3 class="text-lg font-medium dashboard-text-primary">Recent Activity</h3>
          </div>
          <div class="divide-y divide-zinc-200 dark:divide-zinc-700">
            <div v-for="(activity, index) in recentActivities" :key="index" class="px-6 py-4">
              <div class="flex items-start">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 rounded-full flex items-center justify-center"
                    :class="activity.iconBg + ' dark:' + activity.iconBgDark">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" :class="activity.iconColor + ' dark:' + activity.iconColorDark" viewBox="0 0 20 20" fill="currentColor">
                      <path v-if="activity.icon === 'heroicons:calendar'" fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                      <path v-if="activity.icon === 'heroicons:star'" d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      <path v-if="activity.icon === 'heroicons:banknotes'" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                      <path v-if="activity.icon === 'heroicons:chat-bubble-left'" d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z" />
                      <path v-if="activity.icon === 'heroicons:eye'" d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                      <path v-if="activity.icon === 'heroicons:eye'" fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
                    </svg>
                  </div>
                </div>
                <div class="ml-4 flex-1">
                  <p class="dashboard-text-primary">{{ activity.message }}</p>
                  <p class="mt-1 text-xs dashboard-text-muted">{{ activity.time }}</p>
                </div>
              </div>
            </div>
          </div>
          <div class="px-6 py-3 dashboard-bg-hover">
            <a href="#" class="font-medium dashboard-text-brand hover:text-red-800 dashboard-transition">View all
              activity</a>
          </div>
        </div>

        <div class="dashboard-bg-card dashboard-shadow overflow-hidden">
          <div class="px-6 py-4 dashboard-border border-b">
            <h3 class="text-lg font-medium dashboard-text-primary">Upcoming Bookings</h3>
          </div>
          <div class="divide-y dashboard-border-light">
            <div v-for="(booking, index) in upcomingBookings" :key="index" class="px-6 py-4">
              <div class="flex items-center justify-between">
                <div>
                  <p class="font-medium dashboard-text-primary">{{ booking.client }}</p>
                  <p class="mt-1 text-xs dashboard-text-muted">{{ booking.service }}</p>
                </div>
                <div class="text-right">
                  <p class="dashboard-text-primary">{{ booking.date }}</p>
                  <p class="mt-1 text-xs dashboard-text-muted">{{ booking.time }}</p>
                </div>
              </div>
              <div class="mt-2 flex justify-end space-x-2">
                <button
                  class="px-2 py-1 text-xs font-medium dashboard-bg-hover dashboard-text-primary hover:dashboard-bg-active dashboard-transition">
                  Details
                </button>
                <button
                  class="px-2 py-1 text-xs font-medium dashboard-bg-primary-light dashboard-text-brand hover:bg-red-200 dark:hover:bg-red-800 dashboard-transition">
                  Message
                </button>
              </div>
            </div>
          </div>
          <div class="px-6 py-3 dashboard-bg-hover">
            <NuxtLink to="/vendor/dashboard/bookings" class="font-medium dashboard-text-brand hover:text-red-800 dashboard-transition">
              View all bookings
            </NuxtLink>
          </div>
        </div>
      </div>

      <div class="dashboard-bg-card dashboard-shadow overflow-hidden mb-6">
        <div class="px-6 py-4 dashboard-border border-b">
          <h3 class="text-lg font-medium dashboard-text-primary">Quick Actions</h3>
        </div>
        <div class="px-6 py-4">
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <NuxtLink to="/vendor/dashboard/services" class="flex flex-col items-center p-4 dashboard-border dashboard-hover-bg dashboard-transition">
              <div class="p-2 rounded-full bg-blue-100 dark:bg-blue-900 mb-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600 dark:text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                </svg>
              </div>
              <span class="font-medium dashboard-text-primary">Add Service</span>
            </NuxtLink>
            <NuxtLink to="/vendor/dashboard/profile" class="flex flex-col items-center p-4 dashboard-border dashboard-hover-bg dashboard-transition">
              <div class="p-2 rounded-full bg-green-100 dark:bg-green-900 mb-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-600 dark:text-green-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                </svg>
              </div>
              <span class="font-medium dashboard-text-primary">Update Profile</span>
            </NuxtLink>
            <NuxtLink to="/vendor/dashboard/services" class="flex flex-col items-center p-4 dashboard-border dashboard-hover-bg dashboard-transition">
              <div class="p-2 rounded-full bg-purple-100 dark:bg-purple-900 mb-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple-600 dark:text-purple-400" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd" />
                </svg>
              </div>
              <span class="font-medium dashboard-text-primary">Manage Pricing</span>
            </NuxtLink>
            <NuxtLink to="/vendor/dashboard/bookings" class="flex flex-col items-center p-4 dashboard-border dashboard-hover-bg dashboard-transition">
              <div class="p-2 rounded-full bg-yellow-100 dark:bg-yellow-900 mb-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-600 dark:text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                </svg>
              </div>
              <span class="font-medium dashboard-text-primary">View Bookings</span>
            </NuxtLink>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useVendorStore } from '@/store/vendor';
import { useVendorBookingsStore } from '@/store/vendorBookings';
import { useAuthStore } from '@/store/auth';

definePageMeta({
  layout: 'vendor-dashboard',
  middleware: ['vendor']
});

useHead({
  title: "Vendor Dashboard | EventaHub Vendor",
  meta: [
    { name: "description", content: "Manage your vendor services, bookings, and track performance metrics in your EventaHub vendor dashboard" }
  ]
});

const vendorStore = useVendorStore();
const bookingsStore = useVendorBookingsStore();
const authStore = useAuthStore();
const { $toast }: any = useNuxtApp();
const isLoading = ref(true);

const bookingsTotal = computed(() => vendorStore.analytics?.bookings.total || 0);
const bookingsPending = computed(() => vendorStore.analytics?.bookings.pending_approval || 0);
const bookingsGrowth = computed(() => vendorStore.analytics?.bookings.growth_percentage || 0);

const revenueTotal = computed(() => vendorStore.analytics?.revenue.total || 0);
const revenuePending = computed(() => vendorStore.analytics?.revenue.pending || 0);
const revenueGrowth = computed(() => vendorStore.analytics?.revenue.growth_percentage || 0);

const ratingAverage = computed(() => vendorStore.analytics?.ratings.average || 0);
const ratingGrowth = computed(() => vendorStore.analytics?.ratings.growth || 0);
const reviewsTotal = computed(() => vendorStore.analytics?.ratings.total_reviews || 0);

const profileViews = computed(() => vendorStore.analytics?.profile_views.total || 0);
const profileViewsGrowth = computed(() => vendorStore.analytics?.profile_views.growth_percentage || 0);
const profileViewsPeriod = computed(() => vendorStore.analytics?.profile_views.period || 'Last 30 days');

const vendorName = computed(() => vendorStore.details?.name || authStore.user?.name || 'Vendor');
const vendorSlug = computed(() => vendorStore.details?.slug || '');

const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
  }).format(amount);
};

const loadVendorData = async () => {
  isLoading.value = true;
  try {
    if (!vendorStore.permissionsLoaded) {
      await vendorStore.fetchVendorPermissions();
    }

    await vendorStore.fetchVendorDetails();
    await vendorStore.fetchVendorAnalytics();
    await bookingsStore.fetchVendorBookings();

    updateUpcomingBookings();
    updateRecentActivities();
  } catch (error) {
    console.error('Error loading vendor data:', error);
    $toast.error('Failed to load vendor data');
  } finally {
    isLoading.value = false;
  }
};

const updateUpcomingBookings = () => {
  if (bookingsStore.bookings.length > 0) {
    const pendingAndConfirmedBookings = bookingsStore.bookings
      .filter(booking => ['pending', 'approved'].includes(booking.status))
      .sort((a, b) => new Date(a.booking_from).getTime() - new Date(b.booking_from).getTime())
      .slice(0, 3);

    upcomingBookings.value = pendingAndConfirmedBookings.map(booking => {
      const bookingDate = new Date(booking.booking_from);
      const formattedDate = bookingDate.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });

      const startTime = bookingDate.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit'
      });

      const endDate = new Date(booking.booking_to);
      const endTime = endDate.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit'
      });

      return {
        client: booking.user?.name || 'Client',
        service: booking.vendor_service?.service?.name || 'Service',
        date: formattedDate,
        time: `${startTime} - ${endTime}`
      };
    });
  }
};

interface Activity {
  icon: string;
  iconBg: string;
  iconBgDark: string;
  iconColor: string;
  iconColorDark: string;
  message: string;
  time: string;
}

const recentActivities = ref<Activity[]>([]);

const updateRecentActivities = () => {
  const activities: Activity[] = [];

  if (bookingsStore.bookings.length > 0) {
    const recentBookings = [...bookingsStore.bookings]
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
      .slice(0, 2);

    recentBookings.forEach(booking => {
      const createdDate = new Date(booking.created_at);
      const now = new Date();
      const diffTime = Math.abs(now.getTime() - createdDate.getTime());
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
      const diffHours = Math.floor(diffTime / (1000 * 60 * 60));

      let timeAgo;
      if (diffDays > 0) {
        timeAgo = `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
      } else {
        timeAgo = `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
      }

      activities.push({
        icon: 'heroicons:calendar',
        iconBg: 'bg-blue-100',
        iconBgDark: 'bg-blue-900',
        iconColor: 'text-blue-600',
        iconColorDark: 'text-blue-400',
        message: `${booking.status === 'pending' ? 'New' : 'Updated'} booking ${booking.status === 'approved' ? '(approved)' : booking.status === 'rejected' ? '(rejected)' : ''} from ${booking.user?.name || 'Client'} for ${booking.vendor_service?.service?.name || 'Service'}`,
        time: timeAgo
      });
    });
  }

  if (vendorStore.analytics?.ratings?.total_reviews && vendorStore.analytics.ratings.total_reviews > 0) {
    const totalReviews = vendorStore.analytics.ratings.total_reviews;
    const avgRating = vendorStore.analytics.ratings.average?.toFixed(1) || '0.0';

    activities.push({
      icon: 'heroicons:star',
      iconBg: 'bg-yellow-100',
      iconBgDark: 'bg-yellow-900',
      iconColor: 'text-yellow-600',
      iconColorDark: 'text-yellow-400',
      message: `You have ${totalReviews} review${totalReviews > 1 ? 's' : ''} with an average rating of ${avgRating}`,
      time: 'Overall'
    });
  }

  if (vendorStore.analytics?.profile_views?.total && vendorStore.analytics.profile_views.total > 0) {
    const viewCount = vendorStore.analytics.profile_views.total;
    const period = vendorStore.analytics.profile_views.period || 'Last 30 days';

    activities.push({
      icon: 'heroicons:eye',
      iconBg: 'bg-purple-100',
      iconBgDark: 'bg-purple-900',
      iconColor: 'text-purple-600',
      iconColorDark: 'text-purple-400',
      message: `Your profile has been viewed ${viewCount} time${viewCount > 1 ? 's' : ''}`,
      time: period
    });
  }

  if (vendorStore.analytics?.revenue?.total && vendorStore.analytics.revenue.total > 0) {
    const revenue = vendorStore.analytics.revenue.total;
    const formattedRevenue = revenue.toLocaleString('en-US', { style: 'currency', currency: 'MWK' });

    activities.push({
      icon: 'heroicons:banknotes',
      iconBg: 'bg-green-100',
      iconBgDark: 'bg-green-900',
      iconColor: 'text-green-600',
      iconColorDark: 'text-green-400',
      message: `Total revenue of ${formattedRevenue}`,
      time: 'All time'
    });
  }

  recentActivities.value = activities.slice(0, 4);
};

interface UpcomingBooking {
  client: string;
  service: string;
  date: string;
  time: string;
}

const upcomingBookings = ref<UpcomingBooking[]>([]);

onMounted(() => {
  loadVendorData();
});
</script>
