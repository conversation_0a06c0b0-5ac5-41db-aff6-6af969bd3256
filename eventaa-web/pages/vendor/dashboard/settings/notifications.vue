<template>
  <div class="dashboard-bg-main min-h-screen">
    <div class="p-4 sm:p-6">
      <div class="mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 class="text-2xl font-bold dashboard-text-primary">Notification Settings</h1>
            <p class="mt-1 text-sm dashboard-text-muted">Manage how you receive notifications</p>
          </div>
          <div class="mt-4 md:mt-0 flex space-x-3">
            <CorePrimaryButton
              text="Save Changes"
              @click="saveChanges"
              :loading="settingsStore.saving"
            />
          </div>
        </div>
      </div>

      <div v-if="settingsStore.loading" class="space-y-6">
        <div class="dashboard-bg-card dashboard-shadow overflow-hidden mb-6">
          <div class="px-6 py-4 dashboard-border border-b">
            <div class="h-6 w-48 bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
          </div>
          <div class="p-6 space-y-4">
            <div class="h-4 w-full bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
            <div class="space-y-4">
              <div v-for="i in 4" :key="i" class="flex items-start">
                <div class="h-5 w-5 bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
                <div class="ml-3 space-y-2 flex-1">
                  <div class="h-4 w-1/3 bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
                  <div class="h-3 w-2/3 bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="dashboard-bg-card dashboard-shadow overflow-hidden mb-6">
          <div class="px-6 py-4 dashboard-border border-b">
            <div class="h-6 w-48 bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
          </div>
          <div class="p-6 space-y-4">
            <div class="h-4 w-full bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
            <div class="space-y-4">
              <div v-for="i in 3" :key="i" class="flex items-start">
                <div class="h-5 w-5 bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
                <div class="ml-3 space-y-2 flex-1">
                  <div class="h-4 w-1/3 bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
                  <div class="h-3 w-2/3 bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="dashboard-bg-card dashboard-shadow overflow-hidden">
          <div class="px-6 py-4 dashboard-border border-b">
            <div class="h-6 w-48 bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
          </div>
          <div class="p-6 space-y-4">
            <div class="h-4 w-full bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
            <div class="h-10 w-full bg-gray-200 dark:bg-gray-700 animate-pulse mb-6"></div>
            <div class="space-y-4">
              <div v-for="i in 3" :key="i" class="flex items-start">
                <div class="h-5 w-5 bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
                <div class="ml-3 space-y-2 flex-1">
                  <div class="h-4 w-1/3 bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
                  <div class="h-3 w-2/3 bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template v-else>
        <div class="dashboard-bg-card dashboard-shadow overflow-hidden mb-6">
          <div class="px-6 py-4 dashboard-border border-b">
            <h3 class="text-lg font-medium dashboard-text-primary">Email Notifications</h3>
          </div>
          <div class="p-6">
            <p class="dashboard-text-muted text-sm mb-4">
              Choose which email notifications you'd like to receive.
            </p>
            <div class="space-y-4">
              <FormKit
                v-for="option in emailNotifications"
                :key="option.id"
                type="checkbox"
                :name="`email-${option.id}`"
                :label="option.label"
                :help="option.description"
                v-model="option.enabled"
                :classes="{
                  outer: 'flex items-start',
                  wrapper: 'flex items-center h-5',
                  input: 'focus:ring-red-500 h-4 w-4 dashboard-text-brand dashboard-border',
                  label: 'ml-3 text-sm font-medium dashboard-text-primary',
                  help: 'ml-3 mt-1 text-sm dashboard-text-muted'
                }"
              />
            </div>
          </div>
        </div>

        <div class="dashboard-bg-card dashboard-shadow overflow-hidden mb-6">
          <div class="px-6 py-4 dashboard-border border-b">
            <h3 class="text-lg font-medium dashboard-text-primary">Push Notifications</h3>
          </div>
          <div class="p-6">
            <p class="dashboard-text-muted text-sm mb-4">
              Choose which push notifications you'd like to receive on your devices.
            </p>
            <div class="space-y-4">
              <FormKit
                v-for="option in pushNotifications"
                :key="option.id"
                type="checkbox"
                :name="`push-${option.id}`"
                :label="option.label"
                :help="option.description"
                v-model="option.enabled"
                :classes="{
                  outer: 'flex items-start',
                  wrapper: 'flex items-center h-5',
                  input: 'focus:ring-red-500 h-4 w-4 dashboard-text-brand dashboard-border',
                  label: 'ml-3 text-sm font-medium dashboard-text-primary',
                  help: 'ml-3 mt-1 text-sm dashboard-text-muted'
                }"
              />
            </div>
          </div>
        </div>

        <div class="dashboard-bg-card dashboard-shadow overflow-hidden">
          <div class="px-6 py-4 dashboard-border border-b">
            <h3 class="text-lg font-medium dashboard-text-primary">SMS Notifications</h3>
          </div>
          <div class="p-6">
            <p class="dashboard-text-muted text-sm mb-4">
              Choose which SMS notifications you'd like to receive on your phone.
            </p>

            <div class="mb-6">
              <FormKit
                type="tel"
                name="phone"
                label="Phone Number for SMS"
                validation="required"
                prefixIcon="phone"
              />
            </div>

            <div class="space-y-4">
              <FormKit
                v-for="option in smsNotifications"
                :key="option.id"
                type="checkbox"
                :name="`sms-${option.id}`"
                :label="option.label"
                :help="option.description"
                v-model="option.enabled"
                :classes="{
                  outer: 'flex items-start',
                  wrapper: 'flex items-center h-5',
                  input: 'focus:ring-red-500 h-4 w-4 dashboard-text-brand dashboard-border',
                  label: 'ml-3 text-sm font-medium dashboard-text-primary',
                  help: 'ml-3 mt-1 text-sm dashboard-text-muted'
                }"
              />
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useVendorStore } from '@/store/vendor';
import { useVendorSettingsStore, type NotificationSetting } from '@/store/vendorSettings';

definePageMeta({
  layout: 'vendor-dashboard'
});

const vendorStore = useVendorStore();
const settingsStore = useVendorSettingsStore();
const { $toast }: any = useNuxtApp();

const phoneNumber = ref('');

const emailNotifications = computed(() =>
  settingsStore.notificationSettings.filter(setting => setting.type === 'email')
);
const pushNotifications = computed(() =>
  settingsStore.notificationSettings.filter(setting => setting.type === 'push')
);
const smsNotifications = computed(() =>
  settingsStore.notificationSettings.filter(setting => setting.type === 'sms')
);

const loadSettings = async () => {
  try {
    await settingsStore.fetchNotificationSettings();
    if (vendorStore.details?.phone) {
      phoneNumber.value = vendorStore.details.phone;
    }
  } catch (error) {
    console.error('Error loading notification settings:', error);
    $toast.error('Failed to load notification settings');
  }
};

const saveChanges = async () => {
  try {
    const allSettings = [
      ...emailNotifications.value,
      ...pushNotifications.value,
      ...smsNotifications.value
    ];

    const success = await settingsStore.saveNotificationSettings(allSettings);

    if (success) {
      if (vendorStore.details && phoneNumber.value !== vendorStore.details.phone) {
        await vendorStore.updateVendorDetails({
          phone: phoneNumber.value
        });
      }

      $toast.success('Notification settings saved successfully');
    } else {
      $toast.error('Failed to save notification settings');
    }
  } catch (error) {
    console.error('Error saving notification settings:', error);
    $toast.error('Failed to save notification settings');
  }
};

onMounted(async () => {
  if (!vendorStore.details) {
    await vendorStore.fetchVendorDetails();
  }

  await loadSettings();
});
</script>
