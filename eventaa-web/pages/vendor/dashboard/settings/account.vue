<template>
  <div class="dashboard-bg-main min-h-screen">
    <div class="p-4 sm:p-6">
      <div class="mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 class="text-2xl font-bold dashboard-text-primary">Account Settings</h1>
            <p class="mt-1 text-sm dashboard-text-muted">Manage your account preferences</p>
          </div>
          <div class="mt-4 md:mt-0">
            <CorePrimaryButton
              text="Save Changes"
              @click="saveChanges"
              :loading="isSaving"
            />
          </div>
        </div>
      </div>

      <div v-if="isLoading" class="dashboard-bg-card dashboard-shadow overflow-hidden mb-6">
        <div class="px-6 py-4 dashboard-border border-b">
          <div class="h-6 w-48 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
        </div>
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div v-for="i in 6" :key="i" class="space-y-2">
              <div class="h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              <div class="h-10 w-full bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="dashboard-bg-card dashboard-shadow overflow-hidden mb-6">
        <div class="px-6 py-4 dashboard-border border-b">
          <h3 class="text-lg font-medium dashboard-text-primary">Account Information</h3>
        </div>
        <div class="p-6">
          <FormKit type="form" id="accountForm" :actions="false" v-model="accountInfo" @submit="saveChanges">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormKit
                type="email"
                name="email"
                label="Email Address"
                validation="required|email"
                v-model="accountInfo.email"
                prefixIcon="email"
              />

              <FormKit
                type="text"
                name="username"
                label="Username"
                validation="required"
                v-model="accountInfo.username"
              />

              <FormKit
                type="tel"
                name="phone"
                label="Phone Number"
                validation="required"
                v-model="accountInfo.phone"
                prefixIcon="phone"
              />
            </div>
          </FormKit>
        </div>
      </div>

      <div class="dashboard-bg-card dashboard-shadow overflow-hidden">
        <div class="px-6 py-4 dashboard-border border-b">
          <h3 class="text-lg font-medium dashboard-text-primary">Additional Settings</h3>
        </div>
        <div class="p-6">
          <div class="space-y-4">
            <div class="flex items-center justify-between p-4 dashboard-border dashboard-transition hover:dashboard-bg-hover">
              <div>
                <h4 class="text-sm font-medium dashboard-text-primary">Password & Security</h4>
                <p class="mt-1 text-sm dashboard-text-muted">Manage your password, two-factor authentication, and active sessions</p>
              </div>
              <CorePrimaryButton
                text="Manage"
                @click="navigateToSecurity"
                size="small"
              />
            </div>

            <div class="flex items-center justify-between p-4 dashboard-border dashboard-transition hover:dashboard-bg-hover">
              <div>
                <h4 class="text-sm font-medium dashboard-text-primary">Notification Preferences</h4>
                <p class="mt-1 text-sm dashboard-text-muted">Manage your email, push, and SMS notification settings</p>
              </div>
              <CorePrimaryButton
                text="Manage"
                @click="navigateToNotifications"
                size="small"
              />
            </div>

            <div class="flex items-center justify-between p-4 dashboard-border dashboard-transition hover:dashboard-bg-hover">
              <div>
                <h4 class="text-sm font-medium dashboard-text-primary">Billing & Payments</h4>
                <p class="mt-1 text-sm dashboard-text-muted">Manage your payment methods, subscription, and billing history</p>
              </div>
              <CorePrimaryButton
                text="Manage"
                @click="navigateToPayments"
                size="small"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'nuxt/app';
import { useVendorStore } from '@/store/vendor';
import { useAuthStore } from '@/store/auth';
import { ENDPOINTS } from '@/utils/api';

definePageMeta({
  layout: 'vendor-dashboard'
});

const router = useRouter();
const httpClient = useHttpClient();
const vendorStore = useVendorStore();
const authStore = useAuthStore();
const { $toast } = useNuxtApp();

const isLoading = ref(true);
const isSaving = ref(false);

const accountInfo = reactive({
  email: '',
  username: '',
  phone: ''
});

const fetchVendorProfile = async () => {
  try {
    isLoading.value = true;
    if (!vendorStore.details) {
      await vendorStore.fetchVendorDetails();
    }
    const userData = authStore.user;
    if (userData && vendorStore.details) {
      accountInfo.email = userData.email || '';
      accountInfo.username = userData.username || '';
      accountInfo.phone = vendorStore.details.phone || '';
    }
  } catch (error) {
    $toast.error('Failed to load account information');
    console.error('Error fetching vendor profile:', error);
  } finally {
    isLoading.value = false;
  }
};

const saveChanges = async () => {
  try {
    isSaving.value = true;
    const profileData = {
      email: accountInfo.email,
      phone: accountInfo.phone
    };
    await httpClient.post(ENDPOINTS.PROFILE.UPDATE, profileData);
    if (vendorStore.details) {
      const vendorData = {
        phone: accountInfo.phone
      };
      await vendorStore.updateVendorDetails(vendorData);
    }
    $toast.success('Account information saved successfully!');
    await authStore.fetchUser();
  } catch (error) {
    $toast.error('Failed to save account information');
    console.error('Error saving account information:', error);
  } finally {
    isSaving.value = false;
  }
};

const navigateToSecurity = () => {
  router.push('/vendor/dashboard/settings/security');
};

const navigateToNotifications = () => {
  router.push('/vendor/dashboard/settings/notifications');
};

const navigateToPayments = () => {
  router.push('/vendor/dashboard/settings/payments');
};

onMounted(() => {
  fetchVendorProfile();
});
</script>
