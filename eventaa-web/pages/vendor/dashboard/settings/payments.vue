<template>
  <div class="dashboard-bg-main min-h-screen">
    <div class="p-4 sm:p-6">
      <div class="mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 class="text-2xl font-bold dashboard-text-primary">Payment Settings</h1>
            <p class="mt-1 text-sm dashboard-text-muted">Manage your payment methods and billing information</p>
          </div>
          <div class="mt-4 md:mt-0 flex space-x-3">
            <CorePrimaryButton
              text="Save Changes"
              @click="saveChanges"
              :loading="isSaving"
            />
          </div>
        </div>
      </div>

      <div class="dashboard-bg-card dashboard-shadow overflow-hidden mb-6">
        <div class="px-6 py-4 dashboard-border border-b flex justify-between items-center">
          <h3 class="text-lg font-medium dashboard-text-primary">Payment Methods</h3>
          <CorePrimaryButton
            text="Add Payment Method"
            startIcon="heroicons:plus"
            size="small"
            @click="addPaymentMethod"
          />
        </div>
        <div class="p-6">
          <!-- Loading skeleton -->
          <div v-if="paymentsStore.loading" class="space-y-4">
            <div v-for="i in 2" :key="i" class="dashboard-border p-4 flex items-center justify-between animate-pulse">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="h-8 w-8 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                </div>
                <div class="ml-4">
                  <div class="h-4 w-40 bg-gray-200 dark:bg-gray-700 rounded"></div>
                  <div class="mt-2 h-3 w-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
                </div>
              </div>
              <div class="flex space-x-2">
                <div class="h-6 w-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div class="h-6 w-12 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div class="h-6 w-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
            </div>
          </div>

          <!-- Actual content -->
          <div v-else class="space-y-4">
            <div v-for="method in paymentMethods" :key="method.id"
              class="dashboard-border p-4 flex items-center justify-between hover:dashboard-bg-hover dashboard-transition">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <img v-if="method.payment_gateway?.logo" :src="method.payment_gateway.logo" class="h-8 w-8 object-contain" />
                  <Icon v-else icon="heroicons:credit-card" class="h-8 w-8 dashboard-text-light" />
                </div>
                <div class="ml-4">
                  <h4 class="text-sm font-medium dashboard-text-primary">{{ method.payment_gateway?.name }}</h4>
                  <div class="mt-1 text-xs dashboard-text-muted">
                    <span v-if="method.account_details?.account_number">
                      Account ending in {{ method.account_details.account_number.slice(-4) }}
                    </span>
                    <span v-else-if="method.account_details?.phone_number">
                      {{ method.account_details.phone_number }}
                    </span>
                    <span v-if="method.is_default" class="mx-2">•</span>
                    <span v-if="method.is_default" class="dashboard-text-brand">Default</span>
                  </div>
                </div>
              </div>
              <div class="flex space-x-2">
                <button v-if="!method.is_default"
                  class="text-sm dashboard-text-brand hover:text-red-500 dashboard-transition"
                  @click="setDefaultPaymentMethod(method.id)">
                  Set as default
                </button>
                <button
                  class="text-sm dashboard-text-brand hover:text-red-500 dashboard-transition"
                  @click="editPaymentMethod()">
                  Edit
                </button>
                <button
                  class="text-sm text-red-600 hover:text-red-500 dashboard-transition"
                  @click="removePaymentMethod(method.id)">
                  Remove
                </button>
              </div>
            </div>

            <div v-if="paymentMethods.length === 0" class="text-center py-6 dashboard-text-muted">
              No payment methods added yet. Click "Add Payment Method" to get started.
            </div>
          </div>
        </div>
      </div>

      <TransitionRoot appear :show="isAddingPaymentMethod" as="template">
        <Dialog as="div" @close="closeAddPaymentMethodDialog" class="relative z-10">
          <TransitionChild
            as="template"
            enter="duration-300 ease-out"
            enter-from="opacity-0"
            enter-to="opacity-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100"
            leave-to="opacity-0"
          >
            <div class="fixed inset-0 bg-black bg-opacity-25 dark:bg-opacity-40" />
          </TransitionChild>

          <div class="fixed inset-0 overflow-y-auto">
            <div class="flex min-h-full items-center justify-center p-4 text-center">
              <TransitionChild
                as="template"
                enter="duration-300 ease-out"
                enter-from="opacity-0 scale-95"
                enter-to="opacity-100 scale-100"
                leave="duration-200 ease-in"
                leave-from="opacity-100 scale-100"
                leave-to="opacity-0 scale-95"
              >
                <DialogPanel class="w-full max-w-md transform overflow-hidden dashboard-bg-card p-6 text-left align-middle shadow-xl transition-all rounded-lg">
                  <DialogTitle as="h3" class="text-lg font-medium leading-6 dashboard-text-primary mb-4">
                    Add Payment Method
                  </DialogTitle>

                  <div v-if="!selectedGateway">
                    <p class="text-sm dashboard-text-muted mb-4">Select a payment gateway:</p>
                    <div class="grid grid-cols-1 gap-3">
                      <button
                        v-for="gateway in paymentGateways"
                        :key="gateway.id"
                        @click="selectGateway(gateway.id)"
                        class="flex items-center p-3 dashboard-border rounded-lg hover:dashboard-bg-hover dashboard-transition"
                        :disabled="!gateway.is_active"
                      >
                        <img v-if="gateway.logo" :src="gateway.logo" class="h-8 w-8 object-contain mr-3" />
                        <div class="flex-1">
                          <h4 class="text-sm font-medium dashboard-text-primary">{{ gateway.name }}</h4>
                          <p class="text-xs dashboard-text-muted mt-1">{{ gateway.description }}</p>
                        </div>
                        <div v-if="!gateway.is_active" class="text-xs text-red-500">Unavailable</div>
                      </button>
                    </div>
                  </div>

                  <div v-else>
                    <div class="mb-4">
                      <button @click="selectedGateway = null" class="text-sm dashboard-text-brand flex items-center">
                        <Icon icon="heroicons:arrow-left" class="h-4 w-4 mr-1" />
                        Back to payment gateways
                      </button>
                    </div>

                    <h4 class="text-sm font-medium dashboard-text-primary mb-4">
                      {{ selectedGatewayDetails?.name }} Details
                    </h4>

                    <!-- PayChangu Form -->
                    <div v-if="selectedGatewayDetails?.slug === 'paychangu'" class="space-y-4">
                      <div>
                        <label class="block text-sm font-medium dashboard-text-secondary mb-1">Account Name</label>
                        <input
                          type="text"
                          v-model="paymentMethodForm.accountName"
                          class="block w-full dashboard-border dashboard-input dashboard-focus-primary py-2 px-3 sm:text-sm"
                          placeholder="Enter account name"
                        />
                      </div>
                      <div>
                        <label class="block text-sm font-medium dashboard-text-secondary mb-1">Account Number</label>
                        <input
                          type="text"
                          v-model="paymentMethodForm.accountNumber"
                          class="block w-full dashboard-border dashboard-input dashboard-focus-primary py-2 px-3 sm:text-sm"
                          placeholder="Enter account number"
                        />
                      </div>
                    </div>

                    <!-- Mobile Money Form -->
                    <div v-else-if="selectedGatewayDetails?.slug === 'mobile-money'" class="space-y-4">
                      <div>
                        <label class="block text-sm font-medium dashboard-text-secondary mb-1">Phone Number</label>
                        <input
                          type="tel"
                          v-model="paymentMethodForm.phoneNumber"
                          class="block w-full dashboard-border dashboard-input dashboard-focus-primary py-2 px-3 sm:text-sm"
                          placeholder="Enter phone number"
                        />
                      </div>
                      <div>
                        <label class="block text-sm font-medium dashboard-text-secondary mb-1">Provider</label>
                        <select
                          v-model="paymentMethodForm.provider"
                          class="block w-full dashboard-border dashboard-input dashboard-focus-primary py-2 px-3 sm:text-sm"
                        >
                          <option value="">Select provider</option>
                          <option value="Airtel Money">Airtel Money</option>
                          <option value="TNM Mpamba">TNM Mpamba</option>
                        </select>
                      </div>
                    </div>

                    <!-- Bank Transfer Form -->
                    <div v-else-if="selectedGatewayDetails?.slug === 'bank-transfer'" class="space-y-4">
                      <div>
                        <label class="block text-sm font-medium dashboard-text-secondary mb-1">Account Name</label>
                        <input
                          type="text"
                          v-model="paymentMethodForm.accountName"
                          class="block w-full dashboard-border dashboard-input dashboard-focus-primary py-2 px-3 sm:text-sm"
                          placeholder="Enter account name"
                        />
                      </div>
                      <div>
                        <label class="block text-sm font-medium dashboard-text-secondary mb-1">Account Number</label>
                        <input
                          type="text"
                          v-model="paymentMethodForm.accountNumber"
                          class="block w-full dashboard-border dashboard-input dashboard-focus-primary py-2 px-3 sm:text-sm"
                          placeholder="Enter account number"
                        />
                      </div>
                      <div>
                        <label class="block text-sm font-medium dashboard-text-secondary mb-1">Bank Name</label>
                        <input
                          type="text"
                          v-model="paymentMethodForm.bankName"
                          class="block w-full dashboard-border dashboard-input dashboard-focus-primary py-2 px-3 sm:text-sm"
                          placeholder="Enter bank name"
                        />
                      </div>
                      <div>
                        <label class="block text-sm font-medium dashboard-text-secondary mb-1">Branch</label>
                        <input
                          type="text"
                          v-model="paymentMethodForm.branch"
                          class="block w-full dashboard-border dashboard-input dashboard-focus-primary py-2 px-3 sm:text-sm"
                          placeholder="Enter branch"
                        />
                      </div>
                    </div>
                  </div>

                  <div class="mt-6 flex justify-end space-x-3">
                    <button
                      type="button"
                      class="inline-flex justify-center rounded-md border dashboard-border py-2 px-4 text-sm font-medium dashboard-text-primary dashboard-focus-primary"
                      @click="closeAddPaymentMethodDialog"
                    >
                      Cancel
                    </button>
                    <button
                      v-if="selectedGateway"
                      type="button"
                      class="inline-flex justify-center rounded-md border border-transparent bg-red-600 py-2 px-4 text-sm font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                      @click="submitPaymentMethod"
                      :disabled="paymentsStore.saving"
                    >
                      <span v-if="paymentsStore.saving">Saving...</span>
                      <span v-else>Add Payment Method</span>
                    </button>
                  </div>
                </DialogPanel>
              </TransitionChild>
            </div>
          </div>
        </Dialog>
      </TransitionRoot>

      <div class="dashboard-bg-card dashboard-shadow overflow-hidden mb-6">
        <div class="px-6 py-4 dashboard-border border-b">
          <h3 class="text-lg font-medium dashboard-text-primary">Billing Information</h3>
        </div>
        <div class="p-6">
          <FormKit type="form" id="billingForm" v-model="billingInfo" @submit="saveChanges">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormKit
                type="text"
                name="name"
                label="Name"
                validation="required"
                :classes="{
                  outer: 'mb-0',
                  label: 'block text-sm font-medium dashboard-text-secondary mb-1',
                  input: 'block w-full dashboard-border dashboard-input dashboard-focus-primary py-2 px-3 sm:text-sm'
                }"
              />

              <FormKit
                type="email"
                name="email"
                label="Email"
                validation="required|email"
                :classes="{
                  outer: 'mb-0',
                  label: 'block text-sm font-medium dashboard-text-secondary mb-1',
                  input: 'block w-full dashboard-border dashboard-input dashboard-focus-primary py-2 px-3 sm:text-sm'
                }"
              />

              <FormKit
                type="text"
                name="address"
                label="Address"
                validation="required"
                :classes="{
                  outer: 'mb-0',
                  label: 'block text-sm font-medium dashboard-text-secondary mb-1',
                  input: 'block w-full dashboard-border dashboard-input dashboard-focus-primary py-2 px-3 sm:text-sm'
                }"
              />

              <FormKit
                type="text"
                name="city"
                label="City"
                validation="required"
                :classes="{
                  outer: 'mb-0',
                  label: 'block text-sm font-medium dashboard-text-secondary mb-1',
                  input: 'block w-full dashboard-border dashboard-input dashboard-focus-primary py-2 px-3 sm:text-sm'
                }"
              />

              <FormKit
                type="text"
                name="state"
                label="State / Province"
                validation="required"
                :classes="{
                  outer: 'mb-0',
                  label: 'block text-sm font-medium dashboard-text-secondary mb-1',
                  input: 'block w-full dashboard-border dashboard-input dashboard-focus-primary py-2 px-3 sm:text-sm'
                }"
              />

              <FormKit
                type="text"
                name="zip"
                label="ZIP / Postal Code"
                validation="required"
                :classes="{
                  outer: 'mb-0',
                  label: 'block text-sm font-medium dashboard-text-secondary mb-1',
                  input: 'block w-full dashboard-border dashboard-input dashboard-focus-primary py-2 px-3 sm:text-sm'
                }"
              />

              <FormKit
                type="select"
                name="country"
                label="Country"
                :options="['United States', 'Canada', 'United Kingdom', 'Australia']"
                validation="required"
                :classes="{
                  outer: 'mb-0',
                  label: 'block text-sm font-medium dashboard-text-secondary mb-1',
                  input: 'block w-full dashboard-border dashboard-input dashboard-focus-primary py-2 px-3 sm:text-sm'
                }"
              />

              <FormKit
                type="tel"
                name="phone"
                label="Phone"
                validation="required"
                :classes="{
                  outer: 'mb-0',
                  label: 'block text-sm font-medium dashboard-text-secondary mb-1',
                  input: 'block w-full dashboard-border dashboard-input dashboard-focus-primary py-2 px-3 sm:text-sm'
                }"
              />
            </div>
          </FormKit>
        </div>
      </div>




    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';

import { useVendorStore } from '@/store/vendor';
import { useVendorPaymentsStore } from '@/store/vendorPayments';

import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue';

definePageMeta({
  layout: 'vendor-dashboard'
});


const vendorStore = useVendorStore();
const paymentsStore = useVendorPaymentsStore();

const nuxtApp = useNuxtApp();
const $toast = nuxtApp.$toast as {
  success: (message: string) => void;
  error: (message: string) => void;
  info: (message: string) => void;
  warning: (message: string) => void;
};

const isSaving = ref<boolean>(false);
const isAddingPaymentMethod = ref<boolean>(false);
const selectedGateway = ref<number | null>(null);
const paymentMethodForm = reactive({
  accountName: '',
  accountNumber: '',
  bankName: '',
  branch: '',
  phoneNumber: '',
  provider: ''
});

// Computed properties
const paymentMethods = computed(() => paymentsStore.paymentMethods);
const paymentGateways = computed(() => paymentsStore.paymentGateways);
const selectedGatewayDetails = computed(() => {
  if (!selectedGateway.value) return null;
  return paymentGateways.value.find(gateway => gateway.id === selectedGateway.value);
});



// Load data
const loadData = async (): Promise<void> => {
  if (!vendorStore.details) {
    await vendorStore.fetchVendorDetails();
  }

  await Promise.all([
    paymentsStore.fetchPaymentGateways()
  ]);

  if (vendorStore.details?.id) {
    await paymentsStore.fetchPaymentMethods(vendorStore.details.id);
  }
};

// Payment method functions
const addPaymentMethod = (): void => {
  isAddingPaymentMethod.value = true;
};

const closeAddPaymentMethodDialog = (): void => {
  isAddingPaymentMethod.value = false;
  selectedGateway.value = null;

  // Reset form
  Object.keys(paymentMethodForm).forEach(key => {
    paymentMethodForm[key as keyof typeof paymentMethodForm] = '';
  });
};

const selectGateway = (gatewayId: number): void => {
  selectedGateway.value = gatewayId;
};

const submitPaymentMethod = async (): Promise<void> => {
  if (!selectedGateway.value || !vendorStore.details?.id) {
    $toast.error('Please select a payment gateway');
    return;
  }

  const gateway = paymentGateways.value.find(g => g.id === selectedGateway.value);
  if (!gateway) {
    $toast.error('Invalid payment gateway selected');
    return;
  }

  let accountDetails: any = {};

  // Build account details based on gateway type
  if (gateway.slug === 'paychangu') {
    if (!paymentMethodForm.accountName || !paymentMethodForm.accountNumber) {
      $toast.error('Please fill in all required fields');
      return;
    }

    accountDetails = {
      account_name: paymentMethodForm.accountName,
      account_number: paymentMethodForm.accountNumber
    };
  } else if (gateway.slug === 'mobile-money') {
    if (!paymentMethodForm.phoneNumber || !paymentMethodForm.provider) {
      $toast.error('Please fill in all required fields');
      return;
    }

    accountDetails = {
      phone_number: paymentMethodForm.phoneNumber,
      provider: paymentMethodForm.provider
    };
  } else if (gateway.slug === 'bank-transfer') {
    if (!paymentMethodForm.accountName || !paymentMethodForm.accountNumber ||
        !paymentMethodForm.bankName || !paymentMethodForm.branch) {
      $toast.error('Please fill in all required fields');
      return;
    }

    accountDetails = {
      account_name: paymentMethodForm.accountName,
      account_number: paymentMethodForm.accountNumber,
      bank_name: paymentMethodForm.bankName,
      branch: paymentMethodForm.branch
    };
  }

  try {
    const success = await paymentsStore.addPaymentMethod(
      vendorStore.details.id,
      selectedGateway.value,
      accountDetails
    );

    if (success) {
      $toast.success('Payment method added successfully');
      closeAddPaymentMethodDialog();
    } else {
      $toast.error('Failed to add payment method');
    }
  } catch (error) {
    console.error('Error adding payment method:', error);
    $toast.error('An error occurred while adding the payment method');
  }
};

const setDefaultPaymentMethod = async (methodId: number): Promise<void> => {
  try {
    if (!vendorStore.details?.id) {
      $toast.error('Vendor ID is required');
      return;
    }

    const success = await paymentsStore.setDefaultPaymentMethod(methodId, vendorStore.details.id);

    if (success) {
      $toast.success('Default payment method updated');
    } else {
      $toast.error('Failed to update default payment method');
    }
  } catch (error) {
    console.error('Error setting default payment method:', error);
    $toast.error('An error occurred while updating the default payment method');
  }
};

const editPaymentMethod = (): void => {
  $toast.info('Edit payment method functionality coming soon');
};

const removePaymentMethod = async (methodId: number): Promise<void> => {
  try {
    if (!vendorStore.details?.id) {
      $toast.error('Vendor ID is required');
      return;
    }

    const confirmed = await new Promise<boolean>((resolve) => {
      if (confirm('Are you sure you want to remove this payment method?')) {
        resolve(true);
      } else {
        resolve(false);
      }
    });

    if (confirmed) {
      const success = await paymentsStore.removePaymentMethod(methodId, vendorStore.details.id);

      if (success) {
        $toast.success('Payment method removed successfully');
      } else {
        $toast.error('Failed to remove payment method');
      }
    }
  } catch (error) {
    console.error('Error removing payment method:', error);
    $toast.error('An error occurred while removing the payment method');
  }
};



// Billing information
const billingInfo = reactive({
  name: '',
  email: '',
  address: '',
  city: '',
  state: '',
  zip: '',
  country: '',
  phone: ''
});

// Helper functions
const formatDate = (dateString: string): string => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const formatCurrency = (amount: number, currency: string = 'MWK'): string => {
  // Default to MWK if no currency provided
  const currencyCode = currency || 'MWK';

  // Format based on currency
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currencyCode,
    // Use compact notation for large numbers
    notation: amount > 10000 ? 'compact' : 'standard'
  }).format(amount);
};

const getStatusClass = (status: string): string => {
  const classes = {
    'Paid': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    'Pending': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
    'Failed': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
  };
  return classes[status as keyof typeof classes] || '';
};

const downloadInvoice = (id: string | number): void => {
  $toast.info(`Downloading invoice ${id}...`);
};

// Save all changes
const saveChanges = async (): Promise<void> => {
  isSaving.value = true;

  try {
    // Save billing info
    await vendorStore.updateVendorDetails({
      business_email: billingInfo.email,
      phone: billingInfo.phone,
      location: `${billingInfo.address}, ${billingInfo.city}, ${billingInfo.state}, ${billingInfo.zip}, ${billingInfo.country}`
    });

    $toast.success('Payment settings saved successfully');
  } catch (error) {
    console.error('Error saving payment settings:', error);
    $toast.error('Failed to save payment settings');
  } finally {
    isSaving.value = false;
  }
};

// Initialize data
onMounted(async () => {
  await loadData();

  // Set billing info from vendor details if available
  if (vendorStore.details) {
    billingInfo.name = vendorStore.details.name || '';
    billingInfo.email = vendorStore.details.business_email || '';
    billingInfo.phone = vendorStore.details.phone || '';

    // Try to parse location
    if (vendorStore.details.location) {
      const locationParts = vendorStore.details.location.split(',').map(part => part.trim());
      if (locationParts.length >= 5) {
        billingInfo.address = locationParts[0] || '';
        billingInfo.city = locationParts[1] || '';
        billingInfo.state = locationParts[2] || '';
        billingInfo.zip = locationParts[3] || '';
        billingInfo.country = locationParts[4] || '';
      }
    }
  }
});
</script>
