<template>
  <div class="bg-gray-50 min-h-screen">
    <div class="p-4 sm:p-6">
      <!-- Header -->
      <div class="mb-6">
        <div
          class="flex flex-col md:flex-row md:items-center md:justify-between"
        >
          <div>
            <h1 class="text-2xl font-bold text-gray-900">Reviews</h1>
            <p class="mt-1 text-sm text-gray-500">
              Manage and respond to client reviews
            </p>
          </div>
          <div class="mt-4 md:mt-0">
            <div class="relative">
              <select
                v-model="filter"
                @change="handleFilterChange(filter)"
                class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="all">All Reviews</option>
                <option value="positive">Positive (4-5 stars)</option>
                <option value="neutral">Neutral (3 stars)</option>
                <option value="negative">Negative (1-2 stars)</option>
                <option value="pending">Pending Response</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white shadow-sm overflow-hidden mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Rating Overview</h3>
        </div>

        <div v-if="loadingStats" class="p-6">
          <div class="flex flex-col md:flex-row">
            <div
              class="md:w-1/4 mb-6 md:mb-0 flex flex-col items-center justify-center"
            >
              <div class="h-12 w-16 bg-gray-200 animate-pulse"></div>
              <div class="flex items-center mt-2 space-x-1">
                <div
                  v-for="i in 5"
                  :key="i"
                  class="h-5 w-5 bg-gray-200 animate-pulse rounded-full"
                ></div>
              </div>
              <div class="h-4 w-32 bg-gray-200 animate-pulse mt-2"></div>
            </div>
            <div class="md:w-3/4 md:pl-6">
              <div class="space-y-4">
                <div v-for="i in 5" :key="i" class="flex items-center">
                  <div class="w-12 h-4 bg-gray-200 animate-pulse mr-4"></div>
                  <div class="flex-1">
                    <div class="bg-gray-200 rounded-full h-2"></div>
                  </div>
                  <div class="w-12 h-4 bg-gray-200 animate-pulse ml-4"></div>
                </div>
              </div>
              <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
                <div v-for="i in 4" :key="i" class="bg-gray-50 p-3">
                  <div class="h-4 w-20 bg-gray-200 animate-pulse"></div>
                  <div class="mt-2 h-6 w-12 bg-gray-200 animate-pulse"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="p-6">
          <div class="flex flex-col md:flex-row">
            <div
              class="md:w-1/4 mb-6 md:mb-0 flex flex-col items-center justify-center"
            >
              <div class="text-5xl font-bold text-gray-900">
                {{ averageRating }}
              </div>
              <div class="flex items-center mt-2">
                <Icon
                  v-for="i in 5"
                  :key="i"
                  icon="heroicons:star"
                  :class="[
                    i <= Math.round(ratingStats.average)
                      ? 'text-yellow-400'
                      : 'text-gray-300',
                    'h-5 w-5',
                  ]"
                />
              </div>
              <div class="text-sm text-gray-500 mt-2">
                Based on {{ ratingStats.total }} reviews
              </div>
            </div>
            <div class="md:w-3/4 md:pl-6">
              <div class="space-y-2">
                <div v-for="i in 5" :key="i" class="flex items-center">
                  <div class="w-12 text-sm text-gray-600 text-right pr-4">
                    {{ 6 - i }} stars
                  </div>
                  <div class="flex-1">
                    <div class="bg-gray-200 rounded-full h-2">
                      <div
                        class="bg-yellow-400 rounded-full h-2"
                        :style="`width: ${getRatingPercentage(6 - i)}%`"
                      ></div>
                    </div>
                  </div>
                  <div class="w-12 text-sm text-gray-600 pl-4">
                    {{
                      ratingStats.distribution[
                        (6 - Number(i)) as 1 | 2 | 3 | 4 | 5
                      ]
                    }}
                  </div>
                </div>
              </div>
              <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
                <div class="bg-gray-50 p-3">
                  <div class="text-sm font-medium text-gray-500">Service</div>
                  <div class="mt-1 flex items-center">
                    <span class="text-xl font-bold text-gray-900 mr-2">{{
                      ratingStats.categories?.service.toFixed(1)
                    }}</span>
                    <Icon
                      icon="heroicons:star"
                      class="h-5 w-5 text-yellow-400"
                    />
                  </div>
                </div>
                <div class="bg-gray-50 p-3">
                  <div class="text-sm font-medium text-gray-500">Value</div>
                  <div class="mt-1 flex items-center">
                    <span class="text-xl font-bold text-gray-900 mr-2">{{
                      ratingStats.categories?.value.toFixed(1)
                    }}</span>
                    <Icon
                      icon="heroicons:star"
                      class="h-5 w-5 text-yellow-400"
                    />
                  </div>
                </div>
                <div class="bg-gray-50 p-3">
                  <div class="text-sm font-medium text-gray-500">Quality</div>
                  <div class="mt-1 flex items-center">
                    <span class="text-xl font-bold text-gray-900 mr-2">{{
                      ratingStats.categories?.quality.toFixed(1)
                    }}</span>
                    <Icon
                      icon="heroicons:star"
                      class="h-5 w-5 text-yellow-400"
                    />
                  </div>
                </div>
                <div class="bg-gray-50 p-3">
                  <div class="text-sm font-medium text-gray-500">
                    Communication
                  </div>
                  <div class="mt-1 flex items-center">
                    <span class="text-xl font-bold text-gray-900 mr-2">{{
                      ratingStats.categories?.communication.toFixed(1)
                    }}</span>
                    <Icon
                      icon="heroicons:star"
                      class="h-5 w-5 text-yellow-400"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white shadow-sm overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">All Reviews</h3>
        </div>

        <div v-if="loading" class="divide-y divide-gray-200">
          <div v-for="i in 3" :key="i" class="p-6">
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <div
                  class="h-10 w-10 rounded-full bg-gray-200 animate-pulse"
                ></div>
              </div>
              <div class="ml-4 flex-1">
                <div class="flex items-center justify-between">
                  <div class="h-4 w-32 bg-gray-200 animate-pulse rounded"></div>
                  <div class="h-4 w-24 bg-gray-200 animate-pulse rounded"></div>
                </div>
                <div class="flex items-center mt-2">
                  <div class="flex space-x-1">
                    <div
                      v-for="j in 5"
                      :key="j"
                      class="h-4 w-4 bg-gray-200 animate-pulse rounded-full"
                    ></div>
                  </div>
                  <div
                    class="ml-2 h-4 w-32 bg-gray-200 animate-pulse rounded"
                  ></div>
                </div>
                <div
                  class="mt-3 h-4 w-full bg-gray-200 animate-pulse rounded"
                ></div>
                <div
                  class="mt-2 h-4 w-3/4 bg-gray-200 animate-pulse rounded"
                ></div>
              </div>
            </div>
          </div>
        </div>

        <div v-else-if="reviews.length === 0" class="p-10 text-center">
          <Icon
            icon="heroicons:chat-bubble-left-right"
            class="h-12 w-12 mx-auto text-gray-400"
          />
          <h3 class="mt-2 text-sm font-medium text-gray-900">No reviews yet</h3>
          <p class="mt-1 text-sm text-gray-500">
            You haven't received any reviews yet. Reviews will appear here once
            clients rate your services.
          </p>
        </div>

        <div v-else class="divide-y divide-gray-200">
          <div v-for="review in reviews" :key="review.id" class="p-6">
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <img
                  class="h-10 w-10 rounded-full"
                  :src="
                    review.user?.avatar
                      ? `${runtimeConfig.public.baseUrl}storage/avatars/${review.user.avatar}`
                      : `https://ui-avatars.com/api/?name=${encodeURIComponent(
                          review.user?.name || 'User'
                        )}&background=random`
                  "
                  :alt="review.user?.name || 'User'"
                />
              </div>
              <div class="ml-4 flex-1">
                <div class="flex items-center justify-between">
                  <h4 class="text-sm font-medium text-gray-900">
                    {{ review.user?.name }}
                  </h4>
                  <p class="text-sm text-gray-500">
                    {{ formatDate(review.created_at) }}
                  </p>
                </div>
                <div class="flex items-center mt-1">
                  <div class="flex">
                    <Icon
                      v-for="i in 5"
                      :key="i"
                      icon="heroicons:star"
                      :class="
                        i <= review.rating ? 'text-yellow-400' : 'text-gray-300'
                      "
                      class="h-4 w-4"
                    />
                  </div>
                  <span class="ml-2 text-sm text-gray-500">
                    {{
                      vendorStore.vendor?.services?.find(
                        (s) => s.id === review?.vendor_service_id
                      )?.service?.name || "General Service"
                    }}
                  </span>
                </div>
                <p class="mt-2 text-sm text-gray-700">{{ review.comment }}</p>

                <div v-if="review.response" class="mt-4 bg-gray-50 p-4">
                  <div class="flex items-start">
                    <div class="flex-shrink-0">
                      <img
                        class="h-8 w-8 rounded-full"
                        :src="
                          vendorStore.vendor?.logo
                            ? `${runtimeConfig.public.baseUrl}storage/${vendorStore.vendor.logo}`
                            : `https://ui-avatars.com/api/?name=Vendor+User&background=random`
                        "
                        alt="Vendor"
                      />
                    </div>
                    <div class="ml-3">
                      <h5 class="text-sm font-medium text-gray-900">
                        Your Response
                      </h5>
                      <p class="mt-1 text-sm text-gray-700">
                        {{ review.response }}
                      </p>
                      <p class="mt-1 text-xs text-gray-500">
                        {{
                          review.response_date
                            ? formatDate(review.response_date)
                            : ""
                        }}
                      </p>
                    </div>
                  </div>
                </div>

                <div v-else class="mt-4">
                  <div v-if="replyingTo === review.id">
                    <textarea
                      v-model="responseText"
                      rows="3"
                      class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300"
                      placeholder="Write your response here..."
                    ></textarea>
                    <div class="mt-2 flex justify-end space-x-2">
                      <button
                        @click="replyingTo = null"
                        class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        Cancel
                      </button>
                      <button
                        @click="submitResponse(review.id)"
                        :disabled="submittingResponse"
                        class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        <Icon
                          v-if="submittingResponse"
                          icon="heroicons:arrow-path"
                          class="animate-spin -ml-0.5 mr-2 h-4 w-4"
                        />
                        Submit Response
                      </button>
                    </div>
                  </div>
                  <button
                    v-else
                    @click="replyingTo = review.id"
                    class="text-sm text-blue-600 hover:text-blue-500"
                  >
                    Reply to this review
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div
          v-if="!loading && reviews.length > 0"
          class="px-6 py-3 bg-gray-50 flex items-center justify-between"
        >
          <div class="flex-1 flex justify-between sm:hidden">
            <button
              @click="goToPage(pagination.currentPage - 1)"
              :disabled="pagination.currentPage === 1"
              :class="
                pagination.currentPage === 1
                  ? 'opacity-50 cursor-not-allowed'
                  : ''
              "
              class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              Previous
            </button>
            <button
              @click="goToPage(pagination.currentPage + 1)"
              :disabled="pagination.currentPage === pagination.totalPages"
              :class="
                pagination.currentPage === pagination.totalPages
                  ? 'opacity-50 cursor-not-allowed'
                  : ''
              "
              class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              Next
            </button>
          </div>
          <div
            class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"
          >
            <div>
              <p class="text-sm text-gray-700">
                Showing
                <span class="font-medium">{{
                  (pagination.currentPage - 1) * pagination.perPage + 1
                }}</span>
                to
                <span class="font-medium">{{
                  Math.min(
                    pagination.currentPage * pagination.perPage,
                    pagination.totalItems
                  )
                }}</span>
                of
                <span class="font-medium">{{ pagination.totalItems }}</span>
                results
              </p>
            </div>
            <div>
              <nav
                class="relative z-0 inline-flex shadow-sm -space-x-px"
                aria-label="Pagination"
              >
                <button
                  @click="goToPage(pagination.currentPage - 1)"
                  :disabled="pagination.currentPage === 1"
                  :class="
                    pagination.currentPage === 1
                      ? 'opacity-50 cursor-not-allowed'
                      : ''
                  "
                  class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                >
                  <span class="sr-only">Previous</span>
                  <Icon icon="heroicons:chevron-left" class="h-5 w-5" />
                </button>

                <template v-for="page in pagination.totalPages" :key="page">
                  <button
                    v-if="
                      page === 1 ||
                      page === pagination.totalPages ||
                      (page >= pagination.currentPage - 1 &&
                        page <= pagination.currentPage + 1)
                    "
                    @click="goToPage(page)"
                    :class="
                      page === pagination.currentPage
                        ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                        : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                    "
                    class="relative inline-flex items-center px-4 py-2 border text-sm font-medium"
                  >
                    {{ page }}
                  </button>
                  <span
                    v-else-if="page === 2 || page === pagination.totalPages - 1"
                    class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"
                  >
                    ...
                  </span>
                </template>

                <button
                  @click="goToPage(pagination.currentPage + 1)"
                  :disabled="pagination.currentPage === pagination.totalPages"
                  :class="
                    pagination.currentPage === pagination.totalPages
                      ? 'opacity-50 cursor-not-allowed'
                      : ''
                  "
                  class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                >
                  <span class="sr-only">Next</span>
                  <Icon icon="heroicons:chevron-right" class="h-5 w-5" />
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useVendorStore } from "@/store/vendor";
import type { Rating, RatingStats, RatingResponse } from "@/types/vendor";
import { ENDPOINTS } from "@/utils/api";
import dayjs from "dayjs";

definePageMeta({
  layout: "vendor-dashboard",
});

useHead({
  title: "Reviews | EventaHub Vendor",
  meta: [
    {
      name: "description",
      content:
        "Manage your vendor services, bookings, and track performance metrics in your EventaHub vendor dashboard",
    },
  ],
});

const vendorStore = useVendorStore();
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();
const runtimeConfig = useRuntimeConfig();
const loading = ref<boolean>(true);
const loadingStats = ref<boolean>(true);
const reviews = ref<Rating[]>([]);
const pagination = ref({
  currentPage: 1,
  totalPages: 1,
  totalItems: 0,
  perPage: 10,
});
const filter = ref<string>("all");
const responseText = ref<string>("");
const replyingTo = ref<number | null>(null);
const submittingResponse = ref<boolean>(false);

const ratingStats = ref<RatingStats>({
  average: 0,
  total: 0,
  distribution: {
    1: 0,
    2: 0,
    3: 0,
    4: 0,
    5: 0,
  },
  categories: {
    service: 0,
    value: 0,
    quality: 0,
    communication: 0,
  },
});

const averageRating = computed(() => {
  return ratingStats.value.average.toFixed(1);
});

const getRatingPercentage = (stars: number) => {
  if (ratingStats.value.total === 0) return 0;
  return Math.round(
    (ratingStats.value.distribution[
      stars as keyof typeof ratingStats.value.distribution
    ] /
      ratingStats.value.total) *
      100
  );
};

const formatDate = (dateString: string) => {
  return dayjs(dateString).format("MMMM D, YYYY");
};

const fetchReviews = async (page: number = 1, filterType: string = "all") => {
  if (!vendorStore.vendor?.id) return;

  loading.value = true;
  try {
    let url = `${ENDPOINTS.VENDORS.RATINGS.GET}/${vendorStore.vendor.id}?page=${page}`;

    if (filterType !== "all") {
      if (filterType === "positive") {
        url += "&min_rating=4&max_rating=5";
      } else if (filterType === "neutral") {
        url += "&min_rating=3&max_rating=3";
      } else if (filterType === "negative") {
        url += "&min_rating=1&max_rating=2";
      } else if (filterType === "pending") {
        url += "&pending_response=1";
      }
    }

    const response = await httpClient.get<RatingResponse>(url);

    if (response) {
      reviews.value = response.data;
      pagination.value = {
        currentPage: response.current_page,
        totalPages: response.last_page,
        totalItems: response.total,
        perPage: response.per_page,
      };
    }
  } catch (error) {
    console.error("Error fetching reviews:", error);
    $toast.error("Failed to load reviews");
  } finally {
    loading.value = false;
  }
};

const calculateRatingStats = () => {
  loadingStats.value = true;

  try {
    // Check if vendor and ratings exist
    if (!vendorStore.vendor || !vendorStore.vendor.ratings) {
      // Set default empty values if no ratings data
      ratingStats.value = {
        average: 0,
        total: 0,
        distribution: {
          1: 0,
          2: 0,
          3: 0,
          4: 0,
          5: 0,
        },
        categories: {
          service: 0,
          value: 0,
          quality: 0,
          communication: 0,
        },
      };
      return;
    }

    const ratings = vendorStore.vendor.ratings;
    const total = ratings.length;

    if (total === 0) {
      // Set default empty values if ratings array is empty
      ratingStats.value = {
        average: 0,
        total: 0,
        distribution: {
          1: 0,
          2: 0,
          3: 0,
          4: 0,
          5: 0,
        },
        categories: {
          service: 0,
          value: 0,
          quality: 0,
          communication: 0,
        },
      };
      return;
    }

    const sum = ratings.reduce((acc, rating) => acc + rating.rating, 0);
    const average = sum / total;

    const distribution = {
      1: 0,
      2: 0,
      3: 0,
      4: 0,
      5: 0,
    };

    ratings.forEach((rating) => {
      if (rating.rating >= 1 && rating.rating <= 5) {
        distribution[rating.rating as keyof typeof distribution]++;
      }
    });

    ratingStats.value = {
      average,
      total,
      distribution,
      categories: {
        service: 4.9,
        value: 4.7,
        quality: 4.8,
        communication: 4.9,
      },
    };
  } catch (error) {
    console.error("Error calculating rating stats:", error);
    // Set default values in case of error
    ratingStats.value = {
      average: 0,
      total: 0,
      distribution: {
        1: 0,
        2: 0,
        3: 0,
        4: 0,
        5: 0,
      },
      categories: {
        service: 0,
        value: 0,
        quality: 0,
        communication: 0,
      },
    };
  } finally {
    // Always set loading to false, regardless of outcome
    loadingStats.value = false;
  }
};

const submitResponse = async (reviewId: number) => {
  if (!responseText.value.trim()) {
    $toast.error("Please enter a response");
    return;
  }

  submittingResponse.value = true;

  try {
    await httpClient.post(`${ENDPOINTS.VENDORS.RATINGS.UPDATE}/${reviewId}`, {
      response: responseText.value,
    });

    const reviewIndex = reviews.value.findIndex((r) => r.id === reviewId);
    if (reviewIndex !== -1) {
      reviews.value[reviewIndex].response = responseText.value;
      reviews.value[reviewIndex].response_date = new Date().toISOString();
    }

    $toast.success("Response submitted successfully");
    responseText.value = "";
    replyingTo.value = null;
  } catch (error) {
    console.error("Error submitting response:", error);
    $toast.error("Failed to submit response");
  } finally {
    submittingResponse.value = false;
  }
};

const handleFilterChange = (newFilter: string) => {
  filter.value = newFilter;
  fetchReviews(1, newFilter);
};

const goToPage = (page: number) => {
  if (page < 1 || page > pagination.value.totalPages) return;
  fetchReviews(page, filter.value);
};

onMounted(async () => {
  try {
    if (!vendorStore.vendor) {
      await vendorStore.fetchVendorProfile();
    }

    calculateRatingStats();
    await fetchReviews();
  } catch (error) {
    console.error("Error in onMounted:", error);
    $toast.error("Failed to load vendor data");

    // Ensure loading states are reset even if there's an error
    loadingStats.value = false;
    loading.value = false;
  }
});
</script>
