<template>
  <div class="dashboard-bg-main min-h-screen">
    <div class="p-4 sm:p-6">
      <div class="mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 class="text-2xl font-bold dashboard-text-primary">Portfolio</h1>
            <p class="mt-1 text-sm dashboard-text-muted">Showcase your best work to attract more clients</p>
          </div>
          <div class="mt-4 md:mt-0">
            <VendorDashboardPortfolioDialog
              ref="portfolioDialog"
              :portfolio-item="currentEditItem || undefined"
              :edit-mode="!!currentEditItem"
              @item-added="refreshPortfolio"
              @item-updated="refreshPortfolio"
            />
          </div>
        </div>
      </div>

      <div v-if="isLoading" class="flex justify-center items-center h-64">
        <div class="flex flex-col items-center">
          <CoreLoader color="#ef4444" />
          <p class="mt-4 text-gray-600 dark:text-gray-300">Loading portfolio items...</p>
        </div>
      </div>

      <template v-else>
        <div class="mb-6" v-if="portfolioStore.categories.length > 0">
          <div class="flex flex-wrap gap-2">
            <button
              @click="setCategory('all')"
              class="px-4 py-2"
              :class="portfolioStore.selectedCategory === 'all' ? 'dashboard-bg-primary text-white' : 'dashboard-bg-card dashboard-text-secondary dashboard-border dashboard-transition hover:dashboard-bg-hover'"
            >
              All
            </button>
            <button
              v-for="category in portfolioStore.categories"
              :key="category"
              @click="setCategory(category)"
              class="px-4 py-2"
              :class="portfolioStore.selectedCategory === category ? 'dashboard-bg-primary text-white' : 'dashboard-bg-card dashboard-text-secondary dashboard-border dashboard-transition hover:dashboard-bg-hover'"
            >
              {{ category }}
            </button>
          </div>
        </div>

        <div v-if="portfolioStore.items.length === 0 && !portfolioStore.loading" class="flex flex-col items-center justify-center py-12">
          <Icon icon="heroicons:photo" class="h-16 w-16 dashboard-text-light mb-4" />
          <h3 class="text-lg font-medium dashboard-text-primary mb-2">No portfolio items yet</h3>
          <p class="text-sm dashboard-text-muted mb-6 text-center max-w-md">
            Add your best work to showcase your skills and attract more clients
          </p>
          <button
            @click="portfolioDialog.openModal()"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium shadow-sm text-white dashboard-bg-primary hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dashboard-transition"
          >
            <Icon icon="heroicons:plus" class="h-5 w-5 mr-2" />
            Add Your First Portfolio Item
          </button>
        </div>

        <div v-else-if="!portfolioStore.loading" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
          <VendorDashboardPortfolioItemSkeleton v-if="loadingMore" v-for="i in 3" :key="`skeleton-${i}`" />

          <div v-for="item in portfolioStore.items" :key="item.id" class="dashboard-bg-card dashboard-shadow overflow-hidden">
            <div class="relative aspect-video">
              <img
                :src="getImageUrl(item.image_path)"
                :alt="item.title"
                class="w-full h-full object-cover"
              />
              <div class="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-50 transition-opacity flex items-center justify-center opacity-0 hover:opacity-100">
                <div class="flex space-x-2">
                  <button class="p-2 bg-white dark:bg-zinc-800" @click="viewPortfolioItem(item)">
                    <Icon icon="heroicons:eye" class="h-5 w-5 text-gray-700 dark:text-gray-300" />
                  </button>
                  <button class="p-2 bg-white dark:bg-zinc-800" @click="editPortfolioItem(item)">
                    <Icon icon="heroicons:pencil-square" class="h-5 w-5 text-gray-700 dark:text-gray-300" />
                  </button>
                  <button class="p-2 bg-white dark:bg-zinc-800" @click="confirmDeleteItem(item)">
                    <Icon icon="heroicons:trash" class="h-5 w-5 text-red-500 dark:text-red-400" />
                  </button>
                </div>
              </div>
            </div>
            <div class="p-4">
              <h3 class="text-lg font-medium dashboard-text-primary">{{ item.title }}</h3>
              <p class="mt-1 text-sm dashboard-text-muted">{{ item.description || 'No description provided' }}</p>
              <div class="mt-2 flex flex-wrap gap-2">
                <span v-if="item.category" class="inline-flex items-center px-2.5 py-0.5 text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                  {{ item.category }}
                </span>
                <span v-if="item.is_featured" class="inline-flex items-center px-2.5 py-0.5 text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                  Featured
                </span>
              </div>
            </div>
          </div>
        </div>

        <div v-if="portfolioStore.items.length > 0 && portfolioStore.totalPages > 1" class="flex items-center justify-between dashboard-border border-t dashboard-bg-card px-4 py-3 sm:px-6 dashboard-shadow">
          <div class="flex flex-1 justify-between sm:hidden">
            <button
              @click="prevPage"
              :disabled="portfolioStore.currentPage === 1 || loadingMore"
              class="relative inline-flex items-center dashboard-border dashboard-bg-card dashboard-text-secondary px-4 py-2 text-sm font-medium hover:dashboard-bg-hover dashboard-transition disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              @click="nextPage"
              :disabled="portfolioStore.currentPage === portfolioStore.totalPages || loadingMore"
              class="relative ml-3 inline-flex items-center dashboard-border dashboard-bg-card dashboard-text-secondary px-4 py-2 text-sm font-medium hover:dashboard-bg-hover dashboard-transition disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
          <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
            <div>
              <p class="text-sm dashboard-text-secondary">
                Showing <span class="font-medium">{{ (portfolioStore.currentPage - 1) * 9 + 1 }}</span> to
                <span class="font-medium">{{ Math.min(portfolioStore.currentPage * 9, portfolioStore.totalItems) }}</span> of
                <span class="font-medium">{{ portfolioStore.totalItems }}</span> results
              </p>
            </div>
            <div>
              <nav class="isolate inline-flex -space-x-px dashboard-shadow-sm" aria-label="Pagination">
                <button
                  @click="prevPage"
                  :disabled="portfolioStore.currentPage === 1 || loadingMore"
                  class="relative inline-flex items-center px-2 py-2 dashboard-text-light dashboard-border hover:dashboard-bg-hover focus:z-20 focus:outline-offset-0 dashboard-transition disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span class="sr-only">Previous</span>
                  <Icon icon="heroicons:chevron-left" class="h-5 w-5" />
                </button>

                <template v-for="page in paginationPages" :key="page">
                  <button
                    v-if="page !== '...'"
                    @click="goToPage(Number(page))"
                    :disabled="loadingMore"
                    :class="[
                      portfolioStore.currentPage === Number(page)
                        ? 'relative z-10 inline-flex items-center dashboard-bg-primary px-4 py-2 text-sm font-semibold text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600'
                        : 'relative inline-flex items-center px-4 py-2 text-sm font-semibold dashboard-text-primary dashboard-border hover:dashboard-bg-hover focus:z-20 focus:outline-offset-0 dashboard-transition'
                    ]"
                  >
                    {{ page }}
                  </button>
                  <span
                    v-else
                    class="relative inline-flex items-center px-4 py-2 text-sm font-semibold dashboard-text-primary dashboard-border"
                  >
                    {{ page }}
                  </span>
                </template>

                <button
                  @click="nextPage"
                  :disabled="portfolioStore.currentPage === portfolioStore.totalPages || loadingMore"
                  class="relative inline-flex items-center px-2 py-2 dashboard-text-light dashboard-border hover:dashboard-bg-hover focus:z-20 focus:outline-offset-0 dashboard-transition disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span class="sr-only">Next</span>
                  <Icon icon="heroicons:chevron-right" class="h-5 w-5" />
                </button>
              </nav>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>

  <VendorDashboardDeleteConfirmationDialog
    ref="deleteDialog"
    :item-name="itemToDelete?.title || ''"
    @confirm="confirmDelete"
    @cancel="cancelDelete"
  />
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useVendorPortfolioStore, type PortfolioItem } from '@/store/vendorPortfolio';
import { useVendorStore } from '@/store/vendor';
import VendorDashboardPortfolioItemSkeleton from '@/components/vendor/dashboard/PortfolioItemSkeleton.vue';
import VendorDashboardPortfolioDialog from '@/components/vendor/dashboard/PortfolioDialog.vue';

definePageMeta({
  layout: 'vendor-dashboard'
});

useHead({
  title: "Portfolio | EventaHub Vendor",
  meta: [
    { name: "description", content: "Manage your vendor services, bookings, and track performance metrics in your EventaHub vendor dashboard" }
  ]
});

const { $toast }: any = useNuxtApp();
const runtimeConfig = useRuntimeConfig();
const portfolioStore = useVendorPortfolioStore();
const vendorStore = useVendorStore();
const deleteDialog = ref();
const portfolioDialog = ref();
const itemToDelete = ref<PortfolioItem | null>(null);
const currentEditItem = ref<PortfolioItem | null>(null);
const isLoading = ref(true);
const loadingMore = ref(false);

const paginationPages = computed(() => {
  const totalPages = portfolioStore.totalPages;
  const currentPage = portfolioStore.currentPage;

  if (totalPages <= 7) {
    return Array.from({ length: totalPages }, (_, i) => String(i + 1));
  }

  if (currentPage <= 3) {
    return ['1', '2', '3', '4', '...', String(totalPages - 1), String(totalPages)];
  }

  if (currentPage >= totalPages - 2) {
    return ['1', '2', '...', String(totalPages - 3), String(totalPages - 2), String(totalPages - 1), String(totalPages)];
  }

  return [
    '1',
    '...',
    String(currentPage - 1),
    String(currentPage),
    String(currentPage + 1),
    '...',
    String(totalPages)
  ];
});
onMounted(async () => {
  isLoading.value = true;

  try {
    if (!vendorStore.details) {
      await vendorStore.fetchVendorDetails();
    }

    await portfolioStore.fetchPortfolioItems();
  } catch (error) {
    console.error('Error loading portfolio data:', error);
    $toast.error('Failed to load portfolio data');
  } finally {
    isLoading.value = false;
  }
});

const getImageUrl = (imagePath: string): string => {
  if (imagePath.startsWith('http')) {
    return imagePath;
  }
  return `${runtimeConfig.public.baseUrl}storage/${imagePath}`;
};

const prevPage = () => {
  if (portfolioStore.currentPage > 1 && !loadingMore.value) {
    goToPage(portfolioStore.currentPage - 1);
  }
};

const nextPage = () => {
  if (portfolioStore.currentPage < portfolioStore.totalPages && !loadingMore.value) {
    goToPage(portfolioStore.currentPage + 1);
  }
};

const goToPage = async (page: number) => {
  if (page === portfolioStore.currentPage || loadingMore.value) return;

  loadingMore.value = true;
  try {
    await portfolioStore.fetchPortfolioItems(page, portfolioStore.selectedCategory);
  } catch (error) {
    console.error('Error loading page:', error);
    $toast.error('Failed to load portfolio items');
  } finally {
    loadingMore.value = false;
  }
};

const setCategory = async (category: string) => {
  if (category === portfolioStore.selectedCategory || loadingMore.value) return;

  loadingMore.value = true;
  try {
    await portfolioStore.fetchPortfolioItems(1, category);
  } catch (error) {
    console.error('Error filtering by category:', error);
    $toast.error('Failed to filter portfolio items');
  } finally {
    loadingMore.value = false;
  }
};

const viewPortfolioItem = (item: PortfolioItem) => {
  // Open image in a new tab
  const imageUrl = getImageUrl(item.image_path);
  window.open(imageUrl, '_blank');
};

const editPortfolioItem = (item: PortfolioItem) => {
  portfolioDialog.value.openModal();
  currentEditItem.value = item;
};

const confirmDeleteItem = (item: PortfolioItem) => {
  itemToDelete.value = item;
  deleteDialog.value.openModal();
};

const confirmDelete = async () => {
  if (!itemToDelete.value) return;

  try {
    const success = await portfolioStore.deletePortfolioItem(itemToDelete.value.id);
    if (success) {
      $toast.success('Portfolio item deleted successfully');
    } else {
      $toast.error('Failed to delete portfolio item');
    }
  } catch (error) {
    console.error('Error deleting portfolio item:', error);
    $toast.error('Failed to delete portfolio item');
  } finally {
    itemToDelete.value = null;
  }
};

const cancelDelete = () => {
  itemToDelete.value = null;
};

const refreshPortfolio = async (itemId?: number) => {
  // Reset the current edit item if it was closed or after update
  if (!itemId || itemId === 0) {
    currentEditItem.value = null;
  }

  loadingMore.value = true;
  try {
    await portfolioStore.fetchPortfolioItems(1, portfolioStore.selectedCategory);
  } catch (error) {
    console.error('Error refreshing portfolio:', error);
    $toast.error('Failed to refresh portfolio items');
  } finally {
    loadingMore.value = false;
  }
};
</script>
