<template>
  <div class="bg-gray-50 dark:bg-zinc-900 min-h-screen">
    <div class="p-4 sm:p-6">
      <div class="mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Pricing</h1>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Manage your service pricing and packages</p>
          </div>
          <div class="mt-4 md:mt-0">
            <CorePrimaryButton @click="isAddPackageModalOpen = true" text="Add Price Package" startIcon="heroicons:plus"
              color="primary" />
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-zinc-800 shadow-sm overflow-hidden mb-6">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-zinc-700">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">Pricing Packages</h3>
        </div>
        <div v-if="loading" class="p-6 flex justify-center">
          <CoreLoader />
        </div>
        <div v-else-if="packages.length === 0" class="p-6 text-center">
          <p class="text-gray-500 dark:text-gray-400">No pricing packages found. Add your first package to get started.
          </p>
        </div>
        <div v-else class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-zinc-700">
            <thead class="bg-gray-50 dark:bg-zinc-700">
              <tr>
                <th scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Package Name
                </th>
                <th scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Service Category
                </th>
                <th scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Price
                </th>
                <th scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Duration
                </th>
                <th scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col"
                  class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-zinc-800 divide-y divide-gray-200 dark:divide-zinc-700">
              <tr v-for="(pkg, index) in packages" :key="index">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900 dark:text-white">{{ pkg.name }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900 dark:text-gray-300">{{ pkg.service?.name || 'N/A' }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900 dark:text-gray-300">
                    {{ formatCurrency(pkg.price, pkg.currency?.name || 'MWK') }}
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900 dark:text-gray-300">{{ pkg.duration || 'N/A' }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <CoreBadge :color="pkg.active ? 'green' : 'gray'">
                    {{ pkg.active ? 'Active' : 'Inactive' }}
                  </CoreBadge>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div class="flex justify-end space-x-2">
                    <button @click="openEditModal(pkg)"
                      class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300">
                      <Icon icon="heroicons:pencil-square" class="h-5 w-5" />
                    </button>
                    <button @click="openDeleteModal(pkg)"
                      class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300">
                      <Icon icon="heroicons:trash" class="h-5 w-5" />
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div class="bg-white dark:bg-zinc-800 shadow-sm overflow-hidden mb-6">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-zinc-700">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">Custom Pricing</h3>
        </div>
        <div class="p-6">
          <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
            Set your custom hourly and daily rates for services that require flexible pricing. These rates will be shown
            to clients when they request custom services.
          </p>
          <div v-if="loading" class="p-6 flex justify-center">
            <CoreLoader />
          </div>
          <div v-else>
            <FormKit type="form" id="customPricingForm" @submit="saveCustomPricing" :actions="false">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="relative">
                  <FormKit type="number" name="hourlyRate" label="Hourly Rate" v-model="customPricing.hourlyRate"
                    placeholder="0.00" min="0" step="0.01" validation="required|min:0" :validation-messages="{
                      required: 'Hourly rate is required',
                      min: 'Rate must be a positive number'
                    }">
                    <template #prefix>
                      <span class="text-gray-500 dark:text-gray-400">{{ selectedCurrency }}</span>
                    </template>
                    <template #suffix>
                      <span class="text-gray-500 dark:text-gray-400">/hour</span>
                    </template>
                  </FormKit>
                </div>
                <div class="relative">
                  <FormKit type="number" name="dayRate" label="Day Rate" v-model="customPricing.dayRate"
                    placeholder="0.00" min="0" step="0.01" validation="required|min:0" :validation-messages="{
                      required: 'Day rate is required',
                      min: 'Rate must be a positive number'
                    }">
                    <template #prefix>
                      <span class="text-gray-500 dark:text-gray-400">{{ selectedCurrency }}</span>
                    </template>
                    <template #suffix>
                      <span class="text-gray-500 dark:text-gray-400">/day</span>
                    </template>
                  </FormKit>
                </div>
              </div>

              <FormKit type="select" name="currency" label="Currency" :value="customPricing.currencyId.toString()"
                @input="(val) => customPricing.currencyId = val ? parseInt(val) : 0" validation="required"
                :options="currencies.map(c => ({ value: c.id.toString(), label: c.name }))"
                placeholder="Select currency" :validation-messages="{
                  required: 'Currency is required'
                }" />

              <FormKit type="textarea" name="notes" label="Notes on Custom Pricing" v-model="customPricing.notes"
                rows="4" placeholder="Add any additional information about your custom pricing here..." />

              <div class="mt-6">
                <CoreSubmitButton text="Save Custom Pricing" />
              </div>
            </FormKit>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-zinc-800 shadow-sm overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-zinc-700">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">Discounts</h3>
        </div>
        <div class="p-6">
          <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
            Create discounts for your services to attract more clients. Discounts can be applied to specific packages or
            services.
          </p>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div v-for="(discount, index) in discounts" :key="index"
              class="border border-gray-200 dark:border-zinc-700 p-4">
              <div class="flex justify-between items-start">
                <div>
                  <h4 class="text-sm font-medium text-gray-900 dark:text-white">{{ discount.name }}</h4>
                  <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">{{ discount.description }}</p>
                </div>
                <CoreBadge :color="discount.active ? 'green' : 'gray'">
                  {{ discount.active ? 'Active' : 'Inactive' }}
                </CoreBadge>
              </div>
              <div class="mt-4 flex justify-between items-center">
                <span class="text-lg font-bold text-gray-900 dark:text-white">{{ discount.value }}</span>
                <div class="flex space-x-2">
                  <button class="p-1 text-gray-400 dark:text-gray-500 hover:text-gray-500 dark:hover:text-gray-400">
                    <Icon icon="heroicons:pencil-square" class="h-5 w-5" />
                  </button>
                  <button class="p-1 text-gray-400 dark:text-gray-500 hover:text-red-500 dark:hover:text-red-400">
                    <Icon icon="heroicons:trash" class="h-5 w-5" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <TransitionRoot appear :show="isAddPackageModalOpen" as="template">
        <Dialog as="div" @close="isAddPackageModalOpen = false" class="relative z-10">
          <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
            leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
            <div class="fixed inset-0 bg-black bg-opacity-25" />
          </TransitionChild>

          <div class="fixed inset-0 overflow-y-auto">
            <div class="flex min-h-full items-center justify-center p-4">
              <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
                leave-to="opacity-0 scale-95">
                <DialogPanel
                  class="w-full max-w-md transform overflow-hidden bg-white dark:bg-zinc-800 shadow-xl transition-all">
                  <DialogTitle as="h3"
                    class="text-lg font-medium leading-6 text-gray-900 dark:text-white p-4 border-b dark:border-zinc-700">
                    Add New Pricing Package
                  </DialogTitle>

                  <FormKit type="form" id="addPackageForm" @submit="addPackage" :actions="false"
                    #default="{ state: { valid } }">
                    <div class="p-4 space-y-4 text-left">
                      <FormKit type="text" name="name" label="Package Name" v-model="newPackage.name"
                        placeholder="e.g. Basic Wedding Package" validation="required" :validation-messages="{
                          required: 'Package name is required'
                        }" />

                      <FormKit type="select" name="service" label="Service" :value="newPackage.service_id.toString()"
                        @input="(val) => newPackage.service_id = val ? parseInt(val) : 0"
                        :options="availableServices.map(s => ({ value: s.id.toString(), label: s.name }))"
                        placeholder="Select a service" validation="required" :validation-messages="{
                          required: 'Service is required'
                        }" />

                      <div class="grid grid-cols-2 gap-4">
                        <FormKit type="number" name="price" label="Price" :value="newPackage.price.toString()"
                          @input="(val) => newPackage.price = val ? parseFloat(val) : 0" placeholder="0.00"
                          validation="required|min:0" :validation-messages="{
                            required: 'Price is required',
                            min: 'Price must be a positive number'
                          }" />

                        <FormKit type="select" name="currency" label="Currency"
                          :value="newPackage.currency_id.toString()"
                          @input="(val) => newPackage.currency_id = val ? parseInt(val) : 0"
                          :options="currencies.map(c => ({ value: c.id.toString(), label: c.name }))"
                          placeholder="Select currency" validation="required" :validation-messages="{
                            required: 'Currency is required'
                          }" />
                      </div>

                      <FormKit type="text" name="duration" label="Duration" v-model="newPackage.duration"
                        placeholder="e.g. 4 hours" validation="required" :validation-messages="{
                          required: 'Duration is required'
                        }" />

                      <FormKit type="textarea" name="description" label="Description" v-model="newPackage.description"
                        rows="3" placeholder="Describe what's included in this package" />

                      <FormKit type="checkbox" name="active" label="Active" v-model="newPackage.active" />
                    </div>

                    <div
                      class="w-full flex items-center justify-end space-x-3 mt-4 px-4 py-3 border-t dark:border-zinc-700">
                      <button type="button"
                        class="inline-flex justify-center border border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 shadow-sm hover:bg-gray-50 dark:hover:bg-zinc-600"
                        @click="isAddPackageModalOpen = false">
                        Cancel
                      </button>
                      <CoreSubmitButton text="Add Package" :disabled="!valid" />
                    </div>
                  </FormKit>
                </DialogPanel>
              </TransitionChild>
            </div>
          </div>
        </Dialog>
      </TransitionRoot>

      <TransitionRoot appear :show="isEditPackageModalOpen" as="template">
        <Dialog as="div" @close="isEditPackageModalOpen = false" class="relative z-10">
          <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
            leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
            <div class="fixed inset-0 bg-black bg-opacity-25" />
          </TransitionChild>

          <div class="fixed inset-0 overflow-y-auto">
            <div class="flex min-h-full items-center justify-center p-4">
              <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
                leave-to="opacity-0 scale-95">
                <DialogPanel
                  class="w-full max-w-md transform overflow-hidden bg-white dark:bg-zinc-800 shadow-xl transition-all">
                  <DialogTitle as="h3"
                    class="text-lg font-medium leading-6 text-gray-900 dark:text-white p-4 border-b dark:border-zinc-700">
                    Edit Pricing Package
                  </DialogTitle>

                  <FormKit v-if="selectedPackage" type="form" id="editPackageForm" @submit="updatePackage"
                    :actions="false" #default="{ state: { valid } }">
                    <div class="p-4 space-y-4 text-left">
                      <FormKit type="text" name="name" label="Package Name" v-model="selectedPackage.name"
                        validation="required" :validation-messages="{
                          required: 'Package name is required'
                        }" />

                      <FormKit type="select" name="service" label="Service"
                        :value="selectedPackage?.service_id?.toString() || '0'"
                        @input="(val) => { if (selectedPackage) selectedPackage.service_id = val ? parseInt(val) : 0 }"
                        :options="availableServices.map(s => ({ value: s.id.toString(), label: s.name }))"
                        placeholder="Select a service" validation="required" :validation-messages="{
                          required: 'Service is required'
                        }" />

                      <div class="grid grid-cols-2 gap-4">
                        <FormKit type="number" name="price" label="Price"
                          :value="selectedPackage?.price?.toString() || '0'"
                          @input="(val) => { if (selectedPackage) selectedPackage.price = val ? parseFloat(val) : 0 }"
                          validation="required|min:0" :validation-messages="{
                            required: 'Price is required',
                            min: 'Price must be a positive number'
                          }" />

                        <FormKit type="select" name="currency" label="Currency"
                          :value="selectedPackage?.currency_id?.toString() || '0'"
                          @input="(val) => { if (selectedPackage) selectedPackage.currency_id = val ? parseInt(val) : 0 }"
                          :options="currencies.map(c => ({ value: c.id.toString(), label: c.name }))"
                          placeholder="Select currency" validation="required" :validation-messages="{
                            required: 'Currency is required'
                          }" />
                      </div>

                      <FormKit type="text" name="duration" label="Duration" v-model="selectedPackage.duration"
                        placeholder="e.g. 4 hours" validation="required" :validation-messages="{
                          required: 'Duration is required'
                        }" />

                      <FormKit type="textarea" name="description" label="Description"
                        v-model="selectedPackage.description" rows="3"
                        placeholder="Describe what's included in this package" />

                      <FormKit type="checkbox" name="active" label="Active" v-model="selectedPackage.active" />
                    </div>

                    <div
                      class="w-full flex items-center justify-end space-x-3 mt-4 px-4 py-3 border-t dark:border-zinc-700">
                      <button type="button"
                        class="inline-flex justify-center border border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 shadow-sm hover:bg-gray-50 dark:hover:bg-zinc-600"
                        @click="isEditPackageModalOpen = false">
                        Cancel
                      </button>
                      <CoreSubmitButton :loading="isEditing" text="Save Changes" :disabled="!valid" />
                    </div>
                  </FormKit>
                </DialogPanel>
              </TransitionChild>
            </div>
          </div>
        </Dialog>
      </TransitionRoot>

      <TransitionRoot appear :show="isDeletePackageModalOpen" as="template">
        <Dialog as="div" @close="isDeletePackageModalOpen = false" class="relative z-10">
          <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
            leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
            <div class="fixed inset-0 bg-black bg-opacity-25" />
          </TransitionChild>

          <div class="fixed inset-0 overflow-y-auto">
            <div class="flex min-h-full items-center justify-center p-4">
              <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
                leave-to="opacity-0 scale-95">
                <DialogPanel
                  class="w-full max-w-md transform overflow-hidden bg-white dark:bg-zinc-800 shadow-xl transition-all">
                  <DialogTitle as="h3"
                    class="text-lg font-medium leading-6 text-gray-900 dark:text-white p-4 border-b dark:border-zinc-700">
                    Delete Pricing Package
                  </DialogTitle>
                  <div class="p-4 text-left">
                    <p class="text-sm text-gray-500 dark:text-gray-400">
                      Are you sure you want to delete this pricing package? This action cannot be undone.
                    </p>
                    <div v-if="selectedPackage" class="mt-4 bg-gray-50 dark:bg-zinc-700 p-4">
                      <p class="font-medium text-gray-900 dark:text-white">{{ selectedPackage.name }}</p>
                      <p class="text-sm text-gray-500 dark:text-gray-400">{{ formatCurrency(selectedPackage.price,
                        selectedPackage.currency?.name || 'MWK') }}</p>
                    </div>
                  </div>

                  <div
                    class="w-full flex items-center justify-end space-x-3 mt-4 px-4 py-3 border-t dark:border-zinc-700">
                    <button type="button"
                      class="inline-flex justify-center border border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 shadow-sm hover:bg-gray-50 dark:hover:bg-zinc-600"
                      @click="isDeletePackageModalOpen = false">
                      Cancel
                    </button>
                    <CorePrimaryButton @click="deletePackage" text="Delete" color="error" />
                  </div>
                </DialogPanel>
              </TransitionChild>
            </div>
          </div>
        </Dialog>
      </TransitionRoot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useHttpClient } from '@/composables/useHttpClient';
import { ENDPOINTS } from '@/utils/api';
import { useVendorStore } from '@/store/vendor';
import { useVendorServicesStore } from '@/store/vendorServices';
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue';
import { useNuxtApp } from '#app';
import CoreBadge from '@/components/core/Badge.vue';
import CoreLoader from '@/components/core/Loader.vue';
import CoreSubmitButton from '@/components/core/SubmitButton.vue';
import CorePrimaryButton from '@/components/core/PrimaryButton.vue';

definePageMeta({
  layout: 'vendor-dashboard'
});

useHead({
  title: "Pricing | EventaHub Vendor",
  meta: [
    { name: "description", content: "Manage your vendor services, bookings, and track performance metrics in your EventaHub vendor dashboard" }
  ]
});

interface Currency {
  id: number;
  name: string;
  created_at?: string;
  updated_at?: string;
}

interface Service {
  id: number;
  name: string;
}

interface PricePackage {
  id: number;
  vendor_id: number;
  name: string;
  service_id: number;
  service?: {
    id: number;
    name: string;
  };
  price: number;
  currency_id: number;
  currency?: Currency;
  duration: string;
  description: string;
  active: boolean;
  created_at?: string;
  updated_at?: string;
}

interface CustomPricing {
  hourlyRate: string;
  dayRate: string;
  currencyId: number;
  notes: string;
}

interface PackageForm {
  name: string;
  service_id: number;
  price: number;
  currency_id: number;
  duration: string;
  description: string;
  active: boolean;
}

interface ApiResponse<T> {
  data?: T;
  message?: string;
  status?: number;
  currencies?: Currency[];
}

interface Discount {
  name: string;
  description: string;
  value: string;
  active: boolean;
}

const httpClient = useHttpClient();
const vendorStore = useVendorStore();
const vendorServicesStore = useVendorServicesStore();
const { $toast }: any = useNuxtApp();
const packages = ref<PricePackage[]>([]);
const currencies = ref<Currency[]>([]);
const loading = ref<boolean>(false);
const isEditing = ref<boolean>(false);
const loadingCurrencies = ref<boolean>(false);
const loadingServices = ref<boolean>(false);
const isAddPackageModalOpen = ref<boolean>(false);
const isDeletePackageModalOpen = ref<boolean>(false);
const isEditPackageModalOpen = ref<boolean>(false);
const selectedPackage = ref<PricePackage | null>(null);

const customPricing = ref<CustomPricing>({
  hourlyRate: '0',
  dayRate: '0',
  currencyId: 0,
  notes: ''
});

const selectedCurrency = computed<string>(() => {
  if (!customPricing.value.currencyId || !currencies.value.length) return 'MWK';
  const currency = currencies.value.find(c => c.id === Number(customPricing.value.currencyId));
  return currency ? currency.name : 'MWK';
});

const newPackage = ref<PackageForm>({
  name: '',
  service_id: 0,
  price: 0,
  currency_id: 0,
  duration: '',
  description: '',
  active: true
});

onMounted(async () => {
  loadingServices.value = true;
  try {
    await vendorServicesStore.fetchVendorServices();
    await vendorServicesStore.fetchAvailableServices();
  } catch (error) {
    console.error('Error fetching services:', error);
  } finally {
    loadingServices.value = false;
  }
});

const fetchPackages = async (): Promise<void> => {
  loading.value = true;
  try {
    if (!vendorStore.details?.id) {
      await vendorStore.fetchVendorDetails();
    }

    const vendorId = vendorStore.details?.id;
    if (!vendorId) {
      throw new Error('Vendor ID not found');
    }

    const response = await httpClient.get<ApiResponse<PricePackage[]>>(`${ENDPOINTS.VENDORS.PRICES.GET}/${vendorId}`);
    packages.value = response?.data || [];
  } catch (error: any) {
    console.error('Error fetching pricing packages:', error);
  } finally {
    loading.value = false;
  }
};

const fetchCurrencies = async (): Promise<void> => {
  loadingCurrencies.value = true;
  try {
    const response = await httpClient.get<ApiResponse<null>>('/currency');
    currencies.value = response?.currencies || [];
  } catch (error) {
    console.error('Error fetching currencies:', error);
  } finally {
    loadingCurrencies.value = false;
  }
};

const formatCurrency = (price: number, currencyCode: string = 'MWK'): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currencyCode,
    minimumFractionDigits: 0
  }).format(Number(price));
};

const addPackage = async (): Promise<void> => {
  try {
    if (!vendorStore.details?.id) {
      throw new Error('Vendor ID not found');
    }

    const packageData = {
      vendor_id: vendorStore.details.id,
      name: newPackage.value.name,
      service_id: newPackage.value.service_id,
      price: newPackage.value.price,
      currency_id: newPackage.value.currency_id,
      duration: newPackage.value.duration,
      description: newPackage.value.description,
      active: newPackage.value.active
    };

    await httpClient.post(ENDPOINTS.VENDORS.PRICES.CREATE, packageData);
    $toast.success('Package added successfully');
    await fetchPackages();
    isAddPackageModalOpen.value = false;
    resetForm();
  } catch (error: any) {
    console.error('Error adding package:', error);
    $toast.error(error.message || 'Failed to add package');
  }
};

const updatePackage = async (): Promise<void> => {
  try {
    if (!selectedPackage.value || !selectedPackage.value.id) {
      throw new Error('No package selected');
    }

    isEditing.value = true;

    const packageData = {
      name: selectedPackage.value.name,
      service_id: selectedPackage.value.service_id,
      price: selectedPackage.value.price,
      currency_id: selectedPackage.value.currency_id,
      duration: selectedPackage.value.duration,
      description: selectedPackage.value.description,
      active: selectedPackage.value.active
    };

    const response = await httpClient.put(`${ENDPOINTS.VENDORS.PRICES.UPDATE}/${selectedPackage.value.id}`, packageData);
    if (response) {
      $toast.success('Package updated successfully');
      await fetchPackages();
    }
  } catch (error: any) {
    handleError(error, $toast);
  } finally {
    isEditing.value = false;
    isEditPackageModalOpen.value = false;
  }
};

const deletePackage = async (): Promise<void> => {
  try {
    if (!selectedPackage.value || !selectedPackage.value.id) {
      throw new Error('No package selected');
    }

    await httpClient.delete(`${ENDPOINTS.VENDORS.PRICES.DELETE}/${selectedPackage.value.id}`);
    $toast.success('Package deleted successfully');
    await fetchPackages();
    isDeletePackageModalOpen.value = false;
  } catch (error: any) {
    console.error('Error deleting package:', error);
    $toast.error(error.message || 'Failed to delete package');
  }
};

const saveCustomPricing = async (): Promise<void> => {
  try {
    if (!vendorStore.details?.id) {
      throw new Error('Vendor ID not found');
    }

    const customPricingData = {
      vendor_id: vendorStore.details.id,
      hourly_rate: parseFloat(customPricing.value.hourlyRate),
      day_rate: parseFloat(customPricing.value.dayRate),
      currency_id: customPricing.value.currencyId,
      notes: customPricing.value.notes
    };

    await httpClient.post(ENDPOINTS.VENDORS.CUSTOM_PRICING.CREATE, customPricingData);
    $toast.success('Custom pricing saved successfully');
  } catch (error: any) {
    console.error('Error saving custom pricing:', error);
    $toast.error(error.message || 'Failed to save custom pricing');
  }
};

const fetchCustomPricing = async (): Promise<void> => {
  try {
    if (!vendorStore.details?.id) {
      return;
    }

    const response = await httpClient.get<ApiResponse<{
      hourly_rate: number;
      day_rate: number;
      currency_id: number;
      notes: string;
    }>>(`${ENDPOINTS.VENDORS.CUSTOM_PRICING.GET}/${vendorStore.details.id}`);

    if (response?.data) {
      customPricing.value = {
        hourlyRate: String(response.data.hourly_rate || 0),
        dayRate: String(response.data.day_rate || 0),
        currencyId: response.data.currency_id || 0,
        notes: response.data.notes || ''
      };
    }
  } catch (error) {
    console.error('Error fetching custom pricing:', error);
  }
};

const resetForm = (): void => {
  newPackage.value = {
    name: '',
    service_id: 0,
    price: 0,
    currency_id: 0,
    duration: '',
    description: '',
    active: true
  };
};

const openEditModal = (pkg: PricePackage): void => {
  selectedPackage.value = { ...pkg };
  isEditPackageModalOpen.value = true;
};

const openDeleteModal = (pkg: PricePackage): void => {
  selectedPackage.value = pkg;
  isDeletePackageModalOpen.value = true;
};

const availableServices = computed<Service[]>(() => {
  return vendorServicesStore.services.map(service => ({
    id: service.id,
    name: service.service.name
  }));
});

onMounted(async () => {
  await fetchCurrencies();
  await fetchPackages();
  await fetchCustomPricing();
});

const discounts = ref<Discount[]>([
  {
    name: 'Early Bird Discount',
    description: 'For bookings made at least 3 months in advance',
    value: '10% Off',
    active: true
  },
  {
    name: 'Returning Client',
    description: 'For clients who have used our services before',
    value: '15% Off',
    active: true
  },
  {
    name: 'Seasonal Promotion',
    description: 'Special discount for off-season bookings',
    value: '20% Off',
    active: false
  }
]);
</script>
