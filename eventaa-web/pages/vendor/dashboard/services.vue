<template>
  <div class="dashboard-bg-main min-h-screen">
    <div class="p-4 sm:p-6">
      <div class="mb-6">
        <div
          class="flex flex-col md:flex-row md:items-center md:justify-between"
        >
          <div>
            <h1 class="text-2xl font-bold dashboard-text-primary">Services</h1>
            <p class="mt-1 text-sm dashboard-text-muted">
              Manage your service offerings and packages
            </p>
          </div>
          <div class="mt-4 md:mt-0">
            <VendorDashboardServiceDialog
              @service-added="addService"
              @service-updated="updateService"
              ref="serviceDialog"
            />
          </div>
        </div>
      </div>

      <div
        v-if="isLoading || vendorServicesStore.loading"
        class="flex justify-center items-center h-64"
      >
        <div class="flex flex-col items-center">
          <CoreLoader color="#ef4444" />
          <p class="mt-4 text-gray-600 dark:text-gray-300">
            Loading services...
          </p>
        </div>
      </div>

      <div
        v-else-if="services.length === 0"
        class="dashboard-bg-card dashboard-shadow p-8 text-center mb-6"
      >
        <Icon
          icon="heroicons:wrench-screwdriver"
          class="h-12 w-12 mx-auto dashboard-text-muted"
        />
        <h3 class="mt-4 text-lg font-medium dashboard-text-primary">
          No services found
        </h3>
        <p class="mt-2 text-sm dashboard-text-muted">
          Add your first service to get started
        </p>
        <button
          @click="serviceDialog.openModal()"
          class="mt-4 px-4 py-2 bg-red-600 text-white hover:bg-red-700 transition"
        >
          Add New Service
        </button>
      </div>

      <div
        v-else
        class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-6"
      >
        <div
          v-for="(service, idx) in services"
          :key="service.id"
          class="dashboard-bg-card dashboard-shadow overflow-hidden"
        >
          <div class="p-6">
            <h3 class="text-lg font-medium dashboard-text-primary">
              {{ service.name }}
            </h3>
            <p class="mt-1 text-sm dashboard-text-muted line-clamp-2">
              {{ service.description }}
            </p>
            <div class="mt-6 flex justify-end space-x-2">
              <button
                @click="editService(service)"
                class="inline-flex items-center px-3 py-1.5 dashboard-border dashboard-bg-card dashboard-text-secondary hover:dashboard-bg-hover dashboard-transition text-sm font-medium"
              >
                <Icon
                  icon="heroicons:pencil"
                  class="h-4 w-4 text-blue-500 dark:text-blue-400"
                />
              </button>
              <button
                @click="openDeleteDialog(vendorServicesStore.services[idx])"
                class="inline-flex items-center px-3 py-1.5 dashboard-border dashboard-bg-card dashboard-text-secondary hover:dashboard-bg-hover dashboard-transition text-sm font-medium"
              >
                <Icon
                  icon="heroicons:trash"
                  class="h-4 w-4 text-red-500 dark:text-red-400"
                />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <VendorDashboardDeleteConfirmationDialog
    ref="deleteDialog"
    :item-name="serviceToDelete?.service?.name || ''"
    @confirm="confirmDelete"
    @cancel="cancelDelete"
  />
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { useVendorServicesStore } from "@/store/vendorServices";
import type { Service } from "@/types/vendor";

definePageMeta({
  layout: "vendor-dashboard",
});

useHead({
  title: "Services | EventaHub Vendor",
  meta: [
    {
      name: "description",
      content: "Manage your vendor services, bookings, and track performance metrics in your EventaHub vendor dashboard",
    },
  ],
});

const serviceDialog = ref();
const deleteDialog = ref();
const { $toast }: any = useNuxtApp();
const vendorServicesStore = useVendorServicesStore();
const isLoading = ref<boolean>(false);
const serviceToDelete = ref<Service | null>(null);

const services = computed(() => {
  return vendorServicesStore.services.map((service) => {
    return {
      id: service.id,
      service_id: service.service_id,
      vendor_id: service.vendor_id,
      name: service.service?.name || "Unnamed Service",
      description: service.service?.description || "No description available",
    };
  });
});

const fetchServices = async (): Promise<void> => {
  isLoading.value = true;
  try {
    await vendorServicesStore.fetchVendorServices();
    await vendorServicesStore.fetchAvailableServices();
  } catch (error) {
    console.error("Error fetching services:", error);
    $toast.error("Failed to load services");
  } finally {
    isLoading.value = false;
  }
};

onMounted(() => {
  fetchServices();
});

const addService = async (newService: any): Promise<void> => {
  await fetchServices();
};

const editService = (service: any): void => {
  vendorServicesStore.setCurrentService(service);
  serviceDialog.value.openModal(service, true);
};

const updateService = async (updatedService: any): Promise<void> => {
  await fetchServices();
};

const openDeleteDialog = (service: Service): void => {
  serviceToDelete.value = service;
  deleteDialog.value.openModal();
};

const cancelDelete = (): void => {
  serviceToDelete.value = null;
};

const confirmDelete = async (): Promise<void> => {
  if (!serviceToDelete.value) return;

  try {
    deleteDialog.value.setLoading(true);
    const success = await vendorServicesStore.deleteService(
      serviceToDelete.value.id
    );

    if (success) {
      $toast.success(
        `Service "${serviceToDelete.value.service.name}" deleted successfully`
      );
      await fetchServices();
    } else {
      throw new Error("Failed to delete service");
    }
  } catch (error: any) {
    $toast.error(error.message || "An error occurred while deleting service");
  } finally {
    deleteDialog.value.closeAfterDelete();
    serviceToDelete.value = null;
  }
};
</script>
