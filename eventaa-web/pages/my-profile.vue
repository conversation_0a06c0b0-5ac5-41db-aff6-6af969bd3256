<template>
    <div class="relative bg-gray-100 dark:bg-zinc-900 min-h-screen">
        <div class="px-10 pt-5">
            <nav class="flex" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
                    <li class="inline-flex items-center">
                        <a href="/" class="inline-flex items-center font-medium text-gray-700 dark:text-zinc-300 hover:text-red-600 dark:hover:text-red-400 transition-colors">
                            <Icon icon="solar:home-linear" class="h-5 w-5 mr-1" />
                            Home
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="rtl:rotate-180 w-3 h-3 mx-1 text-gray-400 dark:text-zinc-500" aria-hidden="true"
                                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <a href="#" class="ms-1 font-medium text-gray-700 dark:text-zinc-300">My Profile</a>
                        </div>
                    </li>
                </ol>
            </nav>
        </div>

        <div v-if="loading" class="w-full grid grid-cols-1 lg:grid-cols-5 gap-3 px-4 md:px-10 py-5">
            <div class="w-full shadow bg-white dark:bg-zinc-800 lg:col-span-2 flex flex-col px-6 md:px-10 py-10 md:py-20 animate-pulse">
                <div class="w-full flex flex-col space-y-0.5 items-center justify-center relative pb-3 pt-5">
                    <div class="w-20 h-20 bg-gray-200 dark:bg-zinc-600 rounded-full mb-4"></div>
                    <div class="h-6 bg-gray-200 dark:bg-zinc-600 w-32 mb-2"></div>
                    <div class="h-4 bg-gray-200 dark:bg-zinc-600 w-40 mb-2"></div>
                    <div class="h-4 bg-gray-200 dark:bg-zinc-600 w-28"></div>
                </div>
                <div class="w-full grid grid-cols-2 gap-2 mb-6">
                    <div class="flex flex-col items-center space-y-1 border-r border-gray-200 dark:border-zinc-600">
                        <div class="h-6 bg-gray-200 dark:bg-zinc-600 w-8"></div>
                        <div class="h-4 bg-gray-200 dark:bg-zinc-600 w-16"></div>
                    </div>
                    <div class="flex flex-col items-center space-y-1">
                        <div class="h-6 bg-gray-200 dark:bg-zinc-600 w-8"></div>
                        <div class="h-4 bg-gray-200 dark:bg-zinc-600 w-16"></div>
                    </div>
                </div>
                <div class="w-full mx-auto flex flex-col items-center justify-center mt-3 mb-6">
                    <div class="h-4 bg-gray-200 dark:bg-zinc-600 w-full mb-2"></div>
                    <div class="h-4 bg-gray-200 dark:bg-zinc-600 w-3/4 mb-2"></div>
                    <div class="h-8 bg-gray-200 dark:bg-zinc-600 w-8 rounded-full"></div>
                </div>
                <div class="w-full flex flex-col items-center justify-center py-2.5 space-y-3">
                    <div class="h-5 bg-gray-200 dark:bg-zinc-600 w-24"></div>
                    <div class="flex flex-wrap gap-3 justify-center">
                        <div class="h-10 bg-gray-200 dark:bg-zinc-600 w-32"></div>
                        <div class="h-10 bg-gray-200 dark:bg-zinc-600 w-36"></div>
                    </div>
                </div>
            </div>

            <div class="w-full lg:col-span-3 shadow bg-white dark:bg-zinc-800">
                <div class="bg-gray-50 dark:bg-zinc-700 flex items-center border-b border-gray-200 dark:border-zinc-600">
                    <div v-for="i in 4" :key="i" class="px-4 py-2 flex items-center border-r border-gray-200 dark:border-zinc-600">
                        <div class="h-5 bg-gray-200 dark:bg-zinc-600 w-5 mr-2"></div>
                        <div class="h-4 bg-gray-200 dark:bg-zinc-600 w-16"></div>
                    </div>
                </div>
                <div class="p-6">
                    <div v-for="i in 3" :key="i" class="h-20 bg-gray-200 dark:bg-zinc-600 mb-4"></div>
                </div>
            </div>
        </div>

        <div v-else class="w-full grid grid-cols-1 lg:grid-cols-5 gap-3 px-4 md:px-10 py-5">
            <div class="w-full shadow bg-white dark:bg-zinc-800 lg:col-span-2 flex flex-col px-6 md:px-10 py-10 md:py-20">
                <div class="w-full flex flex-col space-y-0.5 items-center justify-center relative pb-3 pt-5">
                    <div class="relative" @mouseover="isHovered = true" @mouseleave="isHovered = false">
                        <img :src="`${runtimeConfig.public.baseUrl}storage/avatars/${profile?.avatar}`" alt="profile-picture"
                            class="w-20 h-20 object-cover rounded-full border border-sky-100 dark:border-zinc-600" />
                        <div v-if="isHovered"
                            class="absolute inset-0 bg-black bg-opacity-10 transiton duration-150 rounded-full flex items-center justify-center">
                            <button @click="onCameraClicked"
                                class="bg-black bg-opacity-25 flex items-center justify-center p-2 rounded-full z-20 cursor-pointer hover:bg-opacity-40 transition-all">
                                <Icon icon="twemoji:camera" class="w-6 h-6 text-gray-200" />
                            </button>
                        </div>
                        <CoreImagePicker :url="ENDPOINTS.PROFILE.UPDATE_PHOTO" field="photo" ref="imageUploadComponent" @onUploaded="fetchUser"/>
                        <Icon v-if="profile?.email_verified_at" icon="material-symbols-light:mark-email-read-sharp"
                            class="absolute w-6 h-6 mr-2 text-sky-500 -bottom-2 -right-0" />
                    </div>

                    <h3 class="text-lg font-semibold text-gray-900 dark:text-zinc-100">{{ profile?.name }}</h3>
                    <p class="text-base font-light text-gray-600 dark:text-zinc-300">{{ profile?.email }}</p>
                </div>
                <div class="w-full grid grid-cols-2 gap-2">
                    <div class="flex flex-col items-center space-y-1 border-r border-gray-200 dark:border-zinc-600">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-zinc-100">{{ profile?.followers_count || 0 }}</h3>
                        <p class="text-base text-gray-500 dark:text-zinc-400">Followers</p>
                    </div>
                    <div class="flex flex-col items-center space-y-1">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-zinc-100">{{ profile?.following_count || 0 }}</h3>
                        <p class="text-base text-gray-500 dark:text-zinc-400">Following</p>
                    </div>
                </div>
                <div class="w-full mx-auto flex flex-col items-center justify-center mt-3">
                    <p class="text-base text-gray-500 dark:text-zinc-400 text-center mb-2 line-clamp-1" v-html="profile?.profile?.about || 'No bio available'"></p>
                </div>
            </div>

            <div class="w-full lg:col-span-3 shadow-lg bg-white dark:bg-zinc-800 border border-gray-200 dark:border-zinc-700 focus:outline-none focus:ring-none">
                <div>
                    <div class="bg-gray-50 dark:bg-zinc-700 border-b border-gray-200 dark:border-zinc-600 md:hidden">
                        <div class="grid grid-cols-3 gap-0">
                            <button v-for="tab in tabs" v-bind:key="tab.name.toString().toLowerCase()"
                                class="px-2 py-3 flex flex-col items-center justify-center text-center hover:bg-red-600 hover:text-white transition duration-150 border-r border-b border-gray-200 dark:border-zinc-600 last:border-r-0"
                                :class="currentTab.name == tab.name ? 'bg-red-600 text-white' : 'text-gray-500 dark:text-zinc-400'"
                                @click="setCurrent(tab)">
                                <Icon :icon="tab.icon" class="w-4 h-4 mb-1" />
                                <span class="text-xs font-medium">{{ tab.name }}</span>
                            </button>
                        </div>
                    </div>

                    <div class="bg-gray-50 dark:bg-zinc-700 border-b border-gray-200 dark:border-zinc-600 overflow-x-auto hidden md:block">
                        <div class="flex min-w-max">
                            <button v-for="tab in tabs" v-bind:key="tab.name.toString().toLowerCase()"
                                class="px-6 py-3 flex items-center text-center hover:bg-red-600 hover:text-white transition duration-150 whitespace-nowrap flex-shrink-0"
                                :class="currentTab.name == tab.name ? 'bg-red-600 text-white' : 'border-r border-gray-200 dark:border-zinc-600 text-gray-500 dark:text-zinc-100'"
                                @click="setCurrent(tab)">
                                <Icon :icon="tab.icon" class="w-5 h-5 mr-2" />
                                <span class="font-medium">{{ tab.name }}</span>
                            </button>
                        </div>
                    </div>
                    <div>
                        <component v-bind="componentProps" :is="tabs.find((tab: Tab) => tab.name == currentTab.name)?.component" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { LazyProfileAbout, LazyProfileSettings, LazyProfileEvents, LazyProfileTickets } from '#components';
import type { Component } from 'vue';
import type { User } from '@/types/user';
import { useAuthStore } from '@/store/auth';

definePageMeta({
    layout: "default",
    middleware: ["auth"]
})

useHead({
    title: `My Profile - EventaHub Malawi`
});

interface Tab {
    name: string;
    icon: string;
    component?: Component;
}

const runtimeConfig = useRuntimeConfig();
const router = useRouter();
const route = useRoute();
const httpClient = useHttpClient();
const profile = ref<User>();
const userStore = useAuthStore();
const isHovered = ref<boolean>(false);
const imageUploadComponent = ref<{ openDialog: () => void } | null>(null);
const loading = ref<boolean>(true);
const tickets = ref<any[]>([]);

const tabs = ref<Tab[]>([
    {
        name: "Events",
        icon: "fluent-mdl2:schedule-event-action",
        component: markRaw(LazyProfileEvents)
    },
    {
        name: "Tickets",
        icon: "icon-park-outline:ticket",
        component: markRaw(LazyProfileTickets)
    },
    {
        name: "Venues",
        icon: "fluent:building-people-24-regular",
        component: markRaw(defineAsyncComponent(() => import('@/components/profile/VenueBookings.vue')))
    },
    {
        name: "Vendors",
        icon: "gala:store",
        component: markRaw(defineAsyncComponent(() => import('@/components/profile/VendorBookings.vue')))
    },
    {
        name: "About",
        icon: "oui:user",
        component: markRaw(LazyProfileAbout)
    },
    {
        name: "Settings",
        icon: "simple-line-icons:settings",
        component: markRaw(LazyProfileSettings)
    }
]);

const componentPropsMap: Record<string, (currentTab: Tab) => any> = {
    'Events': (_currentTab: Tab) => ({
        events: profile.value?.events || [],
        loading: loading.value,
        profile: profile.value,
        onRefresh: fetchUser
    }),
    'Tickets': (_currentTab: Tab) => ({
        tickets: tickets.value,
        loading: loading.value,
        profile: profile.value,
        onRefresh: fetchTickets
    }),
    'Venues': (_currentTab: Tab) => ({
        loading: loading.value,
        profile: profile.value,
        onRefresh: fetchUser
    }),
    'Vendors': (_currentTab: Tab) => ({
        loading: loading.value,
        profile: profile.value,
        onRefresh: fetchUser
    }),
    'About': (_currentTab: Tab) => ({
        profile: profile.value,
        loading: loading.value,
        onProfileUpdated: fetchUser
    }),
    'Settings': (_currentTab: Tab) => ({
        profile: profile.value,
        loading: loading.value,
        onRefresh: fetchUser
    }),
};

const componentProps = computed(() => {
    const propsGenerator = componentPropsMap[currentTab.value.name as keyof typeof componentPropsMap] || (() => ({}));
    return propsGenerator(currentTab.value);
});

const initialTab = computed((): Tab => {
    const foundTab = route.query.tab ? tabs.value.find((tab: Tab) => tab.name === String(route.query.tab)) : undefined;
    return foundTab || tabs.value[0];
})
const currentTab = ref<Tab>(initialTab.value);
const setCurrent = (tab: Tab): void => {
    currentTab.value = tab;
    const currentRoute = router.currentRoute.value;
    const newQuery = {
        ...currentRoute.query,
        tab: tab.name,
    };
    router.replace({ query: newQuery });
}

const onCameraClicked = (): void => {
    imageUploadComponent.value?.openDialog();
}

const fetchUser = async (): Promise<void> => {
    try {
        loading.value = true;
        const response = await httpClient.get<any>(ENDPOINTS.PROFILE.USER);
        if (response) {
            profile.value = response;
            userStore.setUser(response);
        }
    } catch (error) {
        console.error('Error fetching user profile:', error);
    } finally {
        loading.value = false;
    }
};

const fetchTickets = async (): Promise<void> => {
    try {
        const response = await httpClient.get<any>('/tickets/my-purchases?per_page=100');
        if (response) {
            // Handle paginated response structure
            const ticketData = response.data?.data || response.data || [];
            // Filter out null/undefined tickets
            tickets.value = Array.isArray(ticketData) ? ticketData.filter(ticket => ticket != null) : [];
        }
    } catch (error) {
        console.error('Error fetching tickets:', error);
        tickets.value = [];
    }
};

onMounted(() => {
    fetchUser();
    fetchTickets();
});
</script>
<style scoped></style>
