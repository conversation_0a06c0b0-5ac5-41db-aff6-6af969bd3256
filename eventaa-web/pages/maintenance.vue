<template>
  <div class="min-h-screen bg-white dark:bg-zinc-800 flex items-center justify-center px-4">
    <div class="max-w-2xl mx-auto text-center">
      <div class="mb-8">
        <img
          src="/icon.png"
          alt="EventaHub Logo"
          class="h-20 w-20 mx-auto rounded-full shadow-lg"
        >
      </div>

      <div class="mb-8">
        <div class="w-48 h-48 mx-auto mb-6">
          <svg class="w-full h-full text-blue-500" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd" />
          </svg>
        </div>
      </div>

      <div class="mb-8">
        <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
          We'll be back soon!
        </h1>
        <p class="text-xl text-gray-600 dark:text-gray-400 mb-8 max-w-xl mx-auto">
          EventaHub is currently undergoing scheduled maintenance to improve your experience. We appreciate your patience and will be back online shortly.
        </p>
      </div>

      <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-8 mb-8">
        <div class="grid md:grid-cols-2 gap-6">
          <div class="text-center">
            <svg class="w-8 h-8 text-blue-600 dark:text-blue-500 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
            </svg>
            <h3 class="font-semibold text-gray-900 dark:text-white">Started</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">{{ maintenanceStart }}</p>
          </div>
          <div class="text-center">
            <svg class="w-8 h-8 text-green-600 dark:text-green-500 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
            </svg>
            <h3 class="font-semibold text-gray-900 dark:text-white">Expected End</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">{{ maintenanceEnd }}</p>
          </div>
        </div>
      </div>

      <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-8 mb-8">
        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">
          What we're improving:
        </h3>
        <div class="grid md:grid-cols-2 gap-4">
          <div class="flex items-start">
            <svg class="w-6 h-6 text-green-500 mr-3 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd" />
            </svg>
            <div>
              <h4 class="font-semibold text-gray-900 dark:text-white">Performance Enhancements</h4>
              <p class="text-sm text-gray-600 dark:text-gray-400">Faster page loads and better responsiveness</p>
            </div>
          </div>
          <div class="flex items-start">
            <svg class="w-6 h-6 text-green-500 mr-3 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
            <div>
              <h4 class="font-semibold text-gray-900 dark:text-white">Security Updates</h4>
              <p class="text-sm text-gray-600 dark:text-gray-400">Enhanced protection for your data</p>
            </div>
          </div>
          <div class="flex items-start">
            <svg class="w-6 h-6 text-green-500 mr-3 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
            </svg>
            <div>
              <h4 class="font-semibold text-gray-900 dark:text-white">New Features</h4>
              <p class="text-sm text-gray-600 dark:text-gray-400">Exciting new tools and capabilities</p>
            </div>
          </div>
          <div class="flex items-start">
            <svg class="w-6 h-6 text-green-500 mr-3 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clip-rule="evenodd" />
            </svg>
            <div>
              <h4 class="font-semibold text-gray-900 dark:text-white">Bug Fixes</h4>
              <p class="text-sm text-gray-600 dark:text-gray-400">Resolving reported issues</p>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6 mb-8">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Stay Updated
        </h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          Follow us on social media for real-time updates on our maintenance progress.
        </p>
        <div class="flex justify-center space-x-4">
          <a
            href="https://twitter.com/EventaHubMW"
            target="_blank"
            rel="noopener noreferrer"
            class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200"
          >
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path d="M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84" />
            </svg>
            Twitter
          </a>
          <a
            href="https://facebook.com/EventaHubMW"
            target="_blank"
            rel="noopener noreferrer"
            class="inline-flex items-center px-4 py-2 bg-blue-800 hover:bg-blue-900 text-white rounded-lg transition-colors duration-200"
          >
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M20 10C20 4.477 15.523 0 10 0S0 4.477 0 10c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V10h2.54V7.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V10h2.773l-.443 2.89h-2.33v6.988C16.343 19.128 20 14.991 20 10z" clip-rule="evenodd" />
            </svg>
            Facebook
          </a>
        </div>
      </div>

      <div class="text-sm text-gray-500 dark:text-gray-400 mb-8">
        <p>This page automatically refreshes every {{ refreshInterval / 1000 }} seconds.</p>
        <p class="mt-1">Last updated: {{ lastUpdated }}</p>
      </div>

      <div class="text-center text-sm text-gray-500 dark:text-gray-400">
        <p>&copy; {{ new Date().getFullYear() }} EventaHub Malawi. All rights reserved.</p>
        <p class="mt-1">Powered by Brainy Technologies</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const refreshInterval = 60000 // 60 seconds
const lastUpdated = ref(new Date().toLocaleString())
const maintenanceStart = ref('January 20, 2025 - 2:00 AM CAT')
const maintenanceEnd = ref('January 20, 2025 - 6:00 AM CAT')

let refreshTimer: NodeJS.Timeout

onMounted(() => {
  refreshTimer = setInterval(() => {
    lastUpdated.value = new Date().toLocaleString()
    window.location.reload()
  }, refreshInterval)
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})

useHead({
  title: 'Scheduled Maintenance - EventaHub',
  meta: [
    { name: 'description', content: 'EventaHub is currently undergoing scheduled maintenance. We will be back online shortly.' },
    { name: 'robots', content: 'noindex, nofollow' }
  ]
})

definePageMeta({
  layout: false
})
</script>
