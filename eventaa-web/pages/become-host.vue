<template>
  <div class="min-h-screen">
    <div class="container mx-auto px-4 py-8">
      <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="text-center mb-8">
          <h1 class="text-3xl font-bold dashboard-text-primary mb-4">Become a Host</h1>
          <p class="text-lg dashboard-text-muted max-w-2xl mx-auto">
            Join our community of event creators and start hosting amazing events for your audience.
          </p>
        </div>

        <!-- Check if user already has host role -->
        <div v-if="hasHostRole" class="max-w-2xl mx-auto">
          <div class="dashboard-bg-card dashboard-shadow overflow-hidden text-center p-8">
            <div class="mb-4">
              <Icon icon="heroicons:check-circle" class="mx-auto h-16 w-16 text-green-500" />
            </div>
            <h2 class="text-xl font-semibold dashboard-text-primary mb-2">You're Already a Host!</h2>
            <p class="dashboard-text-muted mb-6">
              You have host privileges and can create events. Head to your dashboard to get started.
            </p>
            <NuxtLink
              to="/dashboard"
              class="inline-flex items-center px-6 py-3 bg-red-600 text-white font-medium hover:bg-red-700 dashboard-transition"
            >
              Go to Dashboard
              <Icon icon="heroicons:arrow-right" class="ml-2 h-4 w-4" />
            </NuxtLink>
          </div>
        </div>

        <!-- Show request form or status -->
        <div v-else>
          <HostRequestForm
            v-if="showRequestForm"
            @cancel="showRequestForm = false"
            @success="handleRequestSuccess"
          />

          <HostRequestStatus
            v-else
            ref="statusComponent"
            @request-host="showRequestForm = true"
          />
        </div>

        <!-- Benefits Section -->
        <div v-if="!hasHostRole" class="mt-12">
          <div class="text-center mb-8">
            <h2 class="text-2xl font-bold dashboard-text-primary mb-4">Why Become a Host?</h2>
            <p class="dashboard-text-muted">
              Discover the benefits of hosting events on our platform
            </p>
          </div>

          <div class="grid md:grid-cols-3 gap-6">
            <div class="dashboard-bg-card dashboard-shadow p-6 text-center">
              <div class="mb-4">
                <Icon icon="heroicons:calendar-days" class="mx-auto h-12 w-12 text-red-500" />
              </div>
              <h3 class="text-lg font-semibold dashboard-text-primary mb-2">Create Events</h3>
              <p class="dashboard-text-muted text-sm">
                Design and manage your own events with our powerful event creation tools.
              </p>
            </div>

            <div class="dashboard-bg-card dashboard-shadow p-6 text-center">
              <div class="mb-4">
                <Icon icon="heroicons:users" class="mx-auto h-12 w-12 text-red-500" />
              </div>
              <h3 class="text-lg font-semibold dashboard-text-primary mb-2">Build Community</h3>
              <p class="dashboard-text-muted text-sm">
                Connect with your audience and build a community around your events.
              </p>
            </div>

            <div class="dashboard-bg-card dashboard-shadow p-6 text-center">
              <div class="mb-4">
                <Icon icon="heroicons:chart-bar" class="mx-auto h-12 w-12 text-red-500" />
              </div>
              <h3 class="text-lg font-semibold dashboard-text-primary mb-2">Track Performance</h3>
              <p class="dashboard-text-muted text-sm">
                Get detailed analytics and insights about your events and attendees.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue';
import { useAuthStore } from '@/store/auth';
import HostRequestForm from '@/components/host/RequestForm.vue';
import HostRequestStatus from '@/components/host/RequestStatus.vue';

definePageMeta({
  middleware: 'auth',
  layout: 'default'
});

useHead({
  title: 'Become an Event Host | Start Creating Events - EventaHub Malawi',
  meta: [
    {
      name: 'description',
      content: 'Join our community of event creators and start hosting amazing events in Malawi. Create conferences, workshops, concerts and more with EventaHub\'s powerful event management tools.'
    },
    {
      name: 'keywords',
      content: 'become event host Malawi, create events, event organizer, host events Malawi, event management, event hosting platform'
    },
    {
      property: 'og:title',
      content: 'Become an Event Host | Start Creating Events - EventaHub Malawi'
    },
    {
      property: 'og:description',
      content: 'Join our community of event creators and start hosting amazing events in Malawi. Create conferences, workshops, concerts and more.'
    },
    {
      property: 'og:type',
      content: 'website'
    },
    {
      name: 'twitter:card',
      content: 'summary_large_image'
    },
    {
      name: 'twitter:title',
      content: 'Become an Event Host | Start Creating Events - EventaHub Malawi'
    },
    {
      name: 'twitter:description',
      content: 'Join our community of event creators and start hosting amazing events in Malawi.'
    },
    {
      name: 'robots',
      content: 'index, follow'
    }
  ]
});

const authStore = useAuthStore();
const statusComponent = ref<any>(null);
const showRequestForm = ref(false);

const hasHostRole = computed(() => {
  return authStore.user?.roles?.includes('host') || false;
});

const handleRequestSuccess = (request: any) => {
  showRequestForm.value = false;
  if (statusComponent.value) {
    statusComponent.value.fetchRequest();
  }
};

onMounted(() => {
  if (!hasHostRole.value) {
    nextTick(() => {
      if (statusComponent.value) {
        statusComponent.value.fetchRequest();
      }
    });
  }
});
</script>
