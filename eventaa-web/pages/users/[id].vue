<template>
  <div class="min-h-screen bg-gray-50 dark:bg-zinc-900">
    <div v-if="loading" class="min-h-screen">
      <SkeletonUserProfile />
    </div>

    <div v-else-if="error" class="min-h-screen flex items-center justify-center">
      <div class="max-w-md mx-auto text-center p-8">
        <div class="w-20 h-20 mx-auto mb-6 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
          <Icon icon="heroicons:exclamation-triangle" class="w-10 h-10 text-red-500 dark:text-red-400" />
        </div>
        <h2 class="text-2xl font-bold text-gray-900 dark:text-zinc-100 mb-4">User Not Found</h2>
        <p class="text-gray-600 dark:text-zinc-400 mb-8 leading-relaxed">Please try again later</p>
        <div class="flex flex-col sm:flex-row gap-3 justify-center">
          <CorePrimaryButton
            text="Go Back"
            @click="$router.go(-1)"
            color="neutral"
            variant="outlined"
          />
        </div>
      </div>
    </div>

    <div v-else-if="user" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="bg-white dark:bg-zinc-800 shadow mb-8 overflow-hidden">
        <div class="px-6 py-8">
          <div class="flex flex-col md:flex-row items-start md:items-center gap-6">
            <div class="relative group">
              <div class="w-72 h-72 rounded-xl overflow-hidden border-4 border-white dark:border-zinc-600 shadow-lg bg-gradient-to-br from-red-500 to-red-600">
                <img
                  v-if="user.avatar"
                  :src="`${runtimeConfig.public.baseUrl}storage/avatars/${user.avatar}`"
                  :alt="user.name"
                  class="w-full h-full object-cover transition-transform group-hover:scale-105"
                />
                <div v-else class="w-full h-full flex items-center justify-center text-white text-2xl font-bold">
                  {{ user.name.charAt(0).toUpperCase() }}
                </div>
              </div>
              <div
                v-if="user?.email_verified_at"
                class="absolute -bottom-2 -right-2 w-8 h-8 bg-sky-600 rounded-full border-3 border-white dark:border-zinc-800 flex items-center justify-center shadow-lg"
                :title="'Verified user since ' + formatDate(user.email_verified_at)"
              >
                <Icon icon="material-symbols-light:mark-email-read-sharp" class="w-5 h-5 text-white" />
              </div>
            </div>

            <div class="flex-1 min-w-0">
              <div class="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4">
                <div class="min-w-0 flex-1">
                  <div class="flex items-center gap-3 mb-2">
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-zinc-100 truncate">{{ user.name }}</h1>
                  </div>

                  <p v-if="user.profile?.about" class="text-gray-600 dark:text-zinc-400 mt-2 leading-relaxed line-clamp-2">
                    {{ user.profile.about }}
                  </p>

                  <!-- Rating -->
                  <div class="flex items-center gap-2 mt-3">
                    <CoreStarRating
                      :show-rating="true"
                      :model-value="Number(user.ratings_avg_rating) || 0"
                      :disabled="true"
                      class="text-sm"
                    />
                  </div>

                  <!-- Join Date -->
                  <div class="flex items-center gap-1 mt-2 text-sm text-gray-500 dark:text-zinc-500">
                    <Icon icon="heroicons:calendar" class="w-4 h-4" />
                    <span>Joined {{ formatDate(user.created_at) }}</span>
                  </div>
                </div>

                <div class="flex items-center gap-3 flex-shrink-0">
                  <CorePrimaryButton
                    v-if="!isOwnProfile"
                    :text="isFollowing ? 'Unfollow' : 'Follow'"
                    :start-icon="followLoading ? 'line-md:loading-loop' : (isFollowing ? 'heroicons:user-minus' : 'heroicons:user-plus')"
                    @click="handleFollowToggle"
                    :loading="followLoading"
                    :color="isFollowing ? 'neutral' : 'primary'"
                    :variant="isFollowing ? 'outlined' : 'filled'"
                    size="medium"
                  />
                </div>
              </div>

              <div class="flex items-center gap-8 mt-6 pt-6 border-t border-gray-100 dark:border-zinc-700">
                <div class="text-center">
                  <div class="text-2xl font-bold text-gray-900 dark:text-zinc-100">{{ user.events?.length || 0 }}</div>
                  <div class="text-sm text-gray-600 dark:text-zinc-400">Events</div>
                </div>
                <div class="text-center cursor-pointer hover:bg-gray-50 dark:hover:bg-zinc-700/50  px-3 py-2 transition-colors">
                  <div class="text-2xl font-bold text-gray-900 dark:text-zinc-100">{{ user.followers_count || 0 }}</div>
                  <div class="text-sm text-gray-600 dark:text-zinc-400">Followers</div>
                </div>
                <div class="text-center cursor-pointer hover:bg-gray-50 dark:hover:bg-zinc-700/50  px-3 py-2 transition-colors">
                  <div class="text-2xl font-bold text-gray-900 dark:text-zinc-100">{{ user.following_count || 0 }}</div>
                  <div class="text-sm text-gray-600 dark:text-zinc-400">Following</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-if="user.facebook_url || user.twitter_url || user.profile?.instagram"
        class="bg-white dark:bg-zinc-800 shadow mb-8 overflow-hidden">
        <div class="px-6 py-6">
          <div class="flex items-center gap-2 mb-4">
            <Icon icon="heroicons:link" class="w-5 h-5 text-gray-400 dark:text-zinc-500" />
            <h3 class="text-lg font-semibold text-gray-900 dark:text-zinc-100">Social Links</h3>
          </div>
          <div class="flex items-center gap-4">
            <a v-if="user.facebook_url" :href="user.facebook_url" target="_blank" rel="noopener noreferrer"
              class="flex items-center gap-2 px-4 py-2 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400  hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors">
              <Icon icon="mdi:facebook" class="w-5 h-5" />
              <span class="text-sm font-medium">Facebook</span>
            </a>
            <a v-if="user.twitter_url" :href="user.twitter_url" target="_blank" rel="noopener noreferrer"
              class="flex items-center gap-2 px-4 py-2 bg-sky-50 dark:bg-sky-900/20 text-sky-600 dark:text-sky-400  hover:bg-sky-100 dark:hover:bg-sky-900/30 transition-colors">
              <Icon icon="mdi:twitter" class="w-5 h-5" />
              <span class="text-sm font-medium">Twitter</span>
            </a>
            <a v-if="user.profile?.instagram" :href="user.profile.instagram" target="_blank" rel="noopener noreferrer"
              class="flex items-center gap-2 px-4 py-2 bg-pink-50 dark:bg-pink-900/20 text-pink-600 dark:text-pink-400  hover:bg-pink-100 dark:hover:bg-pink-900/30 transition-colors">
              <Icon icon="mdi:instagram" class="w-5 h-5" />
              <span class="text-sm font-medium">Instagram</span>
            </a>
          </div>
        </div>
      </div>      <!-- Tabbed Content Section -->
      <div class="bg-white dark:bg-zinc-800 shadow overflow-hidden border border-gray-200 dark:border-zinc-700">

        <div class="bg-gray-50 dark:bg-zinc-700 border-b border-gray-200 dark:border-zinc-600 md:hidden">
          <div class="grid grid-cols-3 gap-0">
            <button
              @click="activeTab = 'events'"
              class="px-2 py-3 flex flex-col items-center justify-center text-center hover:bg-red-600 hover:text-white transition duration-150 border-r border-b border-gray-200 dark:border-zinc-600 last:border-r-0"
              :class="activeTab === 'events' ? 'bg-red-600 text-white' : 'text-gray-500 dark:text-zinc-400'"
            >
              <Icon icon="fluent-mdl2:schedule-event-action" class="w-4 h-4 mb-1" />
              <span class="text-xs font-medium">Events</span>
            </button>

            <button
              @click="activeTab = 'vendors'"
              class="px-2 py-3 flex flex-col items-center justify-center text-center hover:bg-red-600 hover:text-white transition duration-150 border-r border-b border-gray-200 dark:border-zinc-600 last:border-r-0"
              :class="activeTab === 'vendors' ? 'bg-red-600 text-white' : 'text-gray-500 dark:text-zinc-400'"
            >
              <Icon icon="gala:store" class="w-4 h-4 mb-1" />
              <span class="text-xs font-medium">Vendors</span>
            </button>

            <button
              @click="activeTab = 'venues'"
              class="px-2 py-3 flex flex-col items-center justify-center text-center hover:bg-red-600 hover:text-white transition duration-150 border-r border-b border-gray-200 dark:border-zinc-600 last:border-r-0"
              :class="activeTab === 'venues' ? 'bg-red-600 text-white' : 'text-gray-500 dark:text-zinc-400'"
            >
              <Icon icon="fluent:building-people-24-regular" class="w-4 h-4 mb-1" />
              <span class="text-xs font-medium">Venues</span>
            </button>
          </div>
        </div>

        <!-- Tabs - Desktop -->
        <div class="bg-gray-50 dark:bg-zinc-700 border-b border-gray-200 dark:border-zinc-600 overflow-x-auto hidden md:block">
          <div class="flex min-w-max">
            <button
              @click="activeTab = 'events'"
              class="px-6 py-3 flex items-center text-center hover:bg-red-600 hover:text-white transition duration-150 whitespace-nowrap flex-shrink-0"
              :class="activeTab === 'events' ? 'bg-red-600 text-white' : 'border-r border-gray-200 dark:border-zinc-600 text-gray-500 dark:text-zinc-100'"
            >
              <Icon icon="fluent-mdl2:schedule-event-action" class="w-5 h-5 mr-2" />
              <span class="font-medium">Events</span>
            </button>

            <button
              @click="activeTab = 'vendors'"
              class="px-6 py-3 flex items-center text-center hover:bg-red-600 hover:text-white transition duration-150 whitespace-nowrap flex-shrink-0"
              :class="activeTab === 'vendors' ? 'bg-red-600 text-white' : 'border-r border-gray-200 dark:border-zinc-600 text-gray-500 dark:text-zinc-100'"
            >
              <Icon icon="gala:store" class="w-5 h-5 mr-2" />
              <span class="font-medium">Vendors</span>
            </button>

            <button
              @click="activeTab = 'venues'"
              class="px-6 py-3 flex items-center text-center hover:bg-red-600 hover:text-white transition duration-150 whitespace-nowrap flex-shrink-0"
              :class="activeTab === 'venues' ? 'bg-red-600 text-white' : 'border-r border-gray-200 dark:border-zinc-600 text-gray-500 dark:text-zinc-100'"
            >
              <Icon icon="fluent:building-people-24-regular" class="w-5 h-5 mr-2" />
              <span class="font-medium">Venues</span>
            </button>
          </div>
        </div>

        <div class="relative">
          <div v-show="activeTab === 'events'">
            <div v-if="user.events && user.events.length > 0" class="p-6">
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div v-for="event in user.events.slice(0, 6)" :key="event.id"
                  class="group border border-gray-200 dark:border-zinc-700 rounded-xl overflow-hidden hover:shadow-lg hover:border-gray-300 dark:hover:border-zinc-600 transition-all duration-200">
                  <nuxt-link :to="`/events/${event.slug}`" class="block">
                    <div class="relative">
                      <img
                        v-if="event.cover_art"
                        :src="`${runtimeConfig.public.baseUrl}storage/events/${event.cover_art}`"
                        :alt="event.slug"
                        class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-200"
                      />
                      <div v-else class="w-full h-48 bg-gradient-to-br from-red-500 to-red-600 flex items-center justify-center">
                        <Icon icon="heroicons:calendar-days" class="w-12 h-12 text-white opacity-50" />
                      </div>
                      <div class="absolute top-3 left-3">
                        <span
                          :class="new Date(event.end) > new Date()
                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                            : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'"
                          class="px-2 py-1 rounded-full text-xs font-medium"
                        >
                          {{ new Date(event.end) > new Date() ? 'Active' : 'Ended' }}
                        </span>
                      </div>
                    </div>
                    <div class="p-4">
                      <h4 class="font-semibold text-lg text-gray-900 dark:text-zinc-100 mb-2 line-clamp-2 group-hover:text-red-600 dark:group-hover:text-red-400 transition-colors">
                        {{ event.title }}
                      </h4>
                      <p v-if="event.description" v-html="event.description" class="text-base text-gray-600 dark:text-zinc-400 mb-3 line-clamp-2">
                      </p>
                      <div class="flex items-center justify-between text-sm">
                        <div class="flex items-center text-gray-500 dark:text-zinc-500">
                          <Icon icon="heroicons:calendar" class="w-4 h-4 mr-1" />
                          {{ formatDate(event.start) }}
                        </div>
                        <div v-if="event.location" class="flex items-center text-gray-500 dark:text-zinc-500 max-w-24 truncate">
                          <Icon icon="heroicons:map-pin" class="w-4 h-4 mr-1 flex-shrink-0" />
                          <span class="truncate">{{ event.location }}</span>
                        </div>
                      </div>
                    </div>
                  </nuxt-link>
                </div>
              </div>
            </div>

            <div v-else class="p-12 text-center">
              <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-zinc-700 rounded-full flex items-center justify-center">
                <Icon icon="heroicons:calendar-days" class="w-8 h-8 text-gray-400 dark:text-zinc-500" />
              </div>
              <h4 class="text-lg font-medium text-gray-900 dark:text-zinc-100 mb-2">No Events Yet</h4>
              <p class="text-gray-600 dark:text-zinc-400 max-w-sm mx-auto">
                This user hasn't created any events yet. Check back later to see what they're planning!
              </p>
            </div>
          </div>

          <div v-show="activeTab === 'vendors'">
            <div v-if="user.vendors && user.vendors.length > 0" class="p-6">
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div v-for="vendor in user.vendors.slice(0, 6)" :key="vendor.id"
                  class="group border border-gray-200 dark:border-zinc-700 rounded-xl overflow-hidden hover:shadow-lg hover:border-gray-300 dark:hover:border-zinc-600 transition-all duration-200">
                  <nuxt-link :to="`/vendors/${vendor.slug}`" class="block">
                    <div class="relative">
                      <img
                        v-if="vendor.logo"
                        :src="`${runtimeConfig.public.baseUrl}storage/${vendor.logo}`"
                        :alt="vendor.name"
                        class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-200"
                      />
                      <div v-else class="w-full h-48 bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center">
                        <Icon icon="heroicons:building-storefront" class="w-12 h-12 text-white opacity-50" />
                      </div>
                      <div class="absolute top-3 left-3">
                        <span :class="vendor.is_available ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'" class="px-2 py-1 rounded-full text-xs font-medium">
                          {{ vendor.is_available ? 'Available' : 'Unavailable' }}
                        </span>
                      </div>
                    </div>
                    <div class="p-4">
                      <h4 class="font-semibold text-gray-900 dark:text-zinc-100 mb-2 line-clamp-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                        {{ vendor.name }}
                      </h4>
                      <p v-if="vendor.bio" class="text-sm text-gray-600 dark:text-zinc-400 mb-3 line-clamp-2">
                        {{ vendor.bio }}
                      </p>
                      <div class="flex items-center justify-between text-sm">
                        <div class="flex items-center text-gray-500 dark:text-zinc-500">
                          <Icon icon="heroicons:star" class="w-4 h-4 mr-1" />
                          {{ Number(vendor.ratings_avg_rating || 0).toFixed(1) }}
                        </div>
                        <div v-if="vendor.location" class="flex items-center text-gray-500 dark:text-zinc-500 max-w-24 truncate">
                          <Icon icon="heroicons:map-pin" class="w-4 h-4 mr-1 flex-shrink-0" />
                          <span class="truncate">{{ vendor.location }}</span>
                        </div>
                      </div>
                    </div>
                  </nuxt-link>
                </div>
              </div>

              <div v-if="user.vendors.length > 6" class="mt-6 text-center">
                <CorePrimaryButton
                  text="View All Vendors"
                  variant="outlined"
                  color="primary"
                  @click="() => $router.push(`/vendors?user=${userUuid}`)"
                />
              </div>
            </div>

            <div v-else class="p-12 text-center">
              <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-zinc-700 rounded-full flex items-center justify-center">
                <Icon icon="heroicons:building-storefront" class="w-8 h-8 text-gray-400 dark:text-zinc-500" />
              </div>
              <h4 class="text-lg font-medium text-gray-900 dark:text-zinc-100 mb-2">No Vendors Yet</h4>
              <p class="text-gray-600 dark:text-zinc-400 max-w-sm mx-auto">
                This user hasn't created any vendor services yet.
              </p>
            </div>
          </div>

          <div v-show="activeTab === 'venues'">
            <div v-if="user.venues && user.venues.length > 0" class="p-6">
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div v-for="venue in user.venues.slice(0, 6)" :key="venue.id"
                  class="group border border-gray-200 dark:border-zinc-700 rounded-xl overflow-hidden hover:shadow-lg hover:border-gray-300 dark:hover:border-zinc-600 transition-all duration-200">
                  <nuxt-link :to="`/venues/${venue.slug}`" class="block">
                    <div class="relative">
                      <img
                        v-if="venue.images && venue.images.length > 0"
                        :src="`${runtimeConfig.public.baseUrl}storage/${venue.images[0].image_path}`"
                        :alt="venue.name"
                        class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-200"
                      />
                      <div v-else class="w-full h-48 bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center">
                        <Icon icon="heroicons:map-pin" class="w-12 h-12 text-white opacity-50" />
                      </div>
                      <div class="absolute top-3 left-3">
                        <span class="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 px-2 py-1 rounded-full text-xs font-medium">
                          Available
                        </span>
                      </div>
                    </div>
                    <div class="p-4">
                      <h4 class="font-semibold text-gray-900 dark:text-zinc-100 mb-2 line-clamp-2 group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors">
                        {{ venue.name }}
                      </h4>
                      <p v-if="venue.description" class="text-sm text-gray-600 dark:text-zinc-400 mb-3 line-clamp-2">
                        {{ venue.description }}
                      </p>
                      <div class="flex items-center justify-between text-sm">
                        <div class="flex items-center text-gray-500 dark:text-zinc-500">
                          <Icon icon="heroicons:star" class="w-4 h-4 mr-1" />
                          {{ Number(venue.ratings_avg_rating || 0).toFixed(1) }}
                        </div>
                        <div v-if="venue.capacity" class="flex items-center text-gray-500 dark:text-zinc-500">
                          <Icon icon="heroicons:users" class="w-4 h-4 mr-1 flex-shrink-0" />
                          <span>{{ venue.capacity }} people</span>
                        </div>
                      </div>
                    </div>
                  </nuxt-link>
                </div>
              </div>

              <div v-if="user.venues.length > 6" class="mt-6 text-center">
                <CorePrimaryButton
                  text="View All Venues"
                  variant="outlined"
                  color="primary"
                  @click="() => $router.push(`/venues`)"
                />
              </div>
            </div>

            <div v-else class="p-12 text-center">
              <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-zinc-700 rounded-full flex items-center justify-center">
                <Icon icon="heroicons:map-pin" class="w-8 h-8 text-gray-400 dark:text-zinc-500" />
              </div>
              <h4 class="text-lg font-medium text-gray-900 dark:text-zinc-100 mb-2">No Venues Yet</h4>
              <p class="text-gray-600 dark:text-zinc-400 max-w-sm mx-auto">
                This user hasn't listed any venues yet.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { User } from '@/types/user'
import { useAuthStore } from '@/store/auth'
import { useUserMappingStore } from '@/store/userMapping'
import { ENDPOINTS } from '@/utils/api'

definePageMeta({
  middleware: 'user-profile'
})

const route = useRoute()
const runtimeConfig = useRuntimeConfig()
const httpClient = useHttpClient()
const { $toast }: any = useNuxtApp()

const authStore = useAuthStore()
const userMappingStore = useUserMappingStore()

const user = ref<User | null>(null);
const loading = ref(true);
const error = ref<string | null>(null);
const activeTab = ref<string>('events');
const followLoading = ref(false);

const userUuid = computed(() => route.params.id as string)
const userId = computed(() => {
  const id = userMappingStore.getIdFromUuid(userUuid.value)
  if (!id) {
    const directId = parseInt(userUuid.value)
    if (!isNaN(directId)) {
      return directId
    }
    return null
  }
  return id
})
const isOwnProfile = computed(() => authStore.user?.id === userId.value)
const isFollowing = computed(() => user.value?.is_following || false)


useHead({
  title: computed(() => user.value ? `${user.value.name} profile | EventaHub` : 'User profile | EventaHub')
})

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const fetchUser = async () => {
  try {
    loading.value = true
    error.value = null

    if (!userId.value) {
      error.value = 'Invalid user reference'
      return
    }

    const response = await httpClient.get<User>(`${ENDPOINTS.USERS.SHOW}/${userId.value}`)
    if (response) {
      user.value = response
      await nextTick(() => {
        useHead({
          title: `${response.name} - EventaHub`
        })
      })
    } else {
      error.value = 'User not found'
    }
  } catch (err: any) {
    console.error('Error fetching user:', err)
    if (err.response?.status === 404) {
      error.value = 'This user doesn\'t exist or has been removed.'
    } else if (err.response?.status === 403) {
      error.value = 'You don\'t have permission to view this user\'s profile.'
    } else {
      error.value = err.message || 'Failed to load user profile. Please try again.'
    }
  } finally {
    loading.value = false
  }
}

const handleFollowToggle = async () => {
  if (!authStore.isAuthenticated) {
    $toast.error('Please login to follow users')
    return
  }

  if (!user.value || !userId.value) return

  try {
    followLoading.value = true

    if (isFollowing.value) {
      const response = await httpClient.post<{ message: string }>(ENDPOINTS.FOLLOWERS.UNFOLLOW, {
        followee_id: userId.value
      })

      if (response) {
        $toast.success('User unfollowed successfully')
        await fetchUser()
      }
    } else {
      const response = await httpClient.post<{ message: string }>(ENDPOINTS.FOLLOWERS.CREATE, {
        followee_id: userId.value
      })

      if (response) {
        $toast.success('User followed successfully')
        await fetchUser()
      }
    }
  } catch (err: any) {
    console.error('Follow toggle error:', err)
    $toast.error(err.response?.data?.error || 'Something went wrong. Please try again.')
  } finally {
    followLoading.value = false
  }
}


onMounted(() => {
  fetchUser()
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.border-3 {
  border-width: 3px;
}

.group:hover .group-hover\:scale-105 {
  transform: scale(1.05);
}

* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
</style>
