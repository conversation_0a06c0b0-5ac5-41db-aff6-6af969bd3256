<template>
  <div class="min-h-screen bg-white dark:bg-zinc-900 py-16">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16">
        <div
          class="inline-block dark:text-zinc-400 px-4 py-2 text-sm font-medium mb-4 tracking-wider"
        >
          CONTACT US
        </div>
        <h1
          class="text-4xl md:text-5xl font-bold text-zinc-900 dark:text-zinc-100 mb-4"
        >
          Get in touch with us
        </h1>
        <p class="text-lg text-zinc-600 dark:text-zinc-400 max-w-2xl mx-auto">
          Fill out the form below or schedule a meeting with us at your
          convenience.
        </p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 mb-20">
        <div class="space-y-6">
          <div
            v-if="showSuccess"
            class="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-800 dark:text-green-200 flex items-center space-x-3"
          >
            <Icon
              icon="heroicons:check-circle"
              class="w-5 h-5 text-green-600 dark:text-green-400"
            />
            <span>{{ successMessage }}</span>
          </div>

          <FormKit
            type="form"
            @submit="handleSubmit"
            submit-label=""
            :actions="false"
            :disabled="loading"
          >
            <div class="space-y-3">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormKit
                  type="text"
                  name="first_name"
                  v-model="formData.first_name"
                  label="First Name"
                  placeholder="Enter your first name"
                  validation="required"
                  :disabled="loading"
                />
                <FormKit
                  type="text"
                  name="last_name"
                  v-model="formData.last_name"
                  label="Last Name"
                  placeholder="Enter your last name"
                  validation="required"
                  :disabled="loading"
                />
              </div>

              <FormKit
                type="email"
                name="email"
                v-model="formData.email"
                label="Email"
                placeholder="Enter your email address"
                validation="required|email"
                :disabled="loading"
                prefix-icon="email"
              />

              <FormKit
                type="tel"
                name="phone"
                prefix-icon="phone"
                v-model="formData.phone"
                label="Phone Number"
                placeholder="Enter your phone number"
                validation="required|length:9,15"
                :disabled="loading"
              />

              <FormKit
                type="text"
                name="subject"
                v-model="formData.subject"
                label="Subject"
                placeholder="Subject of your message"
                validation="required"
                :disabled="loading"
                inner-class="w-full"
                :classes="{
                  input: 'pl-2',
                  prefixIcon: 'w-0 h-0',
                }"
              />

              <FormKit
                type="textarea"
                name="message"
                v-model="formData.message"
                label="Message"
                placeholder="Enter your message"
                validation="required"
                :rows="4"
                :disabled="loading"
              />

              <FormKit
                type="checkbox"
                name="terms_agreement"
                v-model="formData.terms_agreement"
                label="I agree with Terms and Conditions"
                validation="required"
                :disabled="loading"
              >
                <template #label>
                  <span class="text-zinc-600 dark:text-zinc-400">
                    I agree with
                    <a
                      href="/terms-usage-policy"
                      class="text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300 underline"
                    >
                      Terms and Conditions
                    </a>
                  </span>
                </template>
              </FormKit>

              <CoreSubmitButton
                :loading="loading"
                :disabled="loading"
                text="Submit"
              />
            </div>
          </FormKit>
        </div>

        <div class="space-y-8">
          <div>
            <h3
              class="text-xl font-semibold text-zinc-900 dark:text-zinc-100 mb-6"
            >
              With our services you can
            </h3>
            <div class="space-y-4">
              <div class="flex items-center space-x-3">
                <div
                  class="flex-shrink-0 w-5 h-5 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center"
                >
                  <Icon
                    icon="heroicons:check"
                    class="w-3 h-3 text-green-600 dark:text-green-400"
                  />
                </div>
                <span class="text-zinc-700 dark:text-zinc-300"
                  >Generate events revenue, reports, engagements with ease</span
                >
              </div>
              <div class="flex items-center space-x-3">
                <div
                  class="flex-shrink-0 w-5 h-5 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center"
                >
                  <Icon
                    icon="heroicons:check"
                    class="w-3 h-3 text-green-600 dark:text-green-400"
                  />
                </div>
                <span class="text-zinc-700 dark:text-zinc-300"
                  >Engage users at a higher level and outperform your
                  event</span
                >
              </div>
              <div class="flex items-center space-x-3">
                <div
                  class="flex-shrink-0 w-5 h-5 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center"
                >
                  <Icon
                    icon="heroicons:check"
                    class="w-3 h-3 text-green-600 dark:text-green-400"
                  />
                </div>
                <span class="text-zinc-700 dark:text-zinc-300"
                  >Find the perfect venue for your upcoming event</span
                >
              </div>
              <div class="flex items-center space-x-3">
                <div
                  class="flex-shrink-0 w-5 h-5 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center"
                >
                  <Icon
                    icon="heroicons:check"
                    class="w-3 h-3 text-green-600 dark:text-green-400"
                  />
                </div>
                <span class="text-zinc-700 dark:text-zinc-300"
                  >Don't worry about event planning, vendors are right at the
                  corner</span
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import { useHttpClient } from "@/composables/useHttpClient";
import { ENDPOINTS } from "@/utils/api";
import { handleErrorWithToast } from "@/utils/errors";

definePageMeta({
  layout: "default",
});

useHead({
  title: "Contact EventaHub Malawi - Get in Touch with Our Team",
  meta: [
    {
      name: "description",
      content:
        "Get in touch with EventaHub Malawi team. Contact us for support, partnerships, event hosting inquiries, or any questions about our event platform.",
    },
    {
      name: "keywords",
      content:
        "contact EventaHub, support Malawi, event platform support, customer service, get in touch",
    },
    {
      property: "og:title",
      content: "Contact EventaHub Malawi - Get in Touch with Our Team",
    },
    {
      property: "og:description",
      content:
        "Get in touch with EventaHub Malawi team. Contact us for support, partnerships, event hosting inquiries, or any questions.",
    },
    {
      property: "og:type",
      content: "website",
    },
    {
      name: "twitter:card",
      content: "summary_large_image",
    },
    {
      name: "twitter:title",
      content: "Contact EventaHub Malawi - Get in Touch with Our Team",
    },
    {
      name: "twitter:description",
      content:
        "Get in touch with EventaHub Malawi team for support, partnerships, or event hosting inquiries.",
    },
    {
      name: "robots",
      content: "index, follow",
    },
  ],
});

const loading = ref<boolean>(false);
const showSuccess = ref<boolean>(false);
const successMessage = ref<string>("");

const formData = reactive({
  first_name: "",
  last_name: "",
  email: "",
  phone: "",
  subject: "",
  message: "",
  terms_agreement: false,
});

const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const handleSubmit = async () => {
  if (loading.value) return;

  showSuccess.value = false;
  loading.value = true;

  try {
    const response = await httpClient.post<{
      success: boolean;
      message: string;
    }>(ENDPOINTS.CONTACT.SUBMIT, {
      firstName: formData.first_name,
      lastName: formData.last_name,
      email: formData.email,
      phone: formData.phone,
      subject: formData.subject,
      message: formData.message,
      terms_agreement: formData.terms_agreement,
    });

    if (response.success) {
      successMessage.value =
        response.message ||
        "Thank you for your message! We will get back to you soon.";
      showSuccess.value = true;

      $toast.success(successMessage.value);

      // Reset form data
      Object.assign(formData, {
        first_name: "",
        last_name: "",
        email: "",
        phone: "",
        subject: "",
        message: "",
        terms_agreement: false,
      });

      setTimeout(() => {
        showSuccess.value = false;
      }, 5000);
    }
  } catch (error: any) {
    console.error("Contact form submission error:", error);
    handleError(error, $toast);
  } finally {
    loading.value = false;
  }
};
</script>
