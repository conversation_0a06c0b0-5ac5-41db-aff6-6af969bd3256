/* Dashboard Theme CSS Variables */

:root {
  /* Light Theme Colors */
  --color-primary: #ef4444;
  --color-primary-hover: #dc2626;
  --color-primary-light: #fecaca;

  --color-bg-main: #f9fafb;
  --color-bg-card: #ffffff;
  --color-bg-sidebar: #ffffff;
  --color-bg-header: #ffffff;
  --color-bg-input: #ffffff;
  --color-bg-hover: #f3f4f6;
  --color-bg-active: #f3f4f6;

  --color-border: #e5e7eb;
  --color-border-light: #f3f4f6;

  --color-text-primary: #111827;
  --color-text-secondary: #4b5563;
  --color-text-muted: #6b7280;
  --color-text-light: #9ca3af;

  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Dark Theme Colors */
.dark {
  --color-primary: #ef4444;
  --color-primary-hover: #dc2626;
  --color-primary-light: #7f1d1d;

  --color-bg-main: #18181b; /* zinc-900 */
  --color-bg-card: #27272a; /* zinc-800 */
  --color-bg-sidebar: #27272a; /* zinc-800 */
  --color-bg-header: #27272a; /* zinc-800 */
  --color-bg-input: #3f3f46; /* zinc-700 */
  --color-bg-hover: #3f3f46; /* zinc-700 */
  --color-bg-active: #52525b; /* zinc-600 */

  --color-border: #52525b; /* zinc-600 */
  --color-border-light: #71717a; /* zinc-500 */

  --color-text-primary: #f4f4f5; /* zinc-100 */
  --color-text-secondary: #d4d4d8; /* zinc-300 */
  --color-text-muted: #a1a1aa; /* zinc-400 */
  --color-text-light: #71717a; /* zinc-500 */

  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

/* Dashboard Theme Utility Classes */

/* Backgrounds */
.dashboard-bg-main {
  background-color: var(--color-bg-main);
}

.dashboard-bg-card {
  background-color: var(--color-bg-card);
}

.dashboard-bg-sidebar {
  background-color: var(--color-bg-sidebar);
}

.dashboard-bg-header {
  background-color: var(--color-bg-header);
}

.dashboard-bg-input {
  background-color: var(--color-bg-input);
}

.dashboard-bg-hover {
  background-color: var(--color-bg-hover);
}

.dashboard-bg-active {
  background-color: var(--color-bg-active);
}

.dashboard-bg-primary {
  background-color: var(--color-primary);
}

.dashboard-bg-primary-light {
  background-color: var(--color-primary-light);
}

/* Text Colors */
.dashboard-text-primary {
  color: var(--color-text-primary);
}

.dashboard-text-secondary {
  color: var(--color-text-secondary);
}

.dashboard-text-muted {
  color: var(--color-text-muted);
}

.dashboard-text-light {
  color: var(--color-text-light);
}

.dashboard-text-brand {
  color: var(--color-primary);
}

/* Borders */
.dashboard-border {
  border-color: var(--color-border);
}

.dashboard-border-light {
  border-color: var(--color-border-light);
}

/* Shadows */
.dashboard-shadow-sm {
  box-shadow: var(--shadow-sm);
}

.dashboard-shadow {
  box-shadow: var(--shadow);
}

.dashboard-shadow-md {
  box-shadow: var(--shadow-md);
}

.dashboard-shadow-lg {
  box-shadow: var(--shadow-lg);
}

/* Hover States */
.dashboard-hover-bg:hover {
  background-color: var(--color-bg-hover);
}

.dashboard-hover-primary:hover {
  background-color: var(--color-primary-hover);
}

/* Focus States */
.dashboard-focus-primary:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Transitions */
.dashboard-transition {
  transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Form Elements */
.dashboard-input {
  background-color: var(--color-bg-input);
  border-color: var(--color-border);
  color: var(--color-text-primary);
}

.dashboard-input:focus {
  border-color: var(--color-primary);
  outline: none;
  box-shadow: 0 0 0 2px var(--color-primary-light);
}

.dashboard-input::placeholder {
  color: var(--color-text-light);
}

/* Buttons */
.dashboard-btn-primary {
  background-color: var(--color-primary);
  color: white;
}

.dashboard-btn-primary:hover {
  background-color: var(--color-primary-hover);
}

.dashboard-btn-secondary {
  background-color: var(--color-bg-card);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
}

.dashboard-btn-secondary:hover {
  background-color: var(--color-bg-hover);
}
