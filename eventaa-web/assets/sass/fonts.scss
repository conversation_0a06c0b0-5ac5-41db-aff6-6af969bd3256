/* Light */
@font-face {
    font-family: 'Macan';
    src: url('../fonts/MacanPanWeb-Light.ttf') format('truetype');
    font-weight: 300;
    font-style: normal;
}

/* Regular */
@font-face {
    font-family: 'Macan';
    src: url('../fonts/MacanPanWeb-Book.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

/* Medium */
@font-face {
    font-family: 'Macan';
    src: url('../fonts/MacanPanWeb-Medium.ttf') format('truetype');
    font-weight: 500;
    font-style: normal;
}

/* Bold */
@font-face {
    font-family: 'Macan';
    src: url('../fonts/MacanPanWeb-Bold.ttf') format('truetype');
    font-weight: bold;
    font-style: normal;
}

/* Italic */
@font-face {
    font-family: 'Macan';
    src: url('../fonts/MacanPanWeb-Thin.ttf') format('truetype');
    font-weight: normal;
    font-style: italic;
}

/* Bold Italic */
@font-face {
    font-family: 'Macan';
    src: url('../fonts/MacanPanWeb-BoldItalic.ttf') format('truetype');
    font-weight: bold;
    font-style: italic;
}

* {
    font-family: 'Macan';
    font-size: 16px;
    user-select: none;
}


input[type="checkbox"] {
    all: unset;
    appearance: checkbox;
    -webkit-appearance: checkbox; /* For Safari */
    cursor: pointer;
}

input[type="radio"] {
    all: unset;
    appearance: radio;
    -webkit-appearance: radio; /* For Safari */
    cursor: pointer;
}