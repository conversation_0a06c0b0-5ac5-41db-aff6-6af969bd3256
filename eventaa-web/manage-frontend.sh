#!/bin/bash

# EventaHub Frontend Management Script
# Comprehensive management tool for EventaHub frontend applications

set -e

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
WEB_APP_NAME="eventahub-web"
WEB_APP_DIR="/var/www/eventahub/eventaa-web"
ECOSYSTEM_FILE="/var/www/eventahub/ecosystem.config.js"

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

show_help() {
    echo -e "${BLUE}EventaHub Frontend Management Script${NC}"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo -e "${YELLOW}Application Management:${NC}"
    echo "  start           - Start web application"
    echo "  stop            - Stop web application"
    echo "  restart         - Restart web application"
    echo "  reload          - Graceful reload of application"
    echo "  status          - Show status of application"
    echo "  delete          - Delete PM2 process"
    echo ""
    echo -e "${YELLOW}Deployment:${NC}"
    echo "  deploy          - Deploy web application"
    echo "  build           - Build web application"
    echo ""
    echo -e "${YELLOW}Monitoring & Logs:${NC}"
    echo "  logs            - Show logs for application"
    echo "  errors          - Show error logs only"
    echo "  follow          - Follow logs in real-time"
    echo "  monit           - Open PM2 monitoring dashboard"
    echo "  health          - Run health check"
    echo ""
    echo -e "${YELLOW}Maintenance:${NC}"
    echo "  update-deps     - Update dependencies"
    echo "  clear-logs      - Clear PM2 logs"
    echo "  backup          - Create backup of application"
    echo "  restore [file]  - Restore from backup file"
    echo ""
    echo -e "${YELLOW}Environment:${NC}"
    echo "  env-prod        - Switch to production environment"
    echo "  env-dev         - Switch to development environment"
    echo "  env-show        - Show current environment variables"
    echo ""
    echo -e "${YELLOW}System:${NC}"
    echo "  install         - Install/reinstall dependencies"
    echo "  setup           - Initial setup after deployment"
    echo "  help            - Show this help message"
    echo ""
}

check_pm2() {
    if ! command -v pm2 &> /dev/null; then
        print_error "PM2 is not installed. Please run the frontend PM2 setup script first."
        exit 1
    fi
}

check_apps_exist() {
    if [ ! -d "$WEB_APP_DIR" ]; then
        print_error "Application directory not found. Please ensure app is deployed."
        exit 1
    fi
}

start_applications() {
    print_status "Starting web application..."
    pm2 start $ECOSYSTEM_FILE 2>/dev/null || pm2 restart $ECOSYSTEM_FILE
    pm2 save
    print_status "Application started successfully!"
}

stop_applications() {
    print_status "Stopping web application..."
    pm2 stop $WEB_APP_NAME 2>/dev/null || true
    print_status "Application stopped!"
}

restart_applications() {
    print_status "Restarting web application..."
    pm2 restart $WEB_APP_NAME
    print_status "Application restarted successfully!"
}

reload_applications() {
    print_status "Gracefully reloading web application..."
    pm2 reload $WEB_APP_NAME
    print_status "Application reloaded successfully!"
}

show_status() {
    echo -e "${BLUE}=== EventaHub Frontend Status ===${NC}"
    echo ""
    pm2 status
    echo ""
    echo -e "${YELLOW}Application URL:${NC}"
    echo "  Web: http://localhost:4000"
    echo ""
    echo -e "${YELLOW}Health Status:${NC}"
    /usr/local/bin/eventahub-frontend-health 2>/dev/null || echo "Health check script not available"
}

deploy_web() {
    print_status "Deploying web application..."

    if [ ! -d "$WEB_APP_DIR" ]; then
        print_error "Web application directory not found: $WEB_APP_DIR"
        exit 1
    fi

    cd $WEB_APP_DIR

    # Pull latest changes
    print_status "Pulling latest changes..."
    git pull origin main

    # Install dependencies
    print_status "Installing dependencies..."
    npm ci --only=production

    # Build application
    print_status "Building application..."
    npm run build

    # Reload PM2 process
    print_status "Reloading PM2 process..."
    pm2 reload $WEB_APP_NAME

    print_status "Web application deployed successfully!"
}

deploy_all() {
    print_status "Deploying web application..."
    deploy_web
    print_status "Application deployed successfully!"
}build_web() {
    print_status "Building web application..."
    cd $WEB_APP_DIR
    npm run build
    print_status "Web application built successfully!"
}

show_logs() {
    case "${2:-all}" in
        web|all|*)
            pm2 logs $WEB_APP_NAME --lines 100
            ;;
        errors)
            echo -e "${YELLOW}=== Web Application Errors ===${NC}"
            pm2 logs $WEB_APP_NAME --err --lines 20
            ;;
        follow)
            pm2 logs --lines 0
            ;;
    esac
}

update_dependencies() {
    print_status "Updating dependencies for web application..."

    # Update web app dependencies
    if [ -d "$WEB_APP_DIR" ]; then
        print_status "Updating web application dependencies..."
        cd $WEB_APP_DIR
        npm update
        npm audit fix --force || true
    fi

    print_status "Dependencies updated successfully!"
}

clear_logs() {
    print_status "Clearing PM2 logs..."
    pm2 flush
    print_status "Logs cleared successfully!"
}

create_backup() {
    print_status "Creating backup of web application..."

    BACKUP_DIR="/var/backups/eventahub-frontend"
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    BACKUP_FILE="$BACKUP_DIR/frontend_backup_$TIMESTAMP.tar.gz"

    mkdir -p $BACKUP_DIR

    # Create backup excluding node_modules and build files
    tar -czf $BACKUP_FILE \
        --exclude='node_modules' \
        --exclude='.next' \
        --exclude='dist' \
        --exclude='.nuxt' \
        --exclude='build' \
        -C $(dirname $WEB_APP_DIR) \
        $(basename $WEB_APP_DIR)

    print_status "Backup created: $BACKUP_FILE"
}restore_backup() {
    local backup_file="$2"

    if [ -z "$backup_file" ] || [ ! -f "$backup_file" ]; then
        print_error "Please provide a valid backup file path"
        exit 1
    fi

    print_status "Restoring from backup: $backup_file"

    # Stop applications
    stop_applications

    # Extract backup
    tar -xzf "$backup_file" -C $(dirname $WEB_APP_DIR)

    # Reinstall dependencies
    install_dependencies

    # Restart applications
    start_applications

    print_status "Backup restored successfully!"
}

switch_environment() {
    local env="$2"

    case "$env" in
        prod|production)
            print_status "Switching to production environment..."
            pm2 restart $WEB_APP_NAME --env production
            ;;
        dev|development)
            print_status "Switching to development environment..."
            pm2 restart $WEB_APP_NAME --env development
            ;;
        *)
            print_error "Invalid environment. Use 'prod' or 'dev'"
            exit 1
            ;;
    esac

    print_status "Environment switched to $env"
}

show_environment() {
    echo -e "${BLUE}=== Environment Variables ===${NC}"
    echo ""
    echo -e "${YELLOW}Web Application:${NC}"
    pm2 env $WEB_APP_NAME
}install_dependencies() {
    print_status "Installing dependencies for web application..."

    # Install web app dependencies
    if [ -d "$WEB_APP_DIR" ]; then
        print_status "Installing web application dependencies..."
        cd $WEB_APP_DIR
        npm ci
    fi

    print_status "Dependencies installed successfully!"
}initial_setup() {
    print_status "Running initial setup..."

    # Install dependencies
    install_dependencies

    # Build application
    build_web

    # Start application
    start_applications

    print_status "Initial setup completed!"
}

# Main script logic
check_pm2

case "${1:-help}" in
    start)
        start_applications
    ;;
    stop)
        stop_applications
    ;;
    restart)
        restart_applications
    ;;
    reload)
        reload_applications
    ;;
    status)
        show_status
    ;;
    delete)
        print_status "Deleting all PM2 processes..."
        pm2 delete $WEB_APP_NAME $MOBILE_APP_NAME 2>/dev/null || true
        print_status "All processes deleted!"
    ;;
    delete)
        print_status "Deleting PM2 process..."
        pm2 delete $WEB_APP_NAME 2>/dev/null || true
        print_status "Process deleted!"
        ;;
    deploy)
        check_apps_exist
        deploy_all
        ;;
    deploy-web)
        deploy_web
        ;;
    build-web)
        build_web
        ;;
    logs)
        show_logs "$@"
        ;;
    logs-web)
        pm2 logs $WEB_APP_NAME --lines 100
        ;;
    errors)
        show_logs logs errors
        ;;
    follow)
        show_logs logs follow
        ;;
    monit)
        pm2 monit
        ;;
    health)
        /usr/local/bin/eventahub-frontend-health
        ;;
    update-deps)
        check_apps_exist
        update_dependencies
        ;;
    clear-logs)
        clear_logs
        ;;
    backup)
        create_backup
        ;;
    restore)
        restore_backup "$@"
        ;;
    env-prod)
        switch_environment env-prod production
        ;;
    env-dev)
        switch_environment env-dev development
        ;;
    env-show)
        show_environment
        ;;
    install)
        check_apps_exist
        install_dependencies
        ;;
    setup)
        check_apps_exist
        initial_setup
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
