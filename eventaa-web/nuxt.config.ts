export default defineNuxtConfig({
  compatibilityDate: '2024-04-03',
  devtools: { enabled: true },
  ssr: false,
  spaLoadingTemplate: './app/spa-loading-template.html',
  modules: ['@nuxtjs/tailwindcss', '@formkit/nuxt', '@pinia/nuxt', '@pinia-plugin-persistedstate/nuxt', '@vueform/nuxt', '@nuxtjs/color-mode'],
  build: {
    transpile: ["oh-vue-icons"]
  },
  formkit: {
    autoImport: true,
  },
  colorMode: {
    classSuffix: ''
  },
  css: [
    "~/assets/sass/fonts.scss",
    "~/assets/sass/slider.scss",
    "~/assets/sass/scheduler.scss",
    "~/assets/css/dashboard-theme.css",
  ],
  app: {
    head: {
      title: 'EventaHub Malawi - Discover & Book Amazing Events',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { name: 'format-detection', content: 'telephone=no' },
        { name: 'theme-color', content: '#dc2626' },
        { property: 'og:site_name', content: 'EventaHub Malawi' },
        { property: 'og:locale', content: 'en_MW' },
        { name: 'twitter:site', content: '@EventaHubMW' },
        { name: 'author', content: 'Brainy Technologies' }
      ],
      link: [
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
        { rel: 'icon', type: 'image/png', sizes: '32x32', href: '/icon.png' },
        { rel: 'apple-touch-icon', sizes: '180x180', href: '/icon.png' },
        { rel: 'canonical', href: process.env.BASE_URL || 'https://eventahub.com' }
      ]
    }
  },
  runtimeConfig: {
    public: {
      googleMapsApiKey: process.env.GOOGLE_MAPS_API_KEY,
      apiBaseUrl: process.env.API_BASE_URL,
      baseUrl: process.env.BASE_URL,
      wsHost: process.env.WS_HOST,
      reverbAppKey: process.env.VITE_REVERB_APP_KEY,
      reverbHost: process.env.VITE_REVERB_HOST,
      reverbPort: process.env.VITE_REVERB_PORT,
      reverbScheme: process.env.VITE_REVERB_SCHEME,
      recaptchaSiteKey: process.env.NUXT_PUBLIC_RECAPTCHA_SITE_KEY,
      googleAnalyticsId: process.env.GOOGLE_ANALYTICS_ID,
    },
  },
  devServer: {
    port: 4000,
    host: "0.0.0.0"
  }
})
