<template>
    <div>
        <div v-if="loading" class="p-6">
            <div v-for="i in 3" :key="i" class="animate-pulse mb-4">
                <div class="flex items-center space-x-4 p-4 border border-gray-200 dark:border-zinc-600 bg-white dark:bg-zinc-800">
                    <div class="w-16 h-16 bg-gray-200 dark:bg-zinc-600"></div>
                    <div class="flex-1 space-y-2">
                        <div class="h-4 bg-gray-200 dark:bg-zinc-600 w-3/4"></div>
                        <div class="h-3 bg-gray-200 dark:bg-zinc-600 w-1/2"></div>
                        <div class="h-3 bg-gray-200 dark:bg-zinc-600 w-1/4"></div>
                    </div>
                </div>
            </div>
        </div>

        <div v-else-if="!events || events.length === 0" class="flex flex-col items-center justify-center py-20 px-6 text-center">
            <div class="w-24 h-24 bg-gray-100 dark:bg-zinc-700 rounded-full flex items-center justify-center mb-6">
                <Icon icon="fluent-mdl2:schedule-event-action" class="w-12 h-12 text-gray-400 dark:text-zinc-500" />
            </div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-zinc-100 mb-2">No Events Yet</h3>
            <p class="text-gray-500 dark:text-zinc-400 mb-6 max-w-md">
                You haven't liked or attended any event yet
            </p>
            <div class="flex flex-col sm:flex-row gap-3">
                <button @click="refreshEvents"
                    class="inline-flex items-center px-4 text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 transition duration-150">
                    <Icon icon="heroicons:arrow-path" class="w-5 h-5 mr-2" />
                    Refresh
                </button>
            </div>
        </div>

        <div v-else class="w-full flex">
            <div class="w-full flex flex-col">
                <EventsBookmark v-for="event in events" :key="event.id" :event="event" />
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import type { PropType } from 'vue';
import type { EventItem } from '~/types';
import type { User } from '@/types/user';

const props = defineProps({
    events: {
        type: Array as PropType<EventItem[]>,
        default: () => []
    },
    loading: {
        type: Boolean,
        default: false
    },
    profile: {
        type: Object as PropType<User | null | undefined>,
        default: null
    }
});

const emits = defineEmits(['refresh']);

const refreshEvents = () => {
    emits('refresh');
};
</script>

<style lang="scss"></style>
