<template>
    <div class="w-full max-w-full">
        <Popover class="relative">
            <PopoverButton
                class="group inline-flex justify-center items-center p-2 text-base font-medium focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 hover:bg-gray-100 transition duration-150 rounded-full">
                <Icon icon="line-md:edit" class="w-5 h-5 text-gray-600" />
            </PopoverButton>

            <transition enter-active-class="transition duration-200 ease-out" enter-from-class="translate-y-1 opacity-0"
                enter-to-class="translate-y-0 opacity-100" leave-active-class="transition duration-150 ease-in"
                leave-from-class="translate-y-0 opacity-100" leave-to-class="translate-y-1 opacity-0">
                <PopoverPanel
                    class="absolute left-1/2 transform -translate-x-1/2 bg-white z-10 mt-0.5 w-full max-w-full px-4 sm:px-0">
                    <div class="overflow-hidden shadow-lg ring-1 ring-black/5">
                        <div class="mt-3 px-2 py-2 shadow-2xl flex flex-col space-y-2">
                            <RichTextEditor theme="snow" class="editor" required v-model:content="bio"
                                contentType="html" :editor="ClassicEditor" v-model="bio" :config="editorConfig">
                            </RichTextEditor>
                            <CoreSubmitButton :loading="loading" @click="updateBio" />
                        </div>
                    </div>
                </PopoverPanel>
            </transition>
        </Popover>
    </div>
</template>

<script setup lang="ts">
import { Popover, PopoverButton, PopoverPanel } from '@headlessui/vue'
import { ClassicEditor, Bold, Essentials, Heading, BlockQuote, Table, MediaEmbed, Font, FontColor, FontSize, Italic, Mention, Paragraph, Undo, Link, InputNumberView, List, } from 'ckeditor5';
import type { GenericResponse } from '@/types/api';

const emits = defineEmits(['update']);
const props = defineProps({ bio: { required: true, type: String } });
const bio = ref<string>(props.bio || "");
const httpClient = useHttpClient();
const loading = ref<boolean>(false);
const { $toast }: any = useNuxtApp();
const editorConfig = {
    plugins: [Bold, Essentials, Heading, Italic, BlockQuote, Table, Font, FontColor, FontSize, MediaEmbed, Mention, Paragraph, Undo, Link, InputNumberView, List],
    toolbar: ['heading',
        '|',
        'bold',
        'italic',
        'link',
        'bulletedList',
        'numberedList',
        'blockQuote',
        'insertTable',
        'mediaEmbed',
        'undo',
        'redo',
        'imageUpload',
        'fontSize',
        'fontColor',
        'highlight'],
};

const updateBio = async (): Promise<void> => {
    try {
        loading.value = true;
        const formData = new FormData();
        formData.append('bio', bio.value);
        const response = await httpClient.post<GenericResponse>(ENDPOINTS.PROFILE.BIO, formData);
        if (response) {
            $toast.success(response.message);
            emits('update', true);
        }
    } catch (error: any) {
        const errors = error.errors;
        Object.keys(errors).forEach((key) => {
            if (Array.isArray(errors[key])) {
                errors[key].forEach((message: string) => {
                    $toast.error(message);
                });
            } else if (typeof errors[key] === 'string') {
                $toast.error(errors[key]);
            }
        });
    } finally {
        loading.value = false;
    }
}
</script>
