<template>
  <div class="p-6">
    <div v-if="loading" class="space-y-4">
      <div v-for="i in 3" :key="i" class="animate-pulse">
        <div class="bg-gray-200 dark:bg-zinc-600 h-24 w-full mb-4"></div>
      </div>
    </div>

    <div v-else-if="vendorBookings.length === 0" class="text-center py-12">
      <Icon icon="heroicons:briefcase" class="w-16 h-16 text-gray-400 dark:text-zinc-500 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 dark:text-zinc-100 mb-2">No vendor bookings</h3>
      <p class="text-gray-500 dark:text-zinc-400 mb-6">You haven't booked any vendors yet. Discover professional vendors for your events!</p>
      <NuxtLink to="/vendors" class="inline-flex items-center px-4 py-2 bg-red-600 text-white hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-600 transition duration-150">
        <Icon icon="heroicons:plus" class="w-4 h-4 mr-2" />
        Browse Vendors
      </NuxtLink>
    </div>

    <div v-else class="space-y-3">
      <div v-for="booking in vendorBookings" :key="booking.id"
           class="bg-white dark:bg-zinc-800 border border-gray-200 dark:border-zinc-700 p-4 hover:shadow-md dark:hover:shadow-zinc-900/20 transition duration-150 cursor-pointer"
           @click="showBookingDetails(booking)">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3 flex-1">
            <img v-if="booking.vendor?.logo"
                 :src="`${runtimeConfig.public.baseUrl}storage/${booking.vendor.logo}`"
                 :alt="booking.vendor.name"
                 class="w-10 h-10 object-cover rounded-full border border-zinc-200 dark:border-zinc-700" />
            <div class="w-10 h-10 bg-gray-200 dark:bg-zinc-600 rounded-full flex items-center justify-center" v-else>
              <Icon icon="heroicons:briefcase" class="w-5 h-5 text-gray-400 dark:text-zinc-400" />
            </div>
            <div class="flex-1 min-w-0">
              <h3 class="font-medium text-gray-900 dark:text-zinc-100 truncate">{{ booking.vendor?.name || 'Vendor' }}</h3>
              <p class="text-sm text-gray-500 dark:text-zinc-400 truncate">{{ booking.vendor_service?.service?.name || 'Service' }}</p>
            </div>
          </div>

          <div class="flex items-center space-x-4">
            <div class="text-right">
              <p class="text-sm font-medium text-gray-900 dark:text-zinc-100">${{ booking.total_price }}</p>
              <p class="text-xs text-gray-500 dark:text-zinc-400">{{ formatDate(booking.booking_from) }}</p>
            </div>
            <span :class="getStatusClass(booking.status)" class="px-2 py-1 text-xs font-medium">
              {{ booking.status.charAt(0).toUpperCase() + booking.status.slice(1) }}
            </span>
            <Icon icon="heroicons:chevron-right" class="w-5 h-5 text-gray-400 dark:text-zinc-500" />
          </div>
        </div>
      </div>
    </div>

    <!-- Vendor Booking Details Dialog -->
    <ProfileVendorBookingDialog
      :booking="selectedBooking"
      :isOpen="isDialogOpen"
      @close="closeDialog"
    />
  </div>
</template>

<script setup lang="ts">
interface VendorBooking {
  id: number;
  user_id: number;
  vendor_id: number;
  vendor_service_id: number;
  category_id: number;
  booking_from: string;
  booking_to: string;
  number_of_guests: number;
  message: string;
  status: 'pending' | 'approved' | 'rejected' | 'completed' | 'cancelled';
  rejection_reason?: string;
  total_price: number;
  is_paid: boolean;
  created_at: string;
  vendor?: {
    id: number;
    name: string;
    logo?: string;
  };
  vendor_service?: {
    id: number;
    name: string;
    service: {
      id: number;
      name: string;
    };
  };
  category?: {
    id: number;
    name: string;
  };
}

const props = defineProps<{
  profile?: any;
  loading?: boolean;
  onRefresh?: () => void;
}>();

const runtimeConfig = useRuntimeConfig();
const httpClient = useHttpClient();
const vendorBookings = ref<VendorBooking[]>([]);
const bookingsLoading = ref(false);
const selectedBooking = ref<VendorBooking | null>(null);
const isDialogOpen = ref(false);

const fetchVendorBookings = async () => {
  try {
    bookingsLoading.value = true;
    const response = await httpClient.get<any>('/vendors/bookings/user?per_page=3');
    if (response) {
      vendorBookings.value = Array.isArray(response) ? response : response.data || [];
    }
  } catch (error) {
    console.error('Error fetching vendor bookings:', error);
    vendorBookings.value = [];
  } finally {
    bookingsLoading.value = false;
  }
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const showBookingDetails = (booking: VendorBooking) => {
  selectedBooking.value = booking;
  isDialogOpen.value = true;
};

const closeDialog = () => {
  isDialogOpen.value = false;
  selectedBooking.value = null;
};

const getStatusClass = (status: string) => {
  switch (status) {
    case 'approved':
      return 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400';
    case 'rejected':
      return 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-400';
    case 'pending':
      return 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-400';
    case 'completed':
      return 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-400';
    case 'cancelled':
      return 'bg-gray-100 dark:bg-zinc-700 text-gray-800 dark:text-zinc-300';
    default:
      return 'bg-gray-100 dark:bg-zinc-700 text-gray-800 dark:text-zinc-300';
  }
};

const loading = computed(() => props.loading || bookingsLoading.value);

onMounted(() => {
  fetchVendorBookings();
});
</script>
