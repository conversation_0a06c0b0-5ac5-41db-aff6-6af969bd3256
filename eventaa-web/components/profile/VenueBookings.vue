<template>
  <div class="p-6">
    <div v-if="loading" class="space-y-4">
      <div v-for="i in 3" :key="i" class="animate-pulse">
        <div class="bg-gray-200 dark:bg-zinc-600 h-24 w-full mb-4"></div>
      </div>
    </div>

    <div v-else-if="venueBookings.length === 0" class="text-center py-12">
      <Icon icon="heroicons:building-office-2" class="w-16 h-16 text-gray-400 dark:text-zinc-500 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 dark:text-zinc-100 mb-2">No venue bookings</h3>
      <p class="text-gray-500 dark:text-zinc-400 mb-6">You haven't booked any venues yet. Start exploring amazing venues for your events!</p>
      <NuxtLink to="/venues" class="inline-flex items-center px-4 py-2 bg-red-600 text-white hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-600 transition duration-150">
        <Icon icon="heroicons:plus" class="w-4 h-4 mr-2" />
        Browse Venues
      </NuxtLink>
    </div>

    <div v-else class="space-y-4">
      <div v-for="booking in venueBookings" :key="booking.id"
           class="bg-white dark:bg-zinc-800 border border-gray-200 dark:border-zinc-700 p-6 hover:shadow-md dark:hover:shadow-zinc-900/20 transition duration-150">
        <div class="flex items-start justify-between">
          <div class="flex-1">
            <div class="flex items-center space-x-3 mb-3">
              <img v-if="booking.venue?.image"
                   :src="`${runtimeConfig.public.baseUrl}storage/venues/${booking.venue.image}`"
                   :alt="booking.venue.name"
                   class="w-12 h-12 object-cover" />
              <div class="w-12 h-12 bg-gray-200 dark:bg-zinc-600 flex items-center justify-center" v-else>
                <Icon icon="heroicons:building-office-2" class="w-6 h-6 text-gray-400 dark:text-zinc-400" />
              </div>
              <div>
                <h3 class="font-semibold text-gray-900 dark:text-zinc-100">{{ booking.venue?.name || 'Venue' }}</h3>
                <p class="text-sm text-gray-500 dark:text-zinc-400">{{ booking.venue?.location || 'Location not specified' }}</p>
              </div>
            </div>

            <div class="grid grid-cols-2 gap-4 mb-4">
              <div>
                <p class="text-sm font-medium text-gray-700 dark:text-zinc-300">Check-in</p>
                <p class="text-sm text-gray-900 dark:text-zinc-100">{{ formatDate(booking.booking_from) }}</p>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-700 dark:text-zinc-300">Check-out</p>
                <p class="text-sm text-gray-900 dark:text-zinc-100">{{ formatDate(booking.booking_to) }}</p>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-700 dark:text-zinc-300">Guests</p>
                <p class="text-sm text-gray-900 dark:text-zinc-100">{{ booking.number_of_guests || 'Not specified' }}</p>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-700 dark:text-zinc-300">Category</p>
                <p class="text-sm text-gray-900 dark:text-zinc-100">{{ booking.category?.name || 'Not specified' }}</p>
              </div>
            </div>

            <div v-if="booking.message" class="mb-4">
              <p class="text-sm font-medium text-gray-700 dark:text-zinc-300">Message</p>
              <p class="text-sm text-gray-900 dark:text-zinc-100">{{ booking.message }}</p>
            </div>

            <div v-if="booking.venue_price" class="mb-4">
              <p class="text-sm font-medium text-gray-700 dark:text-zinc-300">Price</p>
              <p class="text-sm text-gray-900 dark:text-zinc-100">
                {{ booking.venue_price.currency?.symbol || '$' }}{{ booking.venue_price.price }}
                ({{ booking.venue_price.type }})
              </p>
            </div>
          </div>

          <div class="flex flex-col items-end space-y-2">
            <span :class="getStatusClass(booking.status || 'pending')" class="px-3 py-1 text-xs font-medium">
              {{ (booking.status || 'pending').charAt(0).toUpperCase() + (booking.status || 'pending').slice(1) }}
            </span>
            <p class="text-xs text-gray-500 dark:text-zinc-400">{{ formatDate(booking.created_at) }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface VenueBooking {
  id: number;
  venue_id: number;
  user_id: number;
  category_id: number;
  booking_from: string;
  booking_to: string;
  number_of_guests: number;
  message: string;
  status?: 'pending' | 'approved' | 'rejected';
  venue_price_id: number;
  created_at: string;
  venue?: {
    id: number;
    name: string;
    location: string;
    image?: string;
  };
  category?: {
    id: number;
    name: string;
  };
  venue_price?: {
    id: number;
    name: string;
    type: string;
    price: number;
    currency?: {
      id: number;
      name: string;
      code: string;
      symbol: string;
    };
  };
}

const props = defineProps<{
  profile?: any;
  loading?: boolean;
  onRefresh?: () => void;
}>();

const runtimeConfig = useRuntimeConfig();
const httpClient = useHttpClient();
const venueBookings = ref<VenueBooking[]>([]);
const bookingsLoading = ref(false);

const fetchVenueBookings = async () => {
  try {
    bookingsLoading.value = true;
    const response = await httpClient.get<any>('/venue-bookings?per_page=3');
    if (response) {
      // Handle paginated response structure
      const bookings = response.data || [];

      // Ensure each booking has a status field
      venueBookings.value = bookings.map((booking: any) => ({
        ...booking,
        status: booking.status || 'pending'
      }));
    }
  } catch (error) {
    console.error('Error fetching venue bookings:', error);
    venueBookings.value = [];
  } finally {
    bookingsLoading.value = false;
  }
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const getStatusClass = (status?: string) => {
  switch (status) {
    case 'approved':
      return 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400';
    case 'rejected':
      return 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-400';
    case 'pending':
    default:
      return 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-400';
  }
};

const loading = computed(() => props.loading || bookingsLoading.value);

onMounted(() => {
  fetchVenueBookings();
});
</script>
