<template>
    <div>
        <div v-if="loading" class="p-6">
            <div v-for="i in 4" :key="i" class="animate-pulse mb-4">
                <div class="border border-gray-200 dark:border-zinc-700 p-4">
                    <div class="flex justify-between items-start mb-4">
                        <div class="space-y-2 flex-1">
                            <div class="h-5 bg-gray-200 dark:bg-zinc-700 w-3/4"></div>
                            <div class="h-4 bg-gray-200 dark:bg-zinc-700 w-1/2"></div>
                        </div>
                        <div class="h-6 bg-gray-200 dark:bg-zinc-700 w-20"></div>
                    </div>
                    <div class="grid grid-cols-3 gap-4">
                        <div v-for="j in 3" :key="j" class="flex items-center space-x-2">
                            <div class="w-10 h-10 bg-gray-200 dark:bg-zinc-700 rounded-full"></div>
                            <div class="space-y-1">
                                <div class="h-4 bg-gray-200 dark:bg-zinc-700 w-16"></div>
                                <div class="h-3 bg-gray-200 dark:bg-zinc-700 w-8"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div v-else-if="!tickets || tickets.length === 0" class="flex flex-col items-center justify-center py-20 px-6 text-center">
            <div class="w-24 h-24 bg-gray-100 dark:bg-zinc-800 rounded-full flex items-center justify-center mb-6">
                <Icon icon="icon-park-outline:ticket" class="w-12 h-12 text-gray-400 dark:text-zinc-500" />
            </div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">No Tickets Yet</h3>
            <p class="text-gray-500 dark:text-zinc-400 mb-6 max-w-md">
                You haven't purchased any tickets yet. Browse events and get your tickets to start attending amazing events.
            </p>
            <div class="flex flex-col sm:flex-row gap-3">
                <button @click="refreshTickets"
                    class="inline-flex items-center px-4 py-2 font-medium text-red-600 dark:text-red-400 transition duration-150">
                    <Icon icon="heroicons:arrow-path" class="w-5 h-5 mr-2" />
                    Refresh
                </button>
            </div>
        </div>

        <div v-else>
            <div class="mb-6 px-4 space-y-4">
                <div class="mt-2">
                    <CoreSearchBar
                        v-model="searchQuery"
                        :placeholder="'Search by event name...'"
                        :max-history-items="5"
                    />
                </div>

                <div class="flex flex-wrap gap-3">
                    <Popover v-slot="{ open }" class="relative">
                        <PopoverButton
                            class="inline-flex items-center px-4 py-2 text-base font-medium text-gray-700 dark:text-zinc-300 bg-white dark:bg-zinc-800 border border-gray-300 dark:border-zinc-600 hover:bg-gray-50 dark:hover:bg-zinc-700 focus:outline-none focus:ring-0 focus:ring-red-500 transition-colors duration-200"
                            :class="{ 'ring-0 ring-red-500 border-red-500': open }"
                        >
                            <Icon icon="carbon:settings-adjust" class="w-5 h-5 mr-2" />
                            Status
                            <Icon icon="heroicons:chevron-down" class="w-4 h-4 ml-2" />
                        </PopoverButton>

                        <transition
                            enter-active-class="transition duration-200 ease-out"
                            enter-from-class="translate-y-1 opacity-0"
                            enter-to-class="translate-y-0 opacity-100"
                            leave-active-class="transition duration-150 ease-in"
                            leave-from-class="translate-y-0 opacity-100"
                            leave-to-class="translate-y-1 opacity-0"
                        >
                            <PopoverPanel class="absolute z-10 mt-2 w-56 bg-white dark:bg-zinc-800 border border-gray-200 dark:border-zinc-700 shadow-lg">
                                <div class="p-4 space-y-3">
                                    <label v-for="status in statusOptions" :key="status.value" class="flex items-center">
                                        <input
                                            v-model="selectedStatuses"
                                            :value="status.value"
                                            type="checkbox"
                                            class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 dark:border-zinc-600"
                                        />
                                        <span class="ml-2 text-sm text-gray-700 dark:text-zinc-300">{{ status.label }}</span>
                                    </label>
                                </div>
                                <div class="p-3 flex justify-between">
                                    <button
                                        @click="clearFilters"
                                        class="text-sm text-gray-500 dark:text-zinc-400 hover:text-gray-700 dark:hover:text-zinc-200"
                                    >
                                        Clear All
                                    </button>
                                    <PopoverButton
                                        class="text-sm text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300"
                                    >
                                        Apply
                                    </PopoverButton>
                                </div>
                            </PopoverPanel>
                        </transition>
                    </Popover>

                    <div v-if="hasActiveFilters" class="flex items-center space-x-2">
                        <span class="text-base text-gray-500 dark:text-zinc-400">Filters:</span>
                        <div class="flex flex-wrap gap-2">
                            <span
                                v-if="searchQuery"
                                class="inline-flex items-center px-2 py-1 text-sm font-medium bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200"
                            >
                                "{{ searchQuery }}"
                                <button @click="searchQuery = ''" class="ml-1 hover:text-red-600 dark:hover:text-red-300">
                                    <Icon icon="heroicons:x-mark" class="w-3 h-3" />
                                </button>
                            </span>
                            <span
                                v-for="status in selectedStatuses"
                                :key="status"
                                class="inline-flex rounded-full items-center px-2 py-1 text-sm font-medium bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200"
                            >
                                {{ statusOptions.find(s => s.value === status)?.label }}
                                <button @click="removeStatusFilter(status)" class="ml-1 hover:text-red-600 dark:hover:text-red-300">
                                    <Icon icon="heroicons:x-mark" class="w-3 h-3" />
                                </button>
                            </span>
                        </div>
                        <button
                            @click="clearAllFilters"
                            class="text-sm bg-red-600 text-red-50 rounded-full font-light p-1 dark:text-zinc-400 hover:text-white dark:hover:text-red-400"
                        >
                            Clear All
                        </button>
                    </div>
                </div>
            </div>

            <div v-if="filteredTickets.length === 0" class="flex flex-col items-center justify-center py-20 px-6 text-center">
                <div class="w-24 h-24 bg-gray-100 dark:bg-zinc-800 rounded-full flex items-center justify-center mb-6">
                    <Icon icon="heroicons:magnifying-glass" class="w-12 h-12 text-gray-400 dark:text-zinc-500" />
                </div>
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">No Tickets Found</h3>
                <p class="text-gray-500 dark:text-zinc-400 mb-6 max-w-md">
                    No tickets match your current filters. Try adjusting your search criteria or clearing the filters.
                </p>
                <button @click="clearAllFilters"
                    class="inline-flex items-center px-4 py-2 font-medium text-red-600 dark:text-red-400 transition duration-150">
                    <Icon icon="heroicons:arrow-path" class="w-5 h-5 mr-2" />
                    Clear Filters
                </button>
            </div>

            <div v-else class="space-y-0">
                <EventsTicket v-for="ticket in paginatedTickets" :key="ticket?.id || Math.random()" :ticket="ticket as any" @ticket-updated="handleTicketUpdated"/>
            </div>

            <div v-if="totalPages > 1" class="bg-gray-100 py-1 dark:bg-zinc-900 flex items-center justify-between mt-8 px-4 mb-0">
                <div class="text-gray-500 dark:text-zinc-400">
                    Showing {{ paginationStart }} to {{ paginationEnd }} of {{ filteredTickets.length }} tickets
                    <span v-if="hasActiveFilters && filteredTickets.length !== tickets.length" class="text-gray-400 dark:text-zinc-500">
                        (filtered from {{ tickets.length }} total)
                    </span>
                </div>

                <div class="flex items-center space-x-2">
                    <button
                        @click="goToPage(currentPage - 1)"
                        :disabled="currentPage === 1"
                        class="px-3 py-2 font-medium text-gray-500 dark:text-zinc-400 bg-white dark:bg-zinc-800 border border-gray-300 dark:border-zinc-600 hover:bg-gray-50 dark:hover:bg-zinc-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                        :class="currentPage === 1 ? 'cursor-not-allowed' : 'hover:text-gray-700 dark:hover:text-zinc-200'"
                    >
                        <Icon icon="heroicons:chevron-left" class="w-4 h-4" />
                    </button>

                    <button
                        v-for="page in displayedPages"
                        :key="page"
                        @click="goToPage(page)"
                        class="px-3 py-2 font-medium transition-colors duration-200"
                        :class="page === currentPage
                            ? 'bg-red-600 text-white border border-red-600'
                            : 'text-gray-500 dark:text-zinc-400 bg-white dark:bg-zinc-800 border border-gray-300 dark:border-zinc-600 hover:bg-gray-50 dark:hover:bg-zinc-700 hover:text-gray-700 dark:hover:text-zinc-200'"
                    >
                        {{ page }}
                    </button>

                    <button
                        @click="goToPage(currentPage + 1)"
                        :disabled="currentPage === totalPages"
                        class="px-3 py-2 font-medium text-gray-500 dark:text-zinc-400 bg-white dark:bg-zinc-800 border border-gray-300 dark:border-zinc-600 hover:bg-gray-50 dark:hover:bg-zinc-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                        :class="currentPage === totalPages ? 'cursor-not-allowed' : 'hover:text-gray-700 dark:hover:text-zinc-200'"
                    >
                        <Icon icon="heroicons:chevron-right" class="w-4 h-4" />
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Popover, PopoverButton, PopoverPanel } from '@headlessui/vue';
import type { PropType } from 'vue';
import type { User } from '@/types/user';

interface Ticket {
    id: number;
    [key: string]: any;
}

const props = defineProps({
    tickets: {
        type: Array as PropType<Ticket[]>,
        default: () => []
    },
    loading: {
        type: Boolean,
        default: false
    },
    profile: {
        type: Object as PropType<User | null | undefined>,
        default: null
    }
});

const emits = defineEmits(['refresh']);

const searchQuery = ref('');
const selectedStatuses = ref<string[]>([]);

const statusOptions = [
    { value: 'active', label: 'Active' },
    { value: 'expired', label: 'Expired' },
    { value: 'refunded', label: 'Refunded' },
    { value: 'pending', label: 'Pending' },
    { value: 'cancelled', label: 'Cancelled' }
];

const currentPage = ref(1);
const itemsPerPage = 3;

const getTicketStatus = (ticket: any): string => {
    if (ticket.status === 'refunded') return 'refunded';
    if (ticket.status === 'cancelled') return 'cancelled';
    if (ticket.status === 'pending') return 'pending';

    // Check if event has expired
    if (ticket.event?.end) {
        const eventEndDate = new Date(ticket.event.end);
        const now = new Date();
        if (eventEndDate < now) return 'expired';
    }

    return 'active';
};

// Filtered tickets based on search and status
const filteredTickets = computed(() => {
    let filtered = props.tickets;

    // Apply search filter
    if (searchQuery.value.trim()) {
        const query = searchQuery.value.toLowerCase().trim();
        filtered = filtered.filter(ticket =>
            ticket?.event?.title?.toLowerCase().includes(query) ||
            ticket?.ticket?.name?.toLowerCase().includes(query) ||
            ticket?.attendee_name?.toLowerCase().includes(query)
        );
    }

    // Apply status filter
    if (selectedStatuses.value.length > 0) {
        filtered = filtered.filter(ticket => {
            const status = getTicketStatus(ticket);
            return selectedStatuses.value.includes(status);
        });
    }

    return filtered;
});

const totalPages = computed(() => {
    return Math.ceil(filteredTickets.value.length / itemsPerPage);
});

const paginatedTickets = computed(() => {
    const start = (currentPage.value - 1) * itemsPerPage;
    const end = start + itemsPerPage;
    return filteredTickets.value.slice(start, end);
});

const paginationStart = computed(() => {
    return ((currentPage.value - 1) * itemsPerPage) + 1;
});

const paginationEnd = computed(() => {
    return Math.min(currentPage.value * itemsPerPage, filteredTickets.value.length);
});

// Check if there are active filters
const hasActiveFilters = computed(() => {
    return searchQuery.value.trim() !== '' || selectedStatuses.value.length > 0;
});

const displayedPages = computed(() => {
    const pages = [];
    const maxPages = 5;
    let start = Math.max(1, currentPage.value - Math.floor(maxPages / 2));
    let end = Math.min(totalPages.value, start + maxPages - 1);

    if (end - start + 1 < maxPages) {
        start = Math.max(1, end - maxPages + 1);
    }

    for (let i = start; i <= end; i++) {
        pages.push(i);
    }
    return pages;
});

// Methods
const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages.value) {
        currentPage.value = page;
    }
};

const refreshTickets = () => {
    currentPage.value = 1; // Reset to first page on refresh
    emits('refresh');
};

// Filter methods
const clearFilters = () => {
    selectedStatuses.value = [];
};

const clearAllFilters = () => {
    searchQuery.value = '';
    selectedStatuses.value = [];
    currentPage.value = 1;
};

const removeStatusFilter = (status: string) => {
    const index = selectedStatuses.value.indexOf(status);
    if (index > -1) {
        selectedStatuses.value.splice(index, 1);
    }
};

const handleTicketUpdated = (updatedTicket: any) => {
    // Find and update the ticket in the local array
    const ticketIndex = props.tickets.findIndex(ticket => ticket.id === updatedTicket.id);
    if (ticketIndex !== -1) {
        props.tickets[ticketIndex] = { ...props.tickets[ticketIndex], ...updatedTicket };
    }

    // Optionally refresh the entire list to get latest data from server
    refreshTickets();
};

// Watch for tickets changes to reset pagination
watch(() => props.tickets, () => {
    if (currentPage.value > totalPages.value && totalPages.value > 0) {
        currentPage.value = 1;
    }
});

// Watch for filter changes to reset pagination
watch([searchQuery, selectedStatuses], () => {
    currentPage.value = 1;
}, { deep: true });
</script>

<style lang="sass" scoped></style>
