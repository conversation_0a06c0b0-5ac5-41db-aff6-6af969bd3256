<template>
  <TransitionRoot appear :show="isOpen" as="template">
    <Dialog as="div" @close="closeModal" class="relative z-10">
      <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
        leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
        <div class="fixed inset-0 bg-black/25" />
      </TransitionChild>
      <div class="fixed inset-0 overflow-y-auto">
        <div class="flex min-h-full items-center justify-center p-4">
          <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95">
            <DialogPanel class="w-full max-w-2xl transform overflow-hidden bg-white dark:bg-zinc-800 shadow-xl p-6 text-left align-middle transition-all">
              <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900 dark:text-zinc-100 flex justify-between items-center mb-4">
                <span>Vendor Booking Details</span>
                <button @click="closeModal" class="text-gray-400 dark:text-zinc-500 hover:text-gray-600 dark:hover:text-zinc-300">
                  <Icon icon="heroicons:x-mark" class="w-6 h-6" />
                </button>
              </DialogTitle>

              <div v-if="booking" class="space-y-6">
                <!-- Vendor Information -->
                <div class="flex items-center space-x-4 p-4 bg-gray-50 dark:bg-zinc-700/50">
                  <img v-if="booking.vendor?.logo"
                       :src="`${runtimeConfig.public.baseUrl}storage/${booking.vendor.logo}`"
                       :alt="booking.vendor.name"
                       class="w-16 h-16 object-cover rounded-full border border-zinc-200 dark:border-zinc-600" />
                  <div class="w-16 h-16 bg-gray-200 dark:bg-zinc-600 rounded-full flex items-center justify-center" v-else>
                    <Icon icon="heroicons:briefcase" class="w-8 h-8 text-gray-400 dark:text-zinc-400" />
                  </div>
                  <div class="flex-1">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-zinc-100">{{ booking.vendor?.name || 'Vendor' }}</h3>
                    <p class="text-gray-600 dark:text-zinc-300">{{ booking.vendor_service?.service?.name || 'Service' }}</p>
                    <span :class="getStatusClass(booking.status)" class="inline-block px-3 py-1 text-xs font-medium mt-2">
                      {{ booking.status.charAt(0).toUpperCase() + booking.status.slice(1) }}
                    </span>
                  </div>
                </div>

                <!-- Booking Details -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div class="space-y-4">
                    <div>
                      <h4 class="text-sm font-medium text-gray-900 dark:text-zinc-100 mb-2">Booking Period</h4>
                      <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                          <Icon icon="heroicons:calendar" class="w-4 h-4 text-gray-500 dark:text-zinc-400" />
                          <span class="text-sm text-gray-600 dark:text-zinc-300">Start: {{ formatDate(booking.booking_from) }}</span>
                        </div>
                        <div class="flex items-center space-x-2">
                          <Icon icon="heroicons:calendar" class="w-4 h-4 text-gray-500 dark:text-zinc-400" />
                          <span class="text-sm text-gray-600 dark:text-zinc-300">End: {{ formatDate(booking.booking_to) }}</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 class="text-sm font-medium text-gray-900 dark:text-zinc-100 mb-2">Guest Information</h4>
                      <div class="flex items-center space-x-2">
                        <Icon icon="heroicons:users" class="w-4 h-4 text-gray-500 dark:text-zinc-400" />
                        <span class="text-sm text-gray-600 dark:text-zinc-300">{{ booking.number_of_guests || 'Not specified' }} guests</span>
                      </div>
                    </div>

                    <div v-if="booking.category">
                      <h4 class="text-sm font-medium text-gray-900 dark:text-zinc-100 mb-2">Category</h4>
                      <div class="flex items-center space-x-2">
                        <Icon icon="heroicons:tag" class="w-4 h-4 text-gray-500 dark:text-zinc-400" />
                        <span class="text-sm text-gray-600 dark:text-zinc-300">{{ booking.category.name }}</span>
                      </div>
                    </div>
                  </div>

                  <div class="space-y-4">
                    <div>
                      <h4 class="text-sm font-medium text-gray-900 dark:text-zinc-100 mb-2">Payment Information</h4>
                      <div class="space-y-2">
                        <div class="flex items-center justify-between">
                          <span class="text-sm text-gray-600 dark:text-zinc-300">Total Amount:</span>
                          <span class="text-sm font-semibold text-gray-900 dark:text-zinc-100">${{ booking.total_price }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                          <span class="text-sm text-gray-600 dark:text-zinc-300">Payment Status:</span>
                          <span v-if="booking.is_paid" class="text-green-600 dark:text-green-400 text-sm font-medium">Paid</span>
                          <span v-else class="text-red-600 dark:text-red-400 text-sm font-medium">Unpaid</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 class="text-sm font-medium text-gray-900 dark:text-zinc-100 mb-2">Booking Date</h4>
                      <div class="flex items-center space-x-2">
                        <Icon icon="heroicons:clock" class="w-4 h-4 text-gray-500 dark:text-zinc-400" />
                        <span class="text-sm text-gray-600 dark:text-zinc-300">{{ formatDate(booking.created_at) }}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Message -->
                <div v-if="booking.message" class="border-t border-gray-200 dark:border-zinc-700 pt-4">
                  <h4 class="text-sm font-medium text-gray-900 dark:text-zinc-100 mb-2">Message</h4>
                  <p class="text-sm text-gray-600 dark:text-zinc-300 bg-gray-50 dark:bg-zinc-700/50 p-3">{{ booking.message }}</p>
                </div>

                <!-- Rejection Reason -->
                <div v-if="booking.rejection_reason && booking.status === 'rejected'" class="border-t border-gray-200 dark:border-zinc-700 pt-4">
                  <h4 class="text-sm font-medium text-red-700 dark:text-red-400 mb-2">Rejection Reason</h4>
                  <p class="text-sm text-red-600 dark:text-red-300 bg-red-50 dark:bg-red-900/20 p-3">{{ booking.rejection_reason }}</p>
                </div>
              </div>

              <div v-else class="text-center py-8">
                <p class="text-gray-500 dark:text-zinc-400">No booking details available</p>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from '@headlessui/vue';

interface VendorBooking {
  id: number;
  user_id: number;
  vendor_id: number;
  vendor_service_id: number;
  category_id: number;
  booking_from: string;
  booking_to: string;
  number_of_guests: number;
  message: string;
  status: 'pending' | 'approved' | 'rejected' | 'completed' | 'cancelled';
  rejection_reason?: string;
  total_price: number;
  is_paid: boolean;
  created_at: string;
  vendor?: {
    id: number;
    name: string;
    logo?: string;
  };
  vendor_service?: {
    id: number;
    name: string;
    service: {
      id: number;
      name: string;
    };
  };
  category?: {
    id: number;
    name: string;
  };
}

const props = defineProps<{
  booking: VendorBooking | null;
  isOpen: boolean;
}>();

const emit = defineEmits(['close']);

const runtimeConfig = useRuntimeConfig();

const closeModal = () => {
  emit('close');
};

const getStatusClass = (status: string) => {
  const classes = {
    'pending': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300',
    'approved': 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
    'completed': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
    'rejected': 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300',
    'cancelled': 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300',
  };
  return classes[status as keyof typeof classes] || classes.pending;
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};
</script>
