<template>
  <div class="w-full max-w-md mx-auto">
    <div class="text-center mb-6">
      <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Two-Factor Authentication</h2>
      <p class="mt-2 text-gray-600 dark:text-gray-400">
        We've sent a verification code to your email address. Please enter it below to continue.
      </p>
    </div>

    <div v-if="isLoading" class="flex justify-center items-center py-8">
      <CoreLoader :width="70" :height="70" />
    </div>

    <form v-else @submit.prevent="verifyCode" class="space-y-6 py-4">
      <div class="flex flex-col items-center justify-center">
        <p for="code" class="block text-center font-medium text-gray-700 dark:text-gray-300">Verification Code</p>
        <div class="mt-1">
          <div class="flex justify-center space-x-2">
            <input v-for="(digit, index) in codeDigits" :key="index" type="text" maxlength="1"
              class="w-12 h-12 text-center text-xl font-semibold border border-gray-300 dark:border-gray-700 shadow-sm focus:ring-0 dark:bg-gray-800 dark:text-white"
              :ref="el => {
                if (el && typeof el === 'object' && 'focus' in el) {
                  inputRefs[index as any] = el as HTMLInputElement;
                }
              }" v-model="codeDigits[index]" @input="e => onInput(e, index)" @keydown="e => onKeyDown(e, index)"
              @paste="handlePaste" />
          </div>
        </div>
      </div>

      <div class="text-center">
        <p class="text-sm text-gray-600 dark:text-gray-400">
          Didn't receive a code?
          <span class="text-sky-500 hover:text-sky-600 font-medium" @click="resendCode">
            {{ isResending ? 'Sending...' : 'Resend code' }}
          </span>
        </p>
      </div>
      <div class="w-full flex items-center justify-center">
        <CorePrimaryButton @click="verifyCode" :loading="isVerifying" :text="isVerifying ? 'Verifying...' : 'Verify'"
          :disabled="isVerifying || !isCodeComplete" />
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { ENDPOINTS } from '@/utils/api';
import { useAuthStore } from '@/store/auth';
import type { AuthResponse } from '@/types/api';

interface Props {
  email: string;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (e: 'verified'): void;
  (e: 'cancel'): void;
}>();

const authStore = useAuthStore();
const nuxtApp = useNuxtApp();
const $toast = nuxtApp.$toast as {
  success: (message: string) => void;
  error: (message: string) => void;
  info: (message: string) => void;
  warning: (message: string) => void;
};
const httpClient = useHttpClient();

const isLoading = ref<boolean>(false);
const isVerifying = ref<boolean>(false);
const isResending = ref<boolean>(false);
const codeDigits = ref<string[]>(['', '', '', '', '', '']);
const inputRefs = ref<HTMLInputElement[]>([]);

for (let i = 0; i < 6; i++) {
  inputRefs.value.push(null as unknown as HTMLInputElement);
}

const isCodeComplete = computed(() => {
  return codeDigits.value.every(digit => digit !== '');
});

const getFullCode = (): string => {
  return codeDigits.value.join('');
};

const onInput = (event: Event, index: number): void => {
  const target = event.target as HTMLInputElement;
  const value = target.value;

  if (!/^\d*$/.test(value)) {
    codeDigits.value[index] = '';
    return;
  }

  if (value && index < 5) {
    const nextInput = inputRefs.value[index + 1];
    if (nextInput) nextInput.focus();
  }
};

const onKeyDown = (event: KeyboardEvent, index: number): void => {
  if (event.key === 'Backspace' && !codeDigits.value[index] && index > 0) {
    const prevInput = inputRefs.value[index - 1];
    if (prevInput) prevInput.focus();
  }
};

const handlePaste = (event: ClipboardEvent): void => {
  event.preventDefault();
  const pastedData = event.clipboardData?.getData('text') || '';
  const digits = pastedData.replace(/\D/g, '').slice(0, 6).split('');

  digits.forEach((digit, index) => {
    if (index < 6) {
      codeDigits.value[index] = digit;
    }
  });

  const nextEmptyIndex = codeDigits.value.findIndex(digit => !digit);
  if (nextEmptyIndex !== -1) {
    const nextInput = inputRefs.value[nextEmptyIndex];
    if (nextInput) nextInput.focus();
  } else {
    const lastInput = inputRefs.value[5];
    if (lastInput) lastInput.focus();
  }
};

const verifyCode = async (): Promise<void> => {
  if (!isCodeComplete.value) return;

  isVerifying.value = true;

  try {
    const response = await httpClient.post<AuthResponse>(ENDPOINTS.TWO_FACTOR.VERIFY, {
      email: props.email,
      code: getFullCode()
    });

    if (response && response.token) {
      authStore.setAuth(response);
      $toast.success('Verification successful');
      emit('verified');
    } else {
      $toast.error('Invalid verification code');
    }
  } catch (error: any) {
    console.error('Error verifying code:', error);
    $toast.error('Failed to verify code. Please try again.');
  } finally {
    isVerifying.value = false;
  }
};

const resendCode = async (): Promise<void> => {
  isResending.value = true;

  try {
    await httpClient.post(ENDPOINTS.TWO_FACTOR.SEND_CODE, {
      email: props.email
    });

    $toast.success('Verification code sent to your email');
  } catch (error: any) {
    console.error('Error sending verification code:', error);
    $toast.error('Failed to send verification code');
  } finally {
    isResending.value = false;
  }
};

onMounted(async (): Promise<void> => {
  isLoading.value = true;

  try {
    await httpClient.post(ENDPOINTS.TWO_FACTOR.SEND_CODE, {
      email: props.email
    });

    setTimeout(() => {
      if (inputRefs.value[0]) {
        inputRefs.value[0].focus();
      }
    }, 100);
  } catch (error: any) {
    console.error('Error sending verification code:', error);
    $toast.error('Failed to send verification code');
  } finally {
    isLoading.value = false;
  }
});
</script>
