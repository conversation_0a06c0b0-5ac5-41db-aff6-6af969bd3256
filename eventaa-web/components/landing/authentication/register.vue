<template>
  <button href="#" @click="openModal" class="font-medium dark:text-zinc-100 hover:dark:text-zinc-50 text-gray-700 hover:text-gray-800">
    Create an account
  </button>
  <TransitionRoot appear :show="isOpen" as="template">
    <Dialog as="div" @close="closeModal" class="relative z-[99999]">
      <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
        leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
        <div class="fixed inset-0 bg-black/25" />
      </TransitionChild>

      <div class="fixed inset-0 overflow-y-auto">
        <div class="flex min-h-full items-center justify-center p-4 text-center">
          <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95">
            <DialogPanel
              class="w-full bg-gray-50 dark:bg-zinc-800 max-w-3xl transform overflow-hidden rounded-none text-left shadow-xl transition-all">
              <div>
                <div>
                  <div class="w-full grid grid-cols-1 md:grid-cols-5 gap-2">
                    <div
                      class="col-span-1 md:col-span-2 sm:flex hidden items-center justify-center relative moving-background h-40 md:h-auto">
                      <div class="flex flex-col items-center bg-opacity-25">
                        <img src="/icon.png" alt="eventa-malawi-logo" class="w-16 h-16 md:w-20 md:h-20 object-cover" />
                        <h3 class="w-full text-xl md:text-2xl font-semibold text-gray-100 dark:text-zinc-100 text-center">
                          EventaHub Malawi
                        </h3>
                      </div>
                    </div>
                    <div class="col-span-1 md:col-span-3 p-4 md:p-0">
                      <div class="justify-end flex items-end">
                        <button @click="closeModal" class="p-4 dark:text-zinc-100">
                          <Icon icon="gg:close" class="w-5 h-5" />
                        </button>
                      </div>
                      <div class="justify-center flex flex-col items-center space-y-1 mb-4">
                        <h3 class="text-xl md:text-2xl font-semibold text-center dark:text-zinc-100">
                          Join EventaHub Malawi🎉️
                        </h3>
                        <p class="text-gray-500 text-base text-center dark:text-zinc-200">
                          Register to start browsing, organizing, managing
                          events and many more...
                        </p>
                      </div>
                      <FormKit id="loginForm" @submit="onRegister" type="form" submit-label="Update" :actions="false">
                        <div class="w-full flex flex-col items-center space-y-2 px-2 md:px-5">
                          <FormKit type="text" name="name" placeholder="Enter first and last name" label="Full Name"
                            prefixIcon="user" validation="required" v-model="username" />

                          <FormKit type="email" name="email" placeholder="Enter a valid email address"
                            label="Email address" prefixIcon="email" validation="required|email" v-model="email" />

                          <FormKit type="password" name="password" placeholder="Enter password" label="Password"
                            prefixIcon="password" validation="required|password" validation-visibility="live"
                            v-model="password" />
                          <FormKit type="password" placeholder="Confirm password" label="Confirm Password"
                            prefixIcon="password" name="password_confirm" validation="required|confirm"
                            validation-visibility="live" v-model="confirmPassword" />
                          <CoreSubmitButton :loading="loading" text="Register" />
                          <div>
                            <p class="text-base dark:text-zinc-100">
                              Already have an account?
                              <span class="underline text-sky-500 cursor-pointer" @click="openLoginModal">
                                Login
                              </span>
                            </p>
                          </div>
                          <div class="w-full flex items-center justify-center flex-col space-y-2 pb-5">
                            <p class="text-base dark:text-zinc-100">OR</p>
                            <ClientOnly>
                              <GoogleLogin :callback="callback" :prompt="false" :auto-login="false"></GoogleLogin>
                            </ClientOnly>
                          </div>
                        </div>
                      </FormKit>
                    </div>
                  </div>
                </div>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
} from "@headlessui/vue";
import { decodeCredential } from "vue3-google-login";
import { useAuthStore } from "@/store/auth";
import type { AuthResponse } from "@/types/api";

interface Props {
  onOpenLogin?: () => void;
}

const props = withDefaults(defineProps<Props>(), {
  onOpenLogin: () => {}
});

const emits = defineEmits(["success"]);
const isOpen = ref<boolean>(false);
const loading = ref<boolean>(false);
const username = ref<string>("");
const email = ref<string>("");
const password = ref<string>("");
const confirmPassword = ref<string>("");

const httpClient = useHttpClient();
const { $toast, $router }: any = useNuxtApp();
const { setAuth } = useAuthStore();

const closeModal = (): void => {
  isOpen.value = false;
};
const openModal = (): void => {
  isOpen.value = true;
};

const openLoginModal = (): void => {
  closeModal();
  props.onOpenLogin();
};

const errorHandlers = {
  getErrorMessages: (obj: any): string[] => {
    if (!obj) return [];

    return Object.values(obj).flatMap(value => {
      if (Array.isArray(value)) return value;
      if (typeof value === 'string') return [value];
      if (typeof value === 'object' && value !== null) return errorHandlers.getErrorMessages(value);
      return [];
    });
  },

  formatErrors: (messages: string[]): string =>
    messages.length > 0 ? messages.join('\n') : 'An unexpected error occurred',

  parseError: (error: any): string[] => {
    if (error.message?.message) return errorHandlers.getErrorMessages(error.message.message);
    if (error.message?.email) return Array.isArray(error.message.email) ? error.message.email : [error.message.email];
    return [];
  }
};

const handleError = (error: any, toast: any): void => {
  const messages = errorHandlers.parseError(error);
  toast.error(errorHandlers.formatErrors(messages));
};

const callback = async (e: { credential: string; }): Promise<void> => {
  loading.value = true;
  const userData: any = decodeCredential(e.credential);
  const formData = new FormData();
  formData.append('email', userData.email);
  formData.append('username', userData.name);
  formData.append('email_verified', userData.email_verified);
  formData.append('sub', userData.sub);
  formData.append('picture', userData.picture);
  try {
    const response: any = await httpClient.post(ENDPOINTS.AUTH.GOOGLE, formData);
    if (response) {
      setAuth(response);
      $toast.success(response.message);
      setTimeout(() => {
        $router.go();
      }, 1000)
    }
  } catch (error) {
    handleError(error, $toast);
  } finally {
    loading.value = false;
  }
};

const onRegister = async (): Promise<void> => {
  loading.value = true;
  try {
    const formData = new FormData();
    formData.append('name', username.value);
    formData.append('email', email.value);
    formData.append('password', password.value);
    formData.append('confirmPassword', confirmPassword.value);
    const response = await httpClient.post<AuthResponse>(ENDPOINTS.AUTH.REGISTER, formData);
    if (response) {
      $toast.success(response.message);
      if (response.email_verification_required) {
        localStorage.setItem('pending_verification_email', email.value);
        closeModal();
        $router.push('/email/verify/pending');
      } else {
        setAuth(response);
        closeModal();
        $router.push('/get-started');
      }
    }
  } catch (error) {
    handleError(error, $toast)
  } finally {
    loading.value = false;
  }
}

defineExpose({ openModal });
</script>

<style lang="scss" scoped>
.moving-background {
  background-image: url("../../../assets/images/hero-patterns.jpg");
  background-size: cover;
  background-position: center;
}
</style>
