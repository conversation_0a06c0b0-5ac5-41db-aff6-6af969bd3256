<template>
  <div class="dark:bg-zinc-800 bg-white z-50">
    <TransitionRoot as="template" :show="open">
      <Dialog class="relative z-[9999] lg:hidden" @close="open = false">
        <TransitionChild as="template" enter="transition-opacity ease-linear duration-300" enter-from="opacity-0"
          enter-to="opacity-100" leave="transition-opacity ease-linear duration-300" leave-from="opacity-100"
          leave-to="opacity-0">
          <div class="fixed inset-0 bg-red-900 bg-opacity-25" />
        </TransitionChild>

        <div class="fixed inset-0 z-40 flex">
          <TransitionChild as="template" enter="transition ease-in-out duration-300 transform"
            enter-from="-translate-x-full" enter-to="translate-x-0"
            leave="transition ease-in-out duration-300 transform" leave-from="translate-x-0"
            leave-to="-translate-x-full">
            <DialogPanel class="relative flex w-full max-w-xs flex-col overflow-y-auto bg-white pb-12 shadow-xl">
              <div class="flex px-4 pb-2 pt-5">
                <button type="button" class="relative -m-2 inline-flex items-center justify-center p-2 text-gray-400"
                  @click="open = false">
                  <span class="absolute -inset-0.5" />
                  <span class="sr-only">Close menu</span>
                  <XMarkIcon class="h-6 w-6" aria-hidden="true" />
                </button>
              </div>

              <div class="space-y-6 px-4 py-6">
                <div v-for="page in navigation.pages" :key="page.name" class="flow-root">
                  <a :href="page.path"
                    class="-m-2 block p-2 font-medium transition-colors duration-200"
                    :class="{
                      'text-red-600 border-l-4 border-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20': isActiveRoute(page.path),
                      'text-gray-900 dark:text-zinc-100': !isActiveRoute(page.path)
                    }"
                    @click="open = false">
                    {{ page.name }}
                  </a>
                </div>
              </div>

              <div v-if="!authStore.isAuthenticated" class="space-y-6 border-gray-200 border px-4 py-6">
                <div class="flow-root">
                  <button @click="openLoginModal" class="-m-2 block p-2 font-medium text-gray-900 dark:text-zinc-100">
                    Sign in
                  </button>
                </div>
                <div class="flow-root">
                  <button @click="openRegisterModal"
                    class="-m-2 block p-2 font-medium text-gray-900 dark:text-zinc-100">
                    Create account
                  </button>
                </div>
              </div>

              <div v-if="authStore.isAuthenticated" class="space-y-6 px-4 py-6">
                <div class="w-full flex items-center space-x-3 mb-4 px-2">
                  <img :src="`${runtimeConfig.public.baseUrl}storage/avatars/${authStore.user?.avatar}`"
                    :alt="`${authStore.user?.name.toLowerCase().replaceAll(' ', '-')}`"
                    class="w-10 h-10 object-cover border border-gray-50 dark:border-zinc-700 rounded-full" />
                  <div>
                    <p class="font-medium text-gray-900 dark:text-zinc-100">{{ authStore.user?.name }}</p>
                    <p class="text-sm text-gray-600 dark:text-zinc-300">{{ authStore.user?.email }}</p>
                  </div>
                </div>

                <div class="flow-root">
                  <button @click="() => { $router.push('/my-profile'); open = false; }" class="-m-2 flex items-center p-2 font-medium text-gray-900 dark:text-zinc-100">
                    <Icon icon="uil:user" class="mr-2 h-5 w-5" aria-hidden="true" />
                    My Profile
                  </button>
                </div>

                <div v-if="authStore.user?.roles?.includes('admin') || authStore.user?.roles?.includes('host')" class="flow-root">
                  <button @click="() => { $router.push('/dashboard'); open = false; }" class="-m-2 flex items-center p-2 font-medium text-gray-900 dark:text-zinc-100">
                    <Icon icon="heroicons:home" class="mr-2 h-5 w-5" aria-hidden="true" />
                    Dashboard
                  </button>
                </div>

                <div v-if="authStore.user?.roles?.includes('vendor') || authStore.user?.roles?.includes('host')" class="flow-root">
                  <button @click="() => { navigateToVendorDashboard(); open = false; }" class="-m-2 flex items-center p-2 font-medium text-gray-900 dark:text-zinc-100">
                    <Icon icon="heroicons:wrench-screwdriver" class="mr-2 h-5 w-5" aria-hidden="true" />
                    Vendor Dashboard
                  </button>
                </div>

                <div class="flow-root">
                  <button @click="() => { logoutUser(); open = false; }" class="-m-2 flex items-center p-2 font-medium text-gray-900 dark:text-zinc-100">
                    <Icon icon="ic:round-logout" class="mr-2 h-5 w-5" aria-hidden="true" />
                    Log Out
                  </button>
                </div>
              </div>

              <div class="boder-b border-zinc-200 dark:border-zinc-700 px-4 py-6">
                <a href="#" class="-m-2 flex items-center p-2" @click="open = false">
                  <Icon icon="emojione-v1:flag-for-malawi" class="block h-auto w-5 flex-shrink-0" />
                  <span class="ml-3 block text-base font-medium text-gray-900 dark:text-zinc-100">MW</span>
                  <span class="sr-only">, change country</span>
                </a>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </Dialog>
    </TransitionRoot>

    <header class="transition-all duration-300" :class="{
      'max-w-6xl mx-auto fixed top-0 left-0 right-0 z-[9998] shadow bg-white': scrollPosition > 50,
      'relative z-[9999]': scrollPosition <= 50,
    }">
      <div v-if="scrollPosition <= 50"
        class="w-full flex items-center bg-gray-50 dark:bg-zinc-800 dark:border-zinc-700 border-b px-6 py-2">
        <Icon icon="openmoji:mobile-info" class="w-6 h-6" />
        <div class="w-full text-red-500 flex items-center space-x-2 font-semibold">
          <span class="ml-2 font-normal text-base text-gray-600 dark:text-zinc-50 animate-pulse text-ellipsis truncate">
            <NuxtLink class="hover:underline hover:text-gray-700 transition duration-150">
              EventaHub is still in development, official launch is due 1 August 2025
            </NuxtLink>
          </span>
        </div>
      </div>

      <div class="mx-auto max-w-6xl">
        <nav class="bg-white dark:bg-zinc-800 mx-auto max-w-6xl transition-all duration-300">
          <div class="border-b border-gray-200 dark:border-zinc-700 px-5"
            :class="{ 'border-white dark:border-zinc-900': scrollPosition > 50 }">
            <div class="flex h-16 items-center justify-between">
              <button type="button"
                class="relative rounded-none bg-white dark:bg-zinc-800 dark:text-zinc-100 text-gray-400 lg:hidden"
                @click="open = true">
                <span class="absolute -inset-0.5" />
                <span class="sr-only">Open menu</span>
                <Icon icon="pepicons-pop:menu" class="h-6 w-6" aria-hidden="true" />
              </button>

              <div class="ml-4 flex lg:ml-0">
                <NuxtLink to="/" class="flex items-center space-x-3">
                  <span class="sr-only">EventaHub</span>
                  <img class="h-8 w-auto rounded-full" src="/icon.png" alt="app-icon" />
                  <span class="sr-only text-2xl font-semibold dark:text-zinc-50">EventaHub</span>
                </NuxtLink>
              </div>

              <PopoverGroup class="hidden lg:ml-8 lg:block lg:self-stretch">
                <div class="flex h-full space-x-8">
                  <nuxt-link v-for="page in navigation.pages" :key="page.name"
                    :href="page.path"
                    class="flex items-center font-medium text-gray-700 dark:text-zinc-100 hover:text-gray-800 border-b-4 transition-colors duration-200"
                    :class="[{
                      'border-red-600 text-gray-900 dark:text-zinc-100':
                        isActiveRoute(page.path),
                      'border-transparent': !isActiveRoute(page.path),
                    }, { 'text-white': scrollPosition > 50 }]">
                    {{ page.name }}
                  </nuxt-link>
                </div>
              </PopoverGroup>

              <div class="ml-auto flex items-center" v-if="!showSearch">
                <div class="hidden lg:flex lg:flex-1 lg:items-center lg:justify-end lg:space-x-6">
                  <LandingAuthenticationLogin ref="loginModal" :onOpenRegister="openRegisterModal" v-if="!authStore.isAuthenticated" />
                  <span class="h-6 w-px bg-gray-200" aria-hidden="true" v-if="!authStore.isAuthenticated" />
                  <LandingAuthenticationRegister @success="openLoginModal" ref="registerModal" :onOpenLogin="openLoginModal"
                    v-if="!authStore.isAuthenticated" />
                  <UserNotificationDrawer v-if="authStore.isAuthenticated" />
                  <div v-if="authStore.isAuthenticated">
                    <Menu as="div" class="relative inline-block text-right z-[999999]">
                      <div>
                        <MenuButton class="relative z-[999999]">
                          <img :src="`${runtimeConfig.public.baseUrl}storage/avatars/${authStore.user?.avatar}`"
                            :alt="`${authStore.user?.name.toLowerCase().replaceAll(' ', '-')}`"
                            class="w-10 h-10 object-cover border border-gray-50 dark:border-zinc-700 rounded-full" />
                        </MenuButton>
                      </div>

                      <transition enter-active-class="transition duration-100 ease-out"
                        enter-from-class="transform scale-95 opacity-0" enter-to-class="transform scale-100 opacity-100"
                        leave-active-class="transition duration-75 ease-in"
                        leave-from-class="transform scale-100 opacity-100"
                        leave-to-class="transform scale-95 opacity-0">
                        <MenuItems
                          class="user-menu-dropdown absolute right-0 mt-2 w-56 origin-top-right divide-y divide-gray-100 dark:divide-zinc-700 bg-white dark:bg-zinc-800 shadow-xl focus:outline-none border border-gray-200 dark:border-zinc-700"
                          style="z-index: 999999 !important; position: absolute !important;">
                          <div class="w-full flex flex-col space-y-0.5 items-center justify-center relative pb-3 pt-5">
                            <div v-if="authStore.user?.roles?.includes('vendor') || authStore.user?.roles?.includes('host')"
                              class="absolute top-0 left-0 bg-red-500 text-white flex items-center text-sm uppercase font-medium px-2 py-1 rounded-br-md">
                              Host
                            </div>
                            <div class="relative">
                              <img :src="`${runtimeConfig.public.baseUrl}storage/avatars/${avatar}`"
                                alt="profile-picture"
                                class="w-20 h-20 object-cover border border-gray-50 rounded-full" />
                              <Icon v-if="authStore.user?.is_verified" icon="material-symbols:verified-rounded"
                                class="absolute w-5 h-5 mr-2 text-sky-500 -bottom-0 -right-0" />
                            </div>
                            <h3 class="text-lg font-semibold dark:text-zinc-200">{{ username?.toString() }}</h3>
                            <p class="text-base font-normal dark:text-zinc-100">{{ email }}</p>
                          </div>

                          <div class="px-1 py-1">
                            <MenuItem v-slot="{ active }">
                            <button @click="$router.push('/my-profile')" :class="[
                              active ? 'bg-gray-50' : 'text-gray-600 dark:text-zinc-100',
                              'group flex w-full items-center px-2 py-2 transition duration-150',
                            ]">
                              <Icon icon="uil:user" class="mr-2 h-5 w-5 " aria-hidden="true" />
                              My Profile
                            </button>
                            </MenuItem>

                            <MenuItem v-if="authStore.user?.roles?.includes('admin') || authStore.user?.roles?.includes('host')" v-slot="{ active }">
                            <button @click="$router.push('/dashboard')" :class="[
                              active ? 'bg-gray-50' : 'text-gray-600 dark:text-zinc-100',
                              'group flex w-full items-center px-2 py-2 transition duration-150',
                            ]">
                              <Icon icon="heroicons:home" class="mr-2 h-5 w-5 " aria-hidden="true" />
                              Dashboard
                            </button>
                            </MenuItem>

                            <MenuItem v-if="authStore.user?.roles?.includes('vendor') ||  authStore.user?.roles?.includes('host')" v-slot="{ active }">
                            <button @click="navigateToVendorDashboard" :class="[
                              active ? 'bg-gray-50' : 'text-gray-600 dark:text-zinc-100',
                              'group flex w-full items-center px-2 py-2 transition duration-150',
                            ]">
                              <Icon icon="heroicons:wrench-screwdriver" class="mr-2 h-5 w-5 " aria-hidden="true" />
                              Vendor Dashboard
                            </button>
                            </MenuItem>
                          </div>
                          <div class="px-1 py-1">
                            <MenuItem v-slot="{ active }" @click="logoutUser">
                            <button :class="[
                              active ? 'bg-gray-50' : 'text-gray-900 dark:text-zinc-100',
                              'group flex w-full items-center text-base px-2 py-2 transition duration-150',
                            ]">
                              <Icon icon="ic:round-logout" class="mr-2 h-5 w-5 " aria-hidden="true" />
                              Log Out
                            </button>
                            </MenuItem>
                          </div>
                        </MenuItems>
                      </transition>
                    </Menu>
                  </div>
                </div>

                <div class="hidden lg:ml-8 lg:flex">
                  <a href="#" class="flex items-center text-gray-700 hover:text-gray-800">
                    {{ $location?.flag || '' }}
                    <span class="ml-3 block text-sm font-medium uppercase dark:text-zinc-100">{{
                      $location?.location?.countryCode || ""
                    }}</span>
                    <span class="sr-only">, change location</span>
                  </a>
                </div>

                <div class="flex lg:ml-6">
                  <CoreSearchDialog />
                </div>

                <div class="ml-4 flow-root lg:ml-6">
                  <button @click="toggleColorMode" class="group -m-2 flex items-center p-2">
                    <Icon v-if="colorMode.value == 'light'"
                      icon="line-md:sunny-filled-loop-to-moon-alt-filled-loop-transition"
                      class="h-6 w-6 flex-shrink-0 text-gray-400 group-hover:text-gray-500" aria-hidden="true" />
                    <Icon v-else icon="line-md:sun-rising-filled-loop"
                      class="h-6 w-6 flex-shrink-0 text-yellow-500 group-hover:text-yellow-600" aria-hidden="true" />
                  </button>
                </div>
              </div>
              <div v-else class="flex items-center justify-end">
                <CoreSearch v-model="search" class="items-end justify-end" />
              </div>
            </div>
          </div>
        </nav>
      </div>
    </header>
  </div>
</template>

<script setup lang="ts">
import {
  Dialog,
  DialogPanel,
  PopoverGroup,
  TransitionChild,
  TransitionRoot,
} from "@headlessui/vue";
import { XMarkIcon } from "@heroicons/vue/24/outline";
import { useHeaderRoutes } from "@/routes/header";
import { Menu, MenuButton, MenuItems, MenuItem } from '@headlessui/vue'
import { useAuthStore } from "@/store/auth";
import { useVendorStore } from "@/store/vendor";
import { useHttpClient } from "@/composables/useHttpClient";
import { ENDPOINTS } from "@/utils/api";

const runtimeConfig = useRuntimeConfig();
const navigation = useHeaderRoutes();
const route = useRoute();
const open = ref<boolean>(false);

const isActiveRoute = (pagePath: string): boolean => {
  if (pagePath === '/') {
    return route.path === '/';
  }
  if (route.path === pagePath) {
    return true;
  }
  return route.path.startsWith(pagePath + '/');
};
const registerModal = ref();
const loginModal = ref();
const scrollPosition = ref<number>(0);
const showSearch = ref<boolean>(false);
const { $location, $toast }: any = useNuxtApp();
const router = useRouter();
const search = ref<string>("");
const authStore = useAuthStore();
const userStore = useAuthStore();

const avatar = ref(userStore.user?.avatar);
const username = ref(userStore.user?.name);
const email = ref(userStore.user?.email);

const colorMode = useColorMode();

const toggleColorMode = () => {
  colorMode.preference = colorMode.value === 'dark' ? 'light' : 'dark';
}

const openRegisterModal = (): void => {
  open.value = false;
  registerModal.value.openModal();
};

const openLoginModal = (): void => {
  open.value = false;
  loginModal.value.openModal();
};

const updateScroll = (): void => {
  scrollPosition.value = window.scrollY;
};

const logoutUser = async (): Promise<void> => {
  try {
    $toast.info('Logging out...', {
      timeout: false,
      closeOnClick: false,
    });

    const httpClient = useHttpClient();

    try {
      await httpClient.post(ENDPOINTS.AUTH.LOGOUT);
    } catch (apiError) {
      console.warn('Logout API call failed, but proceeding with local logout:', apiError);
    }

    userStore.clearAuth();

    $toast.success("Logged out successfully!");
    navigateTo('/');
  } catch (error) {
    console.error('Logout error:', error);
    $toast.error('Failed to logout. Please try again.');
  }
};

const navigateToVendorDashboard = () => {
  try {
    router.push('/vendor/dashboard');
  } catch (error) {
    console.error('Error navigating to vendor dashboard:', error);
    $toast.error('Failed to navigate to vendor dashboard');
  }
};

watch((userStore), (newUserStore) => {
  avatar.value = newUserStore.user?.avatar;
  username.value = newUserStore.user?.name;
  email.value = newUserStore.user?.email;
})

onMounted(() => {
  window.addEventListener("scroll", updateScroll);
});

onUnmounted(() => {
  window.removeEventListener("scroll", updateScroll);
});
</script>

<style scoped>
:deep(.user-menu-dropdown) {
  z-index: 999999 !important;
  position: absolute !important;
}

:deep([data-headlessui-state]) {
  z-index: 999999 !important;
}
</style>
