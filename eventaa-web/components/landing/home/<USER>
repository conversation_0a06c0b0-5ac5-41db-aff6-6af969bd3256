<template>
  <div class="py-20 bg-white dark:bg-zinc-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

      <div class="text-center mb-16">
        <h2 class="text-4xl sm:text-5xl font-bold text-zinc-900 dark:text-zinc-100 mb-6">
          How EventaHub Works
        </h2>
        <p class="text-xl text-zinc-600 dark:text-zinc-400 max-w-3xl mx-auto leading-relaxed">
          From concept to sold-out success, we make event management effortless. Follow these simple steps to transform your vision into reality.
        </p>
      </div>

      <div class="relative">
        <div class="hidden lg:block absolute top-1/2 left-0 right-0 h-0.5 bg-zinc-200 dark:bg-zinc-700 transform -translate-y-1/2"></div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">

          <div class="relative text-center">
            <div class="relative z-10 w-20 h-20 bg-red-600 text-white flex items-center justify-center mx-auto mb-6 shadow-lg">
              <span class="text-2xl font-bold">1</span>
            </div>
            <h3 class="text-xl font-bold text-zinc-900 dark:text-zinc-100 mb-4">Create Your Event</h3>
            <p class="text-zinc-600 dark:text-zinc-400 leading-relaxed">
              Set up your event details, upload images, and configure ticket types in minutes. Our intuitive interface makes it simple.
            </p>
          </div>

          <div class="relative text-center">
            <div class="relative z-10 w-20 h-20 bg-red-600 text-white flex items-center justify-center mx-auto mb-6 shadow-lg">
              <span class="text-2xl font-bold">2</span>
            </div>
            <h3 class="text-xl font-bold text-zinc-900 dark:text-zinc-100 mb-4">Customize & Promote</h3>
            <p class="text-zinc-600 dark:text-zinc-400 leading-relaxed">
              Design your event page, set pricing, and use our marketing tools to reach your target audience across multiple channels.
            </p>
          </div>

          <div class="relative text-center">
            <div class="relative z-10 w-20 h-20 bg-red-600 text-white flex items-center justify-center mx-auto mb-6 shadow-lg">
              <span class="text-2xl font-bold">3</span>
            </div>
            <h3 class="text-xl font-bold text-zinc-900 dark:text-zinc-100 mb-4">Sell Tickets</h3>
            <p class="text-zinc-600 dark:text-zinc-400 leading-relaxed">
              Start selling immediately with secure payment processing, mobile-friendly checkout, and real-time sales tracking.
            </p>
          </div>

          <div class="relative text-center">
            <div class="relative z-10 w-20 h-20 bg-red-600 text-white flex items-center justify-center mx-auto mb-6 shadow-lg">
              <span class="text-2xl font-bold">4</span>
            </div>
            <h3 class="text-xl font-bold text-zinc-900 dark:text-zinc-100 mb-4">Manage & Grow</h3>
            <p class="text-zinc-600 dark:text-zinc-400 leading-relaxed">
              Track attendance, analyze performance, and use insights to make your next event even more successful.
            </p>
          </div>

        </div>
      </div>

      <div class="mt-20 text-center">
        <h3 class="text-3xl font-bold text-zinc-900 dark:text-zinc-100 mb-6">
          Ready to Get Started?
        </h3>
        <p class="text-xl text-zinc-600 dark:text-zinc-400 mb-8 max-w-2xl mx-auto">
          Join thousands of event organizers who trust EventaHub to power their success.
        </p>
        <div class="flex flex-col sm:flex-row items-center justify-center gap-4">
          <NuxtLink to="/become-host"
            class="inline-flex items-center px-8 py-4 text-lg font-semibold text-white bg-red-600 hover:bg-red-700 transition-colors duration-200 shadow-lg hover:shadow-xl">
            Become host now
            <Icon icon="heroicons:arrow-right-20-solid" class="ml-2 w-5 h-5" />
          </NuxtLink>
          <NuxtLink to="/events"
            class="inline-flex items-center px-8 py-4 text-lg font-semibold text-red-600 border-2 border-red-600 hover:bg-red-600 hover:text-white transition-colors duration-200">
            Browse events
            <Icon icon="heroicons:play-solid" class="ml-2 w-5 h-5" />
          </NuxtLink>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup lang="ts">
</script>

<style scoped>
</style>
