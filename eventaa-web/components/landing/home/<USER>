<template>
  <div class="py-20 bg-white dark:bg-zinc-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16">
        <h2
          class="text-4xl sm:text-5xl font-bold text-zinc-900 dark:text-zinc-100 mb-6"
        >
          Your Strategic Partner In Growth & Success
        </h2>
        <p
          class="text-xl text-zinc-600 dark:text-zinc-400 max-w-3xl mx-auto leading-relaxed"
        >
          It takes less than 5 minutes to reach your audience for your events,
          sell tickets, generate revenue, and get ratings. Join thousands of
          successful event organizers.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
        <div
          v-for="(card, index) in cards"
          :key="index"
          class="relative group hover:-translate-y-2 transition-all duration-300"
        >
          <div
            class="absolute inset-0 bg-gradient-to-br opacity-90 transition-opacity duration-300 group-hover:opacity-100"
            :class="card.bgGradient"
          ></div>

          <div
            class="relative p-8 text-white h-full min-h-[300px] flex flex-col"
          >
            <div class="w-16 h-16 mb-6 relative z-10">
              <div
                class="w-full h-full bg-white/20 backdrop-blur-sm flex items-center justify-center"
              >
                <Icon :icon="card.icon" class="w-8 h-8 text-white" />
              </div>
            </div>

            <div class="flex-1 relative z-10">
              <h3 class="text-2xl font-bold mb-4">{{ card.title }}</h3>
              <p class="text-white/90 leading-relaxed">
                {{ card.description }}
              </p>
            </div>

            <div
              class="absolute top-6 right-6 w-12 h-12 bg-white/20 backdrop-blur-sm flex items-center justify-center text-2xl font-bold"
            >
              {{ index + 1 }}
            </div>

            <div class="absolute bottom-0 right-0 opacity-10">
              <Icon :icon="card.icon" class="w-32 h-32" />
            </div>
          </div>
        </div>
      </div>

      <div class="text-center">
        <NuxtLink
          to="/become-host"
          class="inline-flex items-center px-8 py-4 text-lg font-semibold text-white bg-red-600 hover:bg-red-700 transition-colors duration-200 shadow-lg hover:shadow-xl"
        >
          Become Host
          <Icon icon="heroicons:arrow-right-20-solid" class="ml-2 w-5 h-5" />
        </NuxtLink>
        <p class="mt-4 text-zinc-600 dark:text-zinc-400">
          No credit card/payment required • Setup in minutes • 24/7 support
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const cards = [
  {
    title: "Create A Free Account",
    description:
      "Register your account on our platform and start creating events. It takes less than 5 minutes to set up your account and reach thousands of potential attendees.",
    icon: "heroicons:user-plus-solid",
    bgGradient: "from-red-500 to-red-600",
  },
  {
    title: "Start Selling Instantly",
    description:
      "No subscription fees or hidden costs. Create your first event and start selling tickets immediately with our completely free platform.",
    icon: "heroicons:bolt-solid",
    bgGradient: "from-emerald-500 to-emerald-600",
  },
  {
    title: "Grow Your Audience",
    description:
      "Leverage our marketing tools and promotional features to reach 30% more customers and maximize your event revenue.",
    icon: "heroicons:chart-bar-square-solid",
    bgGradient: "from-blue-500 to-blue-600",
  },
];
</script>

<style scoped>
.card-shadow {
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}
</style>
