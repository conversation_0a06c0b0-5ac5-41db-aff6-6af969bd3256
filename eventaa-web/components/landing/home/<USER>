<template>
  <div class="py-20 bg-white dark:bg-zinc-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

      <div class="text-center mb-16">
        <h2 class="text-4xl sm:text-5xl font-bold text-zinc-900 dark:text-zinc-100 mb-6">
          Trusted by Event Organizers
        </h2>
        <p class="text-xl text-zinc-600 dark:text-zinc-400 max-w-3xl mx-auto leading-relaxed">
          Join thousands of successful event organizers who have chosen EventaHub to power their events and grow their business.
        </p>
      </div>

      <div ref="statsRef" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
        <div class="text-center">
          <div class="text-4xl sm:text-5xl font-bold text-red-600 mb-2">
            {{ formatNumber(displayStats.totalEvents) }}+
          </div>
          <div class="text-lg font-semibold text-zinc-900 dark:text-zinc-100 mb-1">Events Created</div>
          <div class="text-zinc-600 dark:text-zinc-400 text-sm">Across all categories</div>
        </div>

        <div class="text-center">
          <div class="text-4xl sm:text-5xl font-bold text-red-600 mb-2">
            {{ formatNumber(displayStats.totalTickets) }}+
          </div>
          <div class="text-lg font-semibold text-zinc-900 dark:text-zinc-100 mb-1">Tickets Sold</div>
          <div class="text-zinc-600 dark:text-zinc-400 text-sm">And counting</div>
        </div>

        <div class="text-center">
          <div class="text-4xl sm:text-5xl font-bold text-red-600 mb-2">
            {{ displayStats.customerRating }}
          </div>
          <div class="text-lg font-semibold text-zinc-900 dark:text-zinc-100 mb-1">Customer Rating</div>
          <div class="flex justify-center text-yellow-400 text-sm">
            <Icon v-for="i in 5" :key="i" icon="heroicons:star-solid" class="w-4 h-4" />
          </div>
        </div>

        <div class="text-center">
          <div class="text-4xl sm:text-5xl font-bold text-red-600 mb-2">
            {{ displayStats.averageIncrease }}%
          </div>
          <div class="text-lg font-semibold text-zinc-900 dark:text-zinc-100 mb-1">Average Sales Increase</div>
          <div class="text-zinc-600 dark:text-zinc-400 text-sm">Compared to other platforms</div>
        </div>
      </div>

      <!-- Support Section -->
      <div class="bg-gradient-to-r from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 p-8 text-center">
        <div class="flex items-center justify-center mb-6">
          <img src="https://ticketscandy.com/images/dedicated-support.svg" alt="Dedicated Support" class="w-16 h-16" />
        </div>
        <h3 class="text-2xl font-bold text-zinc-900 dark:text-zinc-100 mb-4">
          Dedicated Support To Events Of All Sizes
        </h3>
        <p class="text-lg text-zinc-600 dark:text-zinc-400 mb-6 max-w-2xl mx-auto">
          Stepping into the unknown? We've got your back, every step of the way. With our dedicated 7-day support, your vision transforms from dream to reality, smoothly, effortlessly, and with the personal touch it truly deserves.
        </p>
        <NuxtLink to="/contact-us"
          class="inline-flex items-center px-6 py-3 text-lg font-semibold text-red-600 bg-white hover:bg-red-50 transition-colors duration-200 shadow-md hover:shadow-lg">
          Contact Support
          <Icon icon="heroicons:arrow-right-20-solid" class="ml-2 w-5 h-5" />
        </NuxtLink>
      </div>

      <div class="mt-20 grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        <div class="relative rounded-2xl">
          <img src="@/assets/illustrations/sales.png" alt="Already Selling Tickets" class="w-full h-auto shadow- rounded-2xl" />
          <div class="absolute rounded-2xl inset-0 bg-gradient-to-tr from-red-600/20 to-transparent"></div>
          <div class="absolute top-4 right-4">
            <Icon icon="fluent-emoji:admission-tickets" class="w-16 h-16 text-red-600" />
          </div>
        </div>
        <div>
          <h2 class="text-4xl sm:text-5xl font-bold text-zinc-900 dark:text-zinc-100 leading-tight mb-6">
            Already Selling Tickets Online?
          </h2>
          <p class="text-xl text-zinc-600 dark:text-zinc-400 leading-relaxed mb-8">
            Already a seasoned player in the online ticketing world? Think of EventaHub as your dynamic sidekick. Integrate our platform alongside your existing solutions, and tap into a fresh audience eager for events. With us, you're not just diversifying; you're amplifying your reach, securing more sales, and leaving no ticket unsold.
          </p>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup lang="ts">
interface LandingStats {
  totalEvents: number;
  totalTickets: number;
  customerRating: number;
  averageIncrease: number;
}

const httpClient = useHttpClient();

const fallbackStats: LandingStats = {
  totalEvents: 0,
  totalTickets: 0,
  customerRating: 0,
  averageIncrease: 0
};

const stats = ref<LandingStats>(fallbackStats);
const displayStats = ref<LandingStats>(fallbackStats);
const isVisible = ref(false);

const fetchStats = async () => {
  try {
    const apiStats = await httpClient.get<LandingStats>(ENDPOINTS.LANDING.STATS, {
      requiresAuth: false
    });
    stats.value = apiStats;
  } catch (error) {
    console.warn('Failed to fetch landing page stats, using fallback data');
  }
};


const startAnimations = async () => {
  if (!isVisible.value) return;

  const duration = 2000;
  const startTime = Date.now();

  const animate = () => {
    const elapsed = Date.now() - startTime;
    const progress = Math.min(elapsed / duration, 1);
    const easeProgress = 1 - Math.pow(1 - progress, 4);

    displayStats.value = {
      totalEvents: Math.floor(stats.value.totalEvents * easeProgress),
      totalTickets: Math.floor(stats.value.totalTickets * easeProgress),
      customerRating: Number((stats.value.customerRating * easeProgress).toFixed(1)),
      averageIncrease: Math.floor(stats.value.averageIncrease * easeProgress)
    };

    if (progress < 1) {
      requestAnimationFrame(animate);
    }
  };

  animate();
};

const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(0) + 'K';
  }
  return num.toString();
};

const statsRef = ref<HTMLElement>();

onMounted(async () => {
  await fetchStats();
  if (statsRef.value) {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !isVisible.value) {
            isVisible.value = true;
            startAnimations();
          }
        });
      },
      { threshold: 0.3 }
    );

    observer.observe(statsRef.value);
  }
});
</script>

<style scoped>
</style>
