<template>
  <div class="py-20 bg-zinc-50 dark:bg-zinc-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-12">
        <h2 class="text-4xl sm:text-5xl font-bold text-zinc-900 dark:text-zinc-100 mb-4">
          Explore By Categories
        </h2>
        <p class="text-xl text-zinc-600 dark:text-zinc-400 max-w-3xl mx-auto">
          Discover amazing events across different categories and find exactly what you're looking for
        </p>
      </div>

      <div class="relative">
        <button
          class="absolute left-0 top-1/2 -translate-y-1/2 z-10 w-12 h-12 bg-white dark:bg-zinc-700 shadow-lg hover:shadow-xl flex items-center justify-center transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          @click="prevSlide"
          :disabled="currentIndex === 0">
          <Icon icon="heroicons:chevron-left-solid" class="w-6 h-6 text-zinc-600 dark:text-zinc-300" />
        </button>

        <button
          class="absolute right-0 top-1/2 -translate-y-1/2 z-10 w-12 h-12 bg-white dark:bg-zinc-700 shadow-lg hover:shadow-xl flex items-center justify-center transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          @click="nextSlide"
          :disabled="currentIndex >= $categories.length - 1">
          <Icon icon="heroicons:chevron-right-solid" class="w-6 h-6 text-zinc-600 dark:text-zinc-300" />
        </button>

        <div class="scroll-container overflow-x-auto px-12">
          <div class="flex space-x-6 pb-4">
            <NuxtLink
              :to="`/events?category=${slide.name}`"
              v-for="(slide, index) in $categories"
              :key="index"
              class="flex-shrink-0 w-80 bg-white dark:bg-zinc-700 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 group">

              <div class="relative overflow-hidden">
                <div class="h-48 bg-gradient-to-br from-red-500 to-red-600 flex items-center justify-center relative">
                  <div class="absolute inset-0 bg-black/20"></div>
                  <img
                    :src="`${runtimeConfig.public.baseUrl}storage/categories/${slide.icon}`"
                    :alt="slide.name"
                    class="w-16 h-16 relative z-10" />
                </div>

                <!-- Category Info -->
                <div class="p-6">
                  <h3 class="text-xl font-bold text-zinc-900 dark:text-zinc-100 mb-2 group-hover:text-red-600 transition-colors duration-200">
                    {{ slide.name }}
                  </h3>
                  <p class="text-zinc-600 dark:text-zinc-400 mb-4">
                    {{ slide.events_count }} {{ slide.events_count === 1 ? 'event' : 'events' }} available
                  </p>
                  <div class="flex items-center text-red-600 font-semibold group-hover:text-red-700 transition-colors duration-200">
                    <span>Explore Events</span>
                    <Icon icon="heroicons:arrow-right-solid" class="w-4 h-4 ml-2 transform group-hover:translate-x-1 transition-transform duration-200" />
                  </div>
                </div>
              </div>
            </NuxtLink>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
const currentIndex = ref<number>(0);
const { $categories }: any = useNuxtApp();
const runtimeConfig = useRuntimeConfig();

const nextSlide = (): void => {
  const scrollContainer = document.querySelector(".scroll-container");
  scrollContainer?.scrollBy({ left: 300, behavior: "smooth" });
  if (currentIndex.value < $categories.value.length - 1) {
    currentIndex.value++;
  }
};

const prevSlide = (): void => {
  const scrollContainer = document.querySelector(".scroll-container");
  scrollContainer?.scrollBy({ left: -300, behavior: "smooth" });
  if (currentIndex.value > 0) {
    currentIndex.value--;
  }
};
</script>

<style scoped>
.scroll-container::-webkit-scrollbar {
  display: none;
}

.scroll-container {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
</style>
