<template>
  <div
    class="w-full flex flex-col items-center justify-center gap-1 py-10 bg-white dark:bg-zinc-900 transition-colors duration-200"
  >
    <h3
      class="text-3xl text-center justify-center sm:text-5xl font-semibold tracking-wider text-zinc-900 dark:text-zinc-100"
    >
      Subscribe to our newsletter
    </h3>
    <p
      class="w-full sm:w-3/4 lg:w-1/2 mx-auto justify-center text-center text-zinc-600 dark:text-zinc-400 px-4"
    >
      Get the latest updates on events and other news from EventaHub. Subscribe
      to our newsletter to stay up to date.
    </p>

    <div
      v-if="errorMessage"
      class="mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 max-w-md"
    >
      <div class="flex items-center">
        <Icon
          icon="heroicons:exclamation-triangle"
          class="w-5 h-5 text-red-600 dark:text-red-400 mr-2"
        />
        <p class="text-red-800 dark:text-red-200 text-sm">{{ errorMessage }}</p>
      </div>
    </div>

    <div class="mt-6 w-full max-w-md px-4">
      <FormKit
        id="newsletterForm"
        type="form"
        :actions="false"
        @submit="handleSubscribe"
        :disabled="loading"
      >
        <div class="flex flex-col sm:flex-row items-center gap-0">
          <div class="flex-1 w-full mb-2">
            <FormKit
              type="email"
              name="email"
              v-model="formData.email"
              required
              placeholder="Enter your email address"
              prefix-icon="email"
              label=""
              :classes="{
                input:
                  'w-full px-2 py-1.5 border border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-800 text-zinc-900 dark:text-zinc-100 placeholder-zinc-500 dark:placeholder-zinc-400 focus:ring-0',
              }"
            />
          </div>

          <CoreSubmitButton
            :loading="loading"
            :disabled="loading || !formData.email"
            text="Subscribe"
          />
        </div>
        <div v-if="showPreferences" class="mt-6 w-full max-w-md px-4">
          <h4 class="text-base font-medium text-zinc-900 dark:text-zinc-100 mb-3">
            What would you like to receive?
          </h4>
          <div class="space-y-2">
            <label
              v-for="preference in availablePreferences"
              :key="preference.key"
              class="flex items-center"
            >
              <input
                type="checkbox"
                :value="preference.key"
                v-model="formData.preferences"
                class="w-4 h-4 text-red-600 bg-zinc-100 dark:bg-zinc-700 border-zinc-300 dark:border-zinc-600 rounded focus:ring-red-500 dark:focus:ring-red-400"
              />
              <span class="ml-2 text-sm text-zinc-700 dark:text-zinc-300">{{
                preference.label
              }}</span>
            </label>
          </div>
        </div>
      </FormKit>
    </div>

    <p
      class="mt-4 text-xs text-zinc-500 dark:text-zinc-400 text-center max-w-md px-4"
    >
      By subscribing, you agree to receive marketing emails from EventaHub Malawi.
      You can unsubscribe at any time.
    </p>
  </div>
</template>

<script setup lang="ts">
import { ENDPOINTS } from "@/utils/api";

const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();
const loading = ref<boolean>(false);
const showSuccess = ref<boolean>(false);
const showPreferences = ref<boolean>(false);
const errorMessage = ref<string>("");

const formData = ref({
  email: "",
  name: "",
  preferences: ["latest_events", "recommended_events", "new_venues"],
});

const availablePreferences = [
  { key: "latest_events", label: "Latest Events" },
  { key: "recommended_events", label: "Recommended Events" },
  { key: "new_venues", label: "New Venues" },
  { key: "event_updates", label: "Event Updates" },
];

const handleSubscribe = async (data: any) => {
  if (!data.email) return;

  loading.value = true;
  errorMessage.value = "";
  showSuccess.value = false;

  try {
    const response = await httpClient.post(ENDPOINTS.NEWSLETTER.SUBSCRIBE, {
      email: data.email,
      name: formData.value.name,
      preferences: formData.value.preferences,
    });

    if (response) {
      showSuccess.value = true;
      formData.value.email = "";
      formData.value.name = "";
      showPreferences.value = false;

      setTimeout(() => {
        showSuccess.value = false;
      }, 5000);

      $toast.success("Successfully subscribed to newsletter!");
    }
  } catch (error: any) {
    console.error("Newsletter subscription error:", error);

    if (error.response?.data?.message) {
      errorMessage.value = error.response.data.message;
    } else if (error.response?.data?.errors?.email) {
      errorMessage.value = error.response.data.errors.email[0];
    } else {
      errorMessage.value = "Failed to subscribe. Please try again.";
    }

    setTimeout(() => {
      errorMessage.value = "";
    }, 5000);
  } finally {
    loading.value = false;
  }
};

watch(
  () => formData.value.email,
  (newEmail) => {
    if (newEmail && newEmail.includes("@")) {
      showPreferences.value = true;
    }
  }
);
</script>
