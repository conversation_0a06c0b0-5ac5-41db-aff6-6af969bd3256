<template>
  <span
    class="inline-flex items-center px-2.5 py-0.5 rounded-full font-medium cursor-pointer"
    :class="[colorClasses, { 'bg-red-600 text-white': isSelected }]"
    @click="toggleSelect"
  >
    <slot></slot>
  </span>
</template>

<script lang="ts" setup>
const props = defineProps({
  color: {
    type: String,
    default: "gray",
    validator: (value: string) =>
      [
        "gray",
        "red",
        "yellow",
        "green",
        "blue",
        "indigo",
        "purple",
        "pink",
      ].includes(value),
  },
});

const emit = defineEmits(["selected"]);
const isSelected = ref<boolean>(false);

const colorClasses = computed(() => {
  const colors = {
    gray: "bg-gray-100 text-gray-800",
    red: "bg-red-100 text-red-800",
    yellow: "bg-yellow-100 text-yellow-800",
    green: "bg-green-100 text-green-800",
    blue: "bg-blue-100 text-blue-800",
    indigo: "bg-indigo-100 text-indigo-800",
    purple: "bg-purple-100 text-purple-800",
    pink: "bg-pink-100 text-pink-800",
  };
  return colors[props.color as keyof typeof colors];
});

const toggleSelect = (): void => {
  isSelected.value = !isSelected.value;
  emit("selected", isSelected.value);
};
</script>
