<template>
  <span
    class="loader"
    :style="{
      borderColor: color + ' transparent',
      width: typeof width === 'number' ? width + 'px' : width,
      height: typeof height === 'number' ? height + 'px' : height
    }"
  ></span>
</template>

<script setup>
defineProps({
  color: {
    type: String,
    default: 'oklch(57.7% 0.245 27.325)'
  },
  width: {
    type: [Number, String],
    default: 70
  },
  height: {
    type: [Number, String],
    default: 70
  }
});
</script>

<style>
.loader {
  border: 5px solid;
  border-radius: 50%;
  display: inline-block;
  box-sizing: border-box;
  animation: rotation 1s linear infinite;
}

@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
