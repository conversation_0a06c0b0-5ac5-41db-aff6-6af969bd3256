<template>
    <CorePrimaryButton @click="handleCheckout" text="Checkout Cart" startIcon="emojione-monotone:shopping-cart" />

    <!-- Cart Summary Dialog -->
    <TransitionRoot as="template" :show="open">
        <Dialog class="relative z-[9999]" @close="open = false">
            <TransitionChild as="template" enter="ease-in-out duration-500" enter-from="opacity-0"
                enter-to="opacity-100" leave="ease-in-out duration-500" leave-from="opacity-100" leave-to="opacity-0">
                <div class="fixed inset-0 bg-red-500 bg-opacity-25 transition-opacity" />
            </TransitionChild>

            <div class="fixed inset-0 overflow-hidden">
                <div class="absolute inset-0 overflow-hidden">
                    <div class="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10">
                        <TransitionChild as="template"
                            enter="transform transition ease-in-out duration-500 sm:duration-700"
                            enter-from="translate-x-full" enter-to="translate-x-0"
                            leave="transform transition ease-in-out duration-500 sm:duration-700"
                            leave-from="translate-x-0" leave-to="translate-x-full">
                            <DialogPanel class="pointer-events-auto relative w-screen max-w-md">
                                <TransitionChild as="template" enter="ease-in-out duration-500" enter-from="opacity-0"
                                    enter-to="opacity-100" leave="ease-in-out duration-500" leave-from="opacity-100"
                                    leave-to="opacity-0">
                                    <div class="absolute left-0 top-0 -ml-8 flex pt-4 sm:-ml-10">
                                        <button type="button"
                                            class="relative bg-red-600 text-white focus:ring-none focus:outline-none px-2 py-2"
                                            @click="open = false">
                                            <span class="absolute -inset-2.5" />
                                            <span class="sr-only">Close panel</span>
                                            <XMarkIcon class="h-6 w-6" aria-hidden="true" />
                                        </button>
                                    </div>
                                </TransitionChild>
                                <div class="flex relative h-full flex-col overflow-y-scroll bg-white dark:bg-zinc-900 shadow-xl">
                                    <div class="w-full flex items-center px-4 py-3 border-b dark:border-zinc-700 justify-between">
                                        <DialogTitle class="text-xl font-semibold leading-6 text-gray-900 dark:text-zinc-50">
                                            Checkout Cart
                                        </DialogTitle>
                                        <div class="flex bg-red-600 text-white pr-1">
                                            <div class="bg-red-300 flex items-center p-1 mr-1">
                                                <Icon icon="noto-v1:shopping-cart" class="w-5 h-5" />
                                            </div>
                                            {{ props.items.length }} items
                                        </div>
                                    </div>
                                    <div>
                                        <div v-for="ticket in items" v-bind:key="ticket.id">
                                            <CartTicketCheckout  :ticket="ticket"/>
                                        </div>
                                    </div>
                                    <div class="absolute bottom-0 w-full bg-white dark:bg-zinc-900 p-4 border-t dark:border-zinc-700">
                                        <div class="w-full py-2">
                                            <div class="flex justify-between items-center">
                                                <h3 class="text-lg font-semibold dark:text-zinc-50">Total:</h3>
                                                <h3 class="text-xl font-semibold dark:text-zinc-50">{{ defaultCurrencySymbol }}{{ totalPrice.toLocaleString() }}</h3>
                                            </div>
                                        </div>

                                        <div class="w-full flex justify-end">
                                            <CorePrimaryButton text="Proceed to Payment" @click="proceedToPayment" />
                                        </div>
                                    </div>
                                </div>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>

    <TicketsPurchaseDialog
        :isOpen="paymentDialogOpen"
        :tickets="formattedTickets"
        @close="paymentDialogOpen = false"
        @success="handlePaymentSuccess"
    />
</template>

<script setup lang="ts">
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue'
import { XMarkIcon } from '@heroicons/vue/24/outline'
import type { PropType } from 'vue';
import { useAuthStore } from '@/store/auth';

const open = ref<boolean>(false);
const paymentDialogOpen = ref<boolean>(false);
const emit = defineEmits(['update']);
const { $toast } : any = useNuxtApp();
const authStore = useAuthStore();
const defaultCurrencySymbol = ref<string>("MK"); // Default to Malawian Kwacha if no other currency specified

const props = defineProps({
    items: {
        type: Array as PropType<any[]>,
        required: true
    }
});

const totalPrice = computed(() => {
    return props.items.reduce((total, item) => total + item.quantity * Number(item.tier.price), 0);
});

// Remove processing fee from being displayed to users
// Platform fees are now handled server-side and included in ticket prices
const formattedTickets = computed(() => {
    return props.items.map(item => ({
        ticket_id: item.tier.id,
        name: item.tier.name,
        price: item.tier.price,
        quantity: item.quantity,
        event_id: item.event.id
    }));
});

const handleCheckout = (): void => {
    if (!authStore.userIsAuthenticated) {
        $toast.info('Please log in to purchase tickets');
        navigateTo('/auth/login');
        return;
    }
    open.value = true;
};

const proceedToPayment = (): void => {
    open.value = false;
    paymentDialogOpen.value = true;
};

const handlePaymentSuccess = (): void => {
    paymentDialogOpen.value = false;
    $toast.success('Tickets purchased successfully!');
    emit('update', true);
};
</script>
