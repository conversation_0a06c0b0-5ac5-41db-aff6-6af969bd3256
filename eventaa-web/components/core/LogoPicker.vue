<template>
    <div class="w-full">
        <label class="block text-base font-medium text-gray-700 mb-1" for="logo">
            Logo <span class="text-red-500">*</span>
        </label>

        <div :class="`border-2 border-dashed p-4 transition-colors ${error ? 'border-red-400 bg-red-50' :
                file ? 'border-green-400 bg-green-50' : 'border-gray-300 hover:border-blue-400 bg-gray-50'
            }`" @dragover.prevent @drop.prevent="handleDrop" @click="triggerFileInput">
            <input type="file" id="logo" name="logo" ref="fileInputRef" accept="image/*" @change="handleFileChange"
                class="hidden" :aria-invalid="!!error" aria-describedby="logo-error" />

            <div v-if="!preview" class="flex flex-col items-center justify-center py-6 cursor-pointer">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-10 h-10 text-gray-400 mb-2" viewBox="0 0 24 24"
                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                    <polyline points="17 8 12 3 7 8"></polyline>
                    <line x1="12" y1="3" x2="12" y2="15"></line>
                </svg>
                <p class="text-sm text-gray-500 text-center">
                    Drag and drop an image file here, or click to select a file
                </p>
                <p class="text-xs text-gray-400 mt-1">
                    Supported formats: JPG, PNG, GIF, SVG, WebP
                </p>
            </div>

            <div v-else class="relative">
                <div class="flex justify-center">
                    <img :src="preview" alt="Preview" class="max-h-40 max-w-full object-contain rounded" />
                </div>
                <button type="button" @click.stop="removeFile"
                    class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                    aria-label="Remove file">
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
        </div>

        <div class="mt-2 flex justify-between items-center">
            <p v-if="error" class="text-sm text-red-600" id="logo-error">{{ error }}</p>
            <p v-else-if="file" class="text-sm text-green-600 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="20 6 9 17 4 12"></polyline>
                </svg>
                {{ file.name }} ({{ Math.round(file.size / 1024) }} KB)
            </p>
            <p v-else class="text-sm text-gray-500">No file selected</p>
        </div>
    </div>
</template>

<script lang="ts">
export default defineComponent({
    name: 'ImageUploadComponent',
    props: {
        modelValue: {
            type: Object as PropType<File | null>,
            default: null
        },
        required: {
            type: Boolean,
            default: true
        }
    },
    emits: ['update:modelValue'],
    setup(props, { emit }) {
        const file = ref<File | null>(props.modelValue);
        const preview = ref<string | null>(null);
        const error = ref<string>('');
        const touched = ref<boolean>(false);
        const fileInputRef = ref<HTMLInputElement | null>(null);

        watch(() => props.modelValue, (newVal) => {
            file.value = newVal;
            if (newVal) {
                createPreview(newVal);
            } else {
                preview.value = null;
            }
        });

        const handleDrop = (e: DragEvent): void => {
            if (e.dataTransfer?.files) {
                const droppedFile = e.dataTransfer.files[0];
                handleFileValidation(droppedFile);
            }
        };

        const handleFileChange = (e: Event): void => {
            const target = e.target as HTMLInputElement;
            if (target.files) {
                const selectedFile = target.files[0];
                handleFileValidation(selectedFile);
            }
        };

        const handleFileValidation = (selectedFile: File | undefined): void => {
            touched.value = true;
            error.value = '';

            if (!selectedFile) {
                error.value = props.required ? 'Logo is required' : '';
                file.value = null;
                preview.value = null;
                emit('update:modelValue', null);
                return;
            }

            if (!selectedFile.type.startsWith('image/')) {
                error.value = 'Please upload an image file';
                file.value = null;
                preview.value = null;
                emit('update:modelValue', null);
                return;
            }

            file.value = selectedFile;
            emit('update:modelValue', selectedFile);
            createPreview(selectedFile);
        };

        const createPreview = (fileToPreview: File): void => {
            const reader = new FileReader();
            reader.onload = () => {
                preview.value = reader.result as string;
            };
            reader.readAsDataURL(fileToPreview);
        };

        const removeFile = (): void => {
            file.value = null;
            preview.value = null;
            error.value = touched.value && props.required ? 'Logo is required' : '';
            emit('update:modelValue', null);
            if (fileInputRef.value) {
                fileInputRef.value.value = '';
            }
        };

        const triggerFileInput = (): void => {
            if (fileInputRef.value) {
                fileInputRef.value.click();
            }
        };

        return {
            file,
            preview,
            error,
            fileInputRef,
            handleDrop,
            handleFileChange,
            removeFile,
            triggerFileInput
        };
    }
});
</script>