<script setup lang="ts">
import type { BreadcrumbItem } from '@/types/breadcrumb';

const props = defineProps<{
    items?: BreadcrumbItem[]
    useRoutePath?: boolean
}>()

const route = useRoute()

const breadcrumbItems = computed<BreadcrumbItem[]>(() => {
    if (props.items) {
        return props.items
    }

    if (props.useRoutePath) {
        const pathParts = route.path.split('/').filter(Boolean)
        return pathParts.map((part, index) => {
            const path = '/' + pathParts.slice(0, index + 1).join('/')
            return {
                text: part.charAt(0).toUpperCase() + part.slice(1),
                to: path,
                disabled: index === pathParts.length - 1
            }
        })
    }

    return []
})
</script>

<template>
    <nav aria-label="Breadcrumb" class="px-2">
        <ol class="flex items-center space-x-2 text-gray-600">
            <li class="flex items-center">
                <NuxtLink to="/" class="text-gray-600 hover:text-gray-900 transition-colors">
                    <Icon icon="fa-solid:home" class="w-5 h-5" />
                </NuxtLink>
            </li>
            <template v-for="(item, index) in breadcrumbItems" :key="index">
                <li class="flex items-center">
                    <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd"
                            d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                            clip-rule="evenodd" />
                    </svg>
                    <component :is="item.disabled ? 'span' : 'NuxtLink'" :to="item.to" :class="[
                        'ml-2',
                        item.disabled
                            ? 'text-gray-400 cursor-default'
                            : 'text-gray-600 hover:text-gray-900 transition-colors'
                    ]">
                        {{ item.text }}
                    </component>
                </li>
            </template>
        </ol>
    </nav>
</template>