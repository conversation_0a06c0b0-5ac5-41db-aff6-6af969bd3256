<template>
  <div class="dashboard-bg-card p-4 dashboard-shadow">
    <div class="flex items-center">
      <div class="p-3 mr-4 rounded-full" :class="iconBackgroundClass">
        <Icon :icon="icon" class="w-6 h-6" :class="iconClass" />
      </div>
      <div class="flex-1">
        <p class="text-sm dashboard-text-muted">
          {{ title }}
        </p>
        <p class="text-2xl font-bold dashboard-text-primary">
          {{ formattedValue }}
        </p>
        <div
          v-if="showGrowth && growth !== undefined"
          class="flex items-center text-sm"
        >
          <span :class="growthClass">
            <Icon :icon="growthIcon" class="w-4 h-4 inline animate-pulse" />
            {{ Math.abs(growth) }}%
          </span>
          <span class="dashboard-text-muted ml-1">
            {{ growthLabel }}
          </span>
        </div>
        <div
          v-if="subtitle"
          class="text-sm dashboard-text-muted mt-1"
        >
          {{ subtitle }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { formatCurrency } from "@/utils/currency";

interface Props {
  title: string;
  value: string | number;
  icon: string;
  iconColor?:
    | "green"
    | "blue"
    | "yellow"
    | "red"
    | "purple"
    | "indigo"
    | "gray";
  growth?: number;
  growthLabel?: string;
  showGrowth?: boolean;
  subtitle?: string;
  formatter?: (value: string | number) => string;
  isCurrency?: boolean;
  currencyCompact?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  iconColor: "blue",
  growthLabel: "vs previous period",
  showGrowth: true,
  isCurrency: false,
  currencyCompact: false,
});

const formattedValue = computed(() => {
  if (props.formatter) {
    return props.formatter(props.value);
  }

  if (props.isCurrency) {
    return formatCurrency(props.value, {
      compact: props.currencyCompact,
      decimals: props.currencyCompact ? 1 : 2
    });
  }

  // Handle numeric formatting for non-currency values
  const numValue = Number(props.value);
  if (!isNaN(numValue) && numValue >= 1000) {
    return numValue.toLocaleString();
  }

  return String(props.value);
});

const iconBackgroundClass = computed(() => {
  const colorMap = {
    green: "bg-green-100 dark:bg-green-900/20",
    blue: "bg-blue-100 dark:bg-blue-900/20",
    yellow: "bg-yellow-100 dark:bg-yellow-900/20",
    red: "bg-red-100 dark:bg-red-900/20",
    purple: "bg-purple-100 dark:bg-purple-900/20",
    indigo: "bg-indigo-100 dark:bg-indigo-900/20",
    gray: "bg-gray-100 dark:bg-gray-900/20",
  };
  return colorMap[props.iconColor];
});

const iconClass = computed(() => {
  const colorMap = {
    green: "text-green-600 dark:text-green-400",
    blue: "text-blue-600 dark:text-blue-400",
    yellow: "text-yellow-600 dark:text-yellow-400",
    red: "text-red-600 dark:text-red-400",
    purple: "text-purple-600 dark:text-purple-400",
    indigo: "text-indigo-600 dark:text-indigo-400",
    gray: "text-gray-600 dark:text-gray-400",
  };
  return colorMap[props.iconColor];
});

const growthClass = computed(() => {
  if (props.growth === undefined) return "";

  return props.growth >= 0
    ? "text-green-600 dark:text-green-400 font-medium"
    : "text-red-600 dark:text-red-400 font-medium";
});

const growthIcon = computed(() => {
  if (props.growth === undefined) return "";

  return props.growth >= 0 ? "mynaui:chevron-up-solid" : "mynaui:chevron-down-solid";
});
</script>
