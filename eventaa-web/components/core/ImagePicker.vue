<template>
    <TransitionRoot appear :show="isOpen" as="template">
        <Dialog as="div" @close="closeDialog" class="relative z-10">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
                leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                <div class="fixed inset-0 bg-black/25" />
            </TransitionChild>

            <div class="fixed inset-0 overflow-y-auto">
                <div class="flex min-h-full items-center justify-center p-4 text-center">
                    <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                        enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
                        leave-to="opacity-0 scale-95">
                        <DialogPanel
                            class="w-full max-w-xl transform overflow-hidden bg-white dark:bg-zinc-800 text-left align-middle shadow-xl transition-all">
                            <DialogTitle as="h3" class="w-full border-b border-gray-200 dark:border-zinc-700 flex items-center justify-between px-4 py-3">
                                <div class="flex items-center">
                                    <Icon icon="ion:image-outline" class="w-6 h-6 mr-2 text-gray-700 dark:text-zinc-300" />
                                    <h3 class="text-xl font-semibold leading-6 text-gray-900 dark:text-zinc-100">Upload Image</h3>
                                </div>
                                <button @click="closeDialog">
                                    <Icon icon="ri:close-fill" class="w-6 h-6 text-gray-500 dark:text-zinc-400 hover:text-gray-700 dark:hover:text-zinc-200 cursor-pointer transition-colors" />
                                </button>
                            </DialogTitle>
                            <div class="flex items-center justify-center min-h-full text-center">
                                <div
                                    class="bg-white dark:bg-zinc-800 rounded-lg text-left overflow-hidden transform transition-all sm:max-w-lg sm:w-full">
                                    <div class="bg-white dark:bg-zinc-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                                        <div>

                                            <div v-if="image" class="mt-4">
                                                <img :src="image" alt="Preview" class="w-full h-auto border border-gray-200 dark:border-zinc-600" />
                                            </div>

                                            <div class="mt-4 border-dashed border-4 border-gray-100 dark:border-zinc-600 p-4 text-center">
                                                <input type="file" class="hidden" ref="fileInput" accept="image/*"
                                                    @change="handleFileChange" />
                                                <div @drop.prevent="handleDrop" @dragover.prevent
                                                    class="p-4 bg-gray-100 dark:bg-zinc-700 border border-dashed border-gray-300 dark:border-zinc-600 rounded-none cursor-pointer hover:bg-gray-200 dark:hover:bg-zinc-600 transition-colors"
                                                    @click="triggerFileInput">
                                                    <p class="text-gray-500 dark:text-zinc-400">Drag & Drop your image here, or
                                                        click to select an image</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div v-if="!loading"
                                        class="bg-gray-50 dark:bg-zinc-700 px-4 py-3 sm:px-6 pb-2 sm:flex sm:flex-row-reverse">
                                        <CorePrimaryButton text="Upload" @click="upload" />
                                    </div>
                                    <div v-else
                                        class="bg-gray-50 dark:bg-zinc-700 px-4 py-3 sm:px-6 pb-2 flex items-center text-sky-500 dark:text-sky-400">
                                        <CoreLoader width="30" height="30" color="dodgerblue" />
                                        <span class="ml-2 text-gray-700 dark:text-zinc-300">Uploading, please wait...</span>
                                    </div>
                                </div>
                            </div>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>
</template>

<script setup lang="ts">
import {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
} from '@headlessui/vue'
import type { GenericResponse } from '@/types/api';

const props = defineProps({
    field: {
        type: String,
        required: true
    },
    url: {
        type: String,
        required: true
    }
});

const emits = defineEmits(['onUploaded']);

const isOpen = ref<boolean>(false);
const loading = ref<boolean>(false);
const image = ref<string | null>(null);
const fileInput = ref<HTMLInputElement | null>(null);
const file = ref<File | null>(null);
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const openDialog = (): void => {
    isOpen.value = true;
};

const closeDialog = (): void => {
    isOpen.value = false;
    image.value = null;
};

const triggerFileInput = (): void => {
    fileInput.value?.click();
};

const handleFileChange = (event: Event) => {
    const target = event.target as HTMLInputElement;
    const files = target.files;
    if (files && files[0]) {
        const reader = new FileReader();
        reader.onload = (e: ProgressEvent<FileReader>) => {
            image.value = e.target?.result as string;
        };
        reader.readAsDataURL(files[0]);
        file.value = files[0];
    }
};

const handleDrop = (event: DragEvent): void => {
    const files = event.dataTransfer?.files;
    if (files && files.length > 0) {
        const reader = new FileReader();
        reader.onload = (e: ProgressEvent<FileReader>) => {
            image.value = e.target?.result as string;
        };
        reader.readAsDataURL(files[0]);
        file.value = files[0];
    }
};

const upload = async (): Promise<void> => {
    try {
        loading.value = true;
        if (!file.value) {
            throw new Error('No file selected');
        }
        const formData = new FormData();
        formData.append(props.field, file.value);
        const response = await httpClient.post<GenericResponse>(props.url, formData);
        if (response) {
            emits('onUploaded', true);
            $toast.success(response.message);
            closeDialog();
        }
    } catch (error: any) {
        if (error.errors) {
            const errors = error.errors;
            Object.keys(errors).forEach((key) => {
                if (Array.isArray(errors[key])) {
                    errors[key].forEach((message: string) => {
                        $toast.error(message);
                    });
                } else if (typeof errors[key] === 'string') {
                    $toast.error(errors[key]);
                }
            });
        } else {
            $toast.error(error.message || 'An error occurred');
        }
    } finally {
        loading.value = false;
        closeDialog();
    }
}
defineExpose({
    openDialog,
    closeDialog
});
</script>
