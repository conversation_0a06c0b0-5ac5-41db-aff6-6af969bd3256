<template>
  <div class="relative">
    <select
      :value="modelValue"
      @input="
        $emit('update:modelValue', ($event.target as HTMLSelectElement)?.value)
      "
      class="w-full border-gray-300 bg-gray-50 text-gray-700 px-2 py-1.5 dark:border-zinc-600 dark:bg-zinc-700 dark:text-white focus:border-red-500 focus:ring-red-500 appearance-none pr-8"
      :class="[customClass]"
      :disabled="disabled"
    >
      <option
        v-for="(option, index) in options"
        :key="index"
        :value="option.value"
      >
        {{ option.label }}
      </option>
    </select>
    <Icon
      :icon="icon || 'mynaui:chevron-up-down-solid'"
      class="w-5 h-5 absolute right-0 top-2 flex items-center pointer-events-none text-gray-500 dark:text-gray-400"
    />
  </div>
</template>

<script setup lang="ts">
export interface SelectOption {
  value: string | number | boolean | object | null;
  label: string;
}

defineProps({
  modelValue: {
    type: [String, Number, Boolean, Object, Array],
    default: "",
  },
  options: {
    type: Array as () => SelectOption[],
    required: true,
    default: () => [],
  },
  customClass: {
    type: String,
    default: "",
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  icon: {
    type: String,
    default: "mynaui:chevron-up-down-solid",
  },
});

defineEmits(["update:modelValue"]);
</script>

<style scoped>
select {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  text-indent: 1px;
  text-overflow: "";
}
</style>
