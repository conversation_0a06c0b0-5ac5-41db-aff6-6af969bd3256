<template>
    <div class="flex flex-col items-center justify-center">
        <div class="relative w-full max-w-4xl h-40">
            <div class="flex h-full items-end space-x-1">
                <div v-for="(bar, index) in chartData" :key="index" :class="[
                    'flex-grow transition-all duration-300',
                    index >= selectedRange[0] && index <= selectedRange[1]
                        ? 'bg-red-500'
                        : 'bg-[#ececec]'
                ]" :style="{ height: `${bar}%` }"></div>
            </div>

            <div class="relative w-full mt-0.5">
                <div class="absolute h-4 w-full" :style="{
                    background: `linear-gradient(
                    to right,
                    #ececec ${startPercentage}%,
                    red ${startPercentage}%,
                    red ${endPercentage}%,
                    #ececec ${endPercentage}%
                    )`,
                }"></div>

                <input type="range" :min="0" :max="chartData.length - 1" v-model.number="selectedRange[0]"
                    @input="updateStartRange" class="absolute w-full appearance-none slider-thumb" />

                <input type="range" :min="0" :max="chartData.length - 1" v-model.number="selectedRange[1]"
                    @input="updateEndRange" class="absolute w-full appearance-none slider-thumb" />
            </div>
        </div>

        <div class="w-full max-w-4xl flex justify-between text-gray-500 text-sm mt-8">
            <span class="text-lg font-semibold">MWK{{ minPrice.toLocaleString() }}</span>
            <span class="text-lg font-semibold">MWK{{ maxPrice.toLocaleString() }}</span>
        </div>
    </div>
</template>

<script setup lang="ts">
type ChartDataType = number[];
type SelectedRangeType = [number, number];

const chartData = ref<ChartDataType>([20, 50, 80, 40, 60, 90, 70, 50, 30, 40, 60, 90, 70, 50, 30]);
const selectedRange = ref<SelectedRangeType>([3, 8]);

const minPrice = ref(0);
const maxPrice = ref(1500000);

const startPercentage = computed(() => {
    return (selectedRange.value[0] / (chartData.value.length - 1)) * 100;
})
const endPercentage = computed(() => {
    return (selectedRange.value[1] / (chartData.value.length - 1)) * 100;
})

const updateStartRange = () => {
    if (selectedRange.value[0] >= selectedRange.value[1]) {
        selectedRange.value[0] = selectedRange.value[1] - 1;
    }

    minPrice.value = Math.round(
        selectedRange.value[0] / (chartData.value.length - 1) * (1789 - 123) + 123
    );
};

const updateEndRange = () => {
    if (selectedRange.value[1] <= selectedRange.value[0]) {
        selectedRange.value[1] = selectedRange.value[0] + 1;
    }

    maxPrice.value = Math.round(
        selectedRange.value[1] / (chartData.value.length - 1) * (1789 - 123) + 123
    );
};
</script>

<style scoped>
input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 25px;
    height: 25px;
    background: #FF4500;
    cursor: pointer;
    border-radius: 50%;
}

input[type="range"]::-moz-range-thumb {
    width: 25px;
    height: 25px;
    background: #FF4500;
    cursor: pointer;
    border-radius: 50%;
}

.slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    background: transparent;
    height: 10px;
    position: relative;
}

.slider-thumb::-webkit-slider-thumb {
    -webkit-appearance: none;
    height: 10px;
    width: 10px;
    border-radius: 50%;
    background: #fff;
    border: 2px solid #ececec;
    cursor: pointer;
}

.slider-thumb::-moz-range-thumb {
    height: 10px;
    width: 10px;
    border-radius: 50%;
    background: #fff;
    border: 2px solid #ececec;
    cursor: pointer;
}
</style>