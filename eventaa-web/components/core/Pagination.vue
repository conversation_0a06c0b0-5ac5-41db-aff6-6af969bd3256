<template>
  <nav class="flex items-center justify-center" aria-label="Pagination">
    <button
      @click="prevPage"
      :disabled="currentPage === 1"
      class="relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-zinc-600 disabled:opacity-50 disabled:cursor-not-allowed"
    >
      <span class="sr-only">Previous</span>
      <Icon icon="heroicons:chevron-left" class="h-5 w-5" />
    </button>
    <button
      v-for="page in displayedPages"
      :key="page"
      @click="goToPage(page)"
      class="relative inline-flex items-center px-4 py-2 border-gray-300 dark:border-zinc-600 dark:bg-zinc-700 text-sm font-medium"
      :class="[
        page === currentPage
          ? 'z-10 bg-red-600 text-red-100 dark:text-red-50 border-none'
          : 'text-gray-500 dark:text-gray-400 border hover:bg-gray-50 dark:hover:bg-zinc-600'
      ]"
    >
      {{ page }}
    </button>
    <button
      @click="nextPage"
      :disabled="currentPage === totalPages"
      class="relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-zinc-600 disabled:opacity-50 disabled:cursor-not-allowed"
    >
      <span class="sr-only">Next</span>
      <Icon icon="heroicons:chevron-right" class="h-5 w-5" />
    </button>
  </nav>
</template>

<script setup lang="ts">
const props = defineProps({
  currentPage: {
    type: Number,
    required: true
  },
  totalPages: {
    type: Number,
    required: true
  },
  totalItems: {
    type: Number,
    default: 0
  },
  perPage: {
    type: Number,
    default: 10
  }
});

const emit = defineEmits<{
  (e: 'page-change', page: number): void
}>();

const displayedPages = computed(() => {
  const pages = [];
  const maxPagesToShow = 5;

  if (props.totalPages <= maxPagesToShow) {
    for (let i = 1; i <= props.totalPages; i++) {
      pages.push(i);
    }
  } else {
    const leftSide = Math.floor(maxPagesToShow / 2);
    const rightSide = maxPagesToShow - leftSide - 1;

    if (
      props.currentPage > leftSide &&
      props.currentPage < props.totalPages - rightSide
    ) {
      for (
        let i = props.currentPage - leftSide;
        i <= props.currentPage + rightSide;
        i++
      ) {
        pages.push(i);
      }
    } else if (props.currentPage <= leftSide) {
      for (let i = 1; i <= maxPagesToShow; i++) {
        pages.push(i);
      }
    } else {
      for (
        let i = props.totalPages - maxPagesToShow + 1;
        i <= props.totalPages;
        i++
      ) {
        pages.push(i);
      }
    }
  }

  return pages;
});

function prevPage() {
  if (props.currentPage > 1) {
    emit('page-change', props.currentPage - 1);
  }
}

function nextPage() {
  if (props.currentPage < props.totalPages) {
    emit('page-change', props.currentPage + 1);
  }
}

function goToPage(page: number) {
  emit('page-change', page);
}
</script>
