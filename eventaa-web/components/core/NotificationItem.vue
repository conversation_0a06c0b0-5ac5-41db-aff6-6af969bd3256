<template>
  <div class="border-b dark:border-zinc-700 flex justify-between px-2"
    :class="notification.read_at ? 'bg-white dark:bg-zinc-800' : 'bg-sky-50 dark:bg-zinc-700'">
    <div class="flex items-center justify-center text-gray-500 dark:text-zinc-300">
      <Icon icon="carbon:notification-filled" class="w-6 h-6" />
    </div>

    <div class="flex-grow px-4 py-2">
      <h3 class="font-medium text-gray-900 dark:text-zinc-100">{{ notification.data.title ?? "" }}</h3>
      <p class="sm:text-base text-sm text-gray-600 dark:text-zinc-300">{{ notification.data.line ?? notification.data.message }}</p>
    </div>

    <div v-if="loading">
      <CoreLoader width="25" height="25" color="#0ea5e9" />
    </div>

    <div v-else class="relative mt-3">
      <Menu as="div" class="relative inline-block text-left">
        <div>
          <MenuButton class="focus:outline-none focus:border-none focus:ring-none">
            <Icon icon="la:ellipsis-v" class="w-5 h-5 text-gray-600" />
          </MenuButton>
        </div>

        <transition enter-active-class="transition duration-100 ease-out"
          enter-from-class="transform scale-95 opacity-0" enter-to-class="transform scale-100 opacity-100"
          leave-active-class="transition duration-75 ease-in" leave-from-class="transform scale-100 opacity-100"
          leave-to-class="transform scale-95 opacity-0">
          <MenuItems
            class="absolute right-0 mt-2 w-40 origin-top-right divide-y divide-gray-100 dark:divide-zinc-700 rounded-none shadow-red-50 bg-white dark:bg-zinc-800 shadow-lg ring-1 ring-black/5 dark:ring-zinc-700 focus:outline-none">
            <div class="px-1 py-1">
              <MenuItem v-slot="{ active }">
              <button @click="notificationAction" :class="[
                active ? 'bg-red-600 text-white' : 'text-gray-900 dark:text-zinc-100',
                'group flex w-full items-center px-2 py-2 text-sm font-thin',
              ]">
                <Icon icon="iconoir:redo-action" :active="active" class="mr-2 h-5 w-5" aria-hidden="true" />
                Action
              </button>
              </MenuItem>
              <MenuItem v-slot="{ active }">
              <button @click="markAsRead" :class="[
                active ? 'bg-red-600 text-white' : 'text-gray-900',
                'group flex w-full items-center px-2 py-2 text-sm font-thin',
              ]">
                <Icon icon="line-md:edit" :active="active" class="mr-2 h-5 w-5" aria-hidden="true" />
                Mark As Read
              </button>
              </MenuItem>
              <MenuItem v-slot="{ active }">
              <button @click="deleteNotification" :class="[
                active ? 'bg-red-600 text-white' : 'text-gray-900',
                'group flex w-full items-center px-2 py-2 text-sm font-thin',
              ]">
                <Icon icon="icon-park-outline:delete" :active="active" class="mr-2 h-5 w-5" aria-hidden="true" />
                Delete
              </button>
              </MenuItem>
            </div>
          </MenuItems>
        </transition>
      </Menu>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Menu, MenuButton, MenuItems, MenuItem } from '@headlessui/vue'
import type { PropType } from 'vue';
import type { Notification } from '@/types/api';

const props = defineProps({
  notification: {
    type: Object as PropType<Notification>,
    required: true
  }
});

const emits = defineEmits(['markAsRead']);
const loading = ref(false);
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const notificationAction = (): void => {
  if (props.notification.data.actionURL !== undefined) {
    const url = props.notification.data.actionURL;
    const link = document.createElement('a');
    link.href = url;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}

const markAsRead = async (): Promise<void> => {
  try {
    const response = await httpClient.put<{ success: string; }>(`${ENDPOINTS.NOTIFICATIONS.READ}/${props.notification.id}`)
    if (response) {
      $toast.success(response.success);
      setTimeout(() => {
        emits('markAsRead');
      }, 1000);
    }
  } catch (error: any) {
    const errors = error.errors;
    Object.keys(errors).forEach((key) => {
      if (Array.isArray(errors[key])) {
        errors[key].forEach((message: string) => {
          $toast.error(message);
        });
      } else if (typeof errors[key] === 'string') {
        $toast.error(errors[key]);
      }
    });
  } finally {

  }
}

const deleteNotification = async (): Promise<void> => {
  try {
    const response = await httpClient.delete<{ success: string; }>(`${ENDPOINTS.NOTIFICATIONS.DELETE}/${props.notification.id}`)
    if (response) {
      $toast.success(response.success);
      setTimeout(() => {
        emits('markAsRead');
      }, 1000);
    }
  } catch (error: any) {
    const errors = error.errors;
    Object.keys(errors).forEach((key) => {
      if (Array.isArray(errors[key])) {
        errors[key].forEach((message: string) => {
          $toast.error(message);
        });
      } else if (typeof errors[key] === 'string') {
        $toast.error(errors[key]);
      }
    });
  } finally {
    loading.value = false;
  }
}
</script>

<style lang="sass" scoped>
</style>
