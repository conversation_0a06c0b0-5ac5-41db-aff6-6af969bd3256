<template>
    <div v-if="!isConsentAccepted"
        class="fixed bottom-0 left-0 right-0 md:bottom-5 md:right-5 md:left-auto w-full md:max-w-md bg-red-600 text-white shadow-lg p-4 md:p-6 z-50">
        <div>
            <h3 class="flex items-center text-lg md:text-xl mb-2 md:mb-3">
                <span class="mr-2" role="img" aria-label="cookie">🍪</span>
                Cookie Notice
            </h3>
            <p class="text-sm md:text-base mb-3 md:mb-4">
                We use cookies to learn how you interact with our content, and show you relevant content and ads
                based on your browsing history. You can adjust your settings below or view our
                <NuxtLink to="/privacy" class="underline hover:text-sky-200">
                    privacy policy
                </NuxtLink>.
            </p>
            <div class="flex flex-col md:flex-row items-center md:justify-between space-y-2 md:space-y-0 md:space-x-4">
                <button @click="handleAcceptAll"
                    class="w-full md:w-auto bg-sky-500 hover:bg-sky-600 text-white py-2 px-4 transition duration-300">
                    Accept All Cookies
                </button>
                <button @click="openPreferencesModal"
                    class="w-full md:w-auto text-white hover:text-sky-200 underline py-2 transition duration-300">
                    Manage Preferences
                </button>
            </div>
        </div>
    </div>

    <div v-if="showPreferencesModal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[100] p-4">
        <div class="bg-white text-black max-w-md w-full p-6 space-y-4">
            <h2 class="text-xl font-semibold mb-4">🍪 Cookie Preferences</h2>

            <div class="space-y-3">
                <div class="flex items-center justify-between">
                    <label class="flex items-center">
                        <input type="checkbox" checked disabled class="mr-2 form-checkbox text-sky-500">
                        Necessary Cookies
                    </label>
                    <span class="text-sm text-gray-500">Always Active</span>
                </div>

                <div class="flex items-center justify-between">
                    <label class="flex items-center">
                        <input type="checkbox" v-model="preferences.analytics" class="mr-2 form-checkbox text-sky-500">
                        Analytics Cookies
                    </label>
                    <span class="text-sm text-gray-500">Improve site performance</span>
                </div>

                <div class="flex items-center justify-between">
                    <label class="flex items-center">
                        <input type="checkbox" v-model="preferences.marketing" class="mr-2 form-checkbox text-sky-500">
                        Marketing Cookies
                    </label>
                    <span class="text-sm text-gray-500">Personalized ads</span>
                </div>
            </div>

            <div class="flex space-x-4 pt-4">
                <button @click="savePreferences"
                    class="flex-1 bg-sky-500 text-white py-2 hover:bg-sky-600 transition">
                    Save Preferences
                </button>
                <button @click="closePreferencesModal"
                    class="flex-1 border border-gray-300 py-2 hover:bg-gray-100 transition">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
const {
    acceptAllCookies,
    updatePreferences,
    isConsentAccepted
} = useCookieConsent()

const showPreferencesModal = ref(false)
const preferences = reactive({
    analytics: false,
    marketing: false
})

const handleAcceptAll = () => {
    acceptAllCookies()
}

const openPreferencesModal = () => {
    showPreferencesModal.value = true
}

const closePreferencesModal = () => {
    showPreferencesModal.value = false
}

const savePreferences = () => {
    updatePreferences({
        analytics: preferences.analytics,
        marketing: preferences.marketing
    })
    closePreferencesModal()
}
</script>

<style scoped>
.form-checkbox {
    @apply text-sky-500 focus:ring-sky-500;
}
</style>