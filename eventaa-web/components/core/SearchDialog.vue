<template>
  <div>
    <button
      @click="openDialog"
      class="p-2 text-gray-400 dark:text-zinc-100 dark:hover:text-zinc-50 hover:text-gray-500"
    >
      <span class="sr-only">Search</span>
      <MagnifyingGlassIcon class="h-6 w-6" aria-hidden="true" />
    </button>
  </div>
  <TransitionRoot appear :show="isOpen" as="template">
    <Dialog as="div" @close="closeDialog" class="relative z-[9999]">
      <TransitionChild
        as="template"
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-black bg-opacity-50" />
      </TransitionChild>

      <div class="fixed inset-0 overflow-y-auto">
        <div class="flex min-h-full items-start justify-center pt-16 px-4">
          <TransitionChild
            as="template"
            enter="duration-300 ease-out"
            enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95"
          >
            <DialogPanel
              class="w-full max-w-3xl transform overflow-hidden bg-white dark:bg-zinc-800 shadow-2xl transition-all"
            >
              <div class="relative">
                <!-- Search Header -->
                <div class="border-b border-gray-200 dark:border-zinc-700 p-4">
                  <div class="relative flex items-center">
                    <div
                      class="absolute left-3 top-1/2 transform -translate-y-1/2"
                    >
                      <MagnifyingGlassIcon
                        class="h-5 w-5 text-gray-400 dark:text-zinc-400"
                      />
                    </div>
                    <input
                      v-model="searchTerm"
                      ref="searchInput"
                      placeholder="Search events, vendors, venues..."
                      class="w-full pl-10 pr-20 py-3 bg-transparent text-gray-900 dark:text-zinc-100 placeholder-gray-500 dark:placeholder-zinc-400 border-0 focus:outline-none focus:ring-0 text-lg"
                      @input="handleSearchInput"
                      @keydown.enter="handleSearch"
                      @keydown.escape="closeDialog"
                      type="search"
                      autocomplete="off"
                    />
                    <div
                      class="absolute top-1/2 transform -translate-y-1/2 right-3"
                    >
                      <div class="flex items-center space-x-2">
                        <div
                          class="hidden sm:flex items-center justify-center px-2 py-1 bg-gray-100 dark:bg-zinc-700 text-xs text-gray-500 dark:text-zinc-300 border border-gray-200 dark:border-zinc-600"
                        >
                          <span class="mr-1">⌘</span>K
                        </div>
                        <button
                          @click="closeDialog"
                          class="p-1 text-gray-400 hover:text-gray-600 dark:text-zinc-400 dark:hover:text-zinc-200"
                        >
                          <XMarkIcon class="h-5 w-5" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Search Filters -->
                <div
                  class="bg-gray-50 dark:bg-zinc-800 border-b border-gray-200 dark:border-zinc-700 px-4 py-3"
                >
                  <div class="flex space-x-2">
                    <button
                      v-for="filter in searchFilters"
                      :key="filter.key"
                      @click="activeFilter = filter.key"
                      :class="[
                        'bg-red-100 dark:bg-zinc-700 rounded-full flex items-center px-3 py-1.5 font-medium transition-colors duration-200',
                        activeFilter === filter.key
                          ? 'bg-red-600 text-red-50 dark:bg-red-900/20 dark:text-red-50'
                          : 'text-gray-600 dark:text-zinc-400 hover:text-gray-900 dark:hover:text-zinc-200 hover:bg-gray-100 dark:hover:bg-zinc-700',
                      ]"
                    >
                      <Icon :icon="filter.icon" class="w-4 h-4 mr-1.5 inline" />
                      {{ filter.label }}
                      <span
                        v-if="getResultCount(filter.key) > 0"
                        class="ml-1.5 px-1.5 py-0.5 text-xs bg-gray-200 dark:bg-zinc-600 text-gray-700 dark:text-zinc-300 rounded-full"
                      >
                        {{ getResultCount(filter.key) }}
                      </span>
                    </button>
                  </div>
                </div>

                <div class="max-h-96 overflow-y-auto">
                  <div v-if="loading" class="flex justify-center py-12">
                    <CoreLoader />
                  </div>

                  <div
                    v-else-if="hasSearched && !hasResults"
                    class="py-12 text-center"
                  >
                    <div class="mb-6 flex justify-center">
                      <div class="relative">
                        <div
                          class="h-16 w-16 bg-gray-100 dark:bg-zinc-700 rounded-full flex items-center justify-center"
                        >
                          <MagnifyingGlassIcon
                            class="h-8 w-8 text-gray-400 dark:text-zinc-500"
                          />
                        </div>
                      </div>
                    </div>
                    <h3
                      class="text-lg font-medium text-gray-900 dark:text-zinc-100 mb-2"
                    >
                      No results found
                    </h3>
                    <p class="text-gray-500 dark:text-zinc-400 mb-4">
                      We couldn't find anything matching "{{ searchTerm }}"
                    </p>
                    <button
                      @click="clearSearch"
                      class="px-4 py-2 bg-red-600 text-white hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-600 transition-colors duration-200"
                    >
                      Clear search
                    </button>
                  </div>

                  <div
                    v-else-if="hasSearched"
                    class="divide-y divide-gray-200 dark:divide-zinc-700"
                  >
                    <!-- Events Results -->
                    <div
                      v-if="
                        (activeFilter === 'all' || activeFilter === 'events') &&
                        events.length > 0
                      "
                      class="p-4"
                    >
                      <div class="space-y-2">
                        <NuxtLink
                          v-for="event in events"
                          :key="event.id"
                          :to="`/events/${event.slug}`"
                          @click="closeDialog"
                          class="block p-3 hover:bg-gray-50 dark:hover:bg-zinc-700 transition-colors duration-200 group"
                        >
                          <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0">
                              <img
                                v-if="event.banner"
                                :src="`${runtimeConfig.public.baseUrl}storage/events/${event.cover_art}`"
                                :alt="event.title"
                                class="w-12 h-12 object-cover"
                              />
                              <div
                                v-else
                                class="w-12 h-12 bg-gray-200 dark:bg-zinc-600 flex items-center justify-center"
                              >
                                <Icon
                                  icon="mdi:calendar-event"
                                  class="w-6 h-6 text-gray-400 dark:text-zinc-400"
                                />
                              </div>
                            </div>
                            <div class="flex-1 min-w-0">
                              <p
                                class="text-sm font-medium text-gray-900 dark:text-zinc-100 group-hover:text-red-600 dark:group-hover:text-red-400 transition-colors"
                              >
                                {{ event.title }}
                              </p>
                              <div
                                class="text-sm text-gray-500 dark:text-zinc-400 truncate"
                                v-html="event.description"
                              ></div>
                              <div
                                class="flex items-center mt-1 text-xs text-gray-400 dark:text-zinc-500"
                              >
                                <Icon
                                  icon="mdi:map-marker"
                                  class="w-3 h-3 mr-1"
                                />
                                {{ event.location || "Location TBD" }}
                              </div>
                            </div>
                          </div>
                        </NuxtLink>
                      </div>
                    </div>

                    <!-- Vendors Results -->
                    <div
                      v-if="
                        (activeFilter === 'all' ||
                          activeFilter === 'vendors') &&
                        vendors.length > 0
                      "
                      class="p-4"
                    >
                      <div class="space-y-2">
                        <NuxtLink
                          v-for="vendor in vendors"
                          :key="vendor.id"
                          :to="`/vendors/${vendor.slug}`"
                          @click="closeDialog"
                          class="block p-3 hover:bg-gray-50 dark:hover:bg-zinc-700 transition-colors duration-200 group"
                        >
                          <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0">
                              <img
                                v-if="vendor.logo"
                                :src="`${runtimeConfig.public.baseUrl}storage/${vendor.logo}`"
                                :alt="vendor.name"
                                class="w-12 h-12 object-cover rounded-full"
                              />
                              <div
                                v-else
                                class="w-12 h-12 bg-gray-200 dark:bg-zinc-600 rounded-full flex items-center justify-center"
                              >
                                <Icon
                                  icon="mdi:store"
                                  class="w-6 h-6 text-gray-400 dark:text-zinc-400"
                                />
                              </div>
                            </div>
                            <div class="flex-1 min-w-0">
                              <p
                                class="text-sm font-medium text-gray-900 dark:text-zinc-100 group-hover:text-red-600 dark:group-hover:text-red-400 transition-colors"
                              >
                                {{ vendor.name }}
                              </p>
                              <p
                                class="text-sm text-gray-500 dark:text-zinc-400 truncate"
                              >
                                {{ vendor.bio || vendor.description }}
                              </p>
                              <div
                                class="flex items-center mt-1 text-xs text-gray-400 dark:text-zinc-500"
                              >
                                <Icon
                                  icon="mdi:map-marker"
                                  class="w-3 h-3 mr-1"
                                />
                                {{
                                  vendor.location || "Location not specified"
                                }}
                              </div>
                            </div>
                          </div>
                        </NuxtLink>
                      </div>
                    </div>

                    <!-- Venues Results -->
                    <div
                      v-if="
                        (activeFilter === 'all' || activeFilter === 'venues') &&
                        venues.length > 0
                      "
                      class="p-4"
                    >
                      <div class="space-y-2">
                        <NuxtLink
                          v-for="venue in venues"
                          :key="venue.id"
                          :to="`/venues/${venue.slug || venue.id}`"
                          @click="closeDialog"
                          class="block p-3 hover:bg-gray-50 dark:hover:bg-zinc-700 transition-colors duration-200 group"
                        >
                          <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0">
                              <img
                                v-if="venue.image"
                                :src="`${runtimeConfig.public.baseUrl}storage/${venue.image}`"
                                :alt="venue.name"
                                class="w-12 h-12 object-cover"
                              />
                              <div
                                v-else
                                class="w-12 h-12 bg-gray-200 dark:bg-zinc-600 flex items-center justify-center"
                              >
                                <Icon
                                  icon="mdi:map-marker-multiple"
                                  class="w-6 h-6 text-gray-400 dark:text-zinc-400"
                                />
                              </div>
                            </div>
                            <div class="flex-1 min-w-0">
                              <p
                                class="text-sm font-medium text-gray-900 dark:text-zinc-100 group-hover:text-red-600 dark:group-hover:text-red-400 transition-colors"
                              >
                                {{ venue.name }}
                              </p>
                              <p
                                class="text-sm text-gray-500 dark:text-zinc-400 truncate"
                              >
                                {{ venue.description }}
                              </p>
                              <div
                                class="flex items-center mt-1 text-xs text-gray-400 dark:text-zinc-500"
                              >
                                <Icon
                                  icon="mdi:map-marker"
                                  class="w-3 h-3 mr-1"
                                />
                                {{ venue.location || venue.address }}
                                <span
                                  v-if="venue.capacity"
                                  class="ml-3 flex items-center"
                                >
                                  <Icon
                                    icon="mdi:account-group"
                                    class="w-3 h-3 mr-1"
                                  />
                                  {{ venue.capacity }} capacity
                                </span>
                              </div>
                            </div>
                          </div>
                        </NuxtLink>
                      </div>
                    </div>
                  </div>

                  <!-- Quick Actions -->
                  <div v-if="!hasSearched && !loading" class="p-6 text-center">
                    <div class="mb-4">
                      <MagnifyingGlassIcon
                        class="h-12 w-12 text-gray-300 dark:text-zinc-600 mx-auto"
                      />
                    </div>
                    <h3
                      class="text-lg font-medium text-gray-900 dark:text-zinc-100 mb-2"
                    >
                      Search EventaHub
                    </h3>
                    <p class="text-gray-500 dark:text-zinc-400 mb-6">
                      Find events, vendors, and venues quickly
                    </p>
                  </div>
                </div>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
} from "@headlessui/vue";
import { MagnifyingGlassIcon, XMarkIcon } from "@heroicons/vue/24/outline";
import { ref, computed, nextTick, onMounted, onUnmounted } from "vue";
import { ENDPOINTS } from "@/utils/api";

interface EventItem {
  id: string | number;
  title: string;
  slug: string;
  description?: string;
  banner?: string;
  location?: string;
  [key: string]: any;
}

interface VendorItem {
  id: string | number;
  name: string;
  slug: string;
  bio?: string;
  description?: string;
  logo?: string;
  location?: string;
  [key: string]: any;
}

interface VenueItem {
  id: string | number;
  name: string;
  slug?: string;
  description?: string;
  image?: string;
  location?: string;
  address?: string;
  capacity?: number;
  [key: string]: any;
}

interface SearchFilter {
  key: string;
  label: string;
  icon: string;
}

const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();
const runtimeConfig = useRuntimeConfig();

const isOpen = ref<boolean>(false);
const loading = ref<boolean>(false);
const searchTerm = ref<string>("");
const events = ref<EventItem[]>([]);
const vendors = ref<VendorItem[]>([]);
const venues = ref<VenueItem[]>([]);
const hasSearched = ref<boolean>(false);
const activeFilter = ref<string>("all");
const searchInput = ref<HTMLInputElement>();

const searchFilters: SearchFilter[] = [
  { key: "all", label: "All", icon: "lets-icons:search-duotone" },
  { key: "events", label: "Events", icon: "stash:calendar" },
  { key: "vendors", label: "Vendors", icon: "gala:store" },
  { key: "venues", label: "Venues", icon: "fluent:building-people-24-regular" },
];

const hasResults = computed(() => {
  return (
    events.value.length > 0 ||
    vendors.value.length > 0 ||
    venues.value.length > 0
  );
});

const getResultCount = (filterKey: string): number => {
  switch (filterKey) {
    case "events":
      return events.value.length;
    case "vendors":
      return vendors.value.length;
    case "venues":
      return venues.value.length;
    case "all":
      return events.value.length + vendors.value.length + venues.value.length;
    default:
      return 0;
  }
};

const openDialog = async (): Promise<void> => {
  isOpen.value = true;
  await nextTick();
  searchInput.value?.focus();
};

const closeDialog = (): void => {
  isOpen.value = false;
  clearSearch();
};

const clearSearch = (): void => {
  searchTerm.value = "";
  events.value = [];
  vendors.value = [];
  venues.value = [];
  hasSearched.value = false;
  activeFilter.value = "all";
};

const handleSearchInput = (): void => {
  if (searchTerm.value.trim().length >= 2) {
    handleSearch();
  } else if (searchTerm.value.trim().length === 0) {
    clearSearch();
  }
};

const handleSearch = async (): Promise<void> => {
  if (!searchTerm.value.trim()) return;

  loading.value = true;
  hasSearched.value = true;

  try {
    const searchPromises = [];

    if (activeFilter.value === "all" || activeFilter.value === "events") {
      searchPromises.push(
        httpClient
          .get(
            `${ENDPOINTS.EVENTS.SEARCH}?title=${encodeURIComponent(
              searchTerm.value
            )}`
          )
          .then((response: any) => {
            events.value = response?.events?.data || [];
          })
          .catch(() => {
            events.value = [];
          })
      );
    }

    if (activeFilter.value === "all" || activeFilter.value === "vendors") {
      searchPromises.push(
        httpClient
          .get(
            `${ENDPOINTS.VENDORS.SEARCH}?name=${encodeURIComponent(
              searchTerm.value
            )}`
          )
          .then((response: any) => {
            vendors.value = response?.vendors?.data || response?.data || [];
          })
          .catch(() => {
            return httpClient
              .get(
                `${ENDPOINTS.VENDORS.GET_ALL}?search=${encodeURIComponent(
                  searchTerm.value
                )}`
              )
              .then((response: any) => {
                vendors.value = response?.vendors?.data || response?.data || [];
              })
              .catch(() => {
                vendors.value = [];
              });
          })
      );
    }

    if (activeFilter.value === "all" || activeFilter.value === "venues") {
      searchPromises.push(
        httpClient
          .get(
            `${ENDPOINTS.VENUES.SEARCH}?name=${encodeURIComponent(
              searchTerm.value
            )}`
          )
          .then((response: any) => {
            venues.value = response?.venues?.data || response?.data || [];
          })
          .catch(() => {
            return httpClient
              .get(
                `${ENDPOINTS.VENUES.GET_ALL}?search=${encodeURIComponent(
                  searchTerm.value
                )}`
              )
              .then((response: any) => {
                venues.value = response?.venues?.data || response?.data || [];
              })
              .catch(() => {
                venues.value = [];
              });
          })
      );
    }

    await Promise.all(searchPromises);
  } catch (error: any) {
    console.error("Search error:", error);
    $toast.error("An error occurred during search");
  } finally {
    loading.value = false;
  }
};

const handleKeydown = (event: KeyboardEvent): void => {
  if ((event.metaKey || event.ctrlKey) && event.key === "k") {
    event.preventDefault();
    openDialog();
  }
  if (event.key === "Escape" && isOpen.value) {
    closeDialog();
  }
};

onMounted(() => {
  document.addEventListener("keydown", handleKeydown);
});

onUnmounted(() => {
  document.removeEventListener("keydown", handleKeydown);
});
</script>
