<template>
    <button type="submit" :class="[
        'inline-flex items-center justify-center transition-colors duration-150 ease-in-out',
        sizeClasses[size],
        colorClasses[color],
        disabled ? 'opacity-50 cursor-not-allowed' : 'hover:opacity-90',
    ]" :disabled="props.disabled || props.loading">
        <CoreLoader color="white" height="20" width="20" class="mr-2" v-if="loading" />
        <p v-if="!loading">{{ text ?? "Submit" }}</p>
        <p v-else>Processing, please wait <span class="animate-pulse">...</span></p>
    </button>
</template>

<script setup lang="ts">
interface Props {
    color?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning'
    size?: 'sm' | 'md' | 'lg'
    loading?: boolean
    disabled?: boolean
    rounded?: boolean
    text?: string
}

const props = withDefaults(defineProps<Props>(), {
    color: 'primary',
    size: 'md',
    loading: false,
    disabled: false,
    rounded: false
})

const colorClasses = {
    primary: 'bg-red-600 text-white',
    secondary: 'bg-gray-600 text-white',
    success: 'bg-green-600 text-white',
    danger: 'bg-red-600 text-white',
    warning: 'bg-yellow-600 text-white'
}

const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
}
</script>