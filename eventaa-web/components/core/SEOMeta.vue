<template>
</template>

<script setup lang="ts">
interface SEOProps {
  title: string
  description: string
  keywords?: string
  ogImage?: string
  ogType?: string
  canonical?: string
  noIndex?: boolean
  noFollow?: boolean
}

const props = withDefaults(defineProps<SEOProps>(), {
  keywords: '',
  ogImage: '/icon.png',
  ogType: 'website',
  canonical: '',
  noIndex: false,
  noFollow: false
})

const route = useRoute()
const runtimeConfig = useRuntimeConfig()

const canonicalUrl = props.canonical || `${runtimeConfig.public.baseUrl || 'https://eventahub.com'}${route.path}`

const robotsContent = props.noIndex || props.noFollow
  ? `${props.noIndex ? 'noindex' : 'index'}, ${props.noFollow ? 'nofollow' : 'follow'}`
  : 'index, follow'

useHead({
  title: props.title,
  meta: [
    {
      name: 'description',
      content: props.description
    },
    ...(props.keywords ? [{
      name: 'keywords',
      content: props.keywords
    }] : []),
    {
      property: 'og:title',
      content: props.title
    },
    {
      property: 'og:description',
      content: props.description
    },
    {
      property: 'og:type',
      content: props.ogType
    },
    {
      property: 'og:url',
      content: canonicalUrl
    },
    {
      property: 'og:image',
      content: props.ogImage.startsWith('http') ? props.ogImage : `${runtimeConfig.public.baseUrl || 'https://eventahub.com'}${props.ogImage}`
    },
    {
      property: 'og:site_name',
      content: 'EventaHub Malawi'
    },
    {
      name: 'twitter:card',
      content: 'summary_large_image'
    },
    {
      name: 'twitter:title',
      content: props.title
    },
    {
      name: 'twitter:description',
      content: props.description
    },
    {
      name: 'twitter:image',
      content: props.ogImage.startsWith('http') ? props.ogImage : `${runtimeConfig.public.baseUrl || 'https://eventahub.com'}${props.ogImage}`
    },
    {
      name: 'robots',
      content: robotsContent
    }
  ],
  link: [
    {
      rel: 'canonical',
      href: canonicalUrl
    }
  ]
})
</script>
