<template>
  <div class="w-full flex flex-col shadow items-center justify-center">
    <div v-if="isValidDate" class="w-full grid grid-cols-4">
      <div class="bg-gray-100 border-gray-200 px-4 py-2 flex flex-col items-center">
        <span class="text-3xl font-semibold text-black">{{ formattedTime.days }}</span>
        <span class="text-base font-medium text-gray-500">Days</span>
      </div>
      <div class="bg-gray-100 border-l border-gray-200 px-4 py-2 flex flex-col items-center">
        <span class="text-3xl font-semibold text-black">{{ formattedTime.hours }}</span>
        <span class="text-base font-medium text-gray-500">Hours</span>
      </div>
      <div class="bg-gray-100 border-l border-gray-200 px-4 py-2 flex flex-col items-center">
        <span class="text-3xl font-semibold text-black">{{ formattedTime.minutes }}</span>
        <span class="text-base font-medium text-gray-500">Minutes</span>
      </div>
      <div class="bg-gray-100 border-l border-gray-200 px-4 py-2 flex flex-col items-center">
        <span class="text-3xl font-semibold text-black">{{ formattedTime.seconds }}</span>
        <span class="text-base font-medium text-gray-500">Seconds</span>
      </div>
    </div>
    <div v-else class="text-red-600 font-medium">
      Invalid date provided
    </div>
  </div>
</template>

<script setup lang="ts">
const INITIAL_TIME: number = 10 * 24 * 60 * 60;

interface TimerState {
  timeRemaining: number;
  isFinished: boolean;
  timerInterval: number | null;
}

interface FormattedTime {
  days: string;
  hours: string;
  minutes: string;
  seconds: string;
}

const props = defineProps({
  startDate: {
    type: [Date, String],
    required: true,
  },
});

const parsedStartDate = computed(() => {
  try {
    const date = props.startDate instanceof Date 
      ? props.startDate 
      : new Date(props.startDate);
    
    return isNaN(date.getTime()) ? null : date;
  } catch {
    return null;
  }
});

const isValidDate = computed(() => {
  return parsedStartDate.value !== null;
});

const getInitialTime = computed((): number => {
  if (!parsedStartDate.value) return 0;
  
  const now = new Date();
  const timeDifference = Math.floor((parsedStartDate.value.getTime() - now.getTime()) / 1000);
  
  if (timeDifference < 0) {
    return 0;
  }
  return Math.min(timeDifference, INITIAL_TIME);
});

const state = ref<TimerState>({
  timeRemaining: getInitialTime.value,
  isFinished: false,
  timerInterval: null,
});

const startTimer = (): void => {
  if (!isValidDate.value) return;
  
  if (state.value.timerInterval === null) {
    state.value.timeRemaining = getInitialTime.value;
    
    state.value.timerInterval = setInterval(() => {
      if (state.value.timeRemaining > 0) {
        state.value.timeRemaining--;
      } else {
        stopTimer();
        state.value.isFinished = true;
      }
    }, 1000) as unknown as number;
  }
};

const stopTimer = (): void => {
  if (state.value.timerInterval !== null) {
    clearInterval(state.value.timerInterval);
    state.value.timerInterval = null;
  }
};

const formattedTime = computed<FormattedTime>(() => {
  const time = state.value.timeRemaining;
  const days = Math.floor(time / (24 * 60 * 60));
  const hours = Math.floor((time % (24 * 60 * 60)) / (60 * 60));
  const minutes = Math.floor((time % (60 * 60)) / 60);
  const seconds = time % 60;

  return {
    days: days.toString().padStart(2, "0"),
    hours: hours.toString().padStart(2, "0"),
    minutes: minutes.toString().padStart(2, "0"),
    seconds: seconds.toString().padStart(2, "0"),
  };
});

watch(() => props.startDate, () => {
  stopTimer();
  state.value.timeRemaining = getInitialTime.value;
  startTimer();
});

onMounted(() => {
  startTimer();
});

onUnmounted(() => {
  stopTimer();
});
</script>