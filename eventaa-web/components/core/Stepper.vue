<script setup lang="ts">
import { defineProps } from 'vue';

interface Step {
    step: number;
    label: string;
    sublabel?: string;
}

const props = defineProps<{
    steps: Step[];
    currentStep: number;
    progressPercentage: number;
}>();

const showConnector = (index: number) => {
    return index < props.steps.length - 1;
};

const getStepStatus = (stepNumber: number) => {
    if (stepNumber < props.currentStep) {
        return 'completed';
    } else if (stepNumber === props.currentStep) {
        return 'current';
    } else {
        return 'upcoming';
    }
};
</script>

<template>
    <div class="stepper-container">
        <ol class="flex items-center w-full space-x-2 text-sm font-medium text-center bg-white dark:bg-black rounded-none shadow-sm sm:text-base sm:p-4 sm:space-x-4 rtl:space-x-reverse">
            <li v-for="(step, index) in steps" :key="step.step"
                class="flex items-center space-x-5"
                :class="{
                    'text-green-600 dark:text-green-400': getStepStatus(step.step) === 'completed',
                    'text-red-500 dark:text-red-400': getStepStatus(step.step) === 'current',
                    'text-gray-500 dark:text-zinc-400': getStepStatus(step.step) === 'upcoming'
                }">
                <span class="flex items-center justify-center w-6 h-6 me-2 text-xs border rounded-full shrink-0"
                      :class="{
                          'border-green-600 bg-green-600 dark:border-green-500 dark:bg-green-600': getStepStatus(step.step) === 'completed',
                          'border-red-500 bg-red-50 dark:border-red-400 dark:bg-zinc-800': getStepStatus(step.step) === 'current',
                          'border-gray-500 dark:border-zinc-600': getStepStatus(step.step) === 'upcoming'
                      }">
                    <svg v-if="getStepStatus(step.step) === 'completed'" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>

                    <svg v-else-if="getStepStatus(step.step) === 'current'" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 dark:text-red-400 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clip-rule="evenodd" />
                    </svg>

                    <template v-else>
                        <span class="dark:text-zinc-400">{{ step.step }}</span>
                    </template>
                </span>

                <span class="font-medium">{{ step.label }}</span>

                <svg v-if="showConnector(index)" class="w-3 h-3 ms-2 sm:ms-4 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 12 10">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m7 9 4-4-4-4M1 9l4-4-4-4"/>
                </svg>
            </li>
        </ol>
    </div>
</template>

<style scoped>
.stepper-container {
    width: 100%;
    padding: 1rem 0;
}

.progress-container {
    padding: 0 0.5rem;
}

ol {
    position: relative;
    z-index: 1;
}

@media (max-width: 640px) {
    .stepper-container {
        overflow-x: auto;
        padding-bottom: 1rem;
    }

    ol {
        min-width: 500px;
    }
}
</style>