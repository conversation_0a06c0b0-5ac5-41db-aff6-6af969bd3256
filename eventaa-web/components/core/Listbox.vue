<template>
  <div class="sm:w-44">
    <Listbox v-model="internalSelected">
      <div class="relative mt-1">
        <ListboxButton
          class="relative w-full cursor-default rounded-lg bg-white py-2 pl-3 pr-10 text-left focus:outline-none focus-visible:border-indigo-500 focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-orange-300 sm:text-sm"
        >
          <span class="truncate text-black flex items-center">
            <div class="w-full flex items-center space-x-2">
                        <img :src="`${runtimeConfig.public.baseUrl}storage/categories/${typeof modelValue == 'object' ? modelValue?.icon : 'other.png'}`" alt="Image" class="h-6 w-6 mr-2" />
                    </div>
            {{ typeof selectedItem === 'object' ? selectedItem.name : selectedItem }}
          </span>
          <span
            class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
          >
            <ChevronUpDownIcon
              class="h-5 w-5 text-gray-400"
              aria-hidden="true"
            />
          </span>
        </ListboxButton>

        <transition
          leave-active-class="transition duration-100 ease-in"
          leave-from-class="opacity-100"
          leave-to-class="opacity-0"
        >
          <ListboxOptions
            class="absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"
          >
            <ListboxOption
              v-slot="{ active, selected }"
              v-for="item in items"
              :key="typeof item === 'object' ? item.name : item"
              :value="item"
              as="template"
            >
              <li
                :class="[
                  active ? 'bg-amber-100 text-amber-900' : 'text-gray-900',
                  'relative cursor-default select-none py-2 pl-10 pr-4',
                ]"
              >
                <span
                  :class="[
                    selected ? 'font-medium' : 'font-normal',
                    'block truncate',
                  ]"
                  class="flex items-center"
                >
                  <template v-if="typeof item === 'object'">
                    <span v-if="item.icon" class="mr-2">
                      <Icon :icon="item.icon" class="w-5 h-5 mr-2" />
                    </span>
                    {{ item.name }}
                  </template>
                  <template v-else>{{ item }}</template>
                </span>
                <span
                  v-if="selected"
                  class="absolute inset-y-0 left-0 flex items-center pl-3 text-amber-600"
                >
                  <CheckIcon class="h-5 w-5" aria-hidden="true" />
                </span>
              </li>
            </ListboxOption>
          </ListboxOptions>
        </transition>
      </div>
    </Listbox>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, defineEmits } from "vue";
import {
  Listbox,
  ListboxButton,
  ListboxOptions,
  ListboxOption,
} from "@headlessui/vue";
import { CheckIcon, ChevronUpDownIcon } from "@heroicons/vue/20/solid";

const runtimeConfig = useRuntimeConfig();
const props = defineProps<{
  items: Array<string | { name: string; icon?: any }>;
  modelValue: string | { name: string; icon?: any };
}>();

const emit = defineEmits<{
  (e: "update:modelValue", value: string | { name: string; icon?: any }): void;
}>();

const internalSelected = ref(props.modelValue);

watch(internalSelected, (newValue) => {
  emit("update:modelValue", newValue);
});

const selectedItem = computed(() =>
  props.items.find(
    (item) =>
      (typeof item === "object" ? item.name : item) ===
      (typeof internalSelected.value === "object"
        ? internalSelected.value.name
        : internalSelected.value)
  )
);
</script>

<style lang="scss" scoped></style>
