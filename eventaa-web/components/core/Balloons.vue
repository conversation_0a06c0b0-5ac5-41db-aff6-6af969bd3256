<template>
    <div id="balloon-container"
        class="absolute inset-0 flex flex-wrap overflow-hidden transition-opacity duration-500"
        :class="{ 'opacity-0': !isVisible, 'opacity-100': isVisible }">
        <template v-for="(balloon, index) in balloons" :key="index">
            <div class="balloon h-32 w-28 rounded-[75%_75%_70%_70%] relative" :style="{
    backgroundColor: `rgba(${balloon.r}, ${balloon.g}, ${balloon.b}, 0.7)`,
    color: `rgba(${balloon.r}, ${balloon.g}, ${balloon.b}, 0.7)`,
    boxShadow: `inset -7px -3px 10px rgba(${balloon.r - 10}, ${balloon.g - 10}, ${balloon.b - 10}, 0.7)`,
    margin: `${balloon.mt}px 0 0 ${balloon.ml}px`,
    '--dur': `${balloon.dur}s`
}">
            </div>
        </template>
    </div>
</template>

<script setup>
import { ref } from 'vue';

const balloons = ref([]);
const isVisible = ref(true);

const random = (num) => Math.floor(Math.random() * num);

const getRandomStyles = () => ({
    r: random(255),
    g: random(255),
    b: random(255),
    mt: random(200),
    ml: random(50),
    dur: random(5) + 5
});

const createBalloons = (num) => {
    isVisible.value = true;
    balloons.value = Array.from({ length: num }, () => getRandomStyles());
};

onMounted(() => {
    createBalloons(10)
})
</script>

<style scoped>
.balloon {
    animation: float var(--dur) ease-in-out forwards;
}

.balloon::before {
    content: "";
    @apply h-[75px] w-[3px] bg-[#FDFD96] absolute top-32 left-0 right-0 mx-auto;
}

.balloon::after {
    content: "▲";
    @apply absolute text-center top-[120px] left-0 right-0 mx-auto;
}

@keyframes float {
    from {
        transform: translateY(0);
        opacity: 1;
    }
    to {
        transform: translateY(-200vh);
        opacity: 0;
    }
}
</style>
