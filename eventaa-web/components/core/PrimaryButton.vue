<template>
  <button :class="[
    'flex items-center justify-center transition-all duration-150',
    sizeClasses[size],
    variant === 'outlined' ? outlinedClasses : filledClasses,
    centered ? 'text-center' : 'text-left',
    colorClasses[color],
    fullWidth ? 'w-full' : '',
    rounded ? 'rounded-md' : '',
    disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
  ]" :disabled="disabled || loading" type="button">
    <CoreLoader v-if="loading" color="white" height="20" width="20" class="mr-2" />
    <Icon v-else-if="startIcon" :icon="startIcon" class="mr-2" />
    {{ loading ? 'Processing, please wait...' : text }}
    <Icon v-if="endIcon" :icon="endIcon" class="ml-2" />
  </button>
</template>

<script lang="ts" setup>
const props = defineProps({
  text: {
    type: String,
    required: true,
    default: "",
  },
  color: {
    type: String as () => keyof typeof colorClasses,
    default: "primary",
    validator: (value: string) =>
      ["primary", "success", "warning", "error", "neutral"].includes(value),
  },
  variant: {
    type: String,
    default: "filled",
    validator: (value: string) => ["filled", "outlined"].includes(value),
  },
  size: {
    type: String as () => keyof typeof sizeClasses,
    default: "medium",
    validator: (value: string) => ["small", "medium", "large"].includes(value),
  },
  startIcon: {
    type: String,
    default: null,
  },
  endIcon: {
    type: String,
    default: null,
  },
  centered: {
    type: Boolean,
    default: true,
  },
  fullWidth: {
    type: Boolean,
    default: false,
  },
  rounded: {
    type: Boolean,
    default: false,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  }
});

const sizeClasses = {
  small: "px-2 py-1 text-sm",
  medium: "px-4 py-2",
  large: "px-6 py-3 text-lg",
};

const colorClasses = {
  primary: "text-white bg-red-600 hover:bg-red-700 border-red-600",
  success: "text-white bg-green-600 hover:bg-green-700 border-green-600",
  warning: "text-white bg-orange-600 hover:bg-orange-700 border-orange-600",
  error: "text-white bg-red-700 hover:bg-red-800 border-red-700",
  neutral: "text-gray-700 bg-gray-200 hover:bg-gray-300 border-gray-200",
};

const outlinedColorClasses = {
  primary: "text-red-600 border-red-600 hover:bg-red-50",
  success: "text-green-600 border-green-600 hover:bg-green-50",
  warning: "text-orange-600 border-orange-600 hover:bg-orange-50",
  error: "text-red-700 border-red-700 hover:bg-red-50",
  neutral: "text-gray-700 border-gray-300 hover:bg-gray-50",
};

const filledClasses = computed(() =>
  props.color === "neutral" ? "" : "text-white"
);

const outlinedClasses = computed(() =>
  `bg-transparent border-2 ${outlinedColorClasses[props.color as keyof typeof outlinedColorClasses]}`
);
</script>

<style scoped></style>