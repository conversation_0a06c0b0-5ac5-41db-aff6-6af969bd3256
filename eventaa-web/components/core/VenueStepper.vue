<script setup lang="ts">
import { useRouter } from 'vue-router';
import { Dialog, DialogOverlay, DialogTitle } from '@headlessui/vue';
import { Cropper } from 'vue-advanced-cropper';
import type { Cropper as CropperInstance } from 'vue-advanced-cropper';
import 'vue-advanced-cropper/dist/style.css';
import type { Category, VenueForm } from '@/types';

const props = defineProps({
    venue: {
        type: Object as PropType<VenueForm>,
        required: true
    },
    endpoint: {
        type: String,
        required: true
    }
});

const formData = reactive<VenueForm>({
    ...props.venue,
    prices: props.venue.prices.map(price => ({
        ...price,
        attributes: Array.isArray(price.attributes) ? price.attributes as string[] : []
    }))
});

const currentStep = ref<number>(1);
const isLoading = ref<boolean>(false);
const router = useRouter();
const formErrors = ref<Record<string, string>>({});
const runtimeConfig = useRuntimeConfig();
const { $toast, $categories }: any = useNuxtApp();
const httpClient = useHttpClient();
const uploading = ref<boolean>(false)
const uploadingFiles = ref<{ name: string; progress: number }[]>([])
const cropDialogOpen = ref<boolean>(false)
const cropperImage = ref<string>('');
const cropTarget = ref<{ type: 'logo' | 'images'; index?: number }>({ type: 'logo' })
const logoInput = ref<HTMLInputElement | null>(null)
const imagesInput = ref<HTMLInputElement | null>(null)

const cropperRef = ref<typeof CropperInstance | null>(null);

const logoPreview = computed(() => {
    if (!formData.logo) return ''
    if (typeof formData.logo === 'string') return formData.logo
    return URL.createObjectURL(formData.logo)
})

function triggerFileInput(inputRef: 'logoInput' | 'imagesInput') {
    if (inputRef === 'logoInput' && logoInput.value) {
        logoInput.value.click()
    } else if (inputRef === 'imagesInput' && imagesInput.value) {
        imagesInput.value.click()
    }
}

function handleFileUpload(event: Event, type: 'logo' | 'images' | 'videos') {
    const input = event.target as HTMLInputElement
    if (!input.files || input.files.length === 0) return

    if (type === 'logo') {
        const file = input.files[0]
        if (validateFile(file)) {
            updateFormData({ logo: file })
            simulateUpload([file])
        }
    } else {
        const newFiles = Array.from(input.files).filter(validateFile)
        if (newFiles.length > 0) {
            updateFormData({ images: [...formData.images, ...newFiles] })
            simulateUpload(newFiles)
        }
    }
    input.value = ''
}

function handleDrop(event: DragEvent, type: 'logo' | 'images') {
    const files = event.dataTransfer?.files
    if (!files || files.length === 0) return

    if (type === 'logo') {
        const file = files[0]
        if (validateFile(file)) {
            updateFormData({ logo: file })
            simulateUpload([file])
        }
    } else {
        const newFiles = Array.from(files).filter(validateFile)
        if (newFiles.length > 0) {
            updateFormData({ images: [...formData.images, ...newFiles] })
            simulateUpload(newFiles)
        }
    }
}

function validateFile(file: File): boolean {
    const validTypes = ['image/jpeg', 'image/png', 'image/gif']
    if (!validTypes.includes(file.type)) {
        $toast.info('Invalid file type. Please upload an image (JPG, PNG, GIF).')
        return false
    }

    if (file.size > 4 * 1024 * 1024) {
        $toast.info('File too large. Maximum size is 4MB.')
        return false
    }

    return true
}

function removeFile(type: 'logo' | 'images' | 'videos', index?: number): void {
    if (type === 'logo') {
        updateFormData({ logo: null })
    } else if (index !== undefined) {
        const newImages = [...formData.images]
        newImages.splice(index, 1)
        updateFormData({ images: newImages })
    }
}

function updateFormData(newData: Partial<typeof formData>): void {
    Object.assign(formData, newData)
}

function getImagePreview(file: File | string): string {
    if (typeof file === 'string') return file
    return URL.createObjectURL(file)
}

function simulateUpload(files: File[]): void {
    uploading.value = true
    const newUploadingFiles = files.map(file => ({
        name: file.name,
        progress: 0
    }))

    uploadingFiles.value = [...uploadingFiles.value, ...newUploadingFiles]

    files.forEach((file, fileIndex) => {
        const index = uploadingFiles.value.findIndex(f => f.name === file.name)

        const interval = setInterval(() => {
            uploadingFiles.value[index].progress += Math.floor(Math.random() * 10) + 5

            if (uploadingFiles.value[index].progress >= 100) {
                uploadingFiles.value[index].progress = 100
                clearInterval(interval)

                if (uploadingFiles.value.every(f => f.progress === 100)) {
                    setTimeout(() => {
                        uploading.value = false
                        uploadingFiles.value = []
                    }, 500)
                }
            }
        }, 200)
    })
}

function openCropDialog(type: 'logo' | 'images', index?: number) {
    cropTarget.value = { type, index }

    let imageFile: File | null = null
    if (type === 'logo') {
        imageFile = formData.logo
    } else if (type === 'images' && index !== undefined) {
        imageFile = formData.images[index]
    }

    if (imageFile) {
        cropperImage.value = typeof imageFile == "string" ? imageFile : URL.createObjectURL(imageFile)
        cropDialogOpen.value = true
    }
}

function closeCropDialog() {
    cropDialogOpen.value = false
    cropperImage.value = ''
}

async function cropImage(): Promise<void> {
    if (!cropperRef.value) return
    const { canvas } = cropperRef.value.getResult()

    const blob = await new Promise<Blob>((resolve) => {
        canvas.toBlob((blob: Blob | PromiseLike<Blob>) => {
            if (blob) resolve(blob)
        }, 'image/jpeg', 0.9)
    })

    const fileName = cropTarget.value.type === 'logo'
        ? formData.logo?.name || 'cropped-logo.jpg'
        : formData.images[cropTarget.value.index || 0]?.name || 'cropped-image.jpg'
    const croppedFile = new File([blob], fileName, { type: 'image/jpeg' })

    if (cropTarget.value.type === 'logo') {
        updateFormData({ logo: croppedFile })
    } else if (cropTarget.value.index !== undefined) {
        const newImages = [...formData.images]
        newImages[cropTarget.value.index] = croppedFile
        updateFormData({ images: newImages })
    }

    closeCropDialog()
}


const steps: { step: number, label: string }[] = [
    { step: 1, label: 'Basic Info' },
    { step: 2, label: 'Pricing' },
    { step: 3, label: 'Location' },
    { step: 4, label: 'Media' },
    { step: 5, label: 'Confirm' }
];

const stepValidations: Record<number, string[]> = {
    1: ['name', 'phone', 'email', 'description', 'capacity'],
    2: ['amount'],
    3: ['address', 'city', 'state', 'zip', 'country', 'latitude', 'longitude'],
    4: ['logo'],
    5: []
};

const currencyOptions = ref<{ id: number; name: string; }[]>([])

const fetchCurrencies = async () => {
    try {
        const response: any = await httpClient.get(ENDPOINTS.CURRENCIES.BASE)
        if (response && response.currencies) {
            currencyOptions.value = response.currencies.map((currency: { name: string; id: number; }) => ({
                name: currency.name,
                id: currency.id
            }))
        }
    } catch (error) {
        console.error('Error fetching currencies:', error)
    }
}

onMounted(() => {
    fetchCurrencies();
});

const isStepValid = (step: number): boolean => {
    const requiredFields = stepValidations[step as keyof typeof stepValidations];
    if (step === 2) {
        if (formData.prices.length === 0) {
            formErrors.value['prices'] = 'At least one price item is required';
            $toast.warning('At least one price item is required');
            return false;
        }

        return formData.prices.every((price, index) => {
            let isValid = true;
            if (!price.amount || price.amount === '0') {
                formErrors.value[`prices[${index}].amount`] = 'Amount is required and must be greater than 0';
                $toast.warning('Amount is required and must be greater than 0');
                isValid = false;
            }
            if (!price.currency) {
                formErrors.value[`prices[${index}].currency`] = 'Currency is required';
                $toast.warning('Currency is required');
                isValid = false;
            }
            return isValid;
        });
    }

    return requiredFields.every(field => {
        const value = formData[field as keyof VenueForm];
        if (value === null || value === '' || (Array.isArray(value) && value.length === 0)) {
            formErrors.value[field] = `${field} is required`;
            return false;
        }
        return true;
    });
};

const nextStep = (): void => {
    if (isStepValid(currentStep.value)) {
        if (currentStep.value < steps.length) {
            currentStep.value++;
            window.scrollTo(0, 0);
        }
        console.log("step", currentStep.value)
    }
};

const onSubmitClick = () => {
    currentStep.value === steps.length ? submitForm() : nextStep()
}

const prevStep = (): void => {
    if (currentStep.value > 1) {
        currentStep.value--;
        window.scrollTo(0, 0);
    }
};

const onLocationChange = (location: { lat: number; lng: number }): void => {
    formData.latitude = location.lat;
    formData.longitude = location.lng;
}

const onAddressChange = (address: string): void => {
    console.info(address)
}

const onSave = (): void => {
    console.info("On save triggered")
}

const submitForm = async (): Promise<void> => {
    isLoading.value = true;

    try {
        const submitData = new FormData();
        for (const [key, value] of Object.entries(formData)) {
            if (key !== 'logo' && key !== 'images' && key !== 'videos' && key !== 'prices' && key !== 'activities' && value !== null) {
                submitData.append(key, value.toString());
            }
        }

        if (formData.prices) {
            submitData.append('prices', JSON.stringify(formData.prices));
        }

        if (formData.activities) {
            const selectedIds = $categories.value
                .filter((category: Category) => formData.activities.includes(category.name))
                .map((category: Category) => category.id);
            console.log(selectedIds);
            submitData.append('activities', JSON.stringify(selectedIds));
        }

        if (formData.logo && formData.logo instanceof File) {
            submitData.append('logo', formData.logo);
        }

        if (formData.images && Array.isArray(formData.images)) {
            formData.images.forEach((image, index) => {
                if (image instanceof File) {
                    submitData.append(`images[${index}]`, image);
                }
            });
        }

        if (formData.videos && Array.isArray(formData.videos)) {
            formData.videos.forEach((video, index) => {
                if (video instanceof File) {
                    submitData.append(`videos[${index}]`, video);
                }
            });
        }

        const response: any = await httpClient.post(props.endpoint, submitData);

        if (response) {
            $toast.success(response.message);
            setTimeout(() => {
                router.push("/dashboard/venues");
            }, 2500)
        }
    } catch (error: any) {
        if (error.response && error.response.status === 422) {
            formErrors.value = error.response.data.errors;
        } else {
            console.error('Error creating venue:', error);
            $toast.error('An error occurred while creating the venue.');
        }
    } finally {
        isLoading.value = false;
    }
};

const progressPercentage = computed((): number => {
    return ((currentStep.value - 1) / (steps.length - 1)) * 100;
});

const attributeInput = ref<string[]>([])

function addPrice() {
    formData.prices.push({
        name: '',
        amount: '0',
        currency: '',
        attributes: [],
    })
    attributeInput.value.push('')
}

function removePrice(index: number) {
    formData.prices.splice(index, 1)
    attributeInput.value.splice(index, 1)
}

function addAttribute(priceIndex: number) {
    const attrValue = attributeInput.value[priceIndex]
    if (attrValue.trim() !== '') {
        formData.prices[priceIndex].attributes.push(attrValue)
        attributeInput.value[priceIndex] = ''
    }
}

function removeAttribute(priceIndex: number, attrIndex: number) {
    formData.prices[priceIndex].attributes.splice(attrIndex, 1)
}

function editAttribute(priceIndex: string | number, attrIndex: string | number) {
    const newValue = prompt('Edit attribute', formData.prices[priceIndex as number].attributes[attrIndex as any])
    if (newValue !== null && newValue.trim() !== '') {
        (formData.prices[priceIndex as number].attributes as string[])[attrIndex as any] = newValue
    }
}
</script>

<template>
    <div class="w-full p-10">
        <CoreStepper :steps="steps" :current-step="currentStep" :progress-percentage="progressPercentage" />
        <div class="form-container mt-2">
            <FormKit type="form" @submit="onSubmitClick" :actions="false">
                <div v-if="currentStep === 1" class="step-container flex flex-col space-y-2.5">
                    <h2 class="text-2xl font-bold mb-6 dark:text-zinc-50">Basic Information</h2>

                    <FormKit type="text" name="name" label="Name" validation="required" v-model="formData.name"
                        :validation-messages="{ required: 'Venue name is required' }" />

                    <FormKit type="tel" name="phone" label="Phone Number" validation="required" v-model="formData.phone"
                        :validation-messages="{ required: 'Phone number is required' }" prefixIcon="phone" />

                    <FormKit type="email" name="email" label="Email Address" validation="required|email"
                        v-model="formData.email" :validation-messages="{
                            required: 'Email is required',
                            email: 'Please enter a valid email address'
                        }" prefixIcon="email" />

                    <FormKit type="url" name="website" label="Website URL" validation="url"
                        v-model="formData.website as any" :validation-messages="{ url: 'Please enter a valid URL' }" />

                    <FormKit type="textarea" name="description" label="Description" validation="required"
                        v-model="formData.description" :validation-messages="{ required: 'Description is required' }" />

                    <FormKit type="number" name="capacity" label="Capacity"
                        help="How many people does it accomodate per event" validation="required|number"
                        v-model="formData.capacity as any" :validation-messages="{
                            required: 'Capacity is required',
                            number: 'Capacity must be a number'
                        }" />

                    <div class="flex flex-col space-y-2">
                        <label class="text-base font-medium">Activities</label>
                        <MultiSelect v-model="formData.activities" mode="tags"
                            :options="$categories.map((category: Category) => category.name)" />
                    </div>
                </div>

                <div v-if="currentStep === 2" class="step-container">
                    <h2 class="text-2xl font-bold mb-6 dark:text-zinc-100">Pricing</h2>

                    <div v-for="(price, index) in formData.prices" :key="index" class="mb-6 space-y-2 border p-4">
                        <FormKit type="number" label="Amount" v-model="price.amount" required />
                        <div>
                            <label class="mb-2 text-base font-medium">Currency</label>
                            <select v-model="price.currency" class="px-2 py-2 w-full mt-2">
                                <option disabled value="">Select Currency</option>
                                <option v-for="currency in currencyOptions" :key="currency?.id" :value="currency.id">
                                    {{ currency.name }}
                                </option>
                            </select>
                        </div>

                        <div class="mb-4 relative">
                            <label class="font-medium text-base mb-2 block">Attributes</label>
                            <div class="relative flex items-center gap-2 mb-2">
                                <FormKit type="text" v-model="attributeInput[index]" placeholder="Add attribute" />
                                <CorePrimaryButton @click="addAttribute(index)" text="Add" start-icon="mdi:plus" />
                            </div>

                            <div v-if="price.attributes.length > 0" class="flex flex-wrap gap-2">
                                <div v-for="(attr, attrIndex) in price.attributes" :key="attrIndex"
                                    class="flex items-center gap-1 bg-gray-100 text-gray-600 rounded-full px-3 py-1 relative group">
                                    <span>{{ attr }}</span>

                                    <div
                                        class="gap-4 p-2 rounded-full absolute right-0 bg-gray-300 hidden group-hover:flex">
                                        <button type="button" @click="editAttribute(index, attrIndex)">
                                            <Icon icon="mdi:pencil" class="w-5 h-5" />
                                        </button>
                                        <button type="button" @click="removeAttribute(index, attrIndex)">
                                            <Icon icon="mdi:trash-can" class="w-5 h-5" />
                                        </button>
                                    </div>
                                </div>
                            </div>

                        </div>

                        <button type="button" @click="removePrice(index)"
                            class="flex items-center text-red-600 px-3 py-1 font-medium">
                            <Icon icon="gala:remove" class="w-5 h-5 mr-2" />Remove price item
                        </button>
                    </div>

                    <CorePrimaryButton start-icon="entypo:price-tag" text="Add price item" @click="addPrice"
                        color="success" />
                </div>

                <div v-if="currentStep === 3" class="step-container">
                    <h2 class="text-2xl font-bold mb-6 dark:text-zinc-100">Location</h2>

                    <FormKit type="text" name="address" label="Street Address" validation="required"
                        v-model="formData.address" :validation-messages="{ required: 'Street address is required' }" />

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                        <FormKit type="text" name="city" label="City" validation="required" v-model="formData.city"
                            :validation-messages="{ required: 'City is required' }" />

                        <FormKit type="text" name="state" label="State/Province/Region" validation="required"
                            v-model="formData.state" :validation-messages="{ required: 'State is required' }" />
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                        <FormKit type="text" name="zip" label="Postal/ZIP Code" validation="required"
                            v-model="formData.zip" :validation-messages="{ required: 'ZIP code is required' }" />

                        <FormKit type="text" name="country" label="Country" validation="required"
                            v-model="formData.country" :validation-messages="{ required: 'Country is required' }" />
                    </div>

                    <CoreMapPicker :api-key="runtimeConfig.public.googleMapsApiKey" @update:location="onLocationChange"
                        @update:onSave="onSave" @update:address="onAddressChange"
                        :default-location="{ lat: Number(venue.latitude), lng: Number(venue.longitude) }" />
                </div>

                <div v-if="currentStep === 4" class="step-container">
                    <h2 class="text-2xl font-bold mb-6">Venue Media</h2>

                    <div class="mb-8">
                        <label class="block mb-2 font-medium">Logo (Required)<span class="text-sky-500">*</span></label>
                        <div class="border-2 border-dashed border-gray-300 p-6 text-center cursor-pointer hover:bg-gray-50 transition-colors"
                            @dragover.prevent @drop.prevent="handleDrop($event, 'logo')"
                            @click="triggerFileInput('logoInput')">
                            <div v-if="!formData.logo" class="space-y-2">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-400"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                                <p class="text-gray-500">Drag and drop your logo here or click to browse</p>
                                <p class="text-sm text-gray-400">PNG, JPG or GIF (max 4MB)</p>
                            </div>
                            <div v-else class="flex flex-col items-center">
                                <img :src="logoPreview" class="h-40 object-contain mb-2 rounded" alt="Logo preview" />
                                <div class="flex items-center mt-2">
                                    <button @click.stop="openCropDialog('logo')" type="button"
                                        class="text-blue-500 mr-3 text-sm">
                                        Edit
                                    </button>
                                    <button @click.stop="removeFile('logo')" type="button" class="text-red-500 text-sm">
                                        Remove
                                    </button>
                                </div>
                            </div>
                        </div>
                        <input ref="logoInput" type="file" accept="image/*" @change="(e) => handleFileUpload(e, 'logo')"
                            class="hidden" />
                    </div>

                    <div class="mb-6">
                        <label class="block mb-2 font-medium">Images</label>
                        <div class="border-2 border-dashed border-gray-300 p-6 text-center cursor-pointer hover:bg-gray-50 transition-colors"
                            @dragover.prevent @drop.prevent="handleDrop($event, 'images')"
                            @click="triggerFileInput('imagesInput')">
                            <div v-if="formData.images.length === 0" class="space-y-2">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-400"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4-4m0 0l-4 4m4-4v12" />
                                </svg>
                                <p class="text-gray-500">Drag and drop multiple images or click to browse</p>
                                <p class="text-sm text-gray-400">PNG, JPG or GIF (max 4MB each)</p>
                            </div>
                            <div v-else>
                                <p class="mb-3 text-sm text-gray-500">{{ formData.images.length }} image(s) selected</p>
                                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                                    <div v-for="(image, index) in formData.images" :key="index" class="relative group">
                                        <img :src="getImagePreview(image)" class="h-24 w-full object-cover"
                                            alt="Image preview" />
                                        <div
                                            class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-opacity flex items-center justify-center opacity-0 group-hover:opacity-100">
                                            <button @click.stop="openCropDialog('images', index)" type="button"
                                                class="text-white mr-2">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5"
                                                    viewBox="0 0 20 20" fill="currentColor">
                                                    <path
                                                        d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                                                </svg>
                                            </button>
                                            <button @click.stop="removeFile('images', index)" type="button"
                                                class="text-white">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5"
                                                    viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd"
                                                        d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                                                        clip-rule="evenodd" />
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <input ref="imagesInput" type="file" accept="image/*" multiple
                            @change="(e) => handleFileUpload(e, 'images')" class="hidden" />
                    </div>

                    <div v-if="uploading" class="mb-6">
                        <div v-for="(file, index) in uploadingFiles" :key="index" class="mb-2">
                            <div class="flex items-center justify-between mb-1">
                                <span class="text-sm truncate max-w-xs">{{ file.name }}</span>
                                <span class="text-sm">{{ file.progress }}%</span>
                            </div>
                            <div class="w-full bg-gray-200 h-2">
                                <div class="bg-gradient-to-r from-red-600 to-pink-600 h-2"
                                    :style="{ width: file.progress + '%' }"></div>
                            </div>
                        </div>
                    </div>

                    <Teleport to="body">
                        <Dialog :open="cropDialogOpen" @close="closeCropDialog"
                            class="fixed inset-0 z-50 overflow-y-auto">
                            <div class="flex items-center justify-center min-h-screen">
                                <DialogOverlay class="fixed inset-0 bg-black opacity-30" />

                                <div class="relative bg-white rounded-lg max-w-xl w-full mx-4 p-6 shadow-xl">
                                    <DialogTitle class="text-xl font-medium mb-4">Crop Image</DialogTitle>

                                    <div class="max-h-96 overflow-hidden mb-4">
                                        <Cropper ref="cropperRef" :src="cropperImage"
                                            :stencil-props="{ aspectRatio: 16 / 9 }" image-restriction="stencil"
                                            class="h-72" />
                                    </div>

                                    <div class="flex justify-end space-x-3">
                                        <button @click="closeCropDialog" type="button"
                                            class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300">
                                            Cancel
                                        </button>
                                        <button @click="cropImage" type="button"
                                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                            Apply
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </Dialog>
                    </Teleport>
                </div>

                <div v-if="currentStep === 5" class="step-container">
                    <h2 class="text-2xl font-bold mb-6">Review and Submit</h2>

                    <div class="bg-gray-50 p-6 rounded-lg">
                        <h3 class="text-lg font-semibold mb-4">Basic Information</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <p class="font-medium">Venue Name:</p>
                                <p>{{ formData.name }}</p>
                            </div>
                            <div>
                                <p class="font-medium">Capacity:</p>
                                <p>{{ formData.capacity }}</p>
                            </div>
                            <div>
                                <p class="font-medium">Phone:</p>
                                <p>{{ formData.phone }}</p>
                            </div>
                            <div>
                                <p class="font-medium">Email:</p>
                                <p>{{ formData.email }}</p>
                            </div>
                            <div>
                                <p class="font-medium">Website:</p>
                                <p>{{ formData.website || 'N/A' }}</p>
                            </div>
                        </div>

                        <h3 class="text-lg font-semibold mt-6 mb-4">Location</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <p class="font-medium">Address:</p>
                                <p>{{ formData.address }}</p>
                            </div>
                            <div>
                                <p class="font-medium">City:</p>
                                <p>{{ formData.city }}</p>
                            </div>
                            <div>
                                <p class="font-medium">State:</p>
                                <p>{{ formData.state }}</p>
                            </div>
                            <div>
                                <p class="font-medium">ZIP Code:</p>
                                <p>{{ formData.zip }}</p>
                            </div>
                            <div>
                                <p class="font-medium">Country:</p>
                                <p>{{ formData.country }}</p>
                            </div>
                        </div>

                        <h3 class="text-lg font-semibold mt-6 mb-4">Media</h3>
                        <div>
                            <p class="font-medium">Logo:</p>
                            <p>{{ formData.logo?.name || 'None' }}</p>
                        </div>
                        <div class="mt-2">
                            <p class="font-medium">Images:</p>
                            <p v-if="formData.images.length === 0">None</p>
                            <ul v-else>
                                <li v-for="(image, index) in formData.images" :key="index">
                                    {{ image.name }}
                                </li>
                            </ul>
                        </div>
                        <div class="mt-2">
                            <p class="font-medium">Videos:</p>
                            <p v-if="formData.videos.length === 0">None</p>
                            <ul v-else>
                                <li v-for="(video, index) in formData.videos" :key="index">
                                    {{ video.name }}
                                </li>
                            </ul>
                        </div>

                        <h3 class="text-lg font-semibold mt-6 mb-4">Description</h3>
                        <p>{{ formData.description }}</p>
                    </div>
                </div>

                <div class="flex justify-between shadow p-3 mt-8">
                    <CorePrimaryButton v-if="currentStep > 1" type="button" @click="prevStep" text="Previous"
                        color="neutral" variant="outlined" />
                    <div v-else></div>

                    <CoreSubmitButton :loading="isLoading" :disabled="isLoading"
                        :text="currentStep === steps.length ? 'Submit Venue' : 'Next'" />
                </div>
            </FormKit>
        </div>
    </div>
</template>

<style></style>