<template>
    <div>
        <Popover v-slot="{ open }" class="relative">
            <PopoverButton :class="open ? 'text-white' : 'text-white/90'" class="focus:outline-none focus:ring-0">
                <button class="bg-black bg-opacity-25 shiny flex flex-col items-center space-y-2 px-2 text-white">
                    <Icon icon="fluent:share-28-filled" class="w-6 h-6 text-white mt-2" />
                    Share
                </button>
            </PopoverButton>

            <transition enter-active-class="transition duration-200 ease-out" enter-from-class="translate-y-1 opacity-0"
                enter-to-class="translate-y-0 opacity-100" leave-active-class="transition duration-150 ease-in"
                leave-from-class="translate-y-0 opacity-100" leave-to-class="translate-y-1 opacity-0">
                <PopoverPanel
                    class="absolute left-1/2 z-10 w-16 max-w-sm -translate-x-1/2 transform sm:px-0 lg:max-w-md">
                    <div class="bg-white shadow-lg overflow-hidden">
                        <div class="flex flex-col items-center space-y-4 p-4">
                            <button @click="shareOnWhatsApp"
                                class="w-full flex items-center justify-center space-x-2 hover:bg-gray-100 p-1 rounded-md">
                                <Icon icon="logos:whatsapp-icon" class="w-7 h-7" />
                            </button>
                            <button @click="shareOnFacebook"
                                class="w-full flex items-center justify-center space-x-2 hover:bg-gray-100 p-1 rounded-md">
                                <Icon icon="devicon:facebook" class="w-7 h-7" />
                            </button>
                            <button @click="shareOnTwitter"
                                class="w-full flex items-center justify-center space-x-2 hover:bg-gray-100 p-1 rounded-md">
                                <Icon icon="devicon:twitter" class="w-7 h-7" />
                            </button>
                            <button @click="shareOnInstagram"
                                class="w-full flex items-center justify-center space-x-2 hover:bg-gray-100 p-1 rounded-md">
                                <Icon icon="skill-icons:instagram" class="w-7 h-7" />
                            </button>
                            <button @click="shareOnLinkedIn"
                                class="w-full flex items-center justify-center space-x-2 hover:bg-gray-100 p-1 rounded-md">
                                <Icon icon="devicon:linkedin" class="w-7 h-7" />
                            </button>
                        </div>
                    </div>
                </PopoverPanel>
            </transition>
        </Popover>
    </div>
</template>

<script setup lang="ts">
import { Popover, PopoverButton, PopoverPanel } from '@headlessui/vue'
import type { PropType } from 'vue';
import type { EventItem } from '~/types';

const props = defineProps({
    event: {
        type: Object as PropType<EventItem>,
        required: true,
    }
});

const eventLink = computed(() => {
    return `${window.location.origin}/events/${props.event.slug}`
});

const shareMessage = computed(() => {
    return `🎉 Check out this event: ${props.event.title}
    📅 Date: ${props.event.start}
    ${props.event.description.substring(0, 200)}...
    More details: ${eventLink.value}`
})

const shareOnWhatsApp = () => {
    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(shareMessage.value)}`
    window.open(whatsappUrl, '_blank')
}

const shareOnFacebook = () => {
    const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(eventLink.value.toString())}`
    window.open(facebookUrl, '_blank')
}

const shareOnTwitter = () => {
    const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareMessage.value)}`
    window.open(twitterUrl, '_blank')
}

const shareOnInstagram = () => {
    navigator.clipboard.writeText(shareMessage.value)
    alert('Event details copied to clipboard. You can now paste on Instagram.')
}

const shareOnLinkedIn = () => {
    const linkedinUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(eventLink.value.toString())}`
    window.open(linkedinUrl, '_blank')
}
</script>