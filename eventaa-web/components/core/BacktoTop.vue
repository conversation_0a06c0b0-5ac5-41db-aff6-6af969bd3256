<template>
  <button v-if="isVisible" class="fixed right-10 bottom-20 flex items-center bg-red-600 text-white px-4 py-2" @click="scrollToTop">
    Back to Top<Icon icon="tdesign:align-top" class="w-5 h-5 ml-1"/>
  </button>
</template>

<script lang="ts" setup>

const isVisible = ref<boolean>(false);

const checkScroll = (): void => {
  isVisible.value = window.scrollY > 300;
};

const scrollToTop = (): void => {
  window.scrollTo({ top: 0, behavior: "smooth" });
};

onMounted(() => {
  window.addEventListener("scroll", checkScroll);
});

onUnmounted(() => {
  window.removeEventListener("scroll", checkScroll);
});
</script>
