<template>
    <div class="flex items-center">
        <div v-for="index in 5" :key="index" class="cursor-pointer" @click="updateRating(index)">
            <svg :class="[
                'w-5 h-5',
                index <= (hoverRating || Math.ceil(modelValue)) ? 'text-yellow-400' : 'text-gray-300 dark:text-zinc-100',
                'transition-colors duration-200'
            ]" @mouseenter="setHoverRating(index)" @mouseleave="clearHoverRating" xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24" fill="currentColor">
                <path
                    d="M12 17.27L18.18 21L16.54 13.97L22 9.24L14.81 8.63L12 2L9.19 8.63L2 9.24L7.46 13.97L5.82 21L12 17.27Z" />
            </svg>
        </div>
        <span v-if="showRating" class="ml-2 text-gray-600 dark:text-zinc-100">
            {{ Math.ceil(modelValue) }} of 5
        </span>
    </div>
</template>

<script setup lang="ts">
const props = defineProps({
    modelValue: {
        type: Number,
        default: 0
    },
    showRating: {
        type: Boolean,
        default: true
    },
    disabled: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['update:modelValue'])

const hoverRating = ref<number>(0)

const updateRating = (value: number): void => {
    if (props.disabled) return
    emit('update:modelValue', value)
}

const setHoverRating = (value: number): void => {
    if (props.disabled) return
    hoverRating.value = value
}

const clearHoverRating = (): void => {
    hoverRating.value = 0
}
</script>