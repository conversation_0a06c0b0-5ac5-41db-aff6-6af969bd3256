<template>
  <div class="search-bar-container">
    <div class="relative w-full">
      <div class="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
        <Icon icon="heroicons:magnifying-glass" class="h-5 w-5 text-gray-400 dark:text-gray-500" />
      </div>

      <input
        ref="searchInput"
        :value="modelValue"
        @input="updateModelValue($event)"
        type="text"
        :placeholder="placeholder"
        class="block w-full pl-10 pr-10 py-1.5 border-gray-300 dark:border-zinc-600 dark:bg-zinc-700 text-gray-900 dark:text-white bg-gray-50 focus:border-red-500 focus:ring-red-500 rounded-none"
        @focus="showHistory = true"
        @blur="onBlur"
        @keydown.enter="submitSearch"
        @keydown.esc="clearSearch"
        @keydown.down="navigateHistory('down')"
        @keydown.up="navigateHistory('up')"
      />

      <button
        v-if="modelValue"
        class="absolute inset-y-0 right-0 pr-2 flex items-center cursor-pointer"
        @click="clearSearch"
      >
        <Icon icon="heroicons:x-mark" class="h-5 w-5 text-gray-400 dark:text-gray-500 hover:text-red-500 dark:hover:text-red-400" />
      </button>
    </div>

    <div
      v-if="showHistory && searchHistory.length > 0"
      class="search-history absolute mt-1 w-full bg-white dark:bg-zinc-800 border border-gray-200 dark:border-zinc-600 rounded-md shadow-lg z-10"
    >
      <div class="flex justify-between items-center px-3 py-2 border-b border-gray-200 dark:border-zinc-600">
        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Recent searches</span>
        <button
          @click.stop="clearHistory"
          class="text-xs text-red-500 hover:text-red-700 dark:hover:text-red-400"
        >
          Clear all
        </button>
      </div>
      <ul>
        <li
          v-for="(item, index) in filteredHistory"
          :key="index"
          :class="[
            'px-3 py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-zinc-700 text-gray-800 dark:text-gray-200',
            { 'bg-gray-100 dark:bg-zinc-700': selectedHistoryIndex === index }
          ]"
          @mousedown.prevent="selectHistoryItem(item)"
          @mouseover="selectedHistoryIndex = index"
        >
          <div class="flex justify-between items-center">
            <div class="flex items-center">
              <Icon icon="heroicons:clock" class="h-4 w-4 mr-2 text-gray-400 dark:text-gray-500" />
              <span>{{ item }}</span>
            </div>
            <button
              @mousedown.prevent.stop="removeHistoryItem(index)"
              class="text-gray-400 hover:text-red-500 dark:hover:text-red-400"
            >
              <Icon icon="heroicons:trash" class="h-4 w-4" />
            </button>
          </div>
        </li>
      </ul>
      <div
        v-if="filteredHistory.length === 0"
        class="px-3 py-2 text-sm text-gray-500 dark:text-gray-400 italic"
      >
        No matching search history
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue'

interface SearchBarProps {
  placeholder: string
  maxHistoryItems: number
  modelValue: string
}

const props = withDefaults(defineProps<SearchBarProps>(), {
  placeholder: 'Search by transaction ID, customer, or event',
  maxHistoryItems: 10,
  modelValue: ''
})

const emit = defineEmits<{
  search: [query: string]
  clear: []
  'update:modelValue': [value: string]
}>()

const searchHistory = ref<string[]>([])
const showHistory = ref<boolean>(false)
const selectedHistoryIndex = ref<number>(-1)
const searchInput = ref<HTMLInputElement | null>(null)

const filteredHistory = computed<string[]>(() => {
  if (!props.modelValue) return searchHistory.value
  return searchHistory.value.filter(item =>
    item.toLowerCase().includes(props.modelValue.toLowerCase())
  )
})

onMounted(() => {
  const savedHistory = localStorage.getItem('searchHistory')
  if (savedHistory) {
    searchHistory.value = JSON.parse(savedHistory)
  }

  document.addEventListener('click', handleClickOutside)
})

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside)
})

function updateModelValue(event: Event): void {
  const target = event.target as HTMLInputElement
  emit('update:modelValue', target.value)
}

function submitSearch(): void {
  if (!props.modelValue.trim()) return

  addToHistory(props.modelValue)
  emit('search', props.modelValue)
  showHistory.value = false
}

function clearSearch(): void {
  emit('update:modelValue', '')
  selectedHistoryIndex.value = -1
  emit('clear')
}

function addToHistory(query: string): void {
  const trimmedQuery = query.trim()
  if (!trimmedQuery) return

  const index = searchHistory.value.indexOf(trimmedQuery)
  if (index !== -1) {
    searchHistory.value.splice(index, 1)
  }

  searchHistory.value.unshift(trimmedQuery)

  if (searchHistory.value.length > props.maxHistoryItems) {
    searchHistory.value = searchHistory.value.slice(0, props.maxHistoryItems)
  }

  saveHistory()
}

function removeHistoryItem(index: number): void {
  searchHistory.value.splice(index, 1)
  if (selectedHistoryIndex.value >= searchHistory.value.length) {
    selectedHistoryIndex.value = searchHistory.value.length - 1
  }
  saveHistory()
}

function clearHistory(): void {
  searchHistory.value = []
  saveHistory()
}

function saveHistory(): void {
  localStorage.setItem('searchHistory', JSON.stringify(searchHistory.value))
}

function selectHistoryItem(item: string): void {
  emit('update:modelValue', item)
  showHistory.value = false
  submitSearch()
}

function navigateHistory(direction: 'up' | 'down'): void {
  if (filteredHistory.value.length === 0) return

  if (direction === 'down') {
    selectedHistoryIndex.value = Math.min(
      selectedHistoryIndex.value + 1,
      filteredHistory.value.length - 1
    )
  } else if (direction === 'up') {
    if (selectedHistoryIndex.value === -1) {
      selectedHistoryIndex.value = filteredHistory.value.length - 1
    } else {
      selectedHistoryIndex.value = Math.max(selectedHistoryIndex.value - 1, 0)
    }
  }

  if (selectedHistoryIndex.value !== -1) {
    emit('update:modelValue', filteredHistory.value[selectedHistoryIndex.value])
  }
}

function handleClickOutside(event: MouseEvent): void {
  if (searchInput.value && !searchInput.value.contains(event.target as Node)) {
    showHistory.value = false
  }
}

function onBlur(): void {
  setTimeout(() => {
    showHistory.value = false
  }, 150)
}

watch(() => props.modelValue, (newVal: string) => {
  if (newVal) {
    selectedHistoryIndex.value = -1
  }
})
</script>

<style scoped>
.search-bar-container {
  position: relative;
  width: 100%;
}

.search-history {
  max-height: 300px;
  overflow-y: auto;
}
</style>
