<template>
    <div class="location-picker">
        <div class="flex gap-4 mb-4">
            <button @click="getCurrentLocation" class="flex items-center gap-2 py-2 transition-colors"
                :disabled="isLoadingLocation">
                <CoreLoader v-if="isLoadingLocation" color="red" width="30" height="30" />
                <span v-else>📍</span>
                {{ isLoadingLocation ? 'Getting location...' : 'Use Current Location' }}
            </button>

            <div v-if="locationError" class="text-red-500 flex items-center">
                {{ locationError }}
            </div>
        </div>

        <div class="search-container mb-4">
            <input type="text" ref="searchInput" v-model="searchQuery" placeholder="Search location..."
                class="w-full px-4 py-2 border rounded-none shadow-sm focus:ring-0 focus:outline-none" />
        </div>

        <div class="map-container h-[500px] rounded-lg overflow-hidden">
            <GoogleMap v-if="isLoaded" :api-key="apiKey" :center="mapCenter" :zoom="zoom" @click="handleMapClick"
                class="w-full h-full">
                <Marker v-if="currentLocation" :options="{
                    position: currentLocation,
                    draggable: true,
                }" @dragend="handleMarkerDragEnd" />

                <Circle v-if="currentLocation" :options="{
                    center: currentLocation,
                    radius: searchRadius,
                    fillColor: '#4299e1',
                    fillOpacity: 0.1,
                    strokeColor: '#4299e1',
                    strokeOpacity: 0.8,
                    strokeWeight: 2
                }" />
            </GoogleMap>
        </div>

        <div v-if="currentLocation" class="location-details mt-4 p-4 bg-gray-50 border">
            <h3 class="text-lg font-semibold mb-2">Selected Location</h3>
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <label class="block text-base font-medium text-gray-700">Latitude</label>
                    <FormKit type="text" :value="currentLocation.lat.toFixed(6)" readonly
                        class="mt-1 block w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-md" />
                </div>
                <div>
                    <label class="block text-base font-medium text-gray-700">Longitude</label>
                    <FormKit type="text" :value="currentLocation.lng.toFixed(6)" readonly
                        class="mt-1 block w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-md" />
                </div>
            </div>
            <div class="mt-4">
                <label class="block text-base font-medium text-gray-700">Address</label>
                <FormKit type="text" v-model="formattedAddress" readonly
                    class="mt-1 block w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-md" />
            </div>
            <div class="w-full justify-end flex mt-2">
                <button @click="onSave" type="button" class="bg-red-600 px-4 py-1.5 text-white">
                    Save
                </button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Loader } from '@googlemaps/js-api-loader';

interface Location {
    lat: number
    lng: number
}

interface Props {
    apiKey: string
    defaultLocation?: Location
    searchRadius?: number
    useGeolocation?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    defaultLocation: () => ({
        lat: 40.7128,
        lng: -74.0060
    }),
    searchRadius: 1000,
    useGeolocation: true
})

const emit = defineEmits<{
    (e: 'update:location', location: Location): void
    (e: 'update:address', address: string): void
}>()

const { $toast }: any = useNuxtApp();
const isLoaded = ref<boolean>(false)
const isLoadingLocation = ref<boolean>(false)
const locationError = ref<string>('')
const searchInput = ref<HTMLInputElement | null>(null)
const mapCenter = ref<Location>(props.defaultLocation)
const currentLocation = ref<Location | null>(null)
const zoom = ref<number>(13)
const searchQuery = ref<string>('')
const formattedAddress = ref<string>('')

let geocoder: google.maps.Geocoder
let searchAutocomplete: google.maps.places.Autocomplete

const getCurrentLocation = (): void => {
    isLoadingLocation.value = true
    locationError.value = ''

    if (!navigator.geolocation) {
        locationError.value = 'Geolocation is not supported by your browser'
        isLoadingLocation.value = false
        $toast.error('Geolocation is not supported by your browser')
        return
    }

    navigator.geolocation.getCurrentPosition(
        (position) => {
            const location = {
                lat: position.coords.latitude,
                lng: position.coords.longitude
            }
            updateLocation(location)
            mapCenter.value = location
            zoom.value = 15
            isLoadingLocation.value = false
        },
        (error) => {
            switch (error.code) {
                case error.PERMISSION_DENIED:
                    locationError.value = 'Permission denied for geolocation'
                    $toast.error('Permission denied for geolocation')
                    break
                case error.POSITION_UNAVAILABLE:
                    locationError.value = 'Location information unavailable'
                    $toast.error('Location information unavailable')
                    break
                case error.TIMEOUT:
                    locationError.value = 'Request for location timed out'
                    $toast.error('Request for location timed out')
                    break
                default:
                    locationError.value = 'An unknown error occurred'
                    $toast.error('An unknown error occurred')
            }
            isLoadingLocation.value = false
        },
        {
            enableHighAccuracy: true,
            timeout: 5000,
            maximumAge: 0
        }
    )
}

onMounted(async () => {
    try {
        const loader = new Loader({
            apiKey: props.apiKey,
            version: "weekly",
            libraries: ["places"]
        });

        await loader.load();
        isLoaded.value = true;

        initializeServices();

        if (props.useGeolocation) {
            getCurrentLocation()
        } else if (props.defaultLocation) {
            updateLocation(props.defaultLocation)
        }
    } catch (error) {
        console.error('Error loading Google Maps:', error);
        $toast.error('Error loading Google Maps')
        locationError.value = 'Error loading Google Maps'
    }
})

const initializeServices = (): void => {
    geocoder = new google.maps.Geocoder()
    if (searchInput.value) {
        searchAutocomplete = new google.maps.places.Autocomplete(searchInput.value, {
            types: ['address']
        })

        searchAutocomplete.addListener('place_changed', () => {
            const place = searchAutocomplete.getPlace()
            if (place?.geometry?.location) {
                const newLocation = {
                    lat: place.geometry.location.lat(),
                    lng: place.geometry.location.lng()
                }
                updateLocation(newLocation)
                mapCenter.value = newLocation
                zoom.value = 15
            }
        })
    }
}

const handleMapClick = (event: google.maps.MapMouseEvent): void => {
    if (event.latLng) {
        const newLocation = {
            lat: event.latLng.lat(),
            lng: event.latLng.lng()
        }
        updateLocation(newLocation)
    }
}

const onSave = (): void => {
    emit('update:onSave', currentLocation.value)
}

const handleMarkerDragEnd = (event: google.maps.MapMouseEvent): void => {
    if (event.latLng) {
        const newLocation = {
            lat: event.latLng.lat(),
            lng: event.latLng.lng()
        }
        updateLocation(newLocation)
    }
}

const updateLocation = async (location: Location): void => {
    currentLocation.value = location
    emit('update:location', location)
    try {
        const response = await geocoder.geocode({
            location
        })
        if (response.results[0]) {
            formattedAddress.value = response.results[0].formatted_address
            emit('update:address', formattedAddress.value)
        }
    } catch (error) {
        console.error('Geocoding error:', error)
    }
}
</script>