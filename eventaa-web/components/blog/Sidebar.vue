<template>
    <div>
        <div class="mb-2">
            <div class="relative flex items-center">
                <div class="absolute left-1.5 top-2.5 flex items-center pl-0">
                    <MagnifyingGlassIcon class="w-6 h-6 font-thin text-gray-400" />
                </div>
                <input v-model="search" placeholder="Search anything..."
                    class="w-full pl-8 text-base font-light text-gray-500 border px-2 py-2 rounded focus:outline-none focus:ring-0"
                    @keydown.enter="onSearch" type="search" autocomplete="off" />
                <div class="absolute top-1.5 right-1.5">
                    <button class="flex items-center text-sm border text-gray-500 p-1 bg-gray-100 rounded font-thin">
                        <Icon icon="solar:command-outline" class="w-5 h5 text-gray-500 mr-1"></Icon>
                        F
                    </button>
                </div>
            </div>
        </div>
        <div>
            <h3 class="text-xl font-semibold mb-2">Twitter Feed</h3>
            <div class="w-full flex flex-col gap-2">
                <BlogTwitterCard v-for="tweet in hashtagTweets" :key="tweet.id" :tweet="tweet" />
            </div>
        </div>
        <div>
            <h3 class="text-xl font-semibold mb-2">Related tags</h3>
            <div class="flex flex-wrap">
                <div v-for="tag in tags" v-bind:key="tag.name"
                    class="bg-gray-100 flex items-center border text-gray-700 rounded-full px-3 py-1 mr-2 mb-2">
                    <Icon icon="gg:tag" class="w-5 h-5 mr-1" />
                    <span>{{ tag.name }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { MagnifyingGlassIcon } from '@heroicons/vue/24/outline';


const search = ref("");
const httpClient = useHttpClient();
const hashtagTweets = ref<any[]>([]);
const tags = [
    {
        name: "Tag 1",
        slug: "tag-1",
    },
    {
        name: "Tag 2",
        slug: "tag-2",
    },
    {
        name: "Tag 3",
        slug: "tag-3",
    },
    {
        name: "Tag 4",
        slug: "tag-4",
    },
    {
        name: "Tag 5",
        slug: "tag-5",
    },
];

const fetchTwitterFeed = async () => {
    try {
        const response = await httpClient.get<{ data: [] }>(ENDPOINTS.BLOG.TWITTER);
        hashtagTweets.value = response.data;
    } catch (error) {
        console.error(error)
    } finally {
        console.info("Done")
    }
}

const onSearch = () => {

}

onMounted(() => {
    fetchTwitterFeed();
})
</script>