<template>
    <div class="max-full mx-auto border rounded-none overflow-hidden">
        <div class="relative">
            <img :src="`${runtimeConfig.public.baseUrl}storage/${props.article.image}`" alt="Golden Gate Bridge"
                class="w-full h-auto" />
            <div class="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-2">
                <span v-for="i in 3" :key="i" class="w-2 h-2 bg-gray-300 rounded-full cursor-pointer"
                    :class="{ 'bg-red-500': activeIndex === i }" @click="setActive(i)"></span>
            </div>
        </div>
        <div class="p-6">
            <NuxtLink to="/blog/post" class="block">
                <h2 class="text-xl hover:text-sky-500 transition duration-150 font-bold text-gray-800">Gallery post
                    format blog post</h2>
            </NuxtLink>
            <div class="flex items-center text-sm text-gray-500 space-x-4 mt-2">
                <span class="flex items-center">
                    <Icon icon="stash:calendar-duotone" class="w-6 h-6 mr-1" />
                    {{ dayjs(props.article.created_at).format("DD/MM/YYYY") }}
                </span>
                <span class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-1" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M5.121 17.804A4 4 0 0112 15m0 0a4 4 0 0110.879 2.804M15 12a3 3 0 100-6 3 3 0 000 6zm-3 2a3 3 0 00-3 3 3 3 0 103-3z" />
                    </svg>
                    {{ props.article.user.name }}
                </span>
                <span class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-1" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M7 8h10M7 12h6m-6 4h8m-2 4a8 8 0 100-16 8 8 0 000 16z" />
                    </svg>
                    Comments Off
                </span>
            </div>
            <div class="text-gray-600 mt-4 line-clamp-2" v-html="props.article.content"></div>

            <NuxtLink :to="`/blog/${article.slug}`" class="mt-4 px-6 py-2 flex items-center text-red-600 rounded-none">
                <Icon icon="gg:read" class="w-5 h-5 mr-2" />
                Read More
            </NuxtLink>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, type PropType } from "vue";
import type { Article } from "@/types";
import dayjs from "dayjs";

const props = defineProps({
    article: {
        type: Object as PropType<Article>,
        required: true,
    },
});
const runtimeConfig = useRuntimeConfig();
const activeIndex = ref(1);
const setActive = (index: number) => {
    activeIndex.value = index;
};
</script>

<style scoped></style>
