<template>
    <div class="relative w-full py-36 overflow-hidden">
        <div class="absolute inset-0 bg-cover bg-center" :style="{
            backgroundImage: `url('/assets/images/mans-texting.jpg')`
        }">
            <div class="absolute inset-0 bg-gradient-to-r from-black/50 to-transparent"></div>
        </div>

        <div class="relative h-full flex flex-col justify-center items-center text-white px-4">
            <h1 class="text-5xl font-bold mb-4 text-center">Welcome to EventaHub Blog</h1>
            <p class="text-xl max-w-2xl text-center">
                Explore current posts and hastags related to your interests
            </p>
        </div>

        <div v-for="(bubble, index) in socialBubbles" :key="index" :class="[
            'absolute bottom-0 transform -translate-x-1/2',
            bubble.position,
            'animate-bounce'
        ]" :style="{
            animationDelay: `${index * 0.2}s`,
            animationDuration: '3s'
        }">
            <div
                class="bg-white p-4 shadow-lg max-w-xs flex items-start space-x-3 transform hover:scale-105 transition-transform">
                <div class="flex-shrink-0">
                    <Icon :icon="bubble.icon" class="w-5 h-5"/>
                </div>
                <p class="text-gray-800 text-sm font-thin">{{ bubble.text }}</p>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'

const socialBubbles = ref([
    {
        icon: 'logos:twitter',
        iconClass: 'w-5 h-5 text-blue-400',
        text: "Amazing event coming! #eventahub",
        position: 'top-20 left-1/4'
    },
    {
        icon: 'skill-icons:instagram',
        iconClass: 'w-5 h-5 text-blue-600',
        text: 'This event has been so helpful for my journey!',
        position: 'top-32 right-1/4'
    },
    {
        icon: 'devicon:facebook',
        iconClass: 'w-5 h-5 text-green-500',
        text: 'Great insights on this topic!',
        position: 'top-52 left-1/3'
    }
])
</script>