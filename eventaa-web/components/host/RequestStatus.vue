<template>
  <div class="max-w-2xl mx-auto">
    <div class="dashboard-bg-card dashboard-shadow overflow-hidden">
      <div class="px-6 py-4 dashboard-border border-b">
        <h3 class="text-lg font-medium dashboard-text-primary">Host Request Status</h3>
      </div>

      <div v-if="loading" class="p-6">
        <div class="animate-pulse space-y-4">
          <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
          <div class="h-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
          <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
        </div>
      </div>

      <div v-else-if="!request" class="p-6 text-center">
        <div class="mb-4">
          <Icon icon="heroicons:user-plus" class="mx-auto h-12 w-12 dashboard-text-muted" />
        </div>
        <h3 class="text-lg font-medium dashboard-text-primary mb-2">No Host Request Found</h3>
        <p class="dashboard-text-muted mb-6">
          You haven't submitted a host request yet. Submit one to start creating events.
        </p>
        <button
          @click="$emit('request-host')"
          class="px-6 py-2 bg-red-600 text-white text-sm font-medium hover:bg-red-700 dashboard-transition"
        >
          Request Host Access
        </button>
      </div>

      <div v-else class="p-6">
        <div class="space-y-6">
          <!-- Status Badge -->
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0">
                <Icon 
                  :icon="getStatusIcon(request.status)" 
                  :class="getStatusIconClass(request.status)"
                  class="h-8 w-8"
                />
              </div>
              <div>
                <h4 class="text-lg font-medium dashboard-text-primary">
                  {{ getStatusTitle(request.status) }}
                </h4>
                <p class="text-sm dashboard-text-muted">
                  Submitted {{ formatDate(request.requested_at) }}
                </p>
              </div>
            </div>
            <span 
              :class="getStatusBadgeClass(request.status)"
              class="px-3 py-1 text-xs font-medium uppercase tracking-wide"
            >
              {{ request.status }}
            </span>
          </div>

          <!-- Request Details -->
          <div class="dashboard-border border-t pt-6">
            <h5 class="text-sm font-medium dashboard-text-primary mb-3">Your Request</h5>
            <div class="dashboard-bg-hover p-4">
              <p class="text-sm dashboard-text-secondary whitespace-pre-wrap">{{ request.reason }}</p>
            </div>
          </div>

          <!-- Admin Response (if processed) -->
          <div v-if="request.status !== 'pending' && request.admin_notes" class="dashboard-border border-t pt-6">
            <h5 class="text-sm font-medium dashboard-text-primary mb-3">Admin Response</h5>
            <div class="dashboard-bg-hover p-4">
              <p class="text-sm dashboard-text-secondary whitespace-pre-wrap">{{ request.admin_notes }}</p>
              <div v-if="request.processed_by" class="mt-3 pt-3 dashboard-border border-t">
                <p class="text-xs dashboard-text-muted">
                  Processed by {{ request.processed_by.name }} on {{ formatDate(request.processed_at) }}
                </p>
              </div>
            </div>
          </div>

          <!-- Actions -->
          <div v-if="request.status === 'rejected'" class="dashboard-border border-t pt-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm dashboard-text-primary font-medium">Want to try again?</p>
                <p class="text-sm dashboard-text-muted">You can submit a new request addressing the feedback above.</p>
              </div>
              <button
                @click="$emit('request-host')"
                class="px-4 py-2 bg-red-600 text-white text-sm font-medium hover:bg-red-700 dashboard-transition"
              >
                Submit New Request
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useHttpClient } from '@/composables/useHttpClient';
import { ENDPOINTS } from '@/utils/api';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

dayjs.extend(relativeTime);

const emit = defineEmits<{
  'request-host': [];
}>();

const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const loading = ref(true);
const request = ref<any>(null);

const fetchRequest = async () => {
  loading.value = true;
  try {
    const response = await httpClient.get(ENDPOINTS.HOST_REQUESTS.MY_REQUEST);
    request.value = response?.data || null;
  } catch (error) {
    console.error('Error fetching host request:', error);
    $toast.error('Failed to load host request status');
  } finally {
    loading.value = false;
  }
};

const formatDate = (date: string) => {
  return dayjs(date).format('MMM D, YYYY [at] h:mm A');
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'pending':
      return 'heroicons:clock';
    case 'approved':
      return 'heroicons:check-circle';
    case 'rejected':
      return 'heroicons:x-circle';
    default:
      return 'heroicons:question-mark-circle';
  }
};

const getStatusIconClass = (status: string) => {
  switch (status) {
    case 'pending':
      return 'text-yellow-500';
    case 'approved':
      return 'text-green-500';
    case 'rejected':
      return 'text-red-500';
    default:
      return 'dashboard-text-muted';
  }
};

const getStatusTitle = (status: string) => {
  switch (status) {
    case 'pending':
      return 'Request Under Review';
    case 'approved':
      return 'Request Approved!';
    case 'rejected':
      return 'Request Rejected';
    default:
      return 'Unknown Status';
  }
};

const getStatusBadgeClass = (status: string) => {
  switch (status) {
    case 'pending':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
    case 'approved':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
    case 'rejected':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
  }
};

onMounted(() => {
  fetchRequest();
});

defineExpose({
  fetchRequest
});
</script>
