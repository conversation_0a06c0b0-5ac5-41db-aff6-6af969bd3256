<template>
  <div class="max-w-2xl mx-auto">
    <div class="dashboard-bg-card dashboard-shadow overflow-hidden">
      <div class="px-6 py-4 dashboard-border border-b">
        <h3 class="text-lg font-medium dashboard-text-primary">Request Host Access</h3>
        <p class="mt-1 text-sm dashboard-text-muted">
          Tell us why you'd like to become a host and start creating events.
        </p>
      </div>

      <div class="p-6">
        <form @submit.prevent="submitRequest">
          <div class="space-y-6">
            <div>
              <label for="reason" class="block text-sm font-medium dashboard-text-primary mb-2">
                Why do you want to become a host? *
              </label>
              <textarea
                id="reason"
                v-model="form.reason"
                rows="6"
                class="w-full px-3 py-2 dashboard-border dashboard-bg-input dashboard-text-primary focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                placeholder="Please explain your motivation for becoming a host, your experience with events, and what types of events you plan to create..."
                :class="{ 'border-red-500': errors.reason }"
                required
              ></textarea>
              <p class="mt-1 text-xs dashboard-text-muted">
                Minimum 10 characters, maximum 1000 characters
              </p>
              <p v-if="errors.reason" class="mt-1 text-sm text-red-600">
                {{ errors.reason[0] }}
              </p>
            </div>

            <div class="flex items-center justify-between pt-4 dashboard-border border-t">
              <div class="text-sm dashboard-text-muted">
                <p>Your request will be reviewed by our admin team.</p>
                <p>You'll receive a notification once it's processed.</p>
              </div>
              
              <div class="flex space-x-3">
                <button
                  type="button"
                  @click="$emit('cancel')"
                  class="px-4 py-2 text-sm font-medium dashboard-text-secondary hover:dashboard-text-primary dashboard-transition"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  :disabled="loading || !form.reason || form.reason.length < 10"
                  class="px-6 py-2 bg-red-600 text-white text-sm font-medium hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed dashboard-transition"
                >
                  {{ loading ? 'Submitting...' : 'Submit Request' }}
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { useHttpClient } from '@/composables/useHttpClient';
import { ENDPOINTS } from '@/utils/api';

const emit = defineEmits<{
  cancel: [];
  success: [request: any];
}>();

const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const loading = ref(false);
const errors = ref<any>({});

const form = reactive({
  reason: ''
});

const submitRequest = async () => {
  if (form.reason.length < 10) {
    $toast.error('Please provide a detailed reason (minimum 10 characters)');
    return;
  }

  loading.value = true;
  errors.value = {};

  try {
    const response = await httpClient.post(ENDPOINTS.HOST_REQUESTS.STORE, {
      reason: form.reason
    });

    if (response) {
      $toast.success('Host request submitted successfully!');
      emit('success', response.data);
    }
  } catch (error: any) {
    console.error('Error submitting host request:', error);
    
    if (error.response?.data?.errors) {
      errors.value = error.response.data.errors;
    } else if (error.response?.data?.message) {
      $toast.error(error.response.data.message);
    } else {
      $toast.error('Failed to submit host request. Please try again.');
    }
  } finally {
    loading.value = false;
  }
};
</script>
