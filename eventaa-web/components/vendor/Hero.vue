<script lang="ts" setup>
import {
    CameraIcon
} from '@heroicons/vue/24/outline';


</script>

<template>
    <div class="relative bg-gradient-to-br from-gray-100 via-zinc-300 to-red-100">
        <div class="absolute inset-0 overflow-hidden">
            <div
                class="absolute top-20 left-10 w-64 h-64 bg-pink-500 rounded-full mix-blend-multiply filter blur-3xl opacity-20">
            </div>
            <div
                class="absolute bottom-20 right-10 w-80 h-80 bg-amber-500 rounded-full mix-blend-multiply filter blur-3xl opacity-20">
            </div>
        </div>

        <div class="container mx-auto px-4 py-5 relative z-10">
            <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/2 text-center md:text-left mb-8 md:mb-0">
                    <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-4">
                        Perfect vendors for your perfect day
                    </h1>
                    <p class="text-lg opacity-90 mb-8">
                        Connect with trusted professionals who will bring your event vision to life. Photographers,
                        caterers, sound engineers and more - all in one place.
                    </p>
                </div>

                <div class="md:w-1/2 flex justify-center">
                    <img src="@/assets/illustrations/help.png" alt="Hero Image" class="w-full md:w-3/4">
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
</style>
