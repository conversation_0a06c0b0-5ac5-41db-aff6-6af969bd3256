<template>
    <div class="w-full overflow-hidden shadow bg-white dark:bg-zinc-800 mb-3 animate-pulse rounded-lg">
        <div class="relative h-48 bg-zinc-200 dark:bg-zinc-700"></div>

        <div class="p-4">
            <div class="flex items-center justify-between mb-3">
                <div class="flex items-center">
                    <div class="w-12 h-12 rounded-full bg-zinc-300 dark:bg-zinc-600"></div>
                    <div class="ml-3 space-y-2">
                        <div class="h-4 w-32 bg-zinc-300 dark:bg-zinc-600 rounded"></div>
                        <div class="flex items-center gap-2">
                            <div class="h-3 w-24 bg-zinc-300 dark:bg-zinc-600 rounded"></div>
                        </div>
                    </div>
                </div>

                <div class="flex flex-col items-end">
                    <div class="h-4 w-12 bg-zinc-300 dark:bg-zinc-600 rounded mb-1"></div>
                    <div class="h-3 w-20 bg-zinc-200 dark:bg-zinc-700 rounded"></div>
                </div>
            </div>

            <div class="flex flex-wrap gap-2 mt-4">
                <div class="h-6 w-16 bg-zinc-200 dark:bg-zinc-700 rounded-full"></div>
                <div class="h-6 w-14 bg-zinc-200 dark:bg-zinc-700 rounded-full"></div>
                <div class="h-6 w-20 bg-zinc-200 dark:bg-zinc-700 rounded-full"></div>
            </div>

            <div class="mt-4 flex justify-between items-center">
                <div class="h-4 w-24 bg-zinc-200 dark:bg-zinc-700 rounded"></div>
                <div class="h-5 w-20 bg-zinc-300 dark:bg-zinc-600 rounded"></div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
</script>