<template>
  <div class="dashboard-bg-card dashboard-shadow overflow-hidden animate-pulse">
    <div class="relative aspect-video bg-zinc-200 dark:bg-zinc-700"></div>
    <div class="p-4">
      <div class="h-5 w-3/4 bg-zinc-200 dark:bg-zinc-700 mb-3"></div>
      <div class="h-4 w-full bg-zinc-200 dark:bg-zinc-700 mb-2"></div>
      <div class="h-4 w-5/6 bg-zinc-200 dark:bg-zinc-700 mb-4"></div>
      <div class="mt-2 flex flex-wrap gap-2">
        <div class="h-5 w-20 bg-zinc-200 dark:bg-zinc-700"></div>
        <div class="h-5 w-16 bg-zinc-200 dark:bg-zinc-700"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
</script>

<style scoped>
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}
</style>
