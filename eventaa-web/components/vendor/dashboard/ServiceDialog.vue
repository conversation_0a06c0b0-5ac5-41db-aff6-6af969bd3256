<template>
  <div>
    <CorePrimaryButton @click="openModal" text="Add New Service" startIcon="heroicons:plus" />
  </div>
  <TransitionRoot appear :show="isOpen" as="template">
    <Dialog as="div" @close="closeModal" class="relative z-10">
      <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
        leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
        <div class="fixed inset-0 bg-black/25" />
      </TransitionChild>
      <div class="fixed inset-0 overflow-y-auto">
        <div class="flex min-h-full items-center justify-center p-4">
          <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95">
            <DialogPanel class="w-full max-w-md transform overflow-hidden bg-white shadow-xl transition-all">
              <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900 p-4 border-b">
                {{ isEditMode ? 'Edit Service' : 'Add New Service' }}
              </DialogTitle>
              <div class="p-6">
                <form @submit.prevent="submitForm" class="space-y-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700">Service</label>
                    <Listbox v-model="selectedServiceOption">
                      <div class="relative mt-1">
                        <ListboxButton
                          class="relative w-full cursor-default bg-white py-2 pl-3 pr-10 text-left border border-gray-300 focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500 sm:text-sm">
                          <span class="block truncate">
                            {{ selectedServiceOption ? selectedServiceOption.name : 'Select a service' }}
                          </span>
                          <span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                            <ChevronUpDownIcon class="h-5 w-5 text-gray-400" aria-hidden="true" />
                          </span>
                        </ListboxButton>

                        <transition leave-active-class="transition duration-100 ease-in" leave-from-class="opacity-100"
                          leave-to-class="opacity-0">
                          <ListboxOptions
                            class="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm">
                            <ListboxOption v-for="service in availableServices" :key="service.id" :value="service" as="template"
                              v-slot="{ active, selected }">
                              <li :class="[
                                active ? 'bg-red-100 text-red-900' : 'text-gray-900',
                                'relative cursor-default select-none py-2 pl-10 pr-4'
                              ]">
                                <span :class="[
                                  selected ? 'font-medium' : 'font-normal',
                                  'block truncate'
                                ]">
                                  {{ service.name }}
                                </span>
                                <span v-if="selected" class="absolute inset-y-0 left-0 flex items-center pl-3 text-red-600">
                                  <CheckIcon class="h-5 w-5" aria-hidden="true" />
                                </span>
                              </li>
                            </ListboxOption>
                          </ListboxOptions>
                        </transition>
                      </div>
                    </Listbox>
                    <p v-if="availableServices.length === 0 && !isLoading" class="mt-1 text-sm text-red-500">
                      No services available. Please try again later.
                    </p>
                    <p v-if="isLoading" class="mt-1 text-sm text-gray-500">
                      Loading services...
                    </p>
                  </div>

                  <div v-if="selectedServiceOption">
                    <label class="block text-sm font-medium text-gray-700">Description</label>
                    <p class="mt-1 text-sm text-gray-500">{{ selectedServiceOption.description }}</p>
                  </div>


                </form>
              </div>
              <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse border-t">
                <CorePrimaryButton
                  :text="isEditMode ? 'Update Service' : 'Add Service'"
                  @click="submitForm"
                  :loading="loading"
                  class="w-full sm:w-auto sm:ml-3"
                />
                <button
                  type="button"
                  class="mt-3 sm:mt-0 w-full sm:w-auto px-4 py-2 bg-white text-gray-700 border border-gray-300 hover:bg-gray-50"
                  @click="closeModal"
                >
                  Cancel
                </button>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
  Listbox,
  ListboxButton,
  ListboxOptions,
  ListboxOption,
} from '@headlessui/vue';
import { CheckIcon, ChevronUpDownIcon } from '@heroicons/vue/20/solid';
import { ref, computed, watch } from 'vue';
import { useVendorServicesStore } from '@/store/vendorServices';
import { useVendorStore } from '@/store/vendor';
import type { Service } from '@/types/vendor';

interface ServiceForm {
  id?: number;
  service_id: number | null;
  vendor_id?: number;
}

interface ServiceOption {
  id: number;
  name: string;
  description: string;
}

const props = withDefaults(defineProps<{
  service?: Service;
  editMode?: boolean;
}>(), {
  editMode: false
});


const isEditMode = ref(props.editMode);

const emit = defineEmits(['service-added', 'service-updated']);

const isOpen = ref(false);
const loading = ref(false);
const { $toast }: any = useNuxtApp();
const vendorServicesStore = useVendorServicesStore();
const vendorStore = useVendorStore();
const isLoading = ref(false);
const runtimeConfig = useRuntimeConfig();

const form = ref<ServiceForm>({
  service_id: null
});

const selectedServiceOption = ref<ServiceOption | null>(null);


watch(selectedServiceOption, (newValue) => {
  if (newValue) {
    form.value.service_id = newValue.id;
  } else {
    form.value.service_id = null;
  }
});

const availableServices = computed(() => vendorServicesStore.availableServices);

watch(() => props.service, (newService) => {
  if (newService && props.editMode) {
    form.value = {
      id: newService.id,
      service_id: newService.service_id,
      vendor_id: newService.vendor_id
    };

    const serviceOption = availableServices.value.find(s => s.id === newService.service_id);
    if (serviceOption) {
      selectedServiceOption.value = serviceOption;
    }


  }
}, { immediate: true });



const openModal = async (service?: Service, editMode = false) => {
  isEditMode.value = editMode;
  isLoading.value = true;

  try {

    await vendorServicesStore.fetchAvailableServices();

    if (service && editMode) {
      form.value = {
        id: service.id,
        service_id: service.service_id,
        vendor_id: service.vendor_id
      };

      const serviceOption = availableServices.value.find(s => s.id === service.service_id);
      if (serviceOption) {
        selectedServiceOption.value = serviceOption;
      }
    } else {

      resetForm();
    }
  } catch (error) {
    console.error('Error loading services:', error);
    $toast.error('Failed to load available services');
  } finally {
    isLoading.value = false;
    isOpen.value = true;
  }
};

const closeModal = () => {
  isOpen.value = false;
  resetForm();
};

const resetForm = () => {
  form.value = {
    service_id: null
  };
  selectedServiceOption.value = null;
  isEditMode.value = false;
};

const submitForm = async () => {
  loading.value = true;

  try {

    if (!form.value.service_id) {
      throw new Error('Please select a service');
    }


    const serviceData = {
      ...form.value,
      vendor_id: vendorStore.details?.id
    };

    let result;

    if (isEditMode.value && form.value.id) {

      result = await vendorServicesStore.updateService(form.value.id, serviceData);

      if (result) {
        $toast.success(`Service "${selectedServiceOption.value?.name}" updated successfully`);
        emit('service-updated', result);
      } else {
        throw new Error('Failed to update service');
      }
    } else {

      result = await vendorServicesStore.createService(serviceData);
      if (result) {
        $toast.success(`Service "${selectedServiceOption.value?.name}" added successfully`);
        emit('service-added', result);
      } else {
        throw new Error('Failed to create service');
      }
    }

    closeModal();
  } catch (error: any) {
    $toast.error(error.message || 'An error occurred');
  } finally {
    loading.value = false;
  }
};

defineExpose({
  openModal
});
</script>
