<template>
  <div class="flex dashboard-bg-sidebar sm:border-r dashboard-border">
    <div :class="isOpen ? 'block' : 'hidden'"
      class="fixed inset-0 z-20 bg-gray-200 dark:bg-zinc-700 bg-opacity-50 dark:bg-opacity-50 transition-opacity lg:hidden"
      @click="toggleSidebar"></div>

    <div :class="[
      isOpen ? 'translate-x-0 ease-out' : '-translate-x-full ease-in',
      isMiniVariant ? 'lg:w-16' : 'lg:w-64',
    ]"
      class="fixed inset-y-0 left-0 z-30 overflow-y-auto transition duration-300 transform dashboard-bg-sidebar lg:translate-x-0 lg:static lg:inset-0">
      <div class="flex items-center justify-between px-4 py-3 dashboard-border border-b">
        <div class="flex items-center">
          <img src="/icon.png" class="w-auto h-8" />
          <span v-if="!isMiniVariant" class="mx-2 text-xl font-semibold dashboard-text-primary">EventaHub</span>
        </div>
        <button v-if="!isMiniVariant" @click="toggleMini"
          class="p-1 rounded-full hover:dashboard-bg-hover lg:block hidden dashboard-text-secondary dashboard-transition">
          <Icon icon="heroicons:chevron-left" class="w-5 h-5" />
        </button>
        <button v-else @click="toggleMini"
          class="p-1 rounded-full hover:dashboard-bg-hover lg:block hidden dashboard-text-secondary dashboard-transition">
          <Icon icon="heroicons:chevron-right" class="w-5 h-5" />
        </button>
      </div>

      <nav class="mt-5 px-2">
        <div v-for="(section, index) in sidebarItems" :key="index" class="mb-4">
          <div v-if="!isMiniVariant"
            class="px-3 mb-2 text-xs font-semibold dashboard-text-light uppercase tracking-wider">
            {{ section.title }}
          </div>
          <div v-else class="dashboard-border-light border-b mb-2"></div>

          <router-link v-for="item in section.items" :key="item.title"
            class="flex items-center px-3 py-2 rounded-md text-sm font-medium dashboard-transition" :class="[
              $route.path === item.link ? activeClass : inactiveClass,
              isMiniVariant ? 'justify-center' : '',
            ]" :to="item.link" @click="active = item.title">
            <Icon :icon="item.icon" class="w-5 h-5" />
            <span v-if="!isMiniVariant" class="ml-3">{{ item.title }}</span>
            <span v-if="!isMiniVariant && item.count && item.count > 0"
              class="ml-auto bg-red-600 text-white py-0.5 px-2 text-xs">
              {{ item.count }}
            </span>
          </router-link>
        </div>
      </nav>

      <div class="absolute bottom-0 w-full" :class="isMiniVariant ? 'px-2' : 'px-4'">
        <div class="p-2 mb-4">
          <div v-if="!isMiniVariant" class="dashboard-bg-hover rounded-md p-3">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <Icon icon="heroicons:question-mark-circle" class="w-6 h-6 dashboard-text-secondary" />
              </div>
              <div class="ml-3">
                <p class="text-sm font-medium dashboard-text-primary">Need help?</p>
                <p class="text-xs dashboard-text-muted">Contact support</p>
              </div>
            </div>
          </div>
          <div v-else class="flex justify-center">
            <Icon icon="heroicons:question-mark-circle" class="w-6 h-6 dashboard-text-secondary" />
          </div>
        </div>
        <div class="dashboard-border border-t py-3 px-4 dashboard-bg-card">
          <div class="flex items-center">
            <img
              :src="authStore.user?.avatar ? `${runtimeConfig.public.baseUrl}storage/avatars/${authStore.user.avatar}` : `https://ui-avatars.com/api/?name=${encodeURIComponent(authStore.user?.name || 'Vendor User')}&background=random`"
              class="w-8 h-8 rounded-full" :alt="authStore.user?.name || 'Vendor User'" />
            <div v-if="!isMiniVariant" class="ml-3">
              <p class="text-sm font-medium dashboard-text-primary">{{ authStore.user?.name || 'Vendor User' }}</p>
              <p class="text-xs dashboard-text-muted">{{ authStore.user?.email || '<EMAIL>' }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import type { SidebarItem } from "@/types";
import { useVendorStore } from "@/store/vendor";
import { useAuthStore } from "@/store/auth";
import { useCountersStore } from "@/store/counters";

interface SidebarSection {
  title: string;
  items: SidebarItem[];
  permissions?: string[];
}

interface SidebarItemWithPermissions extends SidebarItem {
  permissions?: string[];
}

const vendorStore = useVendorStore();
const authStore = useAuthStore();
const countersStore = useCountersStore();
const runtimeConfig = useRuntimeConfig();
const nuxtApp = useNuxtApp();
const $echo = nuxtApp.$echo as any;

const isOpen = ref<boolean>(false);
const isMiniVariant = ref<boolean>(false);
const active = ref<string>("");
const activeClass = ref("dashboard-text-primary dashboard-bg-active");
const inactiveClass = ref("dashboard-text-secondary hover:dashboard-bg-hover hover:dashboard-text-primary");

const allSidebarItems = computed(() => [
  {
    title: "Main",
    items: [
      {
        title: "Dashboard",
        link: "/vendor/dashboard",
        icon: "heroicons:home",
        permissions: []
      },
      {
        title: "Bookings",
        link: "/vendor/dashboard/bookings",
        icon: "heroicons:calendar",
        count: countersStore.pendingBookings,
        permissions: ["bookings.view"]
      },
      {
        title: "Services",
        link: "/vendor/dashboard/services",
        icon: "heroicons:wrench-screwdriver",
        permissions: ["services.view"]
      }
    ]
  },
  {
    title: "Management",
    items: [
      {
        title: "Profile",
        link: "/vendor/dashboard/profile",
        icon: "heroicons:user-circle",
        permissions: ["profile.view"]
      },
      {
        title: "Portfolio",
        link: "/vendor/dashboard/portfolio",
        icon: "heroicons:photo",
        permissions: ["portfolio.view"]
      },
      {
        title: "Pricing",
        link: "/vendor/dashboard/pricing",
        icon: "heroicons:currency-dollar",
        permissions: ["pricing.view"]
      }
    ]
  },
  {
    title: "Communication",
    items: [
      {
        title: "Messages",
        link: "/vendor/dashboard/messages",
        icon: "heroicons:chat-bubble-left-right",
        count: countersStore.unreadMessages,
        permissions: ["messages.view"]
      },
      {
        title: "Reviews",
        link: "/vendor/dashboard/reviews",
        icon: "heroicons:star",
        count: countersStore.pendingReviews,
        permissions: ["reviews.view"]
      },
      {
        title: "Notifications",
        link: "/vendor/dashboard/notifications",
        icon: "heroicons:bell",
        count: countersStore.unreadNotifications,
        permissions: ["notifications.view"]
      }
    ]
  },
  {
    title: "Settings",
    items: [
      {
        title: "Account",
        link: "/vendor/dashboard/settings/account",
        icon: "heroicons:user",
        permissions: ["settings.account.view"]
      },
      {
        title: "Security",
        link: "/vendor/dashboard/settings/security",
        icon: "heroicons:shield-check",
        permissions: ["settings.security.view"]
      },
      {
        title: "Notifications",
        link: "/vendor/dashboard/settings/notifications",
        icon: "heroicons:bell-alert",
        permissions: ["settings.notifications.view"]
      },
      {
        title: "Payments",
        link: "/vendor/dashboard/settings/payments",
        icon: "heroicons:banknotes",
        permissions: ["settings.payments.view"]
      }
    ]
  }
]);

const sidebarItems = computed(() => {
  return allSidebarItems.value.map(section => {
    const filteredItems = section.items.filter(item => {
      if (!item.permissions || item.permissions.length === 0) {
        return true;
      }
      return vendorStore.hasPermissions(item.permissions);
    });

    return {
      ...section,
      items: filteredItems
    };
  }).filter(section => section.items.length > 0);
});

const toggleSidebar = () => {
  isOpen.value = !isOpen.value;
};

const toggleMini = () => {
  isMiniVariant.value = !isMiniVariant.value;
};

onMounted(async () => {
  // Fetch all counts when the component is mounted
  await countersStore.fetchAllCounts();

  // Set up real-time listeners if Echo is available
  if ($echo && authStore.user?.id && typeof $echo.private === 'function') {
    try {
      // Listen for new bookings
      $echo.private(`vendor.${authStore.user.id}`)
        .listen('BookingCreated', () => {
          countersStore.incrementPendingBookings();
        });

      // Listen for booking status changes
      $echo.private(`vendor.${authStore.user.id}`)
        .listen('BookingStatusChanged', (event: any) => {
          if (event.oldStatus === 'pending' && event.newStatus !== 'pending') {
            countersStore.decrementPendingBookings();
          } else if (event.oldStatus !== 'pending' && event.newStatus === 'pending') {
            countersStore.incrementPendingBookings();
          }
        });

      // Listen for new notifications
      $echo.private(`notifications.${authStore.user.id}`)
        .listen('App\\Events\\NotificationCreated', (event: any) => {
          if (!event.read_at) {
            countersStore.incrementUnreadNotifications();
          }
        });

      // Listen for new messages
      $echo.private(`user.${authStore.user.id}.messages`)
        .listen('MessageSent', () => {
          countersStore.incrementUnreadMessages();
        });

      // Listen for new reviews
      $echo.private(`vendor.${authStore.user.id}`)
        .listen('ReviewCreated', () => {
          countersStore.incrementPendingReviews();
        });
    } catch (error) {
      console.error('Failed to subscribe to real-time events:', error);
    }
  }
});

defineExpose({
  toggleMini,
});
</script>
