import { defineNuxtPlugin } from '#app'
import VendorDashboardHeader from './Header.vue'
import VendorDashboardSidebar from './Sidebar.vue'
import VendorDashboardServiceDialog from './ServiceDialog.vue'
import VendorDashboardBookingDialog from './BookingDialog.vue'
import VendorDashboardBookingDetailsDialog from './BookingDetailsDialog.vue'

export default defineNuxtPlugin((nuxtApp) => {
  nuxtApp.vueApp.component('VendorDashboardHeader', VendorDashboardHeader)
  nuxtApp.vueApp.component('VendorDashboardSidebar', VendorDashboardSidebar)
  nuxtApp.vueApp.component('VendorDashboardServiceDialog', VendorDashboardServiceDialog)
  nuxtApp.vueApp.component('VendorDashboardBookingDialog', VendorDashboardBookingDialog)
  nuxtApp.vueApp.component('VendorDashboardBookingDetailsDialog', VendorDashboardBookingDetailsDialog)
})
