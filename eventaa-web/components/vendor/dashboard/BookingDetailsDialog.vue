<template>
  <TransitionRoot appear :show="isOpen" as="template">
    <Dialog as="div" @close="closeModal" class="relative z-10">
      <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
        leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
        <div class="fixed inset-0 bg-black/25" />
      </TransitionChild>
      <div class="fixed inset-0 overflow-y-auto">
        <div class="flex min-h-full items-center justify-center p-4">
          <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95">
            <DialogPanel class="w-full max-w-md transform overflow-hidden dashboard-bg-card dashboard-shadow p-6 text-left align-middle transition-all">
              <div v-if="loading" class="flex justify-center items-center py-10">
                <div class="animate-spin rounded-full h-10 w-10 border-b-2 dashboard-border-primary"></div>
              </div>
              <div v-else>
                <DialogTitle as="h3" class="text-lg font-medium leading-6 dashboard-text-primary flex justify-between items-center">
                  <span>Booking Details</span>
                  <span v-if="booking" class="inline-flex items-center px-2.5 py-0.5 text-xs font-medium rounded-full"
                    :class="getStatusClass(booking.status)">
                    {{ booking.status }}
                  </span>
                </DialogTitle>

                <div v-if="!booking" class="mt-4 dashboard-text-muted">
                  No booking details available
                </div>

                <div v-else class="mt-4">
                  <div class="flex items-center mb-4">
                    <div class="flex-shrink-0 h-12 w-12">
                      <img class="h-12 w-12 rounded-full" :src="`${runtimeConfig.public.baseUrl}storage/avatars/${booking.user?.avatar}`" alt="user-avatar" />
                    </div>
                    <div class="ml-4">
                      <h4 class="text-base font-medium dashboard-text-primary">{{ booking.user?.name }}</h4>
                      <p class="text-sm dashboard-text-muted">{{ booking.user?.email }}</p>
                    </div>
                  </div>

                  <div class="dashboard-border border-t py-4">
                    <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                      <div>
                        <dt class="text-sm font-medium dashboard-text-muted">Service</dt>
                        <dd class="mt-1 text-sm dashboard-text-primary">{{ booking.vendor_service?.service?.name }}</dd>
                      </div>
                      <div>
                        <dt class="text-sm font-medium dashboard-text-muted">Date</dt>
                        <dd class="mt-1 text-sm dashboard-text-primary">{{ formatDate(booking.booking_from) }}</dd>
                      </div>
                      <div>
                        <dt class="text-sm font-medium dashboard-text-muted">Time</dt>
                        <dd class="mt-1 text-sm dashboard-text-primary">{{ formatTime(booking.booking_from) }} - {{ formatTime(booking.booking_to) }}</dd>
                      </div>
                      <div>
                        <dt class="text-sm font-medium dashboard-text-muted">Guests</dt>
                        <dd class="mt-1 text-sm dashboard-text-primary">{{ booking.number_of_guests }}</dd>
                      </div>
                      <div class="sm:col-span-2">
                        <dt class="text-sm font-medium dashboard-text-muted">Amount</dt>
                        <dd class="mt-1 text-sm dashboard-text-primary">${{ booking.total_price }}</dd>
                      </div>
                      <div v-if="booking.message" class="sm:col-span-2">
                        <dt class="text-sm font-medium dashboard-text-muted">Message</dt>
                        <dd class="mt-1 text-sm dashboard-text-primary">{{ booking.message }}</dd>
                      </div>
                      <div v-if="booking.rejection_reason" class="sm:col-span-2">
                        <dt class="text-sm font-medium dashboard-text-muted">Rejection Reason</dt>
                        <dd class="mt-1 text-sm dashboard-text-primary">{{ booking.rejection_reason }}</dd>
                      </div>
                    </dl>
                  </div>

                  <div v-if="booking.status === 'pending'" class="mt-6 flex justify-between">
                    <button
                      type="button"
                      class="px-4 py-2 bg-green-600 text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                      @click="confirmBooking"
                      :disabled="actionLoading"
                    >
                      <span v-if="actionLoading" class="flex items-center">
                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Processing...
                      </span>
                      <span v-else>Confirm Booking</span>
                    </button>
                    <button
                      type="button"
                      class="px-4 py-2 bg-red-600 text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      @click="openRejectDialog"
                      :disabled="actionLoading"
                    >
                      Reject Booking
                    </button>
                  </div>

                  <div v-else-if="booking.status === 'approved'" class="mt-6 flex justify-between">
                    <button
                      type="button"
                      class="px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      @click="completeBooking"
                      :disabled="actionLoading"
                    >
                      <span v-if="actionLoading" class="flex items-center">
                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Processing...
                      </span>
                      <span v-else>Mark as Completed</span>
                    </button>
                    <button
                      type="button"
                      class="px-4 py-2 bg-red-600 text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      @click="cancelBooking"
                      :disabled="actionLoading"
                    >
                      Cancel Booking
                    </button>
                  </div>

                  <div v-else class="mt-6">
                    <button
                      type="button"
                      class="w-full px-4 py-2 dashboard-bg-card dashboard-text-primary dashboard-border hover:dashboard-bg-hover focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      @click="closeModal"
                    >
                      Close
                    </button>
                  </div>
                </div>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>


  <TransitionRoot appear :show="isRejectDialogOpen" as="template">
    <Dialog as="div" @close="closeRejectDialog" class="relative z-20">
      <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
        leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
        <div class="fixed inset-0 bg-black/25" />
      </TransitionChild>
      <div class="fixed inset-0 overflow-y-auto">
        <div class="flex min-h-full items-center justify-center p-4">
          <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95">
            <DialogPanel
              class="w-full max-w-md transform overflow-hidden dashboard-bg-card dashboard-shadow p-6 text-left align-middle transition-all">
              <DialogTitle as="h3" class="text-lg font-medium leading-6 dashboard-text-primary">
                Reject Booking
              </DialogTitle>

              <div class="mt-4">
                <label for="rejection-reason" class="block text-sm font-medium dashboard-text-muted">
                  Reason for rejection
                </label>
                <textarea
                  id="rejection-reason"
                  v-model="rejectionReason"
                  rows="4"
                  class="mt-1 block w-full dashboard-border dashboard-bg-card dashboard-text-primary focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm"
                  placeholder="Please provide a reason for rejecting this booking"
                ></textarea>
              </div>

              <div class="mt-6 flex justify-end space-x-3">
                <button
                  type="button"
                  class="px-4 py-2 dashboard-bg-card dashboard-text-primary dashboard-border hover:dashboard-bg-hover focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                  @click="closeRejectDialog"
                  :disabled="actionLoading"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  class="px-4 py-2 bg-red-600 text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                  @click="rejectBooking"
                  :disabled="actionLoading || !rejectionReason.trim()"
                >
                  <span v-if="actionLoading" class="flex items-center">
                    <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Processing...
                  </span>
                  <span v-else>Confirm Rejection</span>
                </button>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from '@headlessui/vue';
import { ref, watch } from 'vue';
import dayjs from 'dayjs';
import { useVendorBookingsStore } from '@/store/vendorBookings';
import { handleError } from '@/utils/errorHandler';

const props = defineProps<{
  booking: any | null;
  isOpen: boolean;
}>();

const emit = defineEmits(['close', 'update-status']);

const runtimeConfig = useRuntimeConfig();
const loading = ref(false);
const actionLoading = ref(false);
const isRejectDialogOpen = ref(false);
const rejectionReason = ref('');
const { $toast }: any = useNuxtApp();
const vendorBookingsStore = useVendorBookingsStore();

const statusClasses = {
  'pending': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
  'approved': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
  'completed': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
  'rejected': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
  'cancelled': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
};

const getStatusClass = (status: string) => {
  return statusClasses[status as keyof typeof statusClasses] || '';
};

const formatDate = (dateString: string) => {
  return dayjs(dateString).format('MMM D, YYYY');
};

const formatTime = (dateString: string) => {
  return dayjs(dateString).format('h:mm A');
};

const closeModal = () => {
  emit('close');
};

const openRejectDialog = () => {
  rejectionReason.value = '';
  isRejectDialogOpen.value = true;
};

const closeRejectDialog = () => {
  isRejectDialogOpen.value = false;
};

const updateBookingStatus = async (newStatus: string, successMessage: string, reason?: string) => {
  if (!props.booking) return;

  actionLoading.value = true;

  try {
    const result = await vendorBookingsStore.updateBookingStatus(props.booking.id, newStatus, reason);

    if (result) {
      $toast.success(successMessage);
      emit('update-status', { id: props.booking.id, status: newStatus, reason });
      closeModal();
      closeRejectDialog();
    } else {
      throw new Error('Failed to update booking status');
    }
  } catch (error: any) {
    handleError(error, $toast, 'Failed to update booking status');
  } finally {
    actionLoading.value = false;
  }
};

const confirmBooking = () => {
  updateBookingStatus('approved', 'Booking confirmed successfully');
};

const rejectBooking = () => {
  if (!rejectionReason.value.trim()) {
    $toast.error('Please provide a reason for rejection');
    return;
  }

  updateBookingStatus('rejected', 'Booking rejected', rejectionReason.value);
};

const completeBooking = () => {
  updateBookingStatus('completed', 'Booking marked as completed');
};

const cancelBooking = () => {
  updateBookingStatus('cancelled', 'Booking cancelled');
};
</script>
