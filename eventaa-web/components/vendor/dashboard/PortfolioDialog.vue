<template>
  <div>
    <slot name="trigger">
      <CorePrimaryButton @click="openModal" text="Add Portfolio Item" startIcon="heroicons:plus" />
    </slot>
  </div>
  <TransitionRoot appear :show="isOpen" as="template">
    <Dialog as="div" @close="closeModal" class="relative z-10">
      <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
        leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
        <div class="fixed inset-0 bg-black/25" />
      </TransitionChild>
      <div class="fixed inset-0 overflow-y-auto">
        <div class="flex min-h-full items-center justify-center p-4">
          <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95">
            <DialogPanel class="w-full max-w-md transform overflow-hidden dashboard-bg-card dashboard-shadow transition-all">
              <DialogTitle as="h3" class="text-lg font-medium leading-6 dashboard-text-primary p-4 dashboard-border border-b">
                {{ isEditMode ? 'Edit Portfolio Item' : 'Add Portfolio Item' }}
              </DialogTitle>
              <div class="p-6">
                <FormKit type="form" id="portfolioForm" @submit="submitForm" :actions="false" #default="{ state: { valid } }">
                  <div class="space-y-4">
                    <FormKit
                      type="text"
                      name="title"
                      label="Title"
                      v-model="form.title"
                      validation="required"
                      :validation-messages="{
                        required: 'Title is required'
                      }"
                      placeholder="Enter title"
                    />
                    <p v-if="validationErrors.title" class="mt-1 text-sm text-red-600">
                      {{ validationErrors.title[0] }}
                    </p>

                    <FormKit
                      type="textarea"
                      name="description"
                      label="Description"
                      v-model="form.description"
                      placeholder="Enter description"
                      rows="3"
                    />

                    <div>
                      <label class="block text-sm font-medium dashboard-text-primary mb-2">Category</label>
                      <Listbox v-model="form.category">
                        <div class="relative mt-1">
                          <ListboxButton
                            class="relative w-full cursor-default bg-white py-2 pl-3 pr-10 text-left border border-gray-300 dark:border-zinc-700 dark:bg-black dark:text-white focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500 sm:text-sm"
                          >
                            <span class="block truncate">{{ form.category || 'Select a category' }}</span>
                            <span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                              <ChevronUpDownIcon class="h-5 w-5 text-gray-400" aria-hidden="true" />
                            </span>
                          </ListboxButton>
                          <transition
                            leave-active-class="transition duration-100 ease-in"
                            leave-from-class="opacity-100"
                            leave-to-class="opacity-0"
                          >
                            <ListboxOptions
                              class="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-zinc-800 py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"
                            >
                              <ListboxOption
                                v-for="category in availableCategories"
                                :key="category"
                                :value="category"
                                v-slot="{ active, selected }"
                                as="template"
                              >
                                <li
                                  :class="[
                                    active ? 'bg-red-100 text-red-900 dark:bg-red-900 dark:text-red-100' : 'text-gray-900 dark:text-gray-100',
                                    'relative cursor-default select-none py-2 pl-10 pr-4'
                                  ]"
                                >
                                  <span
                                    :class="[
                                      selected ? 'font-medium' : 'font-normal',
                                      'block truncate'
                                    ]"
                                  >
                                    {{ category }}
                                  </span>
                                  <span
                                    v-if="selected"
                                    class="absolute inset-y-0 left-0 flex items-center pl-3 text-red-600 dark:text-red-400"
                                  >
                                    <CheckIcon class="h-5 w-5" aria-hidden="true" />
                                  </span>
                                </li>
                              </ListboxOption>
                            </ListboxOptions>
                          </transition>
                        </div>
                      </Listbox>
                      <p v-if="validationErrors.category" class="mt-1 text-sm text-red-600">
                        {{ validationErrors.category[0] }}
                      </p>
                    </div>

                    <FormKit
                      type="select"
                      name="media_type"
                      label="Media Type"
                      v-model="form.media_type"
                      validation="required"
                      :validation-messages="{
                        required: 'Media type is required'
                      }"
                      :options="[
                        { label: 'Image', value: 'image' },
                        { label: 'Video', value: 'video' }
                      ]"
                    />
                    <p v-if="validationErrors.media_type" class="mt-1 text-sm text-red-600">
                      {{ validationErrors.media_type[0] }}
                    </p>

                    <FormKit
                      type="checkbox"
                      name="is_featured"
                      label="Feature this item"
                      v-model="form.is_featured"
                    />

                    <div>
                      <label class="block text-sm font-medium dashboard-text-primary mb-1">
                        Image
                        <span v-if="!isEditMode || !portfolioItem" class="text-red-500">*</span>
                      </label>

                      <div v-if="isEditMode && portfolioItem && !imageFile" class="mb-2">
                        <div class="flex items-center space-x-4">
                          <img :src="getImageUrl(portfolioItem.image_path)" alt="Current image"
                            class="w-24 h-24 object-cover border" />
                          <button type="button" @click="changeImage"
                            class="text-sm dashboard-text-primary hover:text-red-600">
                            Change Image
                          </button>
                        </div>
                      </div>

                      <EventsImagePicker
                        v-if="!isEditMode || (isEditMode && !portfolioItem) || showImagePicker"
                        :max-files="1"
                        accept="image/*"
                        @files-selected="onImageSelected"
                        @file-removed="onImageRemoved"
                        @error="onImageError"
                      />

                      <p v-if="imageError" class="mt-1 text-sm text-red-600">{{ imageError }}</p>
                      <p v-if="validationErrors.image" class="mt-1 text-sm text-red-600">
                        {{ validationErrors.image[0] }}
                      </p>
                      <p v-if="validationErrors.vendor_id" class="mt-1 text-sm text-red-600">
                        {{ validationErrors.vendor_id[0] }}
                      </p>
                    </div>

                    <div class="flex justify-end space-x-3 pt-4">
                      <button type="button" @click="closeModal"
                        class="px-4 py-2 dashboard-border dashboard-text-secondary dashboard-bg-card hover:dashboard-bg-hover">
                        Cancel
                      </button>
                      <CoreSubmitButton
                        :disabled="!valid || isSubmitting || (!imageFile && !isEditMode)"
                        :loading="isSubmitting"
                        :text="isEditMode ? 'Update' : 'Save'"
                      />
                    </div>
                  </div>
                </FormKit>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
  Listbox,
  ListboxButton,
  ListboxOptions,
  ListboxOption,
} from '@headlessui/vue';
import { ref, reactive, computed, onMounted } from 'vue';
import { useVendorPortfolioStore, type PortfolioItem } from '@/store/vendorPortfolio';
import { useVendorStore } from '@/store/vendor';
import EventsImagePicker from '@/components/events/ImagePicker.vue';
import CoreSubmitButton from '@/components/core/SubmitButton.vue';
import { CheckIcon, ChevronUpDownIcon } from '@heroicons/vue/20/solid';

const props = defineProps<{
  portfolioItem?: PortfolioItem;
  editMode?: boolean;
}>();

const emit = defineEmits(['item-added', 'item-updated']);

const { $toast }: any = useNuxtApp();
const runtimeConfig = useRuntimeConfig();
const portfolioStore = useVendorPortfolioStore();
const vendorStore = useVendorStore();

const isOpen = ref(false);
const isSubmitting = ref(false);
const imageFile = ref<File | null>(null);
const imageError = ref<string | null>(null);
const showImagePicker = ref(false);
const validationErrors = ref<Record<string, string[]>>({});
const availableCategories = ref<string[]>([]);

const isEditMode = computed(() => !!props.editMode && !!props.portfolioItem);

onMounted(async () => {
  try {
    await portfolioStore.fetchPortfolioItems();
    availableCategories.value = portfolioStore.categories;
  } catch (error) {
    console.error('Error fetching categories:', error);
  }
});

const form = reactive({
  title: '',
  description: '',
  category: '',
  media_type: 'image',
  is_featured: false
});

const getImageUrl = (imagePath: string): string => {
  if (imagePath.startsWith('http')) {
    return imagePath;
  }
  return `${runtimeConfig.public.baseUrl}storage/${imagePath}`;
};

const resetForm = () => {
  form.title = '';
  form.description = '';
  form.category = '';
  form.media_type = 'image';
  form.is_featured = false;
  imageFile.value = null;
  imageError.value = null;
  showImagePicker.value = false;
  validationErrors.value = {};
};

const openModal = () => {
  isOpen.value = true;

  if (isEditMode.value && props.portfolioItem) {
    form.title = props.portfolioItem.title;
    form.description = props.portfolioItem.description || '';
    form.category = props.portfolioItem.category || '';
    form.media_type = props.portfolioItem.media_type;
    form.is_featured = props.portfolioItem.is_featured;
  } else {
    resetForm();
  }
};

const closeModal = () => {
  isOpen.value = false;
  resetForm();
  emit('item-updated', 0);
};

const changeImage = () => {
  showImagePicker.value = true;
};

const onImageSelected = (files: File[]) => {
  if (files.length > 0) {
    const file = files[0];

    if (file.size > 5 * 1024 * 1024) {
      imageError.value = 'Image size should not exceed 5MB';
      return;
    }

    imageFile.value = file;
    imageError.value = null;
  }
};

const onImageRemoved = () => {
  imageFile.value = null;
};

const onImageError = (error: string) => {
  imageError.value = error;
};

const submitForm = async () => {
  validationErrors.value = {};

  if (!form.title.trim()) {
    validationErrors.value.title = ['Title is required'];
    $toast.error('Title is required');
    return;
  }

  if (!isEditMode.value && !imageFile.value) {
    validationErrors.value.image = ['Image is required'];
    $toast.error('Image is required');
    return;
  }

  if (!form.media_type) {
    validationErrors.value.media_type = ['Media type is required'];
    $toast.error('Media type is required');
    return;
  }

  if (!vendorStore.details?.id) {
    validationErrors.value.vendor_id = ['Vendor ID is required'];
    $toast.error('Vendor ID is required');
    return;
  }

  isSubmitting.value = true;

  try {
    const formData = new FormData();

    if (isEditMode.value && props.portfolioItem) {
      formData.append('id', props.portfolioItem.id.toString());
    }

    formData.append('vendor_id', vendorStore.details?.id.toString());
    formData.append('title', form.title);
    formData.append('description', form.description || '');
    formData.append('category', form.category || '');
    formData.append('media_type', form.media_type);
    formData.append('is_featured', form.is_featured ? '1' : '0');

    if (imageFile.value) {
      formData.append('image', imageFile.value);
    }

    let success = false;

    if (isEditMode.value && props.portfolioItem) {
      try {
        success = await portfolioStore.updatePortfolioItem(props.portfolioItem.id, formData);
        if (success) {
          $toast.success('Portfolio item updated successfully');
          emit('item-updated', props.portfolioItem.id);
          closeModal();
        }
      } catch (error: any) {
        if (error.response && error.response.data && error.response.data.errors) {
          validationErrors.value = error.response.data.errors;
          Object.keys(validationErrors.value).forEach(key => {
            $toast.error(validationErrors.value[key][0]);
          });
        } else {
          $toast.error('Failed to update portfolio item');
        }
      }
    } else {
      try {
        success = await portfolioStore.addPortfolioItem(formData);
        if (success) {
          $toast.success('Portfolio item added successfully');
          emit('item-added');
          closeModal();
        }
      } catch (error: any) {
        if (error.response && error.response.data && error.response.data.errors) {
          validationErrors.value = error.response.data.errors;
          Object.keys(validationErrors.value).forEach(key => {
            $toast.error(validationErrors.value[key][0]);
          });
        } else {
          $toast.error('Failed to add portfolio item');
        }
      }
    }
  } catch (error: any) {
    console.error('Error saving portfolio item:', error);
    $toast.error('Failed to save portfolio item');
  } finally {
    isSubmitting.value = false;
  }
};

defineExpose({
  openModal
});
</script>
