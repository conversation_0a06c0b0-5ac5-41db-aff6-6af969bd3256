<template>
    <div class="w-full overflow-hidden shadow bg-white dark:bg-zinc-800 hover:shadow-xl transition-shadow duration-300">
        <div class="relative h-48">
            <div v-if="validWorkImages.length > 0" class="relative h-full">
                <div class="flex h-full overflow-x-auto snap-x snap-mandatory scrollbar-hide">
                    <div v-for="(image, index) in validWorkImages" :key="index"
                        class="flex-shrink-0 w-full h-full snap-center">
                        <img :src="`${runtimeConfig.public.baseUrl}storage/${image.url}`"
                            :alt="image.alt || 'Portfolio image'" class="w-full h-full object-cover">
                        <div v-if="image.url == ''" class="w-full h-full flex items-center justify-center bg-zinc-100 dark:bg-zinc-700">
                            <span class="text-zinc-500 dark:text-zinc-400">Image failed to load</span>
                        </div>
                    </div>
                </div>

                <div class="absolute bottom-2 left-0 right-0 flex justify-center gap-2">
                    <div v-for="(_, index) in validWorkImages" :key="index"
                        class="w-2 h-2 rounded-full bg-white bg-opacity-60 cursor-pointer"
                        :class="{ 'bg-opacity-100': currentImageIndex === index }" @click="currentImageIndex = index">
                    </div>
                </div>
            </div>

            <div v-else class="h-full bg-zinc-200 dark:bg-zinc-700 flex items-center justify-center">
                <span class="text-zinc-500 dark:text-zinc-400">No portfolio images</span>
            </div>
        </div>

        <div class="p-4">
            <div class="flex items-center justify-between mb-3">
                <div class="flex items-center">
                    <div class="relative">
                        <img :src="`${runtimeConfig.public.baseUrl}storage/avatars/${vendor.avatar}`" :alt="vendor.name"
                            class="w-12 h-12 rounded-full object-cover border-2 dark:border-zinc-600" />
                        <span v-if="vendor.isVerified" class="absolute -right-1 -bottom-1 text-sky-600">
                            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M23 12l-2.44-2.79.34-3.69-3.61-.82-1.89-3.2L12 2.96 8.6 1.5 6.71 4.69 3.1 5.5l.34 3.7L1 12l2.44 2.79-.34 3.7 3.61.82L8.6 22.5l3.4-1.47 3.4 1.46 1.89-3.19 3.61-.82-.34-3.69L23 12zm-12.91 4.72l-3.8-3.81 1.48-1.48 2.32 2.33 5.85-5.87 1.48 1.48-7.33 7.35z"></path>
                            </svg>
                        </span>
                    </div>

                    <div class="ml-3">
                        <NuxtLink :to="`/vendors/${vendor.slug}`">
                            <h3
                                class="font-semibold hover:text-red-500 transition-all duration-150 text-lg text-zinc-900 dark:text-white line-clamp-1">
                                {{ vendor.name }}</h3>
                        </NuxtLink>
                        <div class="flex items-center text-sm text-zinc-600 dark:text-zinc-400">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            {{ vendor.location }}
                        </div>
                    </div>
                </div>

                <div class="flex flex-col items-end">
                    <div class="flex items-center">
                        <span class="text-amber-500 font-semibold">{{ (typeof vendor.rating === 'number' ? vendor.rating : 0).toFixed(1) }}</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-amber-500 ml-1" viewBox="0 0 20 20"
                            fill="currentColor">
                            <path
                                d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                    </div>
                    <span class="text-xs text-zinc-500 dark:text-zinc-400">{{ vendor.reviewCount }} reviews</span>
                </div>
            </div>

            <div class="mt-4">
                <div class="flex flex-wrap gap-2">
                    <span v-for="(service, index) in vendor.serviceTypes" :key="index"
                        class="px-2 py-1 text-sm rounded-full bg-zinc-200 dark:bg-zinc-700 text-zinc-800 dark:text-zinc-200 font-medium">
                        {{ service }}
                    </span>
                </div>
            </div>

            <div class="mt-4 flex justify-between items-center text-zinc-700 dark:text-zinc-300">
                Price starts <span class="text-lg font-semibold text-red-600 dark:text-red-500">{{ formatCurrency(vendor.startingPrice) }}</span>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { Vendor } from '@/types/vendor';

const props = defineProps<{
    vendor: Vendor;
}>();

const currentImageIndex = ref<number>(0);
const runtimeConfig = useRuntimeConfig();

const validWorkImages = computed(() => {
    return props.vendor.workImages?.filter(image =>
        image &&
        image.url &&
        typeof image.url === 'string' &&
        (image.url.endsWith('.jpg') ||
            image.url.endsWith('.jpeg') ||
            image.url.endsWith('.png') ||
            image.url.endsWith('.gif') ||
            image.url.endsWith('.webp'))
    ) || [];
});

const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: props.vendor.priceCurrency,
        minimumFractionDigits: 0,
    }).format(amount);
};

watch(validWorkImages, (newImages) => {
    if (currentImageIndex.value >= newImages.length) {
        currentImageIndex.value = newImages.length > 0 ? 0 : -1;
    }
}, { immediate: true });
</script>

<style scoped>
.scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
    display: none;
}

.bg-primary {
    background-color: #dc2626; /* bg-red-600 */
}

.text-primary {
    color: #dc2626; /* text-red-600 */
}

.bg-primary-dark {
    background-color: #b91c1c; /* bg-red-700 */
}

.border-primary {
    border-color: #dc2626; /* border-red-600 */
}
</style>u
