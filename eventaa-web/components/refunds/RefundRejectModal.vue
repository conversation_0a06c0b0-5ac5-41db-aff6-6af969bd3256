<template>
  <CoreDialog :open="open" @close="$emit('close')" max-width="lg">
    <template #title>
      <div class="flex items-center space-x-3">
        <Icon name="heroicons:x-circle" class="w-6 h-6 text-red-600" />
        <span>Reject Refund Request</span>
      </div>
    </template>

    <template #content>
      <div v-if="refund" class="space-y-4">
        <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 p-4">
          <div class="flex">
            <Icon name="heroicons:exclamation-triangle" class="w-5 h-5 text-red-400 mt-0.5" />
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                Confirm Rejection
              </h3>
              <p class="mt-1 text-sm text-red-700 dark:text-red-300">
                You are about to reject the refund request for <strong>{{ refund.refund_reference }}</strong>.
                This action will notify the customer and cannot be undone.
              </p>
            </div>
          </div>
        </div>

        <!-- Refund Summary -->
        <div class="bg-zinc-50 dark:bg-zinc-800 p-4 border border-zinc-200 dark:border-zinc-700">
          <h4 class="text-sm font-medium text-zinc-900 dark:text-zinc-100 mb-2">Refund Summary</h4>
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span class="text-zinc-600 dark:text-zinc-400">Customer:</span>
              <span class="ml-2 text-zinc-900 dark:text-zinc-100">{{ refund.user?.name }}</span>
            </div>
            <div>
              <span class="text-zinc-600 dark:text-zinc-400">Amount:</span>
              <span class="ml-2 text-zinc-900 dark:text-zinc-100">MK{{ formatNumber(refund.refund_amount) }}</span>
            </div>
            <div>
              <span class="text-zinc-600 dark:text-zinc-400">Event:</span>
              <span class="ml-2 text-zinc-900 dark:text-zinc-100">{{ refund.ticket_purchase?.event?.title }}</span>
            </div>
            <div>
              <span class="text-zinc-600 dark:text-zinc-400">Ticket:</span>
              <span class="ml-2 text-zinc-900 dark:text-zinc-100">{{ refund.ticket_purchase?.ticket?.name }}</span>
            </div>
          </div>
        </div>

        <!-- Rejection Reason -->
        <div>
          <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">
            Rejection Reason <span class="text-red-500">*</span>
          </label>
          <select
            v-model="rejectionReason"
            class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-zinc-900 dark:text-zinc-100 focus:ring-2 focus:ring-red-500 focus:border-transparent"
            required
          >
            <option value="">Select a reason</option>
            <option value="Event not cancelled">Event is not cancelled</option>
            <option value="Outside refund policy">Request is outside refund policy</option>
            <option value="Insufficient documentation">Insufficient documentation provided</option>
            <option value="Duplicate request">Duplicate refund request</option>
            <option value="Event already occurred">Event has already occurred</option>
            <option value="No valid reason">No valid reason for refund</option>
            <option value="Other">Other (specify in notes)</option>
          </select>
        </div>

        <!-- Custom Reason (if Other is selected) -->
        <div v-if="rejectionReason === 'Other'">
          <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">
            Custom Reason <span class="text-red-500">*</span>
          </label>
          <input
            v-model="customReason"
            type="text"
            placeholder="Please specify the reason for rejection"
            class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-zinc-900 dark:text-zinc-100 focus:ring-2 focus:ring-red-500 focus:border-transparent"
            required
          />
        </div>

        <!-- Additional Notes -->
        <div>
          <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">
            Additional Notes (Optional)
          </label>
          <textarea
            v-model="adminNotes"
            rows="3"
            placeholder="Add any additional notes for internal reference..."
            class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-zinc-900 dark:text-zinc-100 focus:ring-2 focus:ring-red-500 focus:border-transparent"
          />
        </div>

        <!-- Customer Communication -->
        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 p-4">
          <div class="flex">
            <Icon name="heroicons:information-circle" class="w-5 h-5 text-blue-400 mt-0.5" />
            <div class="ml-3">
              <h4 class="text-sm font-medium text-blue-800 dark:text-blue-200">
                Customer Notification
              </h4>
              <p class="mt-1 text-sm text-blue-700 dark:text-blue-300">
                The customer will be automatically notified via email about the rejection with the reason you provide.
              </p>
            </div>
          </div>
        </div>
      </div>
    </template>

    <template #actions>
      <div class="flex justify-end space-x-3">
        <button
          @click="$emit('close')"
          class="inline-flex items-center px-4 py-2 border border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-zinc-700 dark:text-zinc-300 hover:bg-zinc-50 dark:hover:bg-zinc-600 focus:outline-none focus:ring-2 focus:ring-red-500"
        >
          Cancel
        </button>
        <button
          @click="handleConfirm"
          :disabled="!isFormValid || processing"
          class="inline-flex items-center px-4 py-2 bg-red-700 text-white hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {{ processing ? 'Processing...' : 'Reject Refund' }}
        </button>
      </div>
    </template>
  </CoreDialog>
</template>

<script setup lang="ts">
interface Props {
  refund: any
  open: boolean
}

interface Emits {
  (e: 'close'): void
  (e: 'confirm', data: { rejection_reason: string; admin_notes?: string }): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// State
const processing = ref(false)
const rejectionReason = ref('')
const customReason = ref('')
const adminNotes = ref('')

// Computed
const isFormValid = computed(() => {
  if (!rejectionReason.value) return false
  if (rejectionReason.value === 'Other' && !customReason.value.trim()) return false
  return true
})

const finalRejectionReason = computed(() => {
  return rejectionReason.value === 'Other' ? customReason.value : rejectionReason.value
})

// Methods
const handleConfirm = (): void => {
  if (!isFormValid.value) return

  emit('confirm', {
    rejection_reason: finalRejectionReason.value,
    admin_notes: adminNotes.value || undefined
  })
}

const formatNumber = (value: number): string => {
  return new Intl.NumberFormat().format(value || 0)
}

// Reset form when modal opens
watch(() => props.open, (isOpen) => {
  if (isOpen) {
    rejectionReason.value = ''
    customReason.value = ''
    adminNotes.value = ''
  }
})
</script>
