<template>
  <div class="space-y-6">
    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div class="bg-white dark:bg-zinc-800 p-6 border border-zinc-200 dark:border-zinc-700">
        <div class="flex items-center">
          <div class="p-2 bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400">
            <Icon name="heroicons:document-text" class="w-6 h-6" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-zinc-600 dark:text-zinc-400">Total Requests</p>
            <p class="text-2xl font-bold text-zinc-900 dark:text-zinc-100">
              {{ statistics?.total_requests || 0 }}
            </p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-zinc-800 p-6 border border-zinc-200 dark:border-zinc-700">
        <div class="flex items-center">
          <div class="p-2 bg-yellow-100 dark:bg-yellow-900/20 text-yellow-600 dark:text-yellow-400">
            <Icon name="heroicons:clock" class="w-6 h-6" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-zinc-600 dark:text-zinc-400">Pending</p>
            <p class="text-2xl font-bold text-zinc-900 dark:text-zinc-100">
              {{ statistics?.pending_requests || 0 }}
            </p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-zinc-800 p-6 border border-zinc-200 dark:border-zinc-700">
        <div class="flex items-center">
          <div class="p-2 bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400">
            <Icon name="heroicons:check-circle" class="w-6 h-6" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-zinc-600 dark:text-zinc-400">Approved</p>
            <p class="text-2xl font-bold text-zinc-900 dark:text-zinc-100">
              {{ statistics?.approved_requests || 0 }}
            </p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-zinc-800 p-6 border border-zinc-200 dark:border-zinc-700">
        <div class="flex items-center">
          <div class="p-2 bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400">
            <Icon name="heroicons:banknotes" class="w-6 h-6" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-zinc-600 dark:text-zinc-400">Total Refunded</p>
            <p class="text-2xl font-bold text-zinc-900 dark:text-zinc-100">
              MK{{ formatNumber(statistics?.total_refund_amount || 0) }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Detailed Statistics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Status Breakdown -->
      <div class="bg-white dark:bg-zinc-800 p-6 border border-zinc-200 dark:border-zinc-700">
        <h3 class="text-lg font-medium text-zinc-900 dark:text-zinc-100 mb-4">
          Status Breakdown
        </h3>
        <div class="space-y-3">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="w-3 h-3 bg-yellow-400 mr-3"></div>
              <span class="text-sm text-zinc-600 dark:text-zinc-400">Pending</span>
            </div>
            <span class="text-sm font-medium text-zinc-900 dark:text-zinc-100">
              {{ statistics?.pending_requests || 0 }}
            </span>
          </div>

          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="w-3 h-3 bg-blue-400 mr-3"></div>
              <span class="text-sm text-zinc-600 dark:text-zinc-400">Approved</span>
            </div>
            <span class="text-sm font-medium text-zinc-900 dark:text-zinc-100">
              {{ statistics?.approved_requests || 0 }}
            </span>
          </div>

          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="w-3 h-3 bg-red-400 mr-3"></div>
              <span class="text-sm text-zinc-600 dark:text-zinc-400">Rejected</span>
            </div>
            <span class="text-sm font-medium text-zinc-900 dark:text-zinc-100">
              {{ statistics?.rejected_requests || 0 }}
            </span>
          </div>

          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="w-3 h-3 bg-green-400 mr-3"></div>
              <span class="text-sm text-zinc-600 dark:text-zinc-400">Completed</span>
            </div>
            <span class="text-sm font-medium text-zinc-900 dark:text-zinc-100">
              {{ statistics?.completed_requests || 0 }}
            </span>
          </div>
        </div>
      </div>

      <!-- Financial Summary -->
      <div class="bg-white dark:bg-zinc-800 p-6 border border-zinc-200 dark:border-zinc-700">
        <h3 class="text-lg font-medium text-zinc-900 dark:text-zinc-100 mb-4">
          Financial Summary
        </h3>
        <div class="space-y-4">
          <div class="flex items-center justify-between py-2 border-b border-zinc-200 dark:border-zinc-700">
            <span class="text-sm text-zinc-600 dark:text-zinc-400">Total Refunded</span>
            <span class="text-sm font-medium text-zinc-900 dark:text-zinc-100">
              MK{{ formatNumber(statistics?.total_refund_amount || 0) }}
            </span>
          </div>

          <div class="flex items-center justify-between py-2 border-b border-zinc-200 dark:border-zinc-700">
            <span class="text-sm text-zinc-600 dark:text-zinc-400">Processing Fees</span>
            <span class="text-sm font-medium text-zinc-900 dark:text-zinc-100">
              MK{{ formatNumber(statistics?.total_processing_fees || 0) }}
            </span>
          </div>

          <div class="flex items-center justify-between py-2">
            <span class="text-sm text-zinc-600 dark:text-zinc-400">Average Refund</span>
            <span class="text-sm font-medium text-zinc-900 dark:text-zinc-100">
              MK{{ formatNumber(averageRefund) }}
            </span>
          </div>

          <div class="flex items-center justify-between py-2">
            <span class="text-sm text-zinc-600 dark:text-zinc-400">Refund Rate</span>
            <span class="text-sm font-medium text-zinc-900 dark:text-zinc-100">
              {{ refundRate }}%
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white dark:bg-zinc-800 p-6 border border-zinc-200 dark:border-zinc-700">
      <h3 class="text-lg font-medium text-zinc-900 dark:text-zinc-100 mb-4">
        Quick Actions
      </h3>
      <div class="flex flex-wrap gap-3">
        <button
          @click="$emit('filter-pending')"
          class="inline-flex items-center px-3 py-1.5 text-sm border border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-zinc-700 dark:text-zinc-300 hover:bg-zinc-50 dark:hover:bg-zinc-600 focus:outline-none focus:ring-2 focus:ring-red-500"
        >
          <Icon name="heroicons:clock" class="w-4 h-4 mr-2" />
          View Pending ({{ statistics?.pending_requests || 0 }})
        </button>

        <button
          @click="$emit('export-data')"
          class="inline-flex items-center px-3 py-1.5 text-sm border border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-zinc-700 dark:text-zinc-300 hover:bg-zinc-50 dark:hover:bg-zinc-600 focus:outline-none focus:ring-2 focus:ring-red-500"
        >
          <Icon name="heroicons:arrow-down-tray" class="w-4 h-4 mr-2" />
          Export Data
        </button>

        <button
          @click="$emit('refresh')"
          class="inline-flex items-center px-3 py-1.5 text-sm border border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-zinc-700 dark:text-zinc-300 hover:bg-zinc-50 dark:hover:bg-zinc-600 focus:outline-none focus:ring-2 focus:ring-red-500"
        >
          <Icon name="heroicons:arrow-path" class="w-4 h-4 mr-2" />
          Refresh
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  statistics: any
  loading?: boolean
}

interface Emits {
  (e: 'filter-pending'): void
  (e: 'export-data'): void
  (e: 'refresh'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Computed
const averageRefund = computed(() => {
  const total = props.statistics?.total_refund_amount || 0
  const count = props.statistics?.completed_requests || 0
  return count > 0 ? Math.round(total / count) : 0
})

const refundRate = computed(() => {
  const totalRequests = props.statistics?.total_requests || 0
  const completedRefunds = props.statistics?.completed_requests || 0
  return totalRequests > 0 ? Math.round((completedRefunds / totalRequests) * 100) : 0
})

// Methods
const formatNumber = (value: number): string => {
  return new Intl.NumberFormat().format(value || 0)
}
</script>
