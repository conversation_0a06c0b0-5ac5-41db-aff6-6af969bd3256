<template>
  <button @click="open = true">
    <Icon icon="hugeicons:eye" class="w-6 h-6 text-blue-600" />
  </button>

  <TransitionRoot appear :show="open" as="template">
    <Dialog as="div" @close="open = false" class="relative z-[999]">
      <TransitionChild
        as="template"
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-black/25" />
      </TransitionChild>

      <div class="fixed inset-0 overflow-y-auto">
        <div class="flex min-h-full items-center justify-center p-4">
          <TransitionChild
            as="template"
            enter="duration-300 ease-out"
            enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95"
          >
            <DialogPanel class="w-full max-w-5xl transform overflow-hidden bg-white dark:bg-zinc-900 shadow-xl transition-all">
              <div class="p-8">
                <div v-if="refund" class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <!-- Left Column -->
                  <div class="space-y-6">
                    <!-- Header Section -->
                    <div class="space-y-4">
                      <div class="flex items-center justify-between">
                        <h1 class="text-2xl font-bold text-zinc-900 dark:text-zinc-100">
                          {{ refund.refund_reference }}
                        </h1>

                      </div>
                      <div class="text-sm text-zinc-600 dark:text-zinc-400">
                        <span>Request date {{ formatDateShort(refund.created_at) }}</span>
                        <span class="mx-2">•</span>
                        <span>From <span class="font-medium">{{ refund.user?.name }}</span></span>
                      </div>
                      <div class="flex items-center space-x-3">
                          <span :class="getStatusBadgeClass(refund.status)" class="rounded-full inline-flex items-center px-2 py-1 text-xs font-medium">
                            <Icon :icon="getStatusIcon(refund.status)" class="w-3 h-3 mr-1" />
                            {{ getStatusText(refund.status) }}
                          </span>
                          <span class="rounded-full inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                            <Icon icon="heroicons:check-circle" class="w-3 h-3 mr-1" />
                            Received
                          </span>
                        </div>
                    </div>

                    <div class="space-y-4">
                      <h2 class="text-lg font-semibold text-zinc-900 dark:text-zinc-100">Product</h2>
                      <div class="flex items-start space-x-4 p-4 bg-zinc-50 dark:bg-zinc-800">
                        <div class="w-16 h-16 bg-zinc-200 dark:bg-zinc-700 flex items-center justify-center">
                          <Icon icon="heroicons:ticket" class="w-8 h-8 text-zinc-500" />
                        </div>
                        <div class="flex-1">
                          <h3 class="font-medium text-zinc-900 dark:text-zinc-100">
                            {{ refund.ticket_purchase?.event?.title }}
                          </h3>
                          <p class="text-sm text-zinc-600 dark:text-zinc-400">
                            SKU: {{ refund.ticket_purchase?.ticket?.id || 'N/A' }}
                          </p>
                          <p class="text-sm text-zinc-600 dark:text-zinc-400">
                            {{ refund.ticket_purchase?.ticket?.name }} • Quantity 1
                          </p>
                        </div>
                      </div>
                    </div>

                    <div class="space-y-4">
                      <h2 class="text-lg font-semibold text-zinc-900 dark:text-zinc-100">Return Reason</h2>
                      <div class="p-4 bg-orange-50 dark:bg-orange-900/20 border-l-4 border-orange-400">
                        <p class="text-sm text-zinc-900 dark:text-zinc-100">
                          {{ refund.reason || 'The product quality does not meet expectations.' }}
                        </p>
                      </div>
                    </div>

                    <!-- Return Proof -->
                    <div v-if="refund.admin_notes" class="space-y-4">
                      <h2 class="text-lg font-semibold text-zinc-900 dark:text-zinc-100">Return Proof</h2>
                      <div class="p-4 bg-zinc-100 dark:bg-zinc-800">
                        <p class="text-sm text-zinc-900 dark:text-zinc-100">{{ refund.admin_notes }}</p>
                      </div>
                    </div>

                    <!-- Payment Refunded -->
                    <div class="space-y-4">
                      <div class="flex items-center justify-between">
                        <h2 class="text-lg font-semibold text-zinc-900 dark:text-zinc-100">Payment Refunded</h2>
                        <span v-if="refund.status !== 'completed'" class="text-xs text-zinc-500 dark:text-zinc-400">
                          <Icon icon="heroicons:x-circle" class="w-4 h-4 inline mr-1" />
                          Not Refunded
                        </span>
                      </div>
                      <div class="space-y-2">
                        <div class="flex justify-between">
                          <span class="text-sm text-zinc-600 dark:text-zinc-400">Subtotal</span>
                          <span class="text-sm font-medium text-zinc-900 dark:text-zinc-100">
                            MK{{ formatNumber(refund.original_amount) }}
                          </span>
                        </div>
                        <div class="flex justify-between">
                          <span class="text-sm text-zinc-600 dark:text-zinc-400">Discount</span>
                          <span class="text-sm font-medium text-zinc-900 dark:text-zinc-100">MK0.00</span>
                        </div>
                        <div class="border-t border-zinc-200 dark:border-zinc-700 pt-2">
                          <div class="flex justify-between">
                            <span class="font-semibold text-zinc-900 dark:text-zinc-100">Total</span>
                            <span class="font-semibold text-zinc-900 dark:text-zinc-100">
                              MK{{ formatNumber(refund.refund_amount) }}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Right Column -->
                  <div class="space-y-6">
                    <!-- Order Details -->
                    <div class="space-y-4">
                      <h2 class="text-lg font-semibold text-zinc-900 dark:text-zinc-100">Order Details</h2>
                      <div class="space-y-3">
                        <div class="flex items-center justify-between">
                          <span class="text-sm text-zinc-600 dark:text-zinc-400">Order ID</span>
                          <div class="flex items-center space-x-2">
                            <Icon icon="heroicons:document-duplicate" class="w-4 h-4 text-zinc-400" />
                            <span class="text-sm font-medium text-zinc-900 dark:text-zinc-100">
                              Order-{{ refund.ticket_purchase?.id || 'N/A' }}
                            </span>
                          </div>
                        </div>
                        <div class="flex items-center justify-between">
                          <span class="text-sm text-zinc-600 dark:text-zinc-400">Status Order</span>
                          <div class="flex items-center space-x-2">
                            <span class="rounded-full inline-flex items-center px-2 py-1 text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                              <Icon icon="heroicons:check-circle" class="w-3 h-3 mr-1" />
                              Paid
                            </span>
                            <span class="rounded-full inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                              <Icon icon="heroicons:check-circle" class="w-3 h-3 mr-1" />
                              Fulfilled
                            </span>
                          </div>
                        </div>
                        <div class="flex items-center justify-between">
                          <span class="text-sm text-zinc-600 dark:text-zinc-400">Order Date</span>
                          <div class="flex items-center space-x-2">
                            <Icon icon="heroicons:calendar" class="w-4 h-4 text-zinc-400" />
                            <span class="text-sm font-medium text-zinc-900 dark:text-zinc-100">
                              {{ formatDateShort(refund.ticket_purchase?.created_at || refund.created_at) }}
                            </span>
                          </div>
                        </div>
                        <div class="flex items-center justify-between">
                          <span class="text-sm text-zinc-600 dark:text-zinc-400">Total Amount</span>
                          <div class="flex items-center space-x-2">
                            <Icon icon="heroicons:currency-dollar" class="w-4 h-4 text-zinc-400" />
                            <span class="text-sm font-medium text-zinc-900 dark:text-zinc-100">
                              MK{{ formatNumber(refund.original_amount) }}
                            </span>
                          </div>
                        </div>
                        <div class="flex items-center justify-between">
                          <span class="text-sm text-zinc-600 dark:text-zinc-400">Order Channel</span>
                          <div class="flex items-center space-x-2">
                            <Icon icon="heroicons:building-storefront" class="w-4 h-4 text-zinc-400" />
                            <span class="text-sm font-medium text-zinc-900 dark:text-zinc-100">EventaHub</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="space-y-4">
                      <h2 class="text-lg font-semibold text-zinc-900 dark:text-zinc-100">Customer Details</h2>
                      <div class="flex items-start space-x-3">
                        <div class="w-10 h-10 rounded-full bg-gradient-to-br from-red-500 to-purple-600 flex items-center justify-center text-white font-medium text-sm">
                          {{ refund.user?.name?.charAt(0)?.toUpperCase() || 'U' }}
                        </div>
                        <div class="flex-1">
                          <h3 class="font-medium text-zinc-900 dark:text-zinc-100">{{ refund.user?.name }}</h3>
                          <div class="flex items-center space-x-2">
                            <button class="text-sm font-thin">
                              {{ refund.user?.email }}
                            </button>
                            <button v-if="refund.user?.phone" class="px-3 py-1 text-xs bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400 hover:bg-blue-200 dark:hover:bg-blue-900/30">
                              {{ refund.user?.phone || '+(22)-789-907' }}
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div v-if="refund.processed_at || refund.rejection_reason" class="space-y-4">
                      <h2 class="text-lg font-semibold text-zinc-900 dark:text-zinc-100">Processing Information</h2>
                      <div class="space-y-3">
                        <div v-if="refund.processed_at">
                          <span class="text-sm text-zinc-600 dark:text-zinc-400">Processed Date:</span>
                          <span class="text-sm font-medium text-zinc-900 dark:text-zinc-100 ml-2">
                            {{ formatDate(refund.processed_at) }}
                          </span>
                        </div>
                        <div v-if="refund.processed_by">
                          <span class="text-sm text-zinc-600 dark:text-zinc-400">Processed By:</span>
                          <span class="text-sm font-medium text-zinc-900 dark:text-zinc-100 ml-2">
                            {{ refund.processed_by?.name || 'System' }}
                          </span>
                        </div>
                        <div v-if="refund.rejection_reason">
                          <span class="text-sm text-zinc-600 dark:text-zinc-400">Rejection Reason:</span>
                          <p class="text-sm text-red-600 dark:text-red-400 mt-1">{{ refund.rejection_reason }}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="px-8 py-6 border-t border-zinc-200 dark:border-zinc-700 bg-zinc-50 dark:bg-zinc-800">
                <div class="flex items-center justify-between">
                  <div class="text-sm text-zinc-600 dark:text-zinc-400">
                  </div>
                  <div class="flex items-center space-x-3">
                    <template v-if="refund?.status === 'pending'">
                      <button
                        @click="handleReject"
                        :disabled="processing"
                        class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-zinc-200 bg-white dark:bg-zinc-700 border border-gray-300 dark:border-zinc-600 hover:bg-gray-50 dark:hover:bg-zinc-600 transition focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {{ processing ? 'Processing...' : 'Reject' }}
                      </button>
                      <button
                        @click="handleApprove"
                        :disabled="processing"
                        class="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 transition focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {{ processing ? 'Processing...' : 'Approve Refund' }}
                      </button>
                    </template>
                      <button
                        @click="open = false"
                        class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-zinc-200 bg-white dark:bg-zinc-700 border border-gray-300 dark:border-zinc-600 hover:bg-gray-50 dark:hover:bg-zinc-600 transition focus:outline-none focus:ring-2 focus:ring-red-500"
                      >
                        Close
                      </button>
                  </div>
                </div>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
} from '@headlessui/vue'

interface Props {
  refund: any
}

const open = ref<boolean>(false)

interface Emits {
  (e: 'approve', data: { admin_notes: string }): void
  (e: 'reject', data: { rejection_reason: string; admin_notes?: string }): void
}

defineProps<Props>()
const emit = defineEmits<Emits>()

const processing = ref(false)
const adminNotes = ref('')

const handleApprove = (): void => {
  emit('approve', { admin_notes: adminNotes.value })
}

const handleReject = (): void => {
  emit('reject', {
    rejection_reason: 'Rejected by host',
    admin_notes: adminNotes.value
  })
}

const formatNumber = (value: number): string => {
  return new Intl.NumberFormat().format(value || 0)
}

const formatDate = (date: string): string => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatDateShort = (date: string): string => {
  return new Date(date).toLocaleDateString('en-US', {
    month: 'short',
    day: '2-digit',
    year: 'numeric'
  })
}

const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    pending: 'Approved',
    approved: 'Approved',
    rejected: 'Rejected',
    completed: 'Completed'
  }
  return statusMap[status] || status
}

const getStatusIcon = (status: string): string => {
  const iconMap: Record<string, string> = {
    pending: 'heroicons:check-circle',
    approved: 'heroicons:check-circle',
    rejected: 'heroicons:x-circle',
    completed: 'heroicons:check-circle'
  }
  return iconMap[status] || 'heroicons:clock'
}

const getStatusBadgeClass = (status: string): string => {
  const classMap = {
    pending: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    approved: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    rejected: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
    completed: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
  }
  return classMap[status as keyof typeof classMap] || 'bg-zinc-100 text-zinc-800 dark:bg-zinc-700 dark:text-zinc-300'
}

watch(() => open.value, (isOpen) => {
  if (isOpen) {
    adminNotes.value = ''
  }
})
</script>
