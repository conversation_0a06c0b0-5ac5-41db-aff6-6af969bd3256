<template>
  <div class="min-h-screen">
    <div class="p-4 sm:p-6">
      <div class="mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 class="text-2xl font-bold dashboard-text-primary">Security Settings</h1>
            <p class="mt-1 text-sm dashboard-text-muted">Manage your password and account security</p>
          </div>
        </div>
      </div>

      <div class="dashboard-bg-card dashboard-shadow overflow-hidden mb-6">
        <div class="px-6 py-4 dashboard-border border-b">
          <h3 class="text-lg font-medium dashboard-text-primary">Change Password</h3>
        </div>
        <div class="p-6">

          <div v-if="settingsStore.loading" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="md:col-span-1">
                <div class="space-y-2">
                  <div class="h-4 w-32 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                  <div class="h-10 w-full bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                </div>
              </div>
              <div class="md:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-2">
                  <div class="h-4 w-32 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                  <div class="h-10 w-full bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                </div>
                <div class="space-y-2">
                  <div class="h-4 w-40 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                  <div class="h-10 w-full bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                </div>
              </div>
            </div>
            <div class="mt-4">
              <div class="h-10 w-40 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            </div>
          </div>

          <FormKit v-else type="form" id="passwordForm" :actions="false" @submit="changePassword"
            v-model="passwordForm">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="md:col-span-1">
                <FormKit type="password" name="currentPassword" label="Current Password" validation="required"
                  prefixIcon="password" placeholder="••••••••" />
              </div>
              <div class="md:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormKit type="password" name="newPassword" label="New Password" validation="required|length:8"
                  validation-visibility="live" placeholder="••••••••" prefixIcon="password" />

                <FormKit type="password" name="confirmPassword" label="Confirm New Password"
                  validation="required|confirm:newPassword" validation-visibility="live" prefixIcon="password"
                  placeholder="••••••••" />
              </div>
            </div>
            <div class="mt-4">
              <CorePrimaryButton text="Update Password" type="submit" :loading="isChangingPassword" />
            </div>
          </FormKit>
        </div>
      </div>

      <div class="dashboard-bg-card dashboard-shadow overflow-hidden mb-6">
        <div class="px-6 py-4 dashboard-border border-b">
          <h3 class="text-lg font-medium dashboard-text-primary">Two-Factor Authentication</h3>
        </div>
        <div class="p-6">

          <div v-if="settingsStore.loading" class="space-y-4">
            <div class="h-4 w-full bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            <div class="h-10 w-64 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mt-4"></div>
          </div>


          <template v-else>
            <p class="dashboard-text-muted text-sm mb-4">
              Add an extra layer of security to your account by enabling two-factor authentication.
              When enabled, you'll receive a verification code via email when logging in from a new device.
            </p>
            <div class="flex items-center">
              <CorePrimaryButton
                :text="settingsStore.twoFactorEnabled ? 'Disable Two-Factor Authentication' : 'Enable Two-Factor Authentication'"
                @click="toggleTwoFactor" start-icon="mdi:two-factor-authentication" :loading="twoFactorLoading"
                :variant="settingsStore.twoFactorEnabled ? 'outlined' : 'filled'"
                :color="settingsStore.twoFactorEnabled ? 'neutral' : 'primary'" />
            </div>
            <div v-if="settingsStore.twoFactorEnabled" class="mt-4 p-4 bg-green-50 dark:bg-green-900 rounded-md">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><g fill="none"><path fill="url(#fluentColorShield240)" d="M3 5.75A.75.75 0 0 1 3.75 5c2.663 0 5.258-.943 7.8-2.85a.75.75 0 0 1 .9 0C14.992 4.057 17.587 5 20.25 5a.75.75 0 0 1 .75.75V11c0 5.001-2.958 8.676-8.725 10.948a.75.75 0 0 1-.55 0C5.958 19.676 3 16 3 11z"/><defs><radialGradient id="fluentColorShield240" cx="0" cy="0" r="1" gradientTransform="matrix(27.64308 38.74988 -34.23049 24.41907 -4.072 -9.25)" gradientUnits="userSpaceOnUse"><stop offset=".338" stop-color="#0fff2c"/><stop offset=".529" stop-color="#36f240"/><stop offset=".682" stop-color="#50e25d"/><stop offset=".861" stop-color="#23d1a5"/></radialGradient></defs></g></svg>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-green-800 dark:text-green-200">Two-factor authentication is
                    enabled</h3>
                  <div class="mt-2 text-sm text-green-700 dark:text-green-300">
                    <p>Your account is now protected with two-factor authentication. When logging in from a new device
                      or browser, you'll receive a verification code via email.</p>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>

      <div class="dashboard-bg-card dashboard-shadow overflow-hidden">
        <div class="px-6 py-4 dashboard-border border-b flex justify-between items-center">
          <h3 class="text-lg font-medium dashboard-text-primary">Login Sessions</h3>
          <button @click="loadSessions"
            class="text-sm text-sky-500 hover:text-sky-600 dashboard-transition flex items-center"
            :disabled="settingsStore.loading">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd"
                d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                clip-rule="evenodd" />
            </svg>
            <span v-if="settingsStore.loading">Refreshing...</span>
            <span v-else>Refresh</span>
          </button>
        </div>
        <div class="p-6">
          <p class="dashboard-text-muted text-sm mb-4">
            These are the devices that have logged into your account. Revoke any sessions that you do not recognize.
          </p>


          <div v-if="settingsStore.loading" class="space-y-4">
            <div v-for="i in 3" :key="i" class="dashboard-bg-hover p-4 flex items-center justify-between animate-pulse">
              <div class="flex-1">
                <div class="flex items-center">
                  <div class="h-5 w-5 bg-gray-200 dark:bg-gray-700 rounded mr-2"></div>
                  <div class="h-4 w-40 bg-gray-200 dark:bg-gray-700 rounded"></div>
                </div>
                <div class="mt-2 flex space-x-4">
                  <div class="h-3 w-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
                  <div class="h-3 w-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
                </div>
              </div>
              <div>
                <div class="h-6 w-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
            </div>
          </div>

          <div v-else class="space-y-4">
            <div v-if="sessions.length === 0" class="text-center py-4">
              <p class="dashboard-text-muted">No active sessions found.</p>
            </div>

            <div v-for="session in sessions" :key="session.id"
              class="dashboard-bg-hover p-4 flex items-center justify-between">
              <div>
                <div class="flex items-center">
                  <svg v-if="session.device.toLowerCase().includes('mobile')" xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 dashboard-text-light mr-2" viewBox="0 0 32 32">
                    <path fill="currentColor"
                      d="M13.5 24a.5.5 0 0 0 0 1h5a.5.5 0 0 0 0-1zM10.25 2A3.25 3.25 0 0 0 7 5.25v21.5A3.25 3.25 0 0 0 10.25 30h11.5A3.25 3.25 0 0 0 25 26.75V5.25A3.25 3.25 0 0 0 21.75 2zM8 5.25A2.25 2.25 0 0 1 10.25 3h11.5A2.25 2.25 0 0 1 24 5.25v21.5A2.25 2.25 0 0 1 21.75 29h-11.5A2.25 2.25 0 0 1 8 26.75z" />
                  </svg>
                  <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 dashboard-text-light mr-2"
                    viewBox="0 0 48 48">
                    <path fill="currentColor"
                      d="M8.25 5A4.25 4.25 0 0 0 4 9.25v23.5A4.25 4.25 0 0 0 8.25 37H17v3.5h-2.75a1.25 1.25 0 1 0 0 2.5h19.5a1.25 1.25 0 1 0 0-2.5H31V37h8.75A4.25 4.25 0 0 0 44 32.75V9.25A4.25 4.25 0 0 0 39.75 5zM28.5 37v3.5h-9V37zM6.5 9.25c0-.966.784-1.75 1.75-1.75h31.5c.967 0 1.75.784 1.75 1.75v23.5a1.75 1.75 0 0 1-1.75 1.75H8.25a1.75 1.75 0 0 1-1.75-1.75z" />
                  </svg>
                  <span class="text-sm font-medium dashboard-text-primary">{{ session.device }} - {{ session.browser
                    }}</span>
                </div>
                <div class="mt-1 text-xs dashboard-text-muted">
                  <span>IP: {{ session.ip }}</span>
                  <span class="mx-2">•</span>
                  <span>Last active: {{ session.lastActive }}</span>
                </div>
              </div>
              <div>
                <span v-if="session.isCurrent"
                  class="inline-flex items-center px-2.5 py-0.5 text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                  Current Session
                </span>
                <button v-else @click="openRevokeDialog(session.id)"
                  class="dashboard-text-brand hover:text-red-800 dashboard-transition text-sm">
                  Revoke
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <TransitionRoot appear :show="isRevokeDialogOpen" as="template">
    <Dialog as="div" @close="closeRevokeDialog" class="relative z-10">
      <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
        leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
        <div class="fixed inset-0 bg-black bg-opacity-25 dark:bg-opacity-40" />
      </TransitionChild>

      <div class="fixed inset-0 overflow-y-auto">
        <div class="flex min-h-full items-center justify-center p-4 text-center">
          <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95">
            <DialogPanel
              class="w-full max-w-md transform overflow-hidden dashboard-bg-card p-6 text-left align-middle shadow-xl transition-all">
              <DialogTitle as="h3" class="text-lg font-medium leading-6 dashboard-text-primary">
                Revoke Session
              </DialogTitle>
              <div class="mt-2">
                <p class="text-sm dashboard-text-muted">
                  Are you sure you want to revoke this session? This will log out the device from your account.
                </p>
              </div>

              <div class="mt-4 flex justify-end space-x-3">
                <button type="button"
                  class="inline-flex justify-center border dashboard-border px-4 py-2 text-sm font-medium dashboard-text-primary dashboard-transition hover:bg-gray-100 dark:hover:bg-gray-800 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"
                  @click="closeRevokeDialog">
                  Cancel
                </button>
                <CorePrimaryButton :text="settingsStore.saving ? 'Revoking...' : 'Revoke Session'"
                  @click="confirmRevokeSession" :disabled="settingsStore.saving" :loading="settingsStore.saving">
                </CorePrimaryButton>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useVendorSettingsStore } from '@/store/vendorSettings';
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue';
import type { SecuritySession } from '@/store/vendorSettings';
import { useAuthStore } from '@/store/auth';

definePageMeta({
  layout: 'vendor-dashboard'
});

useHead({
  title: "Security | Vendor Dashboard"
});

const settingsStore = useVendorSettingsStore();
const authStore = useAuthStore();
const nuxtApp = useNuxtApp();
const $toast = nuxtApp.$toast as {
  success: (message: string) => void;
  error: (message: string) => void;
  info: (message: string) => void;
  warning: (message: string) => void;
};

interface PasswordForm {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

const isChangingPassword = ref < boolean > (false);
const twoFactorLoading = ref < boolean > (false);
const isRevokeDialogOpen = ref < boolean > (false);
const sessionToRevoke = ref < string | null > (null);

const passwordForm = ref < PasswordForm > ({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
});

const sessions = computed < SecuritySession[] > (() => {
  return Array.isArray(settingsStore.securitySessions)
    ? settingsStore.securitySessions
    : [];
});

const loadSessions = async (): Promise<void> => {
  try {
    await settingsStore.fetchSecuritySessions();
  } catch (error: any) {
    console.error('Error loading security sessions:', error);
    $toast.error('Failed to load security sessions');
  }
};

const loadTwoFactorStatus = async (): Promise<void> => {
  try {
    await settingsStore.fetchTwoFactorStatus();
  } catch (error: any) {
    console.error('Error loading two-factor status:', error);
    $toast.error('Failed to load two-factor authentication status');
  }
};

const changePassword = async (): Promise<void> => {
  if (passwordForm.value.newPassword !== passwordForm.value.confirmPassword) {
    $toast.error('Passwords do not match');
    return;
  }

  isChangingPassword.value = true;

  try {
    const success: boolean = await settingsStore.changePassword(
      passwordForm.value.currentPassword,
      passwordForm.value.newPassword
    );

    if (success) {
      $toast.success('Password changed successfully');
      passwordForm.value = {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      };
    } else {
      $toast.error('Failed to change password');
    }
  } catch (error: any) {
    console.error('Error changing password:', error);
    $toast.error('Failed to change password');
  } finally {
    isChangingPassword.value = false;
  }
};

const toggleTwoFactor = async (): Promise<void> => {
  twoFactorLoading.value = true;

  try {
    let success: boolean;
    if (settingsStore.twoFactorEnabled) {
      success = await settingsStore.disableTwoFactor();
      if (success) {
        $toast.success('Two-factor authentication disabled');
      } else {
        $toast.error('Failed to disable two-factor authentication');
      }
    } else {
      success = await settingsStore.enableTwoFactor();
      if (success) {
        $toast.success('Two-factor authentication enabled');
      } else {
        $toast.error('Failed to enable two-factor authentication');
      }
    }
  } catch (error: any) {
    console.error('Error toggling two-factor authentication:', error);
    $toast.error('Failed to update two-factor authentication');
  } finally {
    twoFactorLoading.value = false;
  }
};

const openRevokeDialog = (sessionId: string): void => {
  sessionToRevoke.value = sessionId;
  isRevokeDialogOpen.value = true;
};

const closeRevokeDialog = (): void => {
  isRevokeDialogOpen.value = false;
  sessionToRevoke.value = null;
};

const confirmRevokeSession = async (): Promise<void> => {
  if (!sessionToRevoke.value) return;

  try {
    const success: boolean = await settingsStore.revokeSession(sessionToRevoke.value);

    if (success) {
      $toast.success('Session revoked successfully');
      closeRevokeDialog();
      authStore.logout();
    } else {
      $toast.error('Failed to revoke session');
    }
  } catch (error: any) {
    console.error('Error revoking session:', error);
    $toast.error('Failed to revoke session');
  }
};

onMounted(async () => {
  await Promise.all([
    loadSessions(),
    loadTwoFactorStatus()
  ]);
});
</script>
