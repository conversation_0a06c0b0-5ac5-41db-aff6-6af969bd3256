<template>
  <div class="space-y-6 shadow bg-white dark:bg-zinc-800">
    <div class="border-b border-gray-200 dark:border-zinc-700 p-4">
      <h2 class="text-xl font-bold text-gray-900 dark:text-white">
        Profile Settings
      </h2>
      <p class="text-gray-500 dark:text-gray-400 mt-1">
        Update your personal information and profile details
      </p>
    </div>

    <div v-if="loading" class="p-6">
      <div class="animate-pulse space-y-6">
        <div class="flex items-center space-x-4">
          <div
            class="h-16 w-16 bg-gray-200 dark:bg-zinc-700 rounded-full"
          ></div>
          <div class="h-8 w-24 bg-gray-200 dark:bg-zinc-700"></div>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="h-12 bg-gray-200 dark:bg-zinc-700"></div>
          <div class="h-12 bg-gray-200 dark:bg-zinc-700"></div>
        </div>
        <div class="h-12 bg-gray-200 dark:bg-zinc-700"></div>
        <div class="h-12 bg-gray-200 dark:bg-zinc-700"></div>
      </div>
    </div>

    <FormKit
      v-else-if="profileForm.email"
      @submit="updateProfile"
      type="form"
      :actions="false"
      class="space-y-6 p-4"
    >
      <div class="bg-gradient-to-br p-6">
        <div class="flex items-center space-x-6">
          <div class="relative group cursor-pointer" @click="openFilePicker">
            <img
              :src="
                profileForm.avatar
                  ? `${config.public.baseUrl}storage/avatars/${profileForm.avatar}`
                  : '/default-avatar.png'
              "
              :alt="profileForm.name || 'Profile'"
              class="h-20 w-20 rounded-full object-cover border-4 border-white dark:border-zinc-700 shadow-lg group-hover:shadow-xl transition-shadow duration-300"
            />
            <div
              class="absolute inset-0 rounded-full bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center cursor-pointer"
              title="Click to change profile photo"
            >
              <Icon
                icon="heroicons:camera"
                class="w-6 h-6 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              />
            </div>
          </div>
          <div class="flex-1">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Update Profile Photo
            </h3>
            <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
              Choose a professional photo that represents you well
            </p>
            <button
              type="button"
              @click="openFilePicker"
              :disabled="uploading"
              class="px-6 py-2 bg-red-600 text-white hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors shadow-md hover:shadow-lg flex items-center"
            >
              <Icon
                v-if="uploading"
                icon="heroicons:arrow-path"
                class="w-4 h-4 mr-2 animate-spin"
              />
              <Icon v-else icon="heroicons:camera" class="w-4 h-4 mr-2" />
              {{ uploading ? "Uploading..." : "Change Photo" }}
            </button>
          </div>
        </div>
      </div>

      <div class="space-y-6 p-4">
        <h3
          class="text-lg font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-zinc-700 pb-2"
        >
          Personal Information
        </h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormKit
            type="text"
            v-model="profileForm.name"
            label="Full Name *"
            placeholder="Enter your full name"
            prefixIcon="user"
            validation="required"
          />
          <FormKit
            type="tel"
            v-model="profileForm.phone"
            label="Phone Number"
            placeholder="Enter your phone number"
            prefixIcon="phone"
            validation="required"
          />
        </div>

        <FormKit
          type="email"
          v-model="profileForm.email"
          label="Email Address *"
          placeholder="Enter your email address"
          prefixIcon="email"
          validation="required|email"
        />
      </div>

      <div class="space-y-3 mt-3 p-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
          Social Media Links
        </h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormKit
            type="url"
            v-model="profileForm.twitter_url"
            label="Twitter URL"
            placeholder="https://twitter.com/username"
            prefixIcon="link"
          />
          <FormKit
            type="url"
            v-model="profileForm.facebook_url"
            label="Facebook URL"
            placeholder="https://facebook.com/username"
            prefixIcon="link"
          />
        </div>
      </div>

      <div class="pt-6 flex items-end justify-end p-4">
        <CoreSubmitButton
          icon="heroicons:arrow-path"
          :text="saving ? 'Saving Changes...' : 'Save Changes'"
          :loading="saving"
        />
      </div>
    </FormKit>

    <div v-else class="text-center py-8 p-3">
      <div class="text-gray-500 dark:text-gray-400">
        <Icon
          icon="heroicons:exclamation-triangle"
          class="w-12 h-12 mx-auto mb-4 opacity-50"
        />
        <p class="text-lg font-medium mb-2">No Profile Data Available</p>
        <p class="text-sm">
          Unable to load your profile information. Please try refreshing the
          page.
        </p>
        <button
          @click="fetchUserProfile"
          class="mt-4 px-4 py-2 bg-red-600 text-white hover:bg-red-700 transition-colors"
        >
          <Icon icon="heroicons:arrow-path" class="w-4 h-4 mr-2" />
          Retry
        </button>
      </div>
    </div>

    <input
      ref="photoInput"
      type="file"
      accept="image/*"
      @change="handleFileSelect"
      class="hidden"
    />

    <Teleport to="body">
      <Dialog
        :open="cropDialogOpen"
        @close="closeCropDialog"
        class="relative z-50"
      >
        <div class="fixed inset-0 bg-black/30" aria-hidden="true" />
        <div class="fixed inset-0 flex items-center justify-center p-4">
          <div
            class="relative bg-white dark:bg-zinc-800 max-w-2xl w-full mx-4 p-6 shadow-xl"
          >
            <h3 class="text-xl font-medium mb-4 text-gray-900 dark:text-white">
              Crop Profile Photo
            </h3>

            <div class="max-h-96 overflow-hidden mb-4">
              <Cropper
                ref="cropperRef"
                :src="cropperImage"
                :stencil-props="{ aspectRatio: 1 }"
                image-restriction="stencil"
                class="h-80"
              />
            </div>

            <div class="flex justify-end space-x-3">
              <button
                @click="closeCropDialog"
                type="button"
                class="px-4 py-2 bg-gray-200 dark:bg-zinc-600 text-gray-800 dark:text-white hover:bg-gray-300 dark:hover:bg-zinc-500 transition-colors"
              >
                Cancel
              </button>
              <CorePrimaryButton
                @click="cropAndUpload"
                :disabled="uploading"
                :text="uploading ? 'Uploading...' : 'Apply & Upload'"
                :loading="uploading"
              />
            </div>
          </div>
        </div>
      </Dialog>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits } from "vue";
import { useRuntimeConfig } from "#app";
import { Dialog } from "@headlessui/vue";
import { Cropper } from "vue-advanced-cropper";
import "vue-advanced-cropper/dist/style.css";

defineProps({
  profileForm: {
    type: Object,
    required: true,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  saving: {
    type: Boolean,
    default: false,
  },
  uploading: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["update-profile", "fetch-profile", "photo-change"]);

const config = useRuntimeConfig();
const photoInput = ref<HTMLInputElement>();
const cropperRef = ref();
const cropDialogOpen = ref<boolean>(false);
const cropperImage = ref<string>("");
const selectedFile = ref<File | null>(null);

const updateProfile = (data: any) => {
  emit("update-profile", data);
};

const fetchUserProfile = () => {
  emit("fetch-profile");
};

const openFilePicker = () => {
  photoInput.value?.click();
};

const handleFileSelect = (event: Event) => {
  if (!event || !event.target) return;

  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];

  if (!file) return;

  selectedFile.value = file;
  cropperImage.value = URL.createObjectURL(file);
  cropDialogOpen.value = true;
};

const closeCropDialog = () => {
  cropDialogOpen.value = false;
  cropperImage.value = "";
  selectedFile.value = null;
  if (photoInput.value) {
    photoInput.value.value = "";
  }
};

const cropAndUpload = async () => {
  if (!cropperRef.value || !selectedFile.value) return;

  const result = cropperRef.value.getResult();
  if (!result || !result.canvas) return;

  const { canvas } = result;

  const blob = await new Promise<Blob>((resolve) => {
    canvas.toBlob(
      (blob: Blob | PromiseLike<Blob>) => {
        if (blob) resolve(blob);
      },
      "image/jpeg",
      0.9
    );
  });

  const fileName = selectedFile.value.name || "cropped-profile.jpg";
  const croppedFile = new File([blob], fileName, { type: "image/jpeg" });

  const formData = new FormData();
  formData.append("photo", croppedFile);

  emit("photo-change", formData);
  closeCropDialog();
};
</script>
