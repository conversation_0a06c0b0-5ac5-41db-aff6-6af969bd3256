<template>
  <div class="min-h-screen">
    <div >
      <div class="mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 class="text-2xl font-bold dashboard-text-primary">Notification Settings</h1>
            <p class="mt-1 text-sm dashboard-text-muted">Manage how you receive notifications</p>
          </div>
          <div class="mt-4 md:mt-0 flex space-x-3">
            <CorePrimaryButton
              text="Save Changes"
              @click="saveNotificationSettings"
              :loading="saving"
            />
          </div>
        </div>
      </div>

      <div v-if="loading" class="space-y-6">
        <div class="dashboard-bg-card dashboard-shadow overflow-hidden mb-6">
          <div class="px-6 py-4 dashboard-border border-b">
            <div class="h-6 w-48 bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
          </div>
          <div class="p-6 space-y-4">
            <div class="h-4 w-full bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
            <div class="space-y-4">
              <div v-for="i in 4" :key="i" class="flex items-start">
                <div class="h-5 w-5 bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
                <div class="ml-3 space-y-2 flex-1">
                  <div class="h-4 w-1/3 bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
                  <div class="h-3 w-2/3 bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template v-else>
        <div class="dashboard-bg-card dashboard-shadow overflow-hidden mb-6">
          <div class="px-6 py-4 dashboard-border border-b">
            <h3 class="text-lg font-medium dashboard-text-primary">Email Notifications</h3>
          </div>
          <div class="p-6">
            <p class="dashboard-text-muted text-sm mb-4">
              Choose which email notifications you'd like to receive.
            </p>
            <div class="space-y-4">
              <FormKit
                v-for="setting in emailNotifications"
                :key="setting.id"
                type="checkbox"
                :name="`email-${setting.id}`"
                :label="setting.name"
                :help="setting.description"
                v-model="setting.user_enabled"
                :classes="{
                  outer: 'flex items-start',
                  wrapper: 'flex items-center h-5',
                  input: 'focus:ring-red-500 h-4 w-4 dashboard-text-brand dashboard-border',
                  label: 'ml-3 text-sm font-medium dashboard-text-primary',
                  help: 'ml-3 mt-1 text-sm dashboard-text-muted'
                }"
              />
            </div>
          </div>
        </div>

        <div class="dashboard-bg-card dashboard-shadow overflow-hidden mb-6">
          <div class="px-6 py-4 dashboard-border border-b">
            <h3 class="text-lg font-medium dashboard-text-primary">Push Notifications</h3>
          </div>
          <div class="p-6">
            <p class="dashboard-text-muted text-sm mb-4">
              Choose which push notifications you'd like to receive on your devices.
            </p>
            <div class="space-y-4">
              <FormKit
                v-for="setting in pushNotifications"
                :key="setting.id"
                type="checkbox"
                :name="`push-${setting.id}`"
                :label="setting.name"
                :help="setting.description"
                v-model="setting.user_enabled"
                :classes="{
                  outer: 'flex items-start',
                  wrapper: 'flex items-center h-5',
                  input: 'focus:ring-red-500 h-4 w-4 dashboard-text-brand dashboard-border',
                  label: 'ml-3 text-sm font-medium dashboard-text-primary',
                  help: 'ml-3 mt-1 text-sm dashboard-text-muted'
                }"
              />
            </div>
          </div>
        </div>

        <div class="dashboard-bg-card dashboard-shadow overflow-hidden">
          <div class="px-6 py-4 dashboard-border border-b">
            <h3 class="text-lg font-medium dashboard-text-primary">SMS Notifications</h3>
          </div>
          <div class="p-6">
            <p class="dashboard-text-muted text-sm mb-4">
              Choose which SMS notifications you'd like to receive on your phone.
            </p>
            <div class="space-y-4">
              <FormKit
                v-for="setting in smsNotifications"
                :key="setting.id"
                type="checkbox"
                :name="`sms-${setting.id}`"
                :label="setting.name"
                :help="setting.description"
                v-model="setting.user_enabled"
                :classes="{
                  outer: 'flex items-start',
                  wrapper: 'flex items-center h-5',
                  input: 'focus:ring-red-500 h-4 w-4 dashboard-text-brand dashboard-border',
                  label: 'ml-3 text-sm font-medium dashboard-text-primary',
                  help: 'ml-3 mt-1 text-sm dashboard-text-muted'
                }"
              />
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, computed } from "vue";

interface Setting {
  id: number;
  name: string;
  description: string;
  user_enabled: boolean;
  type?: string;
}

const props = defineProps({
  userSettings: {
    type: Array as () => Setting[],
    required: true,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  saving: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['toggle-setting', 'save-notification-settings']);

const emailNotifications = computed(() =>
  props.userSettings.filter(setting => setting.type === 'email' || !setting.type)
);

const pushNotifications = computed(() =>
  props.userSettings.filter(setting => setting.type === 'push')
);

const smsNotifications = computed(() =>
  props.userSettings.filter(setting => setting.type === 'sms')
);

const toggleSetting = (settingId: number, enabled: boolean) => {
  emit('toggle-setting', { settingId, enabled });
};

const saveNotificationSettings = () => {
  emit('save-notification-settings');
};
</script>
