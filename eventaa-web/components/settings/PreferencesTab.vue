<template>
  <div class="bg-white dark:bg-zinc-800 shadow p-6">
    <div class="mb-6">
      <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
        Preferences
      </h2>
      <p class="text-gray-500 dark:text-gray-400 mt-1">
        Customize your application preferences and settings
      </p>
    </div>

    <div v-if="loading" class="flex justify-center items-center py-12">
      <CoreLoader />
    </div>

    <FormKit
      v-else
      @submit="savePreferences"
      type="form"
      :actions="false"
      class="space-y-6"
    >
      <div class="space-y-6">
        <h3
          class="text-lg font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-zinc-700 pb-2"
        >
          Currency Settings
        </h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Preferred Currency
            </label>
            <Listbox v-model="selectedCurrency" as="div" class="relative">
              <ListboxButton class="relative w-full cursor-pointer bg-white dark:bg-zinc-700 py-2 pl-3 pr-10 text-left border border-gray-300 dark:border-zinc-600 focus:outline-none focus:ring-2 focus:ring-red-500 sm:text-sm text-gray-900 dark:text-white">
                <span class="flex items-center">
                  <Icon icon="heroicons:banknotes" class="h-5 w-5 text-gray-400 mr-2" />
                  <span class="block truncate">{{ selectedCurrency?.name || 'Select Currency' }}</span>
                </span>
                <span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                  <Icon icon="heroicons:chevron-up-down" class="h-5 w-5 text-gray-400" aria-hidden="true" />
                </span>
              </ListboxButton>
              <transition
                leave-active-class="transition duration-100 ease-in"
                leave-from-class="opacity-100"
                leave-to-class="opacity-0"
              >
                <ListboxOptions class="absolute z-10 mt-1 max-h-60 w-full overflow-auto bg-white dark:bg-zinc-700 py-1 text-base shadow-lg ring-0 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                  <ListboxOption v-for="currency in currencies" :key="currency.id" :value="currency" v-slot="{ active, selected }">
                    <div :class="[
                      active ? 'bg-red-600 text-white' : 'text-gray-900 dark:text-white',
                      'relative cursor-pointer select-none py-2 pl-3 pr-9'
                    ]">
                      <div class="flex items-center">
                        <Icon icon="heroicons:banknotes" class="h-5 w-5 mr-2" :class="active ? 'text-white' : 'text-gray-400'" />
                        <span :class="[selected ? 'font-semibold' : 'font-normal', 'block truncate']">
                          {{ currency.name }}
                        </span>
                      </div>
                      <span v-if="selected" :class="[
                        active ? 'text-white' : 'text-red-600',
                        'absolute inset-y-0 right-0 flex items-center pr-4'
                      ]">
                        <Icon icon="heroicons:check" class="h-5 w-5" aria-hidden="true" />
                      </span>
                    </div>
                  </ListboxOption>
                </ListboxOptions>
              </transition>
            </Listbox>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
              This currency will be used for displaying prices and payments
            </p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Current Selection
            </label>
            <div class="p-3 bg-gray-50 dark:bg-zinc-700 border border-gray-200 dark:border-zinc-600">
              <div class="flex items-center">
                <Icon icon="heroicons:banknotes" class="h-6 w-6 text-blue-600 dark:text-blue-400 mr-3" />
                <div>
                  <p class="text-sm font-medium text-gray-900 dark:text-white">
                    {{ selectedCurrency?.name || 'No currency selected' }}
                  </p>
                  <p class="text-xs text-gray-500 dark:text-gray-400">
                    {{ selectedCurrency ? 'Active currency preference' : 'Please select a currency' }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="space-y-6 mt-3">
        <h3
          class="text-lg font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-zinc-700 pb-2"
        >
          Display Settings
        </h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="flex items-center justify-between">
            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                Show Currency Symbol
              </label>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                Display currency symbols in price displays
              </p>
            </div>
            <Switch
              v-model="showCurrencySymbol"
              :class="showCurrencySymbol ? 'bg-red-600' : 'bg-gray-200 dark:bg-zinc-600'"
              class="relative inline-flex rounded-full h-6 w-11 items-center transition-colors focus:outline-none focus:ring-0 focus:ring-red-500 focus:ring-offset-2"
            >
              <span
                :class="showCurrencySymbol ? 'translate-x-6' : 'translate-x-1'"
                class="inline-block h-4 w-4 transform bg-white transition- rounded-full"
              />
            </Switch>
          </div>

        </div>
      </div>

      <div class="pt-6 border-t border-gray-200 dark:border-zinc-700">
        <div class="flex justify-end space-x-3">
          <button
            type="button"
            @click="resetPreferences"
            class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-zinc-700 border border-gray-300 dark:border-zinc-600 hover:bg-gray-50 dark:hover:bg-zinc-600 focus:outline-none focus:ring-0 focus:ring-offset-2 focus:ring-red-500"
          >
            Reset
          </button>
          <CoreSubmitButton
            icon="heroicons:arrow-path"
            :text="saving ? 'Saving...' : 'Save Preferences'"
            :loading="saving"
          />
        </div>
      </div>
    </FormKit>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Switch, Listbox, ListboxButton, ListboxOption, ListboxOptions } from '@headlessui/vue'

interface Currency {
  id: number
  name: string
  created_at: string
  updated_at: string
}

interface Props {
  loading?: boolean
  saving?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  saving: false
})

const emit = defineEmits<{
  'save-preferences': [data: any]
  'fetch-currencies': []
}>()

const { $toast }: any = useNuxtApp()
const httpClient = useHttpClient()

// State
const currencies = ref<Currency[]>([])
const selectedCurrency = ref<Currency | null>(null)
const showCurrencySymbol = ref(true)
const autoConvertPrices = ref(false)

// Methods
const fetchCurrencies = async () => {
  try {
    const response = await httpClient.get(ENDPOINTS.CURRENCIES.ALL) as any
    if (response?.currencies) {
      currencies.value = response.currencies
    }
  } catch (error: any) {
    console.error('Error fetching currencies:', error)
    $toast.error('Failed to load currencies')
  }
}

const fetchUserPreferences = async () => {
  try {
    const response = await httpClient.get(ENDPOINTS.PROFILE.USER) as any
    if (response?.currency_id) {
      const userCurrency = currencies.value.find(c => c.id === response.currency_id)
      if (userCurrency) {
        selectedCurrency.value = userCurrency
      }
    }
  } catch (error: any) {
    console.error('Error fetching user preferences:', error)
  }
}

const savePreferences = async () => {
  try {
    const data = {
      currency_id: selectedCurrency.value?.id || null,
      show_currency_symbol: showCurrencySymbol.value,
      auto_convert_prices: autoConvertPrices.value
    }

    emit('save-preferences', data)
  } catch (error: any) {
    console.error('Error saving preferences:', error)
    $toast.error('Failed to save preferences')
  }
}

const resetPreferences = () => {
  selectedCurrency.value = null
  showCurrencySymbol.value = true
  autoConvertPrices.value = false
}

// Lifecycle
onMounted(async () => {
  await fetchCurrencies()
  await fetchUserPreferences()
})
</script>
