<template>
    <TransitionRoot appear :show="isOpen" as="template">
        <Dialog as="div" @close="closeModal" class="relative z-10">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
                leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                <div class="fixed inset-0 bg-black/25" />
            </TransitionChild>

            <div class="fixed inset-0 overflow-y-auto">
                <div class="flex min-h-full items-center justify-center p-4 text-center">
                    <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                        enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
                        leave-to="opacity-0 scale-95">
                        <DialogPanel
                            class="w-full max-w-md transform overflow-hidden bg-white p-6 text-left align-middle shadow-xl transition-all">
                            <div class="relative flex flex-col items-center justify-center">
                                <div class="mb-4">
                                    <img src="@/assets/illustrations/completed.svg" class="h-72 w-auto object-cover" alt="completed-illustration"/>
                                </div>
                                <button @click="closeModal" class="absolute top-0 right-0">
                                        <Icon icon="ri:close-fill" class="w-6 h-6 text-gray-500 cursor-pointer" />
                                    </button>
                                <DialogTitle as="h3" class="text-xl font-semibold leading-6">
                                    Tickets generated successfully!
                                    
                                </DialogTitle>
                                <div class="mt-2 text-center">
                                    <p class="text-black">
                                        You can now
                                        download them as a PDF for printing and more.
                                    </p>
                                </div>

                                <div class="mt-4">
                                    <button type="button"
                                        class="inline-flex justify-center rounded-none border border-transparent bg-red-100 px-2 py-2 font-medium text-red-600 hover:bg-red-200 focus:outline-none focus-visible:ring-0 focus-visible:ring-red-500 focus-visible:ring-offset-2"
                                        @click="downloadTickets">
                                        <Icon icon="vscode-icons:file-type-pdf2" class="w-6 h-6 mr-2"/>
                                        Download Tickets
                                    </button>
                                </div>
                            </div>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
} from '@headlessui/vue';

const props = defineProps({
    file: {
        required: true,
        type: String
    }
})
const isOpen = ref(false);
const { $toast }: any = useNuxtApp();
const emit = defineEmits(["update:next"])
const closeModal = () => {
    isOpen.value = false;
    emit("update:next", true)
}
const openModal = () => {
    isOpen.value = true;
}

async function downloadTickets(): Promise<void> {
    try {
        const link = document.createElement('a');
        link.href = props.file;
        document.body.appendChild(link);
        link.target = '_blank';
        link.click();
        link.remove();
        $toast.success('File downloaded successfully');
        closeModal();
    } catch (error) {
        $toast.error('Error downloading PDF:', error);
    }finally{
        emit("update:next", true)
    }
}


defineExpose({
    closeModal,
    openModal,
});
</script>