<template>
    <button type="button" @click="openModal"
        class="flex items-center h-10 mt-9 ml-2 space-x-2 bg-red-600 px-2 text-white">
        <Icon icon="ri:add-line" class="w-6 h-6" />
        Add
    </button>
    <TransitionRoot appear :show="isOpen" as="template">
        <Dialog as="div" @close="closeModal" class="relative z-10">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
                leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                <div class="fixed inset-0 bg-black/25" />
            </TransitionChild>

            <div class="fixed inset-0 overflow-y-auto">
                <div class="flex min-h-full items-center justify-center p-4 text-center">
                    <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                        enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
                        leave-to="opacity-0 scale-95">
                        <DialogPanel
                            class="w-full max-w-2xl transform overflow-hidden bg-white text-left align-middle shadow-xl transition-all">
                            <DialogTitle as="h3" class="w-full px-4 py-2 border-b flex items-center justify-between">
                                <h3 class="text-2xl font-semibold">Create a ticket package</h3>
                                <button @click="closeModal">
                                    <Icon icon="iconamoon:close-fill" class="w-6 h-6" />
                                </button>
                            </DialogTitle>

                            <FormKit id="createPackageForm" @submit="onFormSubmit" type="form" submit-label="Update" :actions="false" #default="{ state: { valid } }">
                                <div class="px-5 py-5">
                                    <FormKit type="text" v-model="ticketPackage.name" label="Title" name="name"
                                        placeholder="Enter package name i.e, package" validation="required|string" />
                                    <FormKit type="text" v-model="ticketPackage.seat" label="Seat" name="seat"
                                        placeholder="Enter seat name or title" validation="required|string" />
                                    <FormKit type="number" v-model="ticketPackage.price" label="Price (MWK)"
                                        name="price" placeholder="Enter amount in MWK" validation="required|string" />
                                    <FormKit type="textarea" v-model="ticketPackage.description" label="Description"
                                        name="description" placeholder="Enter description about this package"
                                        validation="required|string" />
                                    <FormKit type="checkbox" v-model="ticketPackage.is_refundable" label="Is refundable?" name="is_refundable"/>
                                    <div class="mt-2">
                                        <h3 class="text-lg font-semibold mb-2">Ticket Banner</h3>
                                        <EventsImagePicker @files-selected="onBannerPicked"
                                        @file-removed="onFileRemoved"/>
                                    </div>
                                </div>

                                <div class="w-full flex items-center justify-end space-x-3 mt-4 px-5 py-2 border-t">
                                    <button>
                                        Clear form
                                    </button>
                                    <CoreSubmitButton :disabled="!valid" :loading="loading"/>
                                </div>
                            </FormKit>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>
</template>

<script setup lang="ts">
import {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
} from '@headlessui/vue'
import type { Package } from '@/types/ticket';

const route = useRoute();
const { $toast }: any = useNuxtApp();
const isOpen = ref<boolean>(false);
const loading = ref<boolean>(false);
const httpClient = useHttpClient();
const ticketBanner = ref();

const emit = defineEmits(["onTier:created"])
const ticketPackage = ref<Package>({
    name: '',
    seat: '',
    description: '',
    price: "0",
    is_refundable: false,
    event_id: Number(route.query.event_id)
})

const closeModal = (): void => {
    isOpen.value = false
}

const onFileRemoved = (e: any) => {
    $toast.warn("Cover art removed, please upload a new one");
}

const onBannerPicked = (e: File[]) => {
    ticketBanner.value = e[0];
}

const openModal = (): void => {
    isOpen.value = true
}

const onFormSubmit = async(): Promise<void> => {
    loading.value = true;
    try{
        const formData = new FormData();
        formData.append("name", ticketPackage.value.name);
        formData.append("seat", ticketPackage.value.seat);
        formData.append("description", ticketPackage.value.description);
        formData.append("price", ticketPackage.value.price);
        formData.append("is_refundable", ticketPackage.value.is_refundable ? "1" : "0");
        formData.append("event_id", String(ticketPackage.value?.event_id));
        formData.append("banner", ticketBanner.value);

        const response = await httpClient.post<{ message: string }>(ENDPOINTS.TIERS.CREATE, formData);
        if(response){
            $toast.success(response.message);
            emit("onTier:created")
            closeModal();
        }
    }catch(e: any){
        console.error("creating ticket package error: ", e);
        $toast.error(e.message.message);
    }finally{
        loading.value = false;
    }
}
</script>