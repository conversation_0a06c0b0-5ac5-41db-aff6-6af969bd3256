<template>
    <div class="col-span-3 relative">
        <div>
            <div v-for="(selection, index) in packageSelections" :key="selection.id"
                class="relative border bg-white px-4 py-2 flex flex-col space-y-3 mb-3">
                <button type="button" @click="removePackageSelection(selection.id)"
                    class="absolute right-0 top-0 py-2.5 px-2 bg-gray-200">
                    <Icon icon="iconamoon:close-fill" class="w-6 h-6" />
                </button>
                <div class="flex items-center space-x-2">
                    <Icon icon="noto:ticket" class="w-8 h-8" />
                    <h3 class="text-lg font-semibold text-yellow-400">
                        {{ selection.selectedPackage }}
                    </h3>
                </div>
                <div class="w-full flex items-center">
                    <div class="w-full">
                        <h3 class="text-lg font-semibold mb-2">Select package</h3>
                        <MultiSelect class="multiselect-red" mode="single" v-model="selection.selectedPackage"
                            :options="availablePackages.map((item) => item.name)" :classes="{
                                container: 'relative border',
                                containerOpen: 'rounded-none',
                                optionSelected: 'bg-red-600 text-white',
                                optionSelectedPointed: 'bg-red-600 text-white',
                            }" @input="handlePackageSelection(selection.id, $event)" />
                    </div>
                    <button class="bg-gray-50 p-1 h-10 mt-9 ml-2">
                        <Icon icon="line-md:edit" class="w-6 h-6" />
                    </button>
                    <button class="bg-gray-50 p-1 h-10 mt-9 ml-2">
                        <Icon icon="iconamoon:trash-light" class="w-6 h-6" />
                    </button>
                    <TicketsAddDialog v-if="selection.selectedPackage === ''" @on-tier:created="getEventsTiers" />
                </div>
                <div class="form-group">
                    <label class="block mb-1">Number of tickets</label>
                    <FormKit type="number" v-model="selection.tickets" class="w-full p-2 border rounded" min="1" />
                </div>
            </div>
        </div>

        <div class="mt-4">
            <button @click="addPackageSelection" type="button"
                class="flex items-center bg-red-100 text-red-600 font-medium hover:text-red-100 px-4 py-2 hover:bg-red-600">
                <Icon icon="noto-v1:package" class="w-5 h-5 mr-2" />
                Add another package
            </button>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { Package } from '@/types/ticket';
import { v4 as uuidv4 } from 'uuid';

const httpClient = useHttpClient();
const route = useRoute();
const { $toast }: any = useNuxtApp();
const availablePackages = ref<Package[]>([]);

const packageSelections = ref([
    {
        id: uuidv4(),
        selectedPackage: '',
        tickets: '1',
    },
]);

const addPackageSelection = () => {
    packageSelections.value.push({
        id: uuidv4(),
        selectedPackage: '',
        tickets: '1',
    });
};

const removePackageSelection = (id: string) => {
    packageSelections.value = packageSelections.value.filter((selection) => selection.id !== id);
    if (packageSelections.value.length === 0) {
        addPackageSelection();
    }
};

const handlePackageSelection = (id: string, selectedPackage: string) => {
    const existingSelection = packageSelections.value.find((selection) => selection.id !== id && selection.selectedPackage === selectedPackage);
    if (existingSelection) {
        $toast.warning(`You have already selected the "${selectedPackage}" package.`);
        packageSelections.value = packageSelections.value.filter((selection) => selection.id !== id);
    } else {
        const index = packageSelections.value.findIndex((selection) => selection.id === id);
        packageSelections.value[index].selectedPackage = selectedPackage;
    }
};

const emit = defineEmits(['update:selections']);
const getEventsTiers = async (): Promise<void> => {
    try {
        const response = await httpClient.get<any>(`${ENDPOINTS.TIERS.READ}/${route.query.event_id}`);
        if (response) {
            availablePackages.value = response.tiers;
        }
    } catch (error) {
        console.log(error);
    }
};

watch(
    packageSelections,
    (newValue) => {
        emit(
            'update:selections',
            newValue.map(({ selectedPackage, tickets }) => ({
                tier: availablePackages.value.find((pckg: Package) => pckg.name === selectedPackage),
                quantity: parseInt(tickets),
            }))
        );
    },
    { deep: true }
);

onMounted(() => {
    getEventsTiers();
});
</script>

<style scoped>
.form-group {
    margin-bottom: 1rem;
}
</style>