<template>
    <TransitionRoot appear :show="isOpen" as="template">
        <Dialog as="div" @close="close" class="relative z-50">
            <TransitionChild
                as="template"
                enter="duration-300 ease-out"
                enter-from="opacity-0"
                enter-to="opacity-100"
                leave="duration-200 ease-in"
                leave-from="opacity-100"
                leave-to="opacity-0"
            >
                <div class="fixed inset-0 bg-black bg-opacity-25" />
            </TransitionChild>

            <div class="fixed inset-0 overflow-y-auto">
                <div class="flex min-h-full items-center justify-center p-4 text-center">
                    <TransitionChild
                        as="template"
                        enter="duration-300 ease-out"
                        enter-from="opacity-0 scale-95"
                        enter-to="opacity-100 scale-100"
                        leave="duration-200 ease-in"
                        leave-from="opacity-100 scale-100"
                        leave-to="opacity-0 scale-95"
                    >
                        <DialogPanel
                            class="w-full max-w-md transform overflow-hidden bg-white dark:bg-zinc-800 p-6 text-left align-middle shadow-xl transition-all"
                        >
                            <div class="flex items-center justify-between mb-6">
                                <DialogTitle
                                    as="h3"
                                    class="text-lg font-semibold leading-6 text-gray-900 dark:text-white"
                                >
                                    Scan QR Code
                                </DialogTitle>
                                <button
                                    @click="close"
                                    class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                                >
                                    <Icon icon="heroicons:x-mark" class="w-5 h-5" />
                                </button>
                            </div>

                            <div class="space-y-6">
                                <!-- Camera Container -->
                                <div class="relative">
                                    <div v-if="!cameraStarted" class="text-center py-8">
                                        <Icon icon="heroicons:camera" class="w-16 h-16 mx-auto text-gray-400 dark:text-gray-500 mb-4" />
                                        <p class="text-gray-500 dark:text-gray-400 mb-4">
                                            Click the button below to start the camera and scan QR codes
                                        </p>
                                        <button
                                            @click="startCamera"
                                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium text-white bg-red-600 hover:bg-red-700"
                                        >
                                            <Icon icon="heroicons:camera" class="w-4 h-4 mr-2" />
                                            Start Camera
                                        </button>
                                    </div>

                                    <div v-else>
                                        <video
                                            ref="videoElement"
                                            autoplay
                                            playsinline
                                            class="w-full h-64 bg-gray-900 object-cover"
                                        ></video>
                                        <canvas ref="canvasElement" class="hidden"></canvas>

                                        <div class="absolute inset-0 flex items-center justify-center pointer-events-none">
                                            <div class="w-48 h-48 border-2 border-red-500 bg-transparent"></div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Manual Input -->
                                <div class="border-t border-gray-200 dark:border-zinc-700 pt-6">
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Or enter ticket UUID manually:
                                    </label>
                                    <div class="flex space-x-2">
                                        <input
                                            v-model="manualInput"
                                            type="text"
                                            placeholder="Enter ticket UUID"
                                            class="flex-1 border-gray-300 dark:border-zinc-600 dark:bg-zinc-700 text-gray-900 dark:text-white focus:border-red-500 focus:ring-red-500"
                                        />
                                        <button
                                            @click="handleManualScan"
                                            :disabled="!manualInput.trim()"
                                            class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium text-white bg-red-600 hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
                                        >
                                            Scan
                                        </button>
                                    </div>
                                </div>

                <!-- Status Messages -->
                <div v-if="scanning" class="text-center py-4">
                    <div class="animate-spin h-8 w-8 border-2 border-red-500 rounded-full border-t-transparent mx-auto mb-2"></div>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Processing scan...</p>
                </div>

                <!-- Success Message -->
                <div v-if="props.scanResult?.success" class="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800">
                    <div class="flex items-center">
                        <Icon icon="heroicons:check-circle" class="w-5 h-5 text-green-600 dark:text-green-400 mr-2" />
                        <p class="text-sm text-green-800 dark:text-green-200">
                            {{ props.scanResult.message }}
                        </p>
                    </div>
                </div>

                <!-- Error Message -->
                <div v-if="props.scanResult && !props.scanResult.success" class="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800">
                    <div class="flex items-center">
                        <Icon icon="heroicons:exclamation-circle" class="w-5 h-5 text-red-600 dark:text-red-400 mr-2" />
                        <p class="text-sm text-red-800 dark:text-red-200">
                            {{ props.scanResult.message }}
                        </p>
                    </div>
                </div>

                <!-- Last Scanned Code (for reference) -->
                <div v-if="lastScanResult && !props.scanResult" class="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800">
                    <div class="flex items-center">
                        <Icon icon="heroicons:qr-code" class="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
                        <p class="text-sm text-blue-800 dark:text-blue-200">
                            Scanned: {{ lastScanResult }}
                        </p>
                    </div>
                </div>                                <!-- Instructions -->
                                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 p-4">
                                    <div class="flex items-start">
                                        <Icon icon="heroicons:information-circle" class="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2 mt-0.5" />
                                        <div class="text-sm text-blue-800 dark:text-blue-200">
                                            <p class="font-medium mb-1">Instructions:</p>
                                            <ul class="list-disc list-inside space-y-1">
                                                <li>Position the QR code within the red square</li>
                                                <li>Ensure good lighting for best results</li>
                                                <li>The scanner will automatically detect and process QR codes</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                <!-- Actions -->
                                <div class="flex space-x-3 pt-4">
                                    <button
                                        v-if="cameraStarted"
                                        @click="stopCamera"
                                        class="flex-1 inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-zinc-600 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-800 hover:bg-gray-50 dark:hover:bg-zinc-700"
                                    >
                                        <Icon icon="heroicons:stop" class="w-4 h-4 mr-2" />
                                        Stop Camera
                                    </button>
                                    <button
                                        @click="close"
                                        class="flex-1 inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-zinc-600 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-800 hover:bg-gray-50 dark:hover:bg-zinc-700"
                                    >
                                        Close
                                    </button>
                                </div>
                            </div>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>
</template>

<script setup lang="ts">
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue'
import { ref, onUnmounted } from 'vue'
import jsQR from 'jsqr'

interface Props {
    isOpen: boolean
    scanResult?: {
        success: boolean
        message: string
    } | null
}

const props = defineProps<Props>()

const emit = defineEmits<{
    close: []
    scan: [qrData: string]
}>()

const videoElement = ref<HTMLVideoElement>()
const canvasElement = ref<HTMLCanvasElement>()
const cameraStarted = ref(false)
const scanning = ref(false)
const lastScanResult = ref('')
const scanError = ref('')
const scanSuccess = ref('')
const manualInput = ref('')
let stream: MediaStream | null = null
let animationId: number | null = null

const startCamera = async () => {
    try {
        stream = await navigator.mediaDevices.getUserMedia({
            video: {
                facingMode: 'environment',
                width: { ideal: 640 },
                height: { ideal: 480 }
            }
        })

        if (videoElement.value) {
            videoElement.value.srcObject = stream
            cameraStarted.value = true
            startScanning()
        }
    } catch (error) {
        console.error('Error accessing camera:', error)
    }
}

const stopCamera = () => {
    if (stream) {
        stream.getTracks().forEach(track => track.stop())
        stream = null
    }

    if (animationId) {
        cancelAnimationFrame(animationId)
        animationId = null
    }

    cameraStarted.value = false
    scanning.value = false
}

const startScanning = () => {
    const scan = () => {
        if (videoElement.value && canvasElement.value && cameraStarted.value) {
            const video = videoElement.value
            const canvas = canvasElement.value
            const context = canvas.getContext('2d')

            if (video.readyState === video.HAVE_ENOUGH_DATA && context) {
                canvas.width = video.videoWidth
                canvas.height = video.videoHeight
                context.drawImage(video, 0, 0, canvas.width, canvas.height)

                const imageData = context.getImageData(0, 0, canvas.width, canvas.height)
                const code = jsQR(imageData.data, imageData.width, imageData.height)

                if (code) {
                    handleScanResult(code.data)
                    return
                }
            }
        }

        if (cameraStarted.value) {
            animationId = requestAnimationFrame(scan)
        }
    }

    scan()
}

const handleScanResult = (qrData: string) => {
    if (scanning.value) return

    scanning.value = true
    scanError.value = ''
    scanSuccess.value = ''
    lastScanResult.value = qrData

    emit('scan', qrData)

    setTimeout(() => {
        scanning.value = false
    }, 500)
}

const handleManualScan = () => {
    if (manualInput.value.trim()) {
        handleScanResult(manualInput.value.trim())
        manualInput.value = ''
    }
}

const close = () => {
    stopCamera()
    lastScanResult.value = ''
    scanError.value = ''
    scanSuccess.value = ''
    manualInput.value = ''
    emit('close')
}

onUnmounted(() => {
    stopCamera()
})
</script>
