<template>
  <button
    @click.stop="isOpen = true"
    class="flex items-center justify-center space-x-2 px-4 py-3 border border-gray-300 dark:border-zinc-600 text-gray-700 dark:text-zinc-300 hover:bg-gray-50 dark:hover:bg-zinc-700 font-medium transition-colors duration-200"
  >
    <Icon icon="heroicons:arrow-uturn-left" class="w-4 h-4" />
    <span>Request Refund</span>
  </button>
  <TransitionRoot appear :show="isOpen" as="template">
    <Dialog as="div" @close="closeModal" class="relative z-50">
      <TransitionChild
        as="template"
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-black/25" />
      </TransitionChild>

      <div class="fixed inset-0 overflow-y-auto">
        <div
          class="flex min-h-full items-center justify-center p-4 text-center"
        >
          <TransitionChild
            as="template"
            enter="duration-300 ease-out"
            enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95"
          >
            <DialogPanel
              class="w-full max-w-md transform overflow-hidden bg-white dark:bg-zinc-800 p-6 text-left align-middle shadow-xl transition-all"
            >
              <DialogTitle
                as="h3"
                class="text-lg font-medium leading-6 text-gray-900 dark:text-zinc-100 mb-4"
              >
                Request Refund
              </DialogTitle>

              <div v-if="ticket" class="mb-6">
                <div class="bg-gray-50 dark:bg-zinc-700 p-4 mb-4">
                  <h4
                    class="font-semibold text-gray-900 dark:text-zinc-100 mb-2"
                  >
                    {{ ticket.event?.title }}
                  </h4>
                  <div
                    class="text-sm text-gray-600 dark:text-zinc-300 space-y-1"
                  >
                    <p>Quantity: {{ ticket.quantity }}</p>
                    <p>
                      Original Amount:
                      {{ formatPrice(ticket.total_amount ?? 0) }}
                    </p>
                    <p
                      v-if="refundAmount"
                      class="font-semibold text-green-600 dark:text-green-400"
                    >
                      Refund Amount: {{ formatPrice(refundAmount) }}
                    </p>
                    <p
                      v-if="refundFee > 0"
                      class="text-sm text-gray-500 dark:text-zinc-400"
                    >
                      Processing Fee: {{ formatPrice(refundFee) }}
                    </p>
                  </div>
                </div>

                <div class="mb-4">
                  <label
                    for="refund-reason"
                    class="block text-sm font-medium text-gray-700 dark:text-zinc-300 mb-2"
                  >
                    Reason for refund <span class="text-red-500">*</span>
                  </label>
                  <textarea
                    id="refund-reason"
                    v-model="refundReason"
                    rows="4"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-gray-900 dark:text-zinc-100 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    placeholder="Please explain why you're requesting a refund..."
                    maxlength="500"
                    required
                  ></textarea>
                  <div class="text-xs text-gray-500 dark:text-zinc-400 mt-1">
                    {{ refundReason.length }}/500 characters
                  </div>
                </div>

                <div
                  class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 p-3 mb-4"
                >
                  <div class="flex">
                    <Icon
                      icon="heroicons:exclamation-triangle"
                      class="w-5 h-5 text-yellow-400 mr-2 mt-0.5"
                    />
                    <div class="text-sm text-yellow-800 dark:text-yellow-200">
                      <p class="font-medium mb-1">Refund Policy Notice:</p>
                      <ul class="list-disc list-inside space-y-1 text-xs">
                        <li>Refunds are processed within 5-10 business days</li>
                        <li>Processing fees may apply as shown above</li>
                        <li>
                          Refunds are not available 24 hours before the event
                        </li>
                        <li>This action cannot be undone</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              <div class="flex justify-end space-x-3">
                <button
                  type="button"
                  class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-zinc-300 bg-gray-100 dark:bg-zinc-700 hover:bg-gray-200 dark:hover:bg-zinc-600 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors duration-200"
                  @click="closeModal"
                  :disabled="loading"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  class="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 flex items-center"
                  @click="submitRefundRequest"
                  :disabled="loading || !refundReason.trim()"
                >
                  <CoreLoader
                    v-if="loading"
                    color="white"
                    width="16"
                    height="16"
                    class="mr-2"
                  />
                  <Icon
                    v-else
                    icon="heroicons:currency-dollar"
                    class="w-4 h-4 mr-2"
                  />
                  {{ loading ? "Processing..." : "Request Refund" }}
                </button>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from "@headlessui/vue";
import { ref, watch, computed } from "vue";

interface TicketPurchase {
  id: number;
  purchase_reference?: string;
  quantity?: number;
  total_amount?: number;
  unit_price?: number;
  event?: {
    id: number;
    title: string;
    [key: string]: any;
  };
  payment?: {
    currency?: {
      code: string;
      symbol: string;
    };
  };
  [key: string]: any;
}

const props = defineProps<{
  ticket: TicketPurchase | null;
  isOpen: boolean;
}>();

const emit = defineEmits(["close", "refund-requested"]);

const loading = ref(false);
const isOpen = ref(false);
const refundReason = ref("");
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const refundAmount = computed(() => {
  if (!props.ticket?.total_amount) return 0;
  const processingFeePercentage =
    props.ticket.ticket?.tier?.refund_fee_percentage ??
    props.ticket.ticket?.refund_fee_percentage ??
    5;
  const fee = (props.ticket.total_amount * processingFeePercentage) / 100;
  return Math.max(0, props.ticket.total_amount - fee);
});

const refundFee = computed(() => {
  if (!props.ticket?.total_amount) return 0;
  return props.ticket.total_amount - refundAmount.value;
});

watch(
  () => props.isOpen,
  (newValue) => {
    isOpen.value = newValue;
    if (newValue) {
      refundReason.value = "";
    }
  }
);

const formatPrice = (amount: number): string => {
  const currency = props.ticket?.payment?.currency;
  const symbol = currency?.symbol || "$";

  const formattedAmount = Number(amount).toLocaleString("en-US", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });

  return `${symbol}${formattedAmount}`;
};

const closeModal = () => {
  if (loading.value) return;
  isOpen.value = false;
  emit("close");
};

const submitRefundRequest = async () => {
  if (!props.ticket?.id || !refundReason.value.trim()) return;

  loading.value = true;

  try {
    const response = await httpClient.post<any>(
      ENDPOINTS.TICKETS.REQUEST_REFUND,
      {
        purchase_id: props.ticket.id,
        reason: refundReason.value.trim(),
      }
    );

    if (response) {
      $toast.success("Refund request submitted successfully");
      emit("refund-requested", {
        ticket: props.ticket,
        refundData: response.data,
      });
      closeModal();
    }
  } catch (error: any) {
    console.error("Error requesting refund:", error);
    handleError(error, $toast);
  } finally {
    loading.value = false;
  }
};
</script>
