<template>
    <TransitionRoot appear :show="isOpen" as="template">
        <Dialog as="div" @close="close" class="relative z-50">
            <TransitionChild
                as="template"
                enter="duration-300 ease-out"
                enter-from="opacity-0"
                enter-to="opacity-100"
                leave="duration-200 ease-in"
                leave-from="opacity-100"
                leave-to="opacity-0"
            >
                <div class="fixed inset-0 bg-black bg-opacity-25" />
            </TransitionChild>

            <div class="fixed inset-0 overflow-y-auto">
                <div class="flex min-h-full items-center justify-center p-4 text-center">
                    <TransitionChild
                        as="template"
                        enter="duration-300 ease-out"
                        enter-from="opacity-0 scale-95"
                        enter-to="opacity-100 scale-100"
                        leave="duration-200 ease-in"
                        leave-from="opacity-100 scale-100"
                        leave-to="opacity-0 scale-95"
                    >
                        <DialogPanel
                            class="w-full max-w-md transform overflow-hidden bg-white dark:bg-zinc-800 p-6 text-left align-middle shadow-xl transition-all"
                        >
                            <div class="flex items-center justify-between mb-6">
                                <DialogTitle
                                    as="h3"
                                    class="text-lg font-semibold leading-6 text-gray-900 dark:text-white"
                                >
                                    Ticket Details
                                </DialogTitle>
                                <button
                                    @click="close"
                                    class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                                >
                                    <Icon icon="heroicons:x-mark" class="w-5 h-5" />
                                </button>
                            </div>

                            <div v-if="ticket && event" class="space-y-6">
                                <!-- Event Cover -->
                                <div class="w-full">
                                    <img
                                        :src="`${runtimeConfig.public.baseUrl}storage/events/${event.cover_art}`"
                                        :alt="event.title"
                                        class="w-full h-32 object-cover bg-gray-200 dark:bg-zinc-700"
                                    />
                                </div>

                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-base font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            Ticket UUID
                                        </label>
                                        <p class="text-sm text-gray-900 dark:text-white font-mono bg-gray-50 dark:bg-zinc-700 p-2">
                                            {{ ticket.uuid }}
                                        </p>
                                    </div>

                                    <div>
                                        <label class="block text-base font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            Status
                                        </label>
                                        <span
                                            class="inline-flex items-center px-2.5 py-0.5 text-xs font-medium"
                                            :class="[
                                                ticket.status === 'scanned'
                                                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                                    : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                                            ]"
                                        >
                                            {{ ticket.status === 'scanned' ? 'Scanned' : 'Unscanned' }}
                                        </span>
                                    </div>

                                    <div>
                                        <label class="block text-base font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            Event
                                        </label>
                                        <p class="text-sm text-gray-900 dark:text-white">{{ event.title }}</p>
                                    </div>

                                    <div>
                                        <label class="block text-base font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            Date & Time
                                        </label>
                                        <p class="text-sm text-gray-900 dark:text-white">
                                            {{ dayjs(event.start).format('MMMM DD, YYYY - h:mm A') }}
                                        </p>
                                    </div>

                                    <div>
                                        <label class="block text-base font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            Location
                                        </label>
                                        <p class="text-sm text-gray-900 dark:text-white">
                                            {{ event.location || 'Online Event' }}
                                        </p>
                                    </div>

                                    <div>
                                        <label class="block text-base font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            Customer
                                        </label>
                                        <div class="text-sm">
                                            <p class="text-gray-900 dark:text-white">{{ ticket.customer }}</p>
                                            <p class="text-gray-500 dark:text-gray-400">{{ ticket.email }}</p>
                                        </div>
                                    </div>

                                    <div>
                                        <label class="block text-base font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            Purchase Date
                                        </label>
                                        <p class="text-sm text-gray-900 dark:text-white">{{ ticket.purchaseDate }}</p>
                                    </div>

                                    <!-- QR Code -->
                                    <div>
                                        <label class="block text-base font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            QR Code
                                        </label>
                                        <div class="flex justify-center bg-white dark:bg-zinc-900 p-4">
                                            <qrcode-vue
                                                :value="ticket.uuid"
                                                :size="128"
                                                level="M"
                                                render-as="svg"
                                            />
                                        </div>
                                    </div>
                                </div>

                                <!-- Actions -->
                                <div class="flex space-x-3 pt-4 border-t border-gray-200 dark:border-zinc-700">
                                    <button
                                        @click="$emit('scan', ticket)"
                                        :disabled="ticket.status === 'scanned'"
                                        :class="[
                                            'flex-1 inline-flex justify-center items-center px-4 py-2 text-sm font-medium border',
                                            ticket.status === 'scanned'
                                                ? 'border-gray-300 dark:border-zinc-600 text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-zinc-700 cursor-not-allowed'
                                                : 'border-transparent text-white bg-red-600 hover:bg-red-700'
                                        ]"
                                    >
                                        <Icon icon="heroicons:qr-code" class="w-4 h-4 mr-2" />
                                        {{ ticket.status === 'scanned' ? 'Already Scanned' : 'Scan Ticket' }}
                                    </button>
                                    <button
                                        @click="close"
                                        class="flex-1 inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-zinc-600 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-800 hover:bg-gray-50 dark:hover:bg-zinc-700"
                                    >
                                        Close
                                    </button>
                                </div>
                            </div>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>
</template>

<script setup lang="ts">
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue'
import dayjs from 'dayjs'
import QrcodeVue from 'qrcode.vue'

interface Props {
    ticket?: any
    event?: any
    isOpen: boolean
}

defineProps<Props>()

const emit = defineEmits<{
    close: []
    scan: [ticket: any]
}>()

const runtimeConfig = useRuntimeConfig()

const close = () => {
    emit('close')
}
</script>
