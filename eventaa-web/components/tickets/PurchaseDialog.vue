<template>
  <TransitionRoot appear :show="isOpen" as="template">
    <Dialog as="div" @close="closeDialog" class="relative z-[9999]">
      <TransitionChild
        as="template"
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-black bg-opacity-25" />
      </TransitionChild>

      <div class="fixed inset-0 overflow-y-auto">
        <div
          class="flex min-h-full items-center justify-center p-4 text-center"
        >
          <TransitionChild
            as="template"
            enter="duration-300 ease-out"
            enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95"
          >
            <DialogPanel
              class="w-full max-w-md transform overflow-hidden bg-white dark:bg-zinc-900 p-6 text-left align-middle shadow-xl transition-all"
            >
              <DialogTitle
                as="h3"
                class="text-lg font-medium leading-6 text-gray-900 dark:text-white py-2 border-b border-zinc-100 dark:border-zinc-700"
              >
                Purchase Tickets
              </DialogTitle>

              <div
                v-if="tickets && currentStep === 'summary'"
                class="space-y-4"
              >
                <div class="space-y-3">
                  <div
                    v-for="ticket in tickets"
                    :key="ticket.ticket_id"
                    class="bg-gray-50 dark:bg-zinc-800 p-3 space-y-2"
                  >
                    <div class="flex justify-between">
                      <span
                        class="text-sm font-medium text-gray-900 dark:text-white"
                        >{{ ticket.name }}</span
                      >
                      <span class="text-sm text-gray-600 dark:text-gray-400"
                        >x{{ ticket.quantity }}</span
                      >
                    </div>
                    <div class="flex justify-between">
                      <span class="text-sm text-gray-600 dark:text-gray-400"
                        >{{ formatCurrency(ticket.price) }} each</span
                      >
                      <span
                        class="text-sm font-medium text-gray-900 dark:text-white"
                        >{{
                          formatCurrency(ticket.price * ticket.quantity)
                        }}</span
                      >
                    </div>
                  </div>
                </div>

                <div
                  class="border-t border-gray-200 dark:border-zinc-700 pt-3 space-y-2"
                >
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400"
                      >Subtotal:</span
                    >
                    <span
                      class="text-sm font-medium text-gray-900 dark:text-white"
                      >{{ formatCurrency(subtotal) }}</span
                    >
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400"
                      >Processing Fee:</span
                    >
                    <span
                      class="text-sm font-medium text-gray-900 dark:text-white"
                      >{{ formatCurrency(fees) }}</span
                    >
                  </div>
                  <div class="flex justify-between font-semibold">
                    <span class="text-gray-900 dark:text-white">Total:</span>
                    <span class="text-gray-900 dark:text-white">{{
                      formatCurrency(total)
                    }}</span>
                  </div>
                </div>

                <div class="flex space-x-3">
                  <button
                    @click="closeDialog"
                    type="button"
                    class="flex-1 px-4 py-2 border border-gray-300 dark:border-zinc-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-800 hover:bg-gray-50 dark:hover:bg-zinc-700 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    @click="currentStep = 'payment'"
                    type="button"
                    class="flex-1 px-4 py-2 bg-red-600 text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors"
                  >
                    Continue to Payment
                  </button>
                </div>
              </div>

              <PayChanguPayment
                v-if="currentStep === 'payment'"
                ref="paymentComponent"
                :amount="subtotal"
                :fees="fees"
                :timeout="1800"
                type="tickets"
                :metadata="{ tickets: tickets }"
                @cancel="currentStep = 'summary'"
                @success="handlePaymentSuccess"
                @error="handlePaymentError"
                @timeout="handlePaymentTimeout"
              />

              <PaymentStatus
                v-if="currentStep === 'status'"
                :status="paymentStatus"
                :payment-data="paymentData"
                :error="paymentError"
                @retry="retryPayment"
                @continue="handlePaymentComplete"
                @cancel="closeDialog"
                @status-update="handleStatusUpdate"
                @ticket-purchase-completed="handleTicketPurchaseCompleted"
              />
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from "@headlessui/vue";
import { ref, computed, watch, onMounted, onUnmounted } from "vue";
import PayChanguPayment from "@/components/payments/PayChanguPayment.vue";
import PaymentStatus from "@/components/payments/PaymentStatus.vue";
import { useAuthStore } from "@/store/auth";

interface Ticket {
  ticket_id: number;
  name: string;
  price: number;
  quantity: number;
}

interface Props {
  isOpen: boolean;
  tickets: Ticket[] | null;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  close: [];
  success: [purchases: any];
}>();
const { $toast, $echo }: any = useNuxtApp();
const authStore = useAuthStore();

const currentStep = ref<"summary" | "payment" | "status">("summary");
const paymentStatus = ref<"pending" | "completed" | "failed" | "cancelled">(
  "pending"
);
const paymentData = ref<any>(null);
const paymentError = ref<string>("");
const paymentComponent = ref<any>(null);

const subtotal = computed(() => {
  if (!props.tickets) return 0;
  return props.tickets.reduce(
    (sum, ticket) => sum + ticket.price * ticket.quantity,
    0
  );
});

const fees = computed(() => {
  return Math.round(subtotal.value * 0.03);
});

const total = computed(() => {
  return subtotal.value + fees.value;
});

watch(
  () => props.isOpen,
  (isOpen) => {
    if (isOpen) {
      currentStep.value = "summary";
      paymentStatus.value = "pending";
      paymentData.value = null;
      paymentError.value = "";
    }
  }
);

const closeDialog = () => {
  emit("close");
};

const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat("en-MW", {
    style: "currency",
    currency: "MWK",
    minimumFractionDigits: 0,
  }).format(amount);
};

const handlePaymentSuccess = async (paymentResponseData: any) => {
  if (!props.tickets) return;

  try {
    currentStep.value = "status";
    paymentStatus.value = "pending";

    paymentData.value = {
      reference: paymentResponseData.reference,
      charge_id: paymentResponseData.charge_id,
      amount: paymentResponseData.amount,
      operator_name: paymentResponseData.operator_name,
      phone_number: paymentResponseData.phone_number,
    };

    $toast.success(
      "Payment initiated successfully! Please check your phone to authorize the payment."
    );
  } catch (error: any) {
    console.error("Ticket purchase payment error:", error);
    paymentStatus.value = "failed";
    paymentError.value =
      error.response?.data?.message || "Payment initialization failed";
    $toast.error(paymentError.value);

    if (paymentComponent.value?.stopTimeout) {
      paymentComponent.value.stopTimeout();
    }
  }
};

const handlePaymentError = (error: string): void => {
  currentStep.value = "status";
  paymentStatus.value = "failed";
  paymentError.value = error;
  $toast.error(error);
};

const handlePaymentTimeout = (paymentFormData: any): void => {
  console.warn("Ticket payment timed out:", paymentFormData);
  currentStep.value = "status";
  paymentStatus.value = "failed";
  paymentError.value = "Payment timed out. Please try again.";
  $toast.error("Payment timed out. Please try again.");
};

const handleStatusUpdate = (status: string): void => {
  paymentStatus.value = status as any;

  if (paymentComponent.value?.stopTimeout) {
    paymentComponent.value.stopTimeout();
  }

  if (status === "completed") {
    $toast.success("Payment completed successfully!");
  } else if (status === "failed") {
    $toast.error("Payment failed. Please try again.");
  }
};

const handleTicketPurchaseCompleted = (event: any): void => {

  if (paymentStatus.value !== "completed") {
    paymentStatus.value = "completed";
  }

  if (paymentComponent.value?.stopTimeout) {
    paymentComponent.value.stopTimeout();
  }

  const ticketCount = event.purchase_count || event.purchases?.length || 0;
  $toast.success(
    `Tickets purchased successfully! ${ticketCount} ticket(s) confirmed.`
  );

  setTimeout(() => {
    $toast.info(
      'Your tickets have been sent to your email with PDF attachment. Check your inbox!'
    );
  }, 1000);

  if (event.pdf_url) {
    downloadTicketPDF(event.pdf_url, event.event_name || 'tickets');
  } else if (event.purchases && event.purchases.length > 0) {
    const firstPurchase = event.purchases[0];
    if (firstPurchase.id) {
      downloadTicketsByPurchaseId(firstPurchase.id);
    }
  }

  setTimeout(() => {
    handlePaymentComplete();
  }, 3000); // Increased timeout to allow for download
};

const retryPayment = (): void => {
  currentStep.value = "payment";
  paymentStatus.value = "pending";
  paymentError.value = "";
};

const handlePaymentComplete = (): void => {
  emit("success", paymentData.value);
  closeDialog();
};

// Function to download PDF from URL
const downloadTicketPDF = (pdfUrl: string, eventName: string): void => {
  try {
    const link = document.createElement('a');
    link.href = pdfUrl;
    link.download = `tickets-${eventName.replace(/[^a-zA-Z0-9]/g, '-')}.pdf`;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    $toast.success('Ticket PDF downloaded successfully!');
  } catch (error) {
    console.error('Error downloading PDF:', error);
    $toast.error('Failed to download ticket PDF');
  }
};

// Function to download tickets by purchase ID
const downloadTicketsByPurchaseId = async (purchaseId: number): Promise<void> => {
  try {
    const httpClient = useHttpClient();
    const response = await httpClient.get(`${ENDPOINTS.TICKETS.DOWNLOAD}/${purchaseId}`, {
      responseType: 'blob'
    }) as Blob;

    // Create download link
    const url = window.URL.createObjectURL(response);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `tickets-${purchaseId}.pdf`);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);

    $toast.success('Ticket PDF downloaded successfully!');
  } catch (error) {
    console.error('Error downloading tickets:', error);
    $toast.error('Failed to download ticket PDF');
  }
};

onMounted(() => {
  if ($echo && authStore.user?.id) {
    try {
      console.info(`Subscribing to tickets channel: tickets.user.${authStore.user.id}`);
      const channelName = `tickets.user.${authStore.user.id}`;
      const channel = $echo.private(channelName);

      channel
        .listen("ticket.purchase.completed", (event: any) => {
          handleTicketPurchaseCompleted(event);
        })
        .listen(".ticket.purchase.completed", (event: any) => {
          handleTicketPurchaseCompleted(event);
        });
    } catch (error) {
      console.error(
        "PurchaseDialog: Failed to subscribe to ticket purchase completion:",
        error
      );
    }
  } else {
    console.warn(
      "PurchaseDialog: Echo not available or user not authenticated:",
      {
        echo: !!$echo,
        user: authStore.user?.id,
      }
    );
  }
});

onUnmounted(() => {
  if ($echo && authStore.user?.id) {
    try {
      $echo.leave(`tickets.user.${authStore.user.id}`);
    } catch (error) {
      console.error(
        "PurchaseDialog: Failed to unsubscribe from ticket purchase completion:",
        error
      );
    }
  }
});
</script>
