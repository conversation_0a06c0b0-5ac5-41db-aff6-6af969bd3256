<template>
  <TransitionRoot appear :show="isOpen" as="template">
    <Dialog as="div" @close="closeModal" class="relative z-10">
      <TransitionChild
        as="template"
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-black bg-opacity-25 dark:bg-opacity-40" />
      </TransitionChild>

      <div class="fixed inset-0 overflow-y-auto">
        <div class="flex min-h-full items-center justify-center p-4 text-center">
          <TransitionChild
            as="template"
            enter="duration-300 ease-out"
            enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95"
          >
            <DialogPanel class="w-full max-w-2xl transform overflow-hidden dashboard-bg-card p-6 text-left align-middle shadow-xl transition-all">
              <DialogTitle as="h3" class="text-lg font-medium leading-6 dashboard-text-primary mb-4">
                Host Request Details
              </DialogTitle>

              <div v-if="request" class="space-y-6">
                <!-- User Info -->
                <div class="flex items-center space-x-4 p-4 dashboard-bg-hover">
                  <div class="flex-shrink-0 h-12 w-12">
                    <div class="h-12 w-12 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center">
                      <span class="text-lg font-medium text-red-600 dark:text-red-300">
                        {{ request.user.name.charAt(0).toUpperCase() }}
                      </span>
                    </div>
                  </div>
                  <div>
                    <h4 class="text-lg font-medium dashboard-text-primary">{{ request.user.name }}</h4>
                    <p class="dashboard-text-muted">{{ request.user.email }}</p>
                  </div>
                  <div class="ml-auto">
                    <span :class="getStatusBadgeClass(request.status)" class="px-3 py-1 text-xs font-medium uppercase tracking-wide">
                      {{ request.status }}
                    </span>
                  </div>
                </div>

                <!-- Request Details -->
                <div>
                  <h5 class="text-sm font-medium dashboard-text-primary mb-3">Request Reason</h5>
                  <div class="dashboard-bg-hover p-4">
                    <p class="text-sm dashboard-text-secondary whitespace-pre-wrap">{{ request.reason }}</p>
                  </div>
                </div>

                <!-- Timestamps -->
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <h5 class="text-sm font-medium dashboard-text-primary mb-2">Requested At</h5>
                    <p class="text-sm dashboard-text-muted">{{ formatDate(request.requested_at) }}</p>
                  </div>
                  <div v-if="request.processed_at">
                    <h5 class="text-sm font-medium dashboard-text-primary mb-2">Processed At</h5>
                    <p class="text-sm dashboard-text-muted">{{ formatDate(request.processed_at) }}</p>
                  </div>
                </div>

                <!-- Admin Notes (if processed) -->
                <div v-if="request.admin_notes" class="dashboard-border border-t pt-6">
                  <h5 class="text-sm font-medium dashboard-text-primary mb-3">Admin Notes</h5>
                  <div class="dashboard-bg-hover p-4">
                    <p class="text-sm dashboard-text-secondary whitespace-pre-wrap">{{ request.admin_notes }}</p>
                    <div v-if="request.processedBy" class="mt-3 pt-3 dashboard-border border-t">
                      <p class="text-xs dashboard-text-muted">
                        Processed by {{ request.processedBy.name }}
                      </p>
                    </div>
                  </div>
                </div>

                <!-- Action Form (for pending requests) -->
                <div v-if="request.status === 'pending'" class="dashboard-border border-t pt-6">
                  <h5 class="text-sm font-medium dashboard-text-primary mb-3">Take Action</h5>

                  <div class="space-y-4">
                    <div>
                      <label for="admin_notes" class="block text-sm font-medium dashboard-text-primary mb-2">
                        Admin Notes {{ actionType === 'reject' ? '(Required)' : '(Optional)' }}
                      </label>
                      <textarea
                        id="admin_notes"
                        v-model="adminNotes"
                        rows="3"
                        class="w-full px-3 py-2 dashboard-border dashboard-bg-input dashboard-text-primary focus:outline-none focus:ring-2 focus:ring-red-500"
                        :placeholder="actionType === 'approve' ? 'Optional notes about the approval...' : 'Please provide a reason for rejection...'"
                        :class="{ 'border-red-500': actionType === 'reject' && !adminNotes.trim() }"
                      ></textarea>
                    </div>

                    <div class="flex justify-end space-x-3">
                      <button
                        @click="processRequest('approve')"
                        :disabled="processing"
                        class="px-4 py-2 bg-green-600 text-white text-sm font-medium hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed dashboard-transition"
                      >
                        {{ processing && actionType === 'approve' ? 'Approving...' : 'Approve' }}
                      </button>
                      <button
                        @click="processRequest('reject')"
                        :disabled="processing || !adminNotes.trim()"
                        class="px-4 py-2 bg-red-600 text-white text-sm font-medium hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed dashboard-transition"
                      >
                        {{ processing && actionType === 'reject' ? 'Rejecting...' : 'Reject' }}
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <div class="mt-6 flex justify-end">
                <button
                  @click="closeModal"
                  class="px-4 py-2 text-sm font-medium dashboard-text-secondary hover:dashboard-text-primary dashboard-transition"
                >
                  Close
                </button>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue';
import { useHttpClient } from '@/composables/useHttpClient';
import { ENDPOINTS } from '@/utils/api';
import dayjs from 'dayjs';
import type { HostRequest } from '@/types/host-request';

interface Props {
  isOpen: boolean;
  request: HostRequest | null;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  close: [];
  updated: [request: HostRequest];
}>();

const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const processing = ref(false);
const actionType = ref<'approve' | 'reject' | null>(null);
const adminNotes = ref('');

const closeModal = () => {
  emit('close');
  // Reset form
  adminNotes.value = '';
  actionType.value = null;
};

const processRequest = async (action: 'approve' | 'reject') => {
  if (action === 'reject' && !adminNotes.value.trim()) {
    $toast.error('Admin notes are required for rejection');
    return;
  }

  processing.value = true;
  actionType.value = action;

  try {
    if (!props.request) return;

    const endpoint = action === 'approve'
      ? `${ENDPOINTS.HOST_REQUESTS.APPROVE}/${props.request.id}/approve`
      : `${ENDPOINTS.HOST_REQUESTS.REJECT}/${props.request.id}/reject`;

    const payload: any = {};
    if (adminNotes.value.trim()) {
      payload.admin_notes = adminNotes.value.trim();
    }

    const response = await httpClient.post<HostRequest>(endpoint, payload);

    if (response) {
      $toast.success(`Host request ${action}d successfully`);
      emit('updated', response);
      closeModal();
    }
  } catch (error: any) {
    console.error(`Error ${action}ing host request:`, error);

    if (error.response?.data?.message) {
      $toast.error(error.response.data.message);
    } else {
      $toast.error(`Failed to ${action} host request`);
    }
  } finally {
    processing.value = false;
    actionType.value = null;
  }
};

const formatDate = (date: string) => {
  return dayjs(date).format('MMM D, YYYY [at] h:mm A');
};

const getStatusBadgeClass = (status: string) => {
  switch (status) {
    case 'pending':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
    case 'approved':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
    case 'rejected':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
  }
};

// Reset form when modal opens/closes
watch(() => props.isOpen, (isOpen) => {
  if (!isOpen) {
    adminNotes.value = '';
    actionType.value = null;
  }
});
</script>
