<template>
  <div class="w-full border-b border-gray-200 dark:border-zinc-700 flex cursor-pointer hover:bg-gray-50 dark:hover:bg-zinc-800/50 transition duration-150" @click="showEventDetails">
    <div>
      <img :src="`${runtimeConfig.public.baseUrl}storage/events/${event.cover_art}`" :alt="`${event.title}-image`"
        class="w-36 h-28 object-cover group-hover:opacity-100 transition duration-300 ease-in-out" />
    </div>
    <div class="w-full px-2 py-2">
      <div class="w-full flex items-center justify-between">
        <NuxtLink class="hover:text-sky-500 dark:hover:text-sky-400 transition duration-150" :to="`/events/${event.slug}`">
          <h3 class="text-lg font-semibold line-clamp-1 text-gray-900 dark:text-zinc-100">{{ event.title }}</h3>
        </NuxtLink>
        <div class="flex items-center space-x-3">
          <div class="relative" v-if="event.is_liked">
            <button class="bg-gray-50 dark:bg-zinc-700 p-1" :class="{ 'bg-red-600 text-white': useLikeTooltip }"
              @mouseenter="likeTooltip = true" @mouseleave="likeTooltip = false" @click.stop="toggleLikeTooltip">
              <Icon icon="fluent:heart-28-filled" class="w-5 h-5"
                :class="event.is_liked ? 'text-red-500' : 'text-gray-500 dark:text-zinc-400'" />
            </button>
            <div v-if="likeTooltip"
              class="absolute text-sm font-medium left-1/2 transform -translate-x-1/2 -translate-y-full -top-1/2 bg-black dark:bg-zinc-800 bg-opacity-90 dark:bg-opacity-90 p-2 text-gray-100 dark:text-zinc-200 shadow-md pointer-events-none z-10">
              You hearted this event
              <div
                class="absolute left-1/2 transform -translate-x-1/2 translate-y-full w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-black dark:border-t-zinc-800 border-opacity-90 dark:border-opacity-90">
              </div>
            </div>
          </div>
          <div class="relative" v-if="event.is_attendee">
            <button class="bg-gray-50 dark:bg-zinc-700 p-1" :class="{ 'bg-red-600 text-white': useAttendeeTooltip }"
              @mouseenter="attendeeTooltip = true" @mouseleave="attendeeTooltip = false" @click.stop="toggleAttendeeTooltip">
              <Icon icon="entypo:add-user" class="w-5 h-5"
                :class="event.is_attendee ? 'text-red-500' : 'text-gray-500 dark:text-zinc-400'" />
            </button>
            <div v-if="attendeeTooltip"
              class="absolute text-sm font-medium left-1/2 transform -translate-x-1/2 -translate-y-full -top-1/2 bg-black dark:bg-zinc-800 bg-opacity-90 dark:bg-opacity-90 p-2 text-gray-100 dark:text-zinc-200 shadow-md pointer-events-none z-10">
              You added attendance to this event
              <div
                class="absolute left-1/2 transform -translate-x-1/2 translate-y-full w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-black dark:border-t-zinc-800 border-opacity-90 dark:border-opacity-90">
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="w-full flex items-center space-x-2 mb-2">
        <Icon icon="mage:calendar-3" class="w-5 h-5 text-gray-500 dark:text-zinc-400" />
        <p class="text-base text-gray-500 dark:text-zinc-400 line-clamp-1">{{ formatDateRange(event.start, event.end) }}</p>
      </div>
      <div class="mb-2">
        <button class="flex text-xs rounded-full items-center transition duration-150 px-1 py-1 bg-opacity-15 hover:bg-opacity-50 hover:text-white
            " :class="statusConfig.background">
          <div class="mr-1 flex p-1 items-center rounded-full" :class="statusConfig.fadeBackground">
            <Icon :icon="statusConfig.icon" class="w-3 h-3" :class="statusConfig.text" />
          </div>
          <p class="text-sm pr-1 font-medium" :class="statusConfig.text">{{ determineStatus }}</p>
        </button>
      </div>
    </div>
  </div>

  <!-- Event Details Dialog -->
  <TransitionRoot appear :show="isEventDetailsOpen" as="template">
    <Dialog as="div" @close="closeEventDetails" class="relative z-10">
      <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
        leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
        <div class="fixed inset-0 bg-black/25" />
      </TransitionChild>
      <div class="fixed inset-0 overflow-y-auto">
        <div class="flex min-h-full items-center justify-center p-4">
          <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95">
            <DialogPanel class="w-full max-w-2xl transform overflow-hidden bg-white dark:bg-zinc-800 shadow-xl p-6 text-left align-middle transition-all">
              <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900 dark:text-zinc-100 flex justify-between items-center mb-4">
                <span>{{ event.title }}</span>
                <button @click="closeEventDetails" class="text-gray-400 dark:text-zinc-500 hover:text-gray-600 dark:hover:text-zinc-300">
                  <Icon icon="heroicons:x-mark" class="w-6 h-6" />
                </button>
              </DialogTitle>

              <div class="space-y-4">
                <div class="flex items-start space-x-4">
                  <img :src="`${runtimeConfig.public.baseUrl}storage/events/${event.cover_art}`" :alt="`${event.title}-image`"
                    class="w-32 h-24 object-cover" />
                  <div class="flex-1">
                    <div class="flex items-center space-x-2 mb-2">
                      <Icon icon="mage:calendar-3" class="w-5 h-5 text-gray-500 dark:text-zinc-400" />
                      <p class="text-sm text-gray-600 dark:text-zinc-300">{{ formatDateRange(event.start, event.end) }}</p>
                    </div>
                    <div class="flex items-center space-x-2 mb-2" v-if="event.location">
                      <Icon icon="heroicons:map-pin" class="w-5 h-5 text-gray-500 dark:text-zinc-400" />
                      <p class="text-sm text-gray-600 dark:text-zinc-300">{{ event.location }}</p>
                    </div>
                    <div class="mb-2">
                      <button class="flex text-xs items-center transition duration-150 px-2 py-1 bg-opacity-15" :class="statusConfig.background">
                        <div class="mr-1 flex p-1 items-center" :class="statusConfig.fadeBackground">
                          <Icon :icon="statusConfig.icon" class="w-3 h-3" :class="statusConfig.text" />
                        </div>
                        <p class="text-sm pr-1 font-medium" :class="statusConfig.text">{{ determineStatus }}</p>
                      </button>
                    </div>
                  </div>
                </div>

                <div v-if="event.description" class="border-t border-gray-200 dark:border-zinc-700 pt-4">
                  <h4 class="text-sm font-medium text-gray-900 dark:text-zinc-100 mb-2">Description</h4>
                  <p class="text-sm text-gray-600 dark:text-zinc-300" v-html="event.description"></p>
                </div>

                <div class="border-t border-gray-200 dark:border-zinc-700 pt-4 flex justify-between items-center">
                  <div class="flex items-center space-x-4">
                    <div v-if="event.is_liked" class="flex items-center space-x-1 text-red-500">
                      <Icon icon="fluent:heart-28-filled" class="w-4 h-4" />
                      <span class="text-xs">Liked</span>
                    </div>
                    <div v-if="event.is_attendee" class="flex items-center space-x-1 text-red-500">
                      <Icon icon="entypo:add-user" class="w-4 h-4" />
                      <span class="text-xs">Attending</span>
                    </div>
                  </div>
                  <NuxtLink :to="`/events/${event.slug}`"
                    class="inline-flex items-center px-4 py-2 bg-red-600 text-white hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-600 transition duration-150 text-sm font-medium">
                    View Event
                    <Icon icon="heroicons:arrow-right" class="w-4 h-4 ml-2" />
                  </NuxtLink>
                </div>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue';
import type { EventItem } from '@/types';

const props = defineProps<{
  event: EventItem;
}>();

const runtimeConfig = useRuntimeConfig();
const likeTooltip = ref(false);
const useLikeTooltip = ref(false);
const attendeeTooltip = ref(false);
const useAttendeeTooltip = ref(false);
const isEventDetailsOpen = ref(false);

const toggleLikeTooltip = () => {
  useLikeTooltip.value = !useLikeTooltip.value;
};

const toggleAttendeeTooltip = () => {
  useAttendeeTooltip.value = !useAttendeeTooltip.value;
};

const showEventDetails = () => {
  isEventDetailsOpen.value = true;
};

const closeEventDetails = () => {
  isEventDetailsOpen.value = false;
};

const statusClasses = {
  Completed: {
    fadeBackground: 'bg-green-300',
    background: 'bg-green-500 border border-green-100',
    text: 'text-green-500',
    icon: 'fluent-mdl2:completed-solid',
  },
  'In Progress': {
    fadeBackground: 'bg-yellow-300',
    background: 'bg-yellow-500 border border-yellow-100',
    text: 'text-yellow-500',
    icon: 'eos-icons:three-dots-loading',
  },
  Cancelled: {
    fadeBackground: 'bg-red-300',
    background: 'bg-red-500 border border-red-100',
    text: 'text-red-500',
    icon: 'fluent:calendar-cancel-24-regular',
  },
  Upcoming: {
    fadeBackground: 'bg-gray-300',
    background: 'bg-gray-500 border border-gray-100',
    text: 'text-gray-500',
    icon: 'eos-icons:hourglass',
  },
};

const determineStatus = computed(() => {
  const now = new Date();
  const { start, end } = props.event;
  const statusMap = [
    {
      condition: () => now < new Date(start),
      status: 'Upcoming'
    },
    {
      condition: () => now >= new Date(start) && now <= new Date(end),
      status: 'In Progress'
    },
    {
      condition: () => now > new Date(end),
      status: 'Completed'
    }
  ];

  return statusMap.find(item => item.condition())?.status || 'Unknown';
});

const statusConfig = computed(() => {
  const eventStatus = determineStatus.value;
  return statusClasses[eventStatus as keyof typeof statusClasses] || statusClasses.Upcoming;
});

const formatDateRange = (startDate: string, endDate: string) => {
  const start = new Date(startDate);
  const end = new Date(endDate);
  return `${start.toLocaleString()} - ${end.toLocaleString()}`;
};
</script>
