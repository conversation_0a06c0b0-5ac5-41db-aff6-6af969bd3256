<template>
    <div class="w-full grid grid-cols-3 gap-4 p-4 bg-gray-100 min-h-screen overflow-x-auto">
        <div v-for="(column, columnIndex) in columns" :key="column.id"
            class="kanban-column col-span-1 flex-shrink-0 bg-white shadow-md p-3">
            <div class="column-header flex justify-between items-center mb-3">
                <div class="flex items-center space-x-3">
                    <div class="w-3 h-3" :class="column.color"></div>
                    <h3 class="text-lg font-semibold">{{ column.title }}</h3>
                </div>
                <span class="text-gray-500 text-sm">{{ column.events.length }} events</span>
            </div>

            <draggable v-model="column.events" group="events" @change="handleDragChange" class="space-y-3 min-h-[200px]"
                item-key="id">
                <template #item="{ element }">
                    <div class="w-full bg-white border border-dotted duration-200 p-4 cursor-pointer" @click="emits('onEventClick', element)">
                        <div class="flex justify-between items-start mb-3">
                            <h3 class="font-medium text-gray-900 truncate flex-1">{{ element.title }}</h3>
                            <span :class="[
                                'text-xs px-2 py-1 rounded-full',
                                element.published_at ? 'bg-green-100 text-green-700' : 'bg-yellow-100 text-yellow-700'
                            ]">
                                {{ element.published_at ? 'Published' : 'Draft' }}
                            </span>
                        </div>

                        <div v-if="element.cover_art" class="mb-3 aspect-video">
                            <img :src="`${runtimeConfig.public.baseUrl}storage/events/${element.cover_art}`" :alt="element.title"
                                class="w-full h-full object-cover">
                        </div>

                        <div class="flex items-center text-sm text-gray-600 mb-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z" />
                            </svg>
                            <span>{{ formatDateRange(element.start, element.end) }}</span>
                        </div>

                        <div v-if="element.location" class="flex items-center text-sm text-gray-600 mb-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            <span class="truncate">{{ element.location }}</span>
                        </div>
                    </div>
                </template>
            </draggable>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import draggable from 'vuedraggable'
import type { EventItem } from '@/types';

const props = defineProps({
    events: {
        type: Array as () => EventItem[],
        required: true
    }
});

const runtimeConfig = useRuntimeConfig()
const emits = defineEmits(['onEventClick'])

interface Column {
    id: number
    title: 'Draft' | 'In Progress' | 'Completed'
    events: EventItem[]
    color: string
}

const columns = computed<Column[]>(() => [
    {
        id: 1,
        title: 'Draft',
        events: props.events.filter(event => !event.published_at),
        color: 'bg-gray-500'
    },
    {
        id: 2,
        title: 'In Progress',
        events: props.events.filter(event =>
            event.published_at && new Date(event.end) > new Date()
        ),
        color: 'bg-blue-500'
    },
    {
        id: 3,
        title: 'Completed',
        events: props.events.filter(event =>
            event.published_at && new Date(event.end) <= new Date()
        ),
        color: 'bg-green-500'
    }
])

const handleDragChange = (evt: any) => {
    console.log('Drag event', evt)
}

const formatDateRange = (start: string | number | Date, end: string | number | Date) => {
    const startDate = new Date(start);
    const endDate = new Date(end);
    return `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`;
};
</script>
