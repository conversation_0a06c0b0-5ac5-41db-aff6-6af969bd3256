<template>
  <button @click="fetchEventDetails" class="px-2 py-2">
    <Icon icon="hugeicons:eye" class="w-5 h-5" />
  </button>
  <TransitionRoot appear :show="isOpen" as="template">
    <Dialog as="div" @close="closeModal" class="relative z-[9999]">
      <TransitionChild
        as="template"
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-black/25 dark:bg-black/50" />
      </TransitionChild>

      <div class="fixed inset-0 overflow-y-auto">
        <div
          class="flex min-h-full items-center justify-center p-4 text-center"
        >
          <TransitionChild
            as="template"
            enter="duration-300 ease-out"
            enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95"
          >
            <DialogPanel
              class="w-full max-w-2xl transform overflow-hidden bg-white dark:bg-zinc-800 text-left align-middle shadow-xl transition-all"
            >
              <div v-if="eventItem && !loading">
                <div class="">
                  <div class="bg-white dark:bg-zinc-800 shadow-xl overflow-hidden">
                    <div
                      class="h-64 w-full bg-gradient-to-r from-red-500 to-purple-600 relative"
                    >
                      <img
                        :src="`${runtimeConfig.public.baseUrl}storage/events/${eventItem?.cover_art}`"
                        :alt="`${eventItem?.slug}-event-banner`"
                        class="w-full h-full object-cover"
                      />
                      <div
                        class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-6"
                      >
                        <h1 class="text-3xl font-bold text-white">
                          {{ eventItem.title }}
                        </h1>
                        <div class="flex items-center gap-4 mt-2">
                          <span
                            class="text-white font-light flex items-center gap-2"
                          >
                            <Icon
                              icon="radix-icons:calendar"
                              class="w-5 h-5 text-white"
                            />
                            {{
                              dayjs(eventItem.start).format(DATE_FORMAT.FULL)
                            }}
                            -
                            {{ dayjs(eventItem.end).format(DATE_FORMAT.FULL) }}
                          </span>
                        </div>
                      </div>
                    </div>
                    <button
                      class="absolute top-2 right-2 text-white hover:text-gray-300 dark:hover:text-gray-400"
                      @click="closeModal"
                    >
                      <Icon icon="mingcute:close-line" class="w-6 h-6" />
                    </button>
                    <EventsUserCard :user="eventItem.user" />
                    <div class="p-3">
                      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                        <div class="flex items-center gap-3">
                          <div
                            class="w-10 h-10 rounded-none bg-red-600 text-white flex items-center justify-center"
                          >
                            <Icon
                              icon="fa6-solid:map-location"
                              class="w-5 h-5"
                            />
                          </div>
                          <div>
                            <p class="text-black dark:text-white text-lg font-semibold">
                              Location
                            </p>
                            <p class="font-thin text-gray-500 dark:text-gray-400">
                              {{
                                eventItem.location || "Location not specified"
                              }}
                            </p>
                          </div>
                        </div>

                        <div class="flex items-center gap-3">
                          <div
                            class="w-10 h-10 rounded-none bg-red-600 text-white flex items-center justify-center"
                          >
                            <Icon
                              icon="grommet-icons:form-view-hide"
                              class="w-5 h-5"
                            />
                          </div>
                          <div>
                            <p class="text-black dark:text-white text-lg font-semibold">
                              Visibility
                            </p>
                            <p class="font-thin text-gray-500 dark:text-gray-400">
                              {{
                                eventItem?.visibility?.name ||
                                "Visibility not specified"
                              }}
                            </p>
                          </div>
                        </div>

                        <div class="flex items-center gap-2">
                          <div
                            class="w-10 h-10 rounded-none bg-red-600 text-white flex items-center justify-center"
                          >
                            <img
                              :src="`${runtimeConfig.public.baseUrl}storage/categories/${eventItem?.category?.icon}`"
                              class="w-6 h-6 object-cover"
                              :alt="eventItem?.category?.name"
                            />
                          </div>
                          <div>
                            <p class="text-black dark:text-white text-lg font-semibold">
                              Category
                            </p>
                            <p class="font-thin text-gray-500 dark:text-gray-400">
                              {{ eventItem?.category?.name }}
                            </p>
                          </div>
                        </div>

                        <div
                          v-if="eventItem?.meeting_link"
                          class="flex items-center gap-3"
                        >
                          <div
                            class="w-10 h-10 rounded-none bg-red-600 text-white flex items-center justify-center"
                          >
                            <Icon icon="hugeicons:globe" class="w-6 h-6" />
                          </div>
                          <div>
                            <p class="text-black dark:text-white text-lg font-semibold">
                              Meeting Link
                            </p>
                            <a
                              :href="eventItem?.meeting_link.link"
                              target="_blank"
                              class="font-thin text-sky-500 dark:text-sky-400 underline"
                              >{{ eventItem?.meeting_link.link || "" }}
                            </a>
                          </div>
                        </div>
                      </div>

                      <div class="mb-4 bg-gray-50 dark:bg-zinc-700 rounded border-2 border-dotted border-zinc-200 dark:border-zinc-600 p-2">
                        <h3 class="text-lg font-semibold dark:text-white">Description</h3>
                        <div
                          class="prose max-w-none text-gray-500 dark:text-gray-400"
                          v-html="eventItem.description"
                        ></div>
                      </div>
                      <div v-if="eventItem.tiers.length > 0" class="space-y-6">
                        <div class="flex items-center gap-3 mb-6">
                          <div
                            class="w-10 h-10 bg-gradient-to-r from-pink-600 to-red-600 flex items-center justify-center"
                          >
                            <Icon
                              icon="solar:ticket-bold"
                              class="w-6 h-6 text-white"
                            />
                          </div>
                          <div>
                            <h2 class="text-xl font-bold text-gray-900 dark:text-white">
                              Ticket Packages
                            </h2>
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                            Ticket tiers available for this event
                            </p>
                          </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div
                            v-for="tier in eventItem.tiers"
                            :key="tier.id"
                            class="relative border rounded-xl p-6 border-red-50 dark:border-zinc-600 hover:shadow-xl transition-all duration-300 group bg-white dark:bg-zinc-800"
                          >

                            <div class="flex items-start justify-between mb-4">
                              <div class="flex items-center gap-3">
                                <div
                                  class="w-12 h-12 bg-gradient-to-r from-red-500 to-pink-600 flex items-center justify-center"
                                >
                                  <Icon
                                    icon="solar:star-bold"
                                    class="w-6 h-6 text-white"
                                  />
                                </div>
                                <div>
                                  <h3
                                    class="text-lg font-bold text-gray-900 dark:text-white group-hover:text-red-600 dark:group-hover:text-red-400 transition-colors"
                                  >
                                    {{ tier.name }}
                                  </h3>
                                  <div class="flex items-center gap-2 mt-1">
                                    <Icon
                                      icon="solar:ticket-linear"
                                      class="w-4 h-4 text-gray-400 dark:text-gray-500"
                                    />
                                    <span class="text-sm text-gray-500 dark:text-gray-400"
                                      >Digital Ticket</span
                                    >
                                  </div>
                                </div>
                              </div>
                            </div>

                            <div class="mb-4">
                              <p class="text-gray-600 dark:text-gray-300 text-sm leading-relaxed">
                                {{ tier.description }}
                              </p>
                            </div>

                            <div
                              class="flex items-center justify-between pt-4 border-t border-red-100 dark:border-zinc-600"
                            >
                              <div class="flex items-center gap-2">
                                <Icon
                                  icon="solar:wallet-money-bold"
                                  class="w-5 h-5 text-green-600 dark:text-green-400"
                                />
                                <span class="text-2xl font-bold text-gray-900 dark:text-white">
                                  {{ Object.keys($location.currency)[0]
                                  }}{{ tier.price.toLocaleString() }}
                                </span>
                              </div>
                            </div>

                            <div
                              class="absolute inset-0 bg-gradient-to-r from-red-600/5 to-purple-600/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"
                            ></div>
                          </div>
                        </div>

                        <div
                          class="mt-6 p-4 bg-gray-50 dark:bg-zinc-700 border-gray-200 dark:border-zinc-600"
                        >
                          <div class="flex items-start gap-3">
                            <Icon
                              icon="solar:info-circle-bold"
                              class="w-5 h-5 text-red-600 dark:text-red-400 mt-0.5"
                            />
                            <div>
                              <h4
                                class="text-sm font-semibold text-gray-900 dark:text-white mb-1"
                              >
                                Ticket Information
                              </h4>
                              <p class="text-xs text-gray-600 dark:text-gray-300">
                                All tickets include event access and digital
                                receipt. Refund policy applies as per terms and
                                conditions.
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                v-else
                class="w-full flex flex-col items-center justify-center py-20 space-y-2"
              >
                <CoreLoader />
                <p class="text-black dark:text-white">
                  Loading event details, please wait
                  <span class="animate-pulse">...</span>
                </p>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
} from "@headlessui/vue";
import type { EventItem } from "@/types";
import dayjs from "dayjs";

interface Props {
  event: EventItem;
}
const httpClient = useHttpClient();
const props = defineProps<Props>();
const isOpen = ref<boolean>(false);
const eventItem = ref<EventItem>();
const loading = ref<boolean>(false);
const { $toast, $location }: any = useNuxtApp();
const runtimeConfig = useRuntimeConfig();

const fetchEventDetails = async (): Promise<void> => {
  isOpen.value = true;
  loading.value = true;
  try {
    const response: { event: EventItem } = await httpClient.get(
      `${ENDPOINTS.EVENTS.READ}/${props.event.id}`
    );
    if (response) {
      eventItem.value = response.event;
      loading.value = false;
    }
    if (!response) {
      $toast.error("Error fetching event details");
      loading.value = false;
    }
  } catch (e: any) {
    console.error("error fetching event details: ", e);
  } finally {
    loading.value = false;
  }
};

const closeModal = (): void => {
  isOpen.value = false;
};

const openModal = (): void => {
  fetchEventDetails();
};

defineExpose({
  closeModal,
  openModal,
});
</script>
