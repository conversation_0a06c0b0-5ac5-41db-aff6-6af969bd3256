<template>
    <div class="calendar-container bg-white shadow-sm p-6 mt-2">
        <div class="calendar-header flex items-center justify-between mb-6">
            <div class="flex items-center space-x-4">
                <button 
                    @click="previousMonth"
                    class="p-2 hover:bg-gray-100 transition-colors"
                >
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                    </svg>
                </button>
                <h2 class="text-2xl font-bold text-gray-900">
                    {{ currentMonthYear }}
                </h2>
                <button 
                    @click="nextMonth"
                    class="p-2 hover:bg-gray-100 transition-colors"
                >
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </button>
            </div>
            
            <div class="flex items-center space-x-2">
                <button 
                    @click="goToToday"
                    class="px-4 py-2 bg-red-600 text-white hover:bg-red-700 transition-colors"
                >
                    Today
                </button>
            </div>
        </div>

        <div class="grid grid-cols-7 gap-px bg-gray-200 overflow-hidden mb-2">
            <div 
                v-for="day in daysOfWeek" 
                :key="day"
                class="bg-gray-50 p-3 text-center text-sm font-semibold text-gray-700"
            >
                {{ day }}
            </div>
        </div>

        <div class="grid grid-cols-7 gap-px bg-gray-200 overflow-hidden">
            <div
                v-for="date in calendarDates"
                :key="`${date.date}-${date.month}`"
                class="bg-white min-h-32 p-2 relative"
                :class="{
                    'bg-gray-50': !date.isCurrentMonth,
                    'bg-red-50': date.isToday
                }"
            >
                <div class="flex justify-between items-start mb-2">
                    <span 
                        class="text-sm font-medium"
                        :class="{
                            'text-gray-400': !date.isCurrentMonth,
                            'text-red-600 font-bold': date.isToday,
                            'text-gray-900': date.isCurrentMonth && !date.isToday
                        }"
                    >
                        {{ date.date }}
                    </span>
                </div>

                <div class="space-y-1">
                    <div
                        v-for="event in getEventsForDate(date.fullDate)"
                        :key="event.id"
                        @click="handleEventClick(event)"
                        class="text-xs px-2 py-1 truncate cursor-pointer transition-colors"
                        :class="getEventClasses(event)"
                        :title="event.title"
                    >
                        <div class="flex items-center space-x-1">
                            <div class="w-2 h-2 bg-current opacity-75"></div>
                            <span class="truncate">{{ event.title }}</span>
                        </div>
                    </div>
                    
                    <div 
                        v-if="getEventsForDate(date.fullDate).length > 3"
                        class="text-xs text-gray-500 cursor-pointer hover:text-gray-700"
                        @click="showMoreEvents(date, getEventsForDate(date.fullDate))"
                    >
                        +{{ getEventsForDate(date.fullDate).length - 3 }} more
                    </div>
                </div>
            </div>
        </div>

        <div 
            v-if="showEventModal" 
            class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            @click="closeEventModal"
        >
            <div 
                class="bg-white p-6 max-w-md w-full m-4"
                @click.stop
            >
                <div class="flex justify-between items-start mb-4">
                    <h3 class="text-lg font-bold text-gray-900">{{ selectedEvent?.title }}</h3>
                    <button 
                        @click="closeEventModal"
                        class="text-gray-400 hover:text-gray-600"
                    >
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                
                <div class="space-y-3">
                    <div v-if="selectedEvent?.description" class="text-gray-700">
                        <p class="font-medium mb-1">Description:</p>
                        <p>{{ selectedEvent.description }}</p>
                    </div>
                    
                    <div class="text-gray-700">
                        <p class="font-medium mb-1">Date & Time:</p>
                        <p>{{ formatEventDateTime(selectedEvent?.start, selectedEvent?.end) }}</p>
                    </div>
                    
                    <div v-if="selectedEvent?.location" class="text-gray-700">
                        <p class="font-medium mb-1">Location:</p>
                        <p>{{ selectedEvent.location }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { EventItem } from '~/types';

interface CalendarDate {
    date: number;
    month: number;
    year: number;
    fullDate: string;
    isCurrentMonth: boolean;
    isToday: boolean;
}

const props = defineProps<{
    events: EventItem[]
}>()

const emits = defineEmits<{
    onEventClick: [event: EventItem]
}>()

const currentDate = ref<Date>(new Date())
const showEventModal = ref<boolean>(false)
const selectedEvent = ref<EventItem | null>(null)

const daysOfWeek: string[] = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']

const currentMonthYear = computed(() => {
    return new Intl.DateTimeFormat('en-US', { 
        month: 'long', 
        year: 'numeric' 
    }).format(currentDate.value)
})

const calendarDates = computed<CalendarDate[]>(() => {
    const year = currentDate.value.getFullYear()
    const month = currentDate.value.getMonth()
    
    const firstDay = new Date(year, month, 1)
    const firstDayOfWeek = firstDay.getDay()
    
    const lastDay = new Date(year, month + 1, 0)
    const lastDate = lastDay.getDate()
    
    const prevMonthLastDate = new Date(year, month, 0).getDate()
    
    const dates: CalendarDate[] = []
    const today = new Date()
    
    for (let i = firstDayOfWeek - 1; i >= 0; i--) {
        const date = prevMonthLastDate - i
        const dateObj = new Date(year, month - 1, date)
        dates.push({
            date,
            month: month - 1,
            year: month - 1 < 0 ? year - 1 : year,
            fullDate: formatDateString(dateObj),
            isCurrentMonth: false,
            isToday: isSameDay(dateObj, today)
        })
    }
    
    for (let date = 1; date <= lastDate; date++) {
        const dateObj = new Date(year, month, date)
        dates.push({
            date,
            month,
            year,
            fullDate: formatDateString(dateObj),
            isCurrentMonth: true,
            isToday: isSameDay(dateObj, today)
        })
    }
    
    const remainingSlots = 42 - dates.length;
    for (let date = 1; date <= remainingSlots; date++) {
        const dateObj = new Date(year, month + 1, date)
        dates.push({
            date,
            month: month + 1,
            year: month + 1 > 11 ? year + 1 : year,
            fullDate: formatDateString(dateObj),
            isCurrentMonth: false,
            isToday: isSameDay(dateObj, today)
        })
    }
    
    return dates
})

const formatDateString = (date: Date): string => {
    return date.toISOString().split('T')[0]
}

const isSameDay = (date1: Date, date2: Date): boolean => {
    return formatDateString(date1) === formatDateString(date2)
}

const getEventsForDate = (dateString: string): EventItem[] => {
    return props.events.filter(event => {
        const eventStart = new Date(event.start).toISOString().split('T')[0]
        const eventEnd = new Date(event.end).toISOString().split('T')[0]
        return dateString >= eventStart && dateString <= eventEnd
    }).slice(0, 4);
}

const getEventClasses = (event: EventItem): string => {
    const baseClasses = "hover:opacity-80"
    
    if (event.published_at) {
        return `${baseClasses} bg-red-100 text-red-800 hover:bg-red-200`
    } else {
        return `${baseClasses} bg-gray-100 text-gray-700 hover:bg-gray-200`
    }
}

const handleEventClick = (event: EventItem) => {
    selectedEvent.value = event
    showEventModal.value = true
    emits('onEventClick', event)
}

const closeEventModal = () => {
    showEventModal.value = false
    selectedEvent.value = null
}

const showMoreEvents = (date: CalendarDate, events: EventItem[]) => {
    console.log('Show more events for', date, events)
}

const previousMonth = () => {
    currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() - 1, 1)
}

const nextMonth = () => {
    currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() + 1, 1)
}

const goToToday = () => {
    currentDate.value = new Date()
}

const formatEventDateTime = (start?: string, end?: string): string => {
    if (!start) return ''
    
    const startDate = new Date(start)
    const endDate = end ? new Date(end) : null
    
    const dateFormat = new Intl.DateTimeFormat('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
    })
    
    const timeFormat = new Intl.DateTimeFormat('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    })
    
    if (endDate && !isSameDay(startDate, endDate)) {
        return `${dateFormat.format(startDate)} ${timeFormat.format(startDate)} - ${dateFormat.format(endDate)} ${timeFormat.format(endDate)}`
    } else {
        const timeEnd = endDate ? ` - ${timeFormat.format(endDate)}` : ''
        return `${dateFormat.format(startDate)} ${timeFormat.format(startDate)}${timeEnd}`
    }
}
</script>