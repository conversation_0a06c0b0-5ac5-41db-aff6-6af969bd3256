<template>
    <div>
        <div class="flex justify-between items-center mb-6">
            <div class="h-8 w-48 bg-gray-200 animate-pulse"></div>
            <div class="flex space-x-2">
                <div class="h-9 w-24 bg-gray-200 animate-pulse"></div>
                <div class="h-9 w-20 bg-gray-200 animate-pulse"></div>
            </div>
        </div>

        <div v-if="isGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div v-for="n in itemsToShow" :key="n" class="bg-white-lg shadow-sm overflow-hidden">
                <div class="w-full h-48 bg-gray-200 animate-pulse"></div>

                <div class="p-4">
                    <div class="h-6 bg-gray-200 w-3/4 mb-4 animate-pulse"></div>

                    <div class="flex items-center space-x-2 mb-3">
                        <div class="h-4 w-4 bg-gray-200 animate-pulse"></div>
                        <div class="h-4 w-24 bg-gray-200 animate-pulse"></div>
                    </div>

                    <div class="flex items-center space-x-2 mb-3">
                        <div class="h-4 w-4 bg-gray-200 animate-pulse"></div>
                        <div class="h-4 w-32 bg-gray-200 animate-pulse"></div>
                    </div>

                    <div class="flex items-center justify-between mt-4">
                        <div class="h-6 w-20 bg-gray-200 animate-pulse"></div>
                        <div class="h-8 w-24 bg-gray-200 animate-pulse"></div>
                    </div>
                </div>
            </div>
        </div>

        <div v-else class="space-y-4">
            <div v-for="n in itemsToShow" :key="n"
                class="bg-white-lg shadow-sm overflow-hidden flex max-md:flex-col">
                <div class="w-64 h-40 bg-gray-200 animate-pulse max-md:w-full"></div>

                <div class="flex-1 p-4">
                    <div class="flex justify-between items-start">
                        <div class="space-y-2 flex-1">
                            <div class="h-6 bg-gray-200 w-3/4 animate-pulse"></div>
                            <div class="h-4 bg-gray-200 w-24 animate-pulse"></div>
                        </div>
                        <div class="h-8 w-24 bg-gray-200 animate-pulse"></div>
                    </div>

                    <div class="mt-4 space-y-2">
                        <div class="flex items-center space-x-2">
                            <div class="h-4 w-4 bg-gray-200 animate-pulse"></div>
                            <div class="h-4 w-32 bg-gray-200 animate-pulse"></div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="h-4 w-4 bg-gray-200 animate-pulse"></div>
                            <div class="h-4 w-48 bg-gray-200 animate-pulse"></div>
                        </div>
                    </div>

                    <div class="mt-4 space-y-2">
                        <div class="h-4 bg-gray-200 w-full animate-pulse"></div>
                        <div class="h-4 bg-gray-200 w-3/4 animate-pulse"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="flex flex-col items-center mt-8">
            <div class="h-5 w-64 bg-gray-200 animate-pulse mb-4"></div>
            <div class="flex space-x-2">
                <div class="h-10 w-24 bg-gray-200 animate-pulse"></div>
                <div class="h-10 w-24 bg-gray-200 animate-pulse"></div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
defineProps({
    isGrid: {
        type: Boolean,
        default: true
    },
    itemsToShow: {
        type: Number,
        default: 6
    }
});
</script>

<style scoped>
.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: .5;
    }
}
</style>