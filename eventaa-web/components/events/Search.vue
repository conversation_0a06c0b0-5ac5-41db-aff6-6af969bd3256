<template>
    <div class="flex items-center border rounded">
        <img :src="`${runtimeConfig.public.baseUrl}storage/events/${event.cover_art}`" :alt="`${props.event.slug}-image`"
        class="w-32 h-32 rounded-l border-r object-cover group-hover:opacity-100 transition duration-300 ease-in-out" />
        <div>
            <NuxtLink :to="`/events/${props.event.slug}`"class="text-lg font-semibold hover:text-sky-500 pl-5">{{ event.title }}</NuxtLink>
            <div class="w-full grid grid-cols-2 px-5">
                <div class="flex items-center space-x-2">
                    <div class="w-10 h-10 flex items-center justify-center bg-red-100 rounded-full">
                        <Icon icon="ion:calendar-sharp" class="w-5 h-5 text-red-600" />
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold">Event Starts On</h3>
                        <p class="text-base text-gray-600">{{ dayjs(props.event.start).format("DD/MM/YYYY HH:mm") }}</p>
                    </div>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-10 h-10 flex items-center justify-center bg-red-100 rounded-full">
                        <Icon icon="dashicons:location-alt" class="w-5 h-5 text-red-600" />
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold">Where?</h3>
                        <p class="text-base font-medium text-gray-600">{{ props.event.location }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import type { PropType } from 'vue';
import type { EventItem } from '@/types';
import dayjs from 'dayjs';

const props = defineProps({
    event: {
        type: Object as PropType<EventItem>,
        required: true,
    },
});
const runtimeConfig = useRuntimeConfig();
</script>
