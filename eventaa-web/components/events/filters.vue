<template>
  <div class="relative">
    <Popover class="relative">
      <PopoverButton
        class="bg-white dark:bg-zinc-800 border border-gray-200 dark:border-zinc-700 px-4 py-2 font-medium flex items-center hover:bg-gray-50 dark:hover:bg-zinc-700 transition duration-150 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-zinc-800">
        <Icon icon="heroicons:funnel" class="w-5 h-5 mr-2 text-gray-600 dark:text-zinc-300" />
        <span class="text-gray-700 dark:text-zinc-200">Filters</span>
        <span v-if="activeFilterCount > 0"
          class="ml-2 bg-red-600 text-white text-xs px-2 py-0.5 font-semibold">
          {{ activeFilterCount }}
        </span>
        <Icon icon="heroicons:chevron-down" class="w-4 h-4 ml-2 text-gray-500 dark:text-zinc-400" />
      </PopoverButton>

      <transition
        enter-active-class="transition duration-200 ease-out"
        enter-from-class="translate-y-1 opacity-0"
        enter-to-class="translate-y-0 opacity-100"
        leave-active-class="transition duration-150 ease-in"
        leave-from-class="translate-y-0 opacity-100"
        leave-to-class="translate-y-1 opacity-0">
        <PopoverPanel class="absolute right-0 z-[9999] mt-2 w-96 bg-white dark:bg-zinc-800 shadow-xl border border-gray-200 dark:border-zinc-700 focus:outline-none">
          <div class="p-6">
            <div class="flex items-center justify-between mb-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-zinc-100">Filter Events</h3>
              <div class="flex space-x-2">
                <button @click="clearAllFilters"
                  class="text-sm text-gray-500 dark:text-zinc-400 hover:text-gray-700 dark:hover:text-zinc-200 transition">
                  Clear all
                </button>
              </div>
            </div>
            <div class="space-y-6">
              <!-- Date Range Filter -->
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-zinc-300 mb-3">
                  Date Range
                </label>
                <datepicker
                  @cleared="isCleared"
                  placeholder="Select date range"
                  :range="true"
                  input-class-name="w-full px-3 py-2 border border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-gray-900 dark:text-zinc-100 focus:ring-2 focus:ring-red-500 focus:border-red-500"
                  format="dd/MM/yyyy"
                  v-model="dateRange" />
              </div>
              <!-- Location Filter -->
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-zinc-300 mb-3">
                  Location
                </label>
                <div class="space-y-3">
                  <div class="text-sm text-gray-600 dark:text-zinc-400">
                    Current: {{ currentLocation || 'Not set' }}
                  </div>
                  <button @click="getCurrentLocation"
                    :disabled="isLocating"
                    class="w-full flex items-center justify-center px-3 py-2 border border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-gray-700 dark:text-zinc-200 hover:bg-gray-50 dark:hover:bg-zinc-600 transition disabled:opacity-50">
                    <Icon icon="heroicons:map-pin" class="w-4 h-4 mr-2" />
                    {{ isLocating ? 'Locating...' : 'Use Current Location' }}
                  </button>
                  <div>
                    <label class="block text-xs text-gray-500 dark:text-zinc-400 mb-2">
                      Radius: {{ radius }}m
                    </label>
                    <input v-model.number="radius" type="range" min="100" max="5000" step="100"
                      class="w-full h-2 bg-gray-200 dark:bg-zinc-600 appearance-none cursor-pointer slider" />
                    <div class="flex justify-between text-xs text-gray-500 dark:text-zinc-400 mt-1">
                      <span>100m</span>
                      <span>5km</span>
                    </div>
                  </div>
                </div>
              </div>
              <!-- Categories Filter -->
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-zinc-300 mb-3">
                  Categories
                </label>
                <div class="grid grid-cols-2 gap-2">
                  <button
                    v-for="category in $categories"
                    :key="category.id"
                    @click="modifySelectedCategory(category)"
                    :class="[
                      'flex items-center p-2 border transition text-sm',
                      selectedCategory.find((cat) => category.name == cat.name)
                        ? 'border-red-500 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300'
                        : 'border-gray-200 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-gray-700 dark:text-zinc-200 hover:border-gray-300 dark:hover:border-zinc-500'
                    ]">
                    <img :src="`${runtimeConfig.public.baseUrl}storage/categories/${category.icon}`"
                      class="w-4 h-4 mr-2" />
                    <span class="truncate">{{ category.name }}</span>
                  </button>
                </div>
              </div>

              <!-- Price Range Filter -->
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-zinc-300 mb-3">
                  Price Range
                </label>
                <CorePriceRange />
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-zinc-700 mt-6">
              <PopoverButton
                @click="clearAllFilters"
                class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-zinc-200 bg-white dark:bg-zinc-700 border border-gray-300 dark:border-zinc-600 hover:bg-gray-50 dark:hover:bg-zinc-600 transition">
                Clear All
              </PopoverButton>
              <PopoverButton
                @click="applyFilters"
                class="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 transition">
                Apply Filters
              </PopoverButton>
            </div>
          </div>
        </PopoverPanel>
      </transition>
    </Popover>
  </div>
</template>

<script setup lang="ts">
import { Popover, PopoverButton, PopoverPanel } from "@headlessui/vue";
import type { Category } from "@/types";

interface FilterState {
  dateRange: { start: Date; end: Date };
  center: { lat: number; lng: number };
  radius: number;
  currentLocation: string;
  selectedCategory: Category[];
}

const $emit = defineEmits(["apply-filters", "clear-filters"]);
const runtimeConfig = useRuntimeConfig();
const dateRange = ref({
  start: new Date(),
  end: new Date(),
});
const center = ref<{ lat: number; lng: number }>({
  lat: -13.963280001090016,
  lng: 33.792221716425516,
});
const selectedCategory = ref<Category[]>([]);
const { $toast, $categories }: any = useNuxtApp();
const currentLocation = ref("");
const isLocating = ref(false);
const isCleared = () => { };
const radius = ref(1000);
const minPrice = ref(123);
const maxPrice = ref(1789);

const activeFilterCount = computed(() => {
  let count = 0;
  if (dateRange.value.start && dateRange.value.end) count++;
  if (currentLocation.value) count++;
  if (selectedCategory.value.length > 0) count++;
  return count;
});


const defaultFilterState: FilterState = {
  dateRange: {
    start: new Date(),
    end: new Date()
  },
  center: {
    lat: -13.963280001090016,
    lng: 33.792221716425516
  },
  radius: 1000,
  currentLocation: '',
  selectedCategory: []
};



const getPersistedFilters = (): FilterState => {
  const savedFilters = localStorage.getItem('eventFilters');
  return savedFilters
    ? JSON.parse(savedFilters)
    : { ...defaultFilterState };
};

const modifySelectedCategory = (category: Category) => {
  if (selectedCategory.value.find(c => c.name === category.name)) {
    selectedCategory.value = selectedCategory.value.filter(
      (c) => c.name !== category.name
    );
  } else {
    selectedCategory.value.push(category);
  }
};

const saveFilters = () => {
  const currentFilters: FilterState = {
    dateRange: dateRange.value,
    center: center.value,
    radius: radius.value,
    currentLocation: currentLocation.value,
    selectedCategory: selectedCategory.value
  };

  localStorage.setItem('eventFilters', JSON.stringify(currentFilters));
};

const applyFilters = (): void => {
  saveFilters();

  const filtersToApply: any = {};

  if (center.value.lat && center.value.lng) {
    filtersToApply.location = {
      center: center.value,
      radius: radius.value,
      address: currentLocation.value
    };
  }

  if (dateRange.value.start && dateRange.value.end) {
    filtersToApply.dateRange = dateRange.value;
  }

  if (selectedCategory.value.length > 0) {
    filtersToApply.categories = selectedCategory.value;
  }

  $toast.success("Filters applied")
  $emit('apply-filters', filtersToApply);
};

const clearAllFilters = (): void => {
  dateRange.value = { ...defaultFilterState.dateRange };
  center.value = { ...defaultFilterState.center };
  currentLocation.value = defaultFilterState.currentLocation;
  radius.value = defaultFilterState.radius;
  selectedCategory.value = [];
  localStorage.removeItem('eventFilters');
  $toast.success("All filters cleared");
  $emit('clear-filters');
};

watch([dateRange, center, radius, currentLocation, selectedCategory], () => {
  saveFilters();
}, { deep: true });

const getCurrentLocation = async () => {
  isLocating.value = true;
  if (navigator.geolocation) {
    try {
      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject)
      });
      const { latitude, longitude } = position.coords;
      center.value = {
        lat: latitude,
        lng: longitude,
      };
      try {
        const response = await fetch(
          `${ENDPOINTS.GOOGLE.GEOCODE}=${latitude},${longitude}&key=${runtimeConfig.public.googleMapsApiKey}`
        );
        const data = await response.json();
        if (data.status === "OK" && data.results.length > 0) {
          const address = data.results[0].formatted_address;
          currentLocation.value = address;
        } else {
          console.error("No address found for the location.");
        }
      } catch (error) {
        console.error("Error fetching geocoding data:", error);
      }
    } catch (error) {
      console.error("Error getting current location:", error);
      currentLocation.value = "Unable to retrieve location";
    } finally {
      isLocating.value = false;
    }
  } else {
    console.error("Geolocation is not supported by this browser.");
    isLocating.value = false;
  }
};

onMounted(() => {
  const persistedFilters = getPersistedFilters();

  dateRange.value = persistedFilters.dateRange;
  center.value = persistedFilters.center;
  radius.value = persistedFilters.radius;
  currentLocation.value = persistedFilters.currentLocation;
  selectedCategory.value = persistedFilters.selectedCategory;

  if (!currentLocation.value) {
    getCurrentLocation();
  }
});
</script>

<style scoped>
.slider {
  @apply h-2 bg-gray-200 dark:bg-zinc-600 appearance-none cursor-pointer;
  border-radius: 4px;
}

.slider::-webkit-slider-thumb {
  @apply w-4 h-4 bg-red-600 hover:bg-red-700 appearance-none cursor-pointer;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.slider::-moz-range-thumb {
  @apply w-4 h-4 bg-red-600 hover:bg-red-700 cursor-pointer;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>
