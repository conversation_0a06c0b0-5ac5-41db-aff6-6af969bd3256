<template>
    <FormKit id="createTicketsForm" @submit="$emit('submit')" type="form"
        submit-label="Update" @submit-invalid="$emit('submit-invalid', $event)" :actions="false">
        <div class="bg-white dark:bg-zinc-900 px-6 py-4 border-b dark:border-zinc-800">
            <div class="flex items-center justify-between">
                <h3 class="text-lg text-black font-semibold dark:text-zinc-100">Tickets</h3>
                <button @click="$emit('skip')"
                    class="flex items-center font-medium text-red-500 hover:text-red-600 transition-colors duration-200">
                    Skip
                    <Icon icon="heroicons:arrow-right" class="w-4 h-4 ml-1" />
                </button>
            </div>
        </div>
        <div class="w-full sm:grid sm:grid-cols-3 gap-6 p-6 bg-white dark:bg-zinc-900">
            <div class="col-span-2 order-1">
                <TicketsManager @update:selections="$emit('update:selections', $event)" />
                <TicketsGenerationSuccess :file="generationResponse" ref="ticketsGenerationRef"
                    @update:next="$emit('next', $event)" />
            </div>
            <div class="col-span-1 order-2">
                <div class="bg-gray-50 dark:bg-zinc-800 p-5 border border-zinc-100 border-dotted dark:border-zinc-700">
                    <div class="flex items-center text-lg font-medium mb-4 dark:text-white">
                        <Icon icon="solar:help-bold-duotone" class="w-5 h-5 mr-2" />
                        <h3>How to add and select tickets</h3>
                    </div>
                    <p class="mb-3 text-gray-600 dark:text-zinc-300">Follow these steps to select your tickets:</p>
                    <ol class="list-decimal list-inside mb-4 space-y-2 text-gray-600 dark:text-zinc-300">
                        <li>Select a package from the dropdown menu.</li>
                        <li>Specify the number of tickets you wish to purchase.</li>
                        <li>If you need to add another package, click on the "Add another package" button.</li>
                        <li>To remove a package selection, click the close button (X) on the top right of the package card.</li>
                    </ol>
                    <p class="mb-2 text-gray-600 dark:text-zinc-300">Make sure to review your selections before proceeding!</p>
                    <p class="text-gray-600 dark:text-zinc-300">If you have any questions, feel free to reach out to our support team.</p>
                </div>
            </div>
        </div>
    </FormKit>
</template>

<script setup lang="ts">
interface Props {
    generationResponse?: string;
}

const props = withDefaults(defineProps<Props>(), {
    generationResponse: ''
});

const emit = defineEmits(['submit', 'submit-invalid', 'skip', 'update:selections', 'next']);

const ticketsGenerationRef = ref<any>(null);

const openGenerationModal = () => {
    if (ticketsGenerationRef.value) {
        ticketsGenerationRef.value.openModal();
    }
};

defineExpose({
    openGenerationModal
});
</script>
