<template>
    <div class="absolute bottom-0 left-0 right-0 bg-white dark:bg-black shadow border-t dark:border-zinc-800 p-4">
        <div class="flex justify-between items-center">
            <button v-if="currentStep > 1" @click="$emit('previous')"
                class="border dark:border-zinc-700 text-gray-700 dark:text-zinc-300 hover:bg-gray-50 dark:hover:bg-zinc-800 px-4 py-2 flex items-center transition-colors duration-200">
                <Icon icon="fluent:arrow-previous-16-regular" class="w-5 h-5 mr-2" />
                Previous
            </button>
            <div v-else></div>

            <button @click="$emit('next')"
                class="bg-red-600 hover:bg-red-700 text-white flex items-center px-4 py-2 transition-colors duration-200">
                {{ getButtonText }}
                <Icon icon="fluent:arrow-next-16-regular" class="w-5 h-5 ml-2" />
            </button>
        </div>
    </div>
</template>

<script setup lang="ts">
interface Props {
    currentStep: number;
}

const props = defineProps<Props>();
const emit = defineEmits(['previous', 'next']);

const getButtonText = computed(() => {
    switch (props.currentStep) {
        case 1: return "Create Event";
        case 2: return "Generate tickets & Continue";
        case 3: return "Add Sponsors & Continue";
        case 4: return "Publish Event";
        default: return "Continue";
    }
});
</script>
