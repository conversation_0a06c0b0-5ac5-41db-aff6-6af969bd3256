<template>
    <FormKit type="form" :actions="false" @submit="$emit('submit')">
        <div class="bg-white dark:bg-zinc-900 px-6 py-4 border-b dark:border-zinc-800">
            <div class="flex items-center justify-between">
                <h3 class="text-lg text-black font-semibold dark:text-zinc-100">Sponsors</h3>
                <div class="flex items-center">
                    <button type="button" @click="showSponsorManual = true"
                        class="text-gray-500 dark:text-zinc-400 hover:text-gray-700 dark:hover:text-zinc-300 mr-3 sm:hidden"
                        aria-label="Show Info Panel">
                        <Icon icon="game-icons:info" class="w-5 h-5" />
                    </button>
                    <button type="button" @click="$emit('skip')"
                        class="flex items-center font-medium text-red-500 hover:text-red-600 transition-colors duration-200">
                        Skip
                        <Icon icon="heroicons:arrow-right" class="w-4 h-4 ml-1" />
                    </button>
                </div>
            </div>
        </div>

        <div class="w-full sm:grid sm:grid-cols-3 gap-6 bg-white dark:bg-zinc-900">
            <div class="col-span-2">
                <SponsorManager @update:selectedSponsors="$emit('update:selectedSponsors', $event)" />
            </div>
            <div :class="[
                'col-span-1 bg-gray-50 dark:bg-zinc-800 p-5 border border-gray-200 dark:border-zinc-700 shadow-sm',
                {
                    'hidden': !showSponsorManual,
                    'block fixed inset-0 z-50 bg-white dark:bg-zinc-900 p-4 overflow-auto': isMobile && showSponsorManual
                }
            ]" ref="infoPanel">
                <div v-if="isMobile && showSponsorManual" class="flex justify-end mb-4">
                    <button @click="showSponsorManual = false"
                        class="text-gray-500 dark:text-zinc-400 hover:text-gray-700 dark:hover:text-zinc-300">
                        <Icon icon="heroicons:x-mark" class="w-6 h-6" />
                    </button>
                </div>
                <h2 class="flex items-center text-lg font-medium mb-4 dark:text-white">
                    <Icon icon="ic:baseline-info" class="w-5 h-5 mr-2 text-red-500" />
                    Sponsor Information
                </h2>
                <p class="text-gray-600 dark:text-zinc-300">
                    Adding sponsors to your event can increase visibility and provide additional resources.
                    Select from the available sponsors or skip this step if you don't have sponsors yet.
                </p>
            </div>
        </div>
    </FormKit>
</template>

<script setup lang="ts">
const emit = defineEmits(['submit', 'skip', 'update:selectedSponsors']);

const showSponsorManual = ref<boolean>(false);

const isMobile = computed((): boolean => {
    if (typeof window === 'undefined') return false;
    return window.innerWidth < 640;
});
</script>
