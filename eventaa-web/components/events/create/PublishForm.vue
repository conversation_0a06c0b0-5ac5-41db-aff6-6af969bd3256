<template>
    <FormKit type="form" :actions="false" @submit="$emit('submit')">
        <div class="bg-white dark:bg-zinc-900 px-6 py-4 border-b dark:border-zinc-800">
            <h3 class="text-lg text-black font-semibold dark:text-zinc-100">Publish Event</h3>
            <p class="text-gray-600 dark:text-zinc-400 mt-1">Choose when you want your event to be visible to the public</p>
        </div>

        <div class="w-full sm:grid sm:grid-cols-3 gap-6 p-6 bg-white dark:bg-zinc-900">
            <div class="col-span-2 space-y-6">
                <EventsPublish ref="publishRef" :event="eventDetails!" />

                <div class="bg-white dark:bg-zinc-900 p-6 border border-gray-200 dark:border-zinc-800 shadow-sm">
                    <h4 class="text-base font-medium mb-4 dark:text-zinc-200">Publishing Options</h4>
                    <div class="form-group">
                        <label class="block text-base font-medium mb-2 dark:text-zinc-200">Publish Type</label>
                        <CoreSelect
                            v-model="publishType"
                            :options="[
                                { label: 'Publish now', value: 'immediate' },
                                { label: 'Schedule for later', value: 'schedule' }
                            ]"
                            placeholder="Select publish type"
                        />
                    </div>

                    <div v-if="publishType === 'schedule'" class="mt-4">
                        <FormKit type="group" name="scheduleDateGroup">
                            <label class="block text-base font-medium mb-2 dark:text-zinc-200">Schedule Date & Time</label>
                            <datepicker v-model="scheduleDate"
                                placeholder="Enter the publishing date and time"
                                input-class-name="w-full px-4 py-3 border border-gray-200 dark:border-zinc-700 dark:bg-zinc-800 dark:text-white" />
                        </FormKit>
                    </div>
                </div>

                <div class="bg-white dark:bg-zinc-900 p-6 border border-gray-200 dark:border-zinc-800 shadow-sm">
                    <h4 class="text-base font-medium mb-4 dark:text-zinc-200">Event Preview</h4>
                    <div class="space-y-4">
                        <div class="flex space-x-4">
                            <img :src="`${runtimeConfig.public.baseUrl}storage/events/${eventDetails?.cover_art}`"
                                alt="event-cover"
                                class="w-24 h-24 object-cover border border-gray-200 dark:border-zinc-700" />
                            <div>
                                <h5 class="font-medium text-lg dark:text-white">{{ eventDetails?.title }}</h5>
                                <p class="text-gray-600 dark:text-zinc-400">
                                    {{ dayjs(eventDetails?.start).format('MMM D, YYYY h:mm A') }}
                                </p>
                            </div>
                        </div>
                        <div class="text-gray-600 dark:text-zinc-400 prose dark:prose-invert max-w-none"
                            v-html="eventDetails?.description"></div>
                    </div>
                </div>
            </div>

            <div class="col-span-1">
                <div class="bg-gray-50 dark:bg-zinc-800 p-6 border border-gray-200 dark:border-zinc-700 shadow-sm">
                    <h4 class="text-base font-medium mb-4 dark:text-white">Publishing Checklist</h4>
                    <ul class="space-y-4">
                        <li class="flex items-center">
                            <Icon
                                :icon="eventDetails?.title ? 'heroicons:check-circle' : 'heroicons:x-circle'"
                                class="w-5 h-5 mr-2"
                                :class="eventDetails?.title ? 'text-green-500' : 'text-red-500'" />
                            <span class="dark:text-zinc-300">Event title and description</span>
                        </li>
                        <li class="flex items-center">
                            <Icon
                                :icon="eventDetails?.start || eventDetails?.end ? 'heroicons:check-circle' : 'heroicons:x-circle'"
                                class="w-5 h-5 mr-2"
                                :class="eventDetails?.start || eventDetails?.end ? 'text-green-500' : 'text-red-500'" />
                            <span class="dark:text-zinc-300">Event date and time</span>
                        </li>
                        <li class="flex items-center">
                            <Icon
                                :icon="eventDetails?.category_id ? 'heroicons:check-circle' : 'heroicons:x-circle'"
                                class="w-5 h-5 mr-2"
                                :class="eventDetails?.category_id ? 'text-green-500' : 'text-red-500'" />
                            <span class="dark:text-zinc-300">Event category</span>
                        </li>
                        <li class="flex items-center">
                            <Icon
                                :icon="eventDetails?.cover_art ? 'heroicons:check-circle' : 'heroicons:x-circle'"
                                class="w-5 h-5 mr-2"
                                :class="eventDetails?.cover_art ? 'text-green-500' : 'text-red-500'" />
                            <span class="dark:text-zinc-300">Cover image</span>
                        </li>
                    </ul>

                    <div class="mt-6 p-4 bg-yellow-50 dark:bg-amber-900/20 border border-yellow-100 dark:border-amber-900/30">
                        <p class="text-yellow-700 dark:text-yellow-500 text-sm flex items-start">
                            <Icon icon="heroicons:exclamation-triangle" class="w-5 h-5 mr-2 flex-shrink-0" />
                            <span>Once published, basic event details cannot be modified. Make sure all information is correct.</span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </FormKit>
</template>

<script setup lang="ts">
import type { EventItem } from '@/types';
import dayjs from 'dayjs';

interface Props {
    eventDetails?: EventItem | null;
}

const props = withDefaults(defineProps<Props>(), {
    eventDetails: null
});

const emit = defineEmits(['submit', 'update:publishType', 'update:scheduleDate']);

const publishRef = ref<any>(null);
const runtimeConfig = useRuntimeConfig();

const publishType = defineModel<string>('publishType', { default: 'immediate' });
const scheduleDate = defineModel<string>('scheduleDate', { default: '' });

const openPublishModal = () => {
    if (publishRef.value) {
        publishRef.value.openModal();
    }
};

defineExpose({
    openPublishModal
});
</script>
