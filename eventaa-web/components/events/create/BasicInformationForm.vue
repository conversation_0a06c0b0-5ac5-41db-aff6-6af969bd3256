<template>
  <FormKit
    id="createEventForm"
    @submit="$emit('submit')"
    type="form"
    submit-label="Update"
    @submit-invalid="$emit('submit-invalid', $event)"
    :actions="false"
  >
    <div class="flex-grow">
      <div
        class="bg-white dark:bg-zinc-900 px-6 py-4 border-b dark:border-zinc-800"
      >
        <h3 class="text-lg text-black font-semibold dark:text-zinc-400">
          Basic Information
        </h3>
      </div>

      <div
        class="w-full relative bg-white dark:bg-zinc-900 sm:grid sm:grid-cols-3 gap-6 p-6"
      >
        <div class="col-span-2 order-1">
          <div class="flex flex-col space-y-3">
            <FormKit
              type="text"
              name="title"
              label="Title"
              v-model="title"
              placeholder="Enter the title of your event"
              validation="required"
              :classes="{
                input: 'pl-0',
                prefixIcon: 'w-0 h-0',
              }"
            />

            <div class="form-group">
              <label class="block text-base font-medium mb-2 dark:text-zinc-200"
                >Description</label
              >
              <RichTextEditor
                theme="snow"
                class="editor dark:text-white min-h-full"
                required
                v-model:content="description"
                contentType="html"
                :editor="ClassicEditor"
                v-model="description"
                :config="editorConfig"
              ></RichTextEditor>
            </div>

            <FormKit type="group" name="dateGroup">
              <label class="block text-base font-medium dark:text-zinc-200"
                >Start & End Date</label
              >
              <datepicker
                @cleared="$emit('date-cleared', $event)"
                required
                position="left"
                placeholder="Select start & end date"
                :range="true"
                input-class-name="w-full px-4 py-3 border border-gray-200 dark:border-zinc-700 dark:bg-zinc-800 dark:text-white"
                format="dd/MM/yyyy HH:mm"
                v-model="dateRange"
              />
            </FormKit>

            <div class="form-group">
              <label class="block text-base font-medium mb-3 dark:text-zinc-200"
                >Category</label
              >
              <CoreImageDropdown
                :items="$categories"
                v-model="selectedCategory"
              />
            </div>

            <div class="form-group">
              <label class="block text-base font-medium mb-2 dark:text-zinc-200"
                >Location type</label
              >
              <CoreSelect
                v-model="selectedLocationType"
                :options="[
                  { label: 'Online', value: 'Online' },
                  { label: 'Venue', value: 'Venue' },
                ]"
                placeholder="Select location type"
              />
            </div>

            <FormKit
              v-if="selectedLocationType === 'Online'"
              type="url"
              name="meetingLink"
              label="Meeting link"
              v-model="meetingLink"
              placeholder="Enter or copy & paste meeting link"
              validation="url"
              prefix-icon="globe"
            />

            <FormKit
              v-else-if="selectedLocationType == 'Venue'"
              type="group"
              name="locationGroup"
            >
              <label class="block text-base font-medium mb-2 dark:text-zinc-200"
                >Location Picker</label
              >
              <CoreLocationPicker @update:location="onUpdateLocation" />
              <div
                v-if="location?.address || location?.street"
                class="mt-3 p-3 bg-gray-50 dark:bg-zinc-800 border border-gray-200 dark:border-zinc-700 text-gray-700 dark:text-zinc-300"
              >
                <div v-if="location?.address">{{ location?.address }}</div>
                <div v-else-if="location?.street">
                  {{ location?.street }}, {{ location?.city }},
                  {{ location?.country }}
                </div>
              </div>
            </FormKit>

            <div class="form-group">
              <label class="block text-base font-medium mb-2 dark:text-zinc-200"
                >Visibility</label
              >
              <CoreSelect
                v-model="selectedVisibility"
                :options="[
                  { label: 'Public', value: 'Public' },
                  { label: 'Private', value: 'Private' },
                ]"
                placeholder="Select visibility"
              />
            </div>
          </div>
        </div>

        <div class="col-span-1 order-2">
          <div
            class="bg-white dark:bg-zinc-900 border border-gray-200 dark:border-zinc-800 shadow-sm"
          >
            <div
              class="w-full bg-gray-50 dark:bg-zinc-800 flex items-center justify-between border-b dark:border-zinc-700 px-4 py-3"
            >
              <h3 class="text-lg font-medium dark:text-white">Uploads</h3>
              <button
                class="text-gray-500 dark:text-zinc-400 hover:text-gray-700 dark:hover:text-zinc-300"
              >
                <Icon icon="lucide:minimize-2" class="w-5 h-5" />
              </button>
            </div>
            <div class="p-4">
              <h4 class="text-base font-medium mb-3 dark:text-zinc-200">
                Cover Art
              </h4>
              <EventsImagePicker
                @files-selected="$emit('cover-selected', $event)"
                @file-removed="$emit('file-removed')"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="hidden">
      <CoreSubmitButton
        ref="submitButtonRef"
        color="primary"
        :loading="loading"
      />
    </div>
  </FormKit>
</template>

<script setup lang="ts">
import type { Category } from "@/types";
import {
  ClassicEditor,
  Bold,
  Essentials,
  Heading,
  BlockQuote,
  Table,
  MediaEmbed,
  Font,
  FontColor,
  FontSize,
  Italic,
  Mention,
  Paragraph,
  Undo,
  Link,
  InputNumberView,
  List,
} from "ckeditor5";

interface Props {
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
});

const emit = defineEmits([
  "submit",
  "submit-invalid",
  "date-cleared",
  "cover-selected",
  "file-removed",
  "update:title",
  "update:description",
  "update:selectedCategory",
  "update:selectedLocationType",
  "update:selectedVisibility",
  "update:dateRange",
  "update:location",
  "update:meetingLink",
]);

const { $categories }: any = useNuxtApp();
const submitButtonRef = ref();

const title = defineModel<string>("title", { default: "" });
const description = defineModel<string>("description", { default: "" });
const selectedCategory = defineModel<Category>("selectedCategory", {
  default: { id: 0, name: "select Category", icon: "other.png" },
});
const selectedLocationType = defineModel<string>("selectedLocationType", {
  default: "",
});
const selectedVisibility = defineModel<string>("selectedVisibility", {
  default: "",
});
const dateRange = defineModel<any[]>("dateRange", { default: () => [] });
const location = defineModel<any>("location");
const meetingLink = defineModel<string>("meetingLink", { default: "" });

const editorConfig = {
  plugins: [
    Bold,
    Essentials,
    Heading,
    Italic,
    BlockQuote,
    Table,
    Font,
    FontColor,
    FontSize,
    MediaEmbed,
    Mention,
    Paragraph,
    Undo,
    Link,
    InputNumberView,
    List,
  ],
  toolbar: [
    "heading",
    "|",
    "bold",
    "italic",
    "link",
    "bulletedList",
    "numberedList",
    "blockQuote",
    "insertTable",
    "mediaEmbed",
    "undo",
    "redo",
    "imageUpload",
    "fontSize",
    "fontColor",
    "highlight",
  ],
};

const onUpdateLocation = (e: any): void => {
  location.value = e;
};

const triggerSubmit = () => {
  submitButtonRef.value?.$el.click();
};

defineExpose({
  triggerSubmit,
});
</script>

<style lang="css" scoped>
/* Rich text editor container styling */
.editor :deep(.ck-editor__editable) {
  min-height: 200px !important;
  max-height: 240px !important;
  overflow-y: auto !important;
}

.editor :deep(.ck-editor__main) {
  height: 100%;
}

/* Ensure proper scrolling in the editor container */
.editor :deep(.ck-content) {
  height: 100% !important;
}

/* Form group spacing */
.form-group {
  margin-bottom: 1rem;
}
</style>
