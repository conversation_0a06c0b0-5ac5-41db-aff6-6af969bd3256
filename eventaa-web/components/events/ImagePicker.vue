<script lang="ts">
import { defineComponent, ref, watch } from 'vue';

interface FilePreview {
  id: string;
  name: string;
  url: string;
  file: File;
}

export default defineComponent({
  name: 'DragDrop',
  props: {
    multiple: {
      type: Boolean,
      default: false
    },
    accept: {
      type: String,
      default: 'image/*'
    },
    maxSize: {
      type: Number,
      default: 5242880
    },
    file: {
      type: String,
      required: false
    }
  },
  emits: ['files-selected', 'file-removed', 'error'],
  setup(props, { emit }) {
    const isDragging = ref(false);
    const fileInput = ref<HTMLInputElement | null>(null);
    const previews = ref<FilePreview[]>([]);

    const createPreview = (file: File): FilePreview => ({
      id: Math.random().toString(36).substring(7),
      name: file.name,
      url: URL.createObjectURL(file),
      file
    });

    const handleFiles = (files: FileList | null) => {
      if (!files) return;

      const validFiles = Array.from(files).filter(file => {
        if (file.size > props.maxSize) {
          emit('error', `File ${file.name} is too large. Maximum size is ${props.maxSize / 1048576}MB`);
          return false;
        }
        return true;
      });

      if (!props.multiple && validFiles.length > 0) {
        previews.value = [createPreview(validFiles[0])];
      } else {
        previews.value = [...previews.value, ...validFiles.map(createPreview)];
      }

      emit('files-selected', validFiles);
    };

    const removeFile = (previewId: string) => {
      const preview = previews.value.find(p => p.id === previewId);
      if (preview) {
        URL.revokeObjectURL(preview.url);
        previews.value = previews.value.filter(p => p.id !== previewId);
        emit('file-removed', preview.file);
      }
    };

    // Watch for file prop changes to handle existing files
    watch(() => props.file, (newFile, oldFile) => {
      // Clear existing previews if file changes
      if (oldFile && newFile !== oldFile) {
        previews.value = [];
      }

      if (newFile && !previews.value.some(p => p.url === newFile)) {
        previews.value.push({
          id: Math.random().toString(36).substring(7),
          name: newFile.split('/').pop() || 'existing-file',
          url: newFile,
          file: new File([], newFile.split('/').pop() || 'existing-file')
        });
      }
    }, { immediate: true });

    const onDragEnter = (e: DragEvent) => {
      e.preventDefault();
      isDragging.value = true;
    };

    const onDragLeave = (e: DragEvent) => {
      e.preventDefault();
      isDragging.value = false;
    };

    const onDrop = (e: DragEvent) => {
      e.preventDefault();
      isDragging.value = false;
      handleFiles(e.dataTransfer?.files || null);
    };

    const triggerFileInput = () => {
      fileInput.value?.click();
    };

    return {
      isDragging,
      fileInput,
      previews,
      handleFiles,
      removeFile,
      onDragEnter,
      onDragLeave,
      onDrop,
      triggerFileInput
    };
  }
});
</script>

<template>
  <div class="w-full">
    <input
      ref="fileInput"
      type="file"
      :accept="accept"
      :multiple="multiple"
      class="hidden"
      @change="(($event: any) => handleFiles($event.target.files))"
    >

    <div
      class="relative border-2 border-dashed rounded-none p-8 text-center cursor-pointer transition-colors"
      :class="{
        'border-blue-400 bg-blue-50': isDragging,
        'border-gray-300 hover:border-gray-400': !isDragging
      }"
      @click="triggerFileInput"
      @dragenter="onDragEnter"
      @dragleave="onDragLeave"
      @dragover.prevent
      @drop="onDrop"
    >
      <div class="space-y-4">
        <div class="text-gray-600">
          <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
          </svg>
        </div>
        <div class="text-gray-700 font-medium">
          Drop your files here or click to browse
        </div>
        <div class="text-sm text-gray-500">
          {{ multiple ? 'Upload multiple files' : 'Upload a single file' }}
        </div>
      </div>
    </div>

    <div v-if="previews.length > 0" class="mt-4">
      <div
        v-for="preview in previews"
        :key="preview.id"
        class="relative group rounded-none overflow-hidden border border-gray-200"
      >
        <img
          :src="preview.url"
          :alt="preview.name"
          class="w-full h-72 object-cover"
        >
        <div class="absolute inset-0 bg-black bg-opacity-40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
          <button
            @click.stop="removeFile(preview.id)"
            class="p-2 bg-red-500 rounded-none text-white hover:bg-red-600 focus:outline-none"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
        <div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-sm p-1 truncate">
          {{ preview.name }}
        </div>
      </div>
    </div>
  </div>
</template>
