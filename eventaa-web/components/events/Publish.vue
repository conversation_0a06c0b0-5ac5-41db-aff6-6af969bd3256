<template>
  <TransitionRoot appear :show="isOpen" as="template">
    <Dialog as="div" @close="closeModal" class="relative z-50">
      <TransitionChild
        as="template"
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-black/25" />
      </TransitionChild>

      <div class="fixed inset-0 overflow-y-auto">
        <div
          class="flex min-h-full items-center justify-center p-4 text-center"
        >
          <TransitionChild
            as="template"
            enter="duration-300 ease-out"
            enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95"
          >
            <DialogPanel
              class="w-full relative max-w-5xl transform overflow-hidden bg-white dark:bg-zinc-900 text-left align-middle shadow-xl transition-all"
            >
              <DialogTitle class="w-full flex justify-end p-2">
                <button
                  @click.stop="closeModal"
                  type="button"
                  class="cursor-pointer p-1 hover:bg-gray-100 dark:hover:bg-zinc-700 rounded focus:outline-none focus:ring-2 focus:ring-gray-500 dark:focus:ring-zinc-400"
                >
                  <Icon icon="iconamoon:close" class="h-6 w-6 text-gray-700 dark:text-zinc-300" />
                </button>
              </DialogTitle>
              <CoreBalloons class="absolute top-0 left-0 pointer-events-none z-0" />
              <div class="grid grid-cols-1 md:grid-cols-2 gap-8 p-6 bg-white dark:bg-zinc-900 relative z-10">
                <div class="space-y-6">
                  <div>
                    <h2 class="text-3xl font-semibold text-gray-900 dark:text-white">
                      Your event is published 🎉
                    </h2>
                    <p class="text-gray-600 dark:text-zinc-400">
                      Future change will be published automatically.
                    </p>
                  </div>

                  <div class="space-y-4">
                    <div class="flex gap-2">
                      <input
                        type="text"
                        readonly
                        :value="eventLink"
                        class="flex-1 px-4 py-2 border border-gray-300 dark:border-zinc-600 bg-gray-50 dark:bg-zinc-800 text-gray-900 dark:text-zinc-100"
                      />
                      <button
                        @click.stop="copyLink"
                        :disabled="isCopyLoading"
                        type="button"
                        class="px-4 py-2 border border-gray-300 dark:border-zinc-600 hover:bg-gray-50 dark:hover:bg-zinc-700 flex items-center gap-2 disabled:opacity-50 focus:outline-none focus:ring-2 focus:ring-gray-500 dark:focus:ring-zinc-400 focus:ring-offset-2 text-gray-700 dark:text-zinc-300"
                      >
                        <CoreLoader
                          v-if="isCopyLoading"
                          :width="16"
                          :height="16"
                        />
                        <svg
                          v-else
                          class="w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                          />
                        </svg>
                        <span v-if="!isCopyLoading">Copy</span>
                      </button>
                    </div>
                  </div>

                  <div class="space-y-4">
                    <p class="text-center text-gray-600 dark:text-zinc-400">Scan to open in app</p>
                    <div class="flex justify-center">
                      <div class="border-4 border-red-600 dark:border-red-500 p-4 inline-block">
                        <qrcode-vue :value="eventLink" class="w-32 h-32" />
                      </div>
                    </div>
                    <p class="text-center text-gray-600 dark:text-zinc-400">
                      Scan this code with your phone to<br />
                      open your event in the app.
                    </p>
                  </div>
                </div>

                <div class="space-y-6">
                  <div>
                    <h2 class="text-2xl font-semibold text-gray-900 dark:text-white">
                      Invite people to your event
                    </h2>
                    <p class="text-gray-600 dark:text-zinc-400">
                      We'll email them instructions and a link to create an
                      account.
                    </p>
                  </div>

                  <div class="space-y-4">
                    <div class="flex gap-2">
                      <FormKit
                        v-model="newEmail"
                        type="email"
                        prefix-icon="email"
                        placeholder="Enter email address"
                        :disabled="isInviteLoading"
                        validation="email"
                        :classes="{
                          input: 'flex-1 px-4 py-2 border border-gray-300 dark:border-zinc-600 disabled:opacity-50 bg-white dark:bg-zinc-800 text-gray-900 dark:text-zinc-100'
                        }"
                        @keyup.enter="sendInvite"
                      />

                      <button
                        @click="sendInvite"
                        :disabled="!isValidEmail || isInviteLoading"
                        :class="[
                          'px-4 py-2 bg-red-600 dark:bg-red-500 text-white transition-colors duration-200',
                          'hover:bg-red-700 dark:hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 dark:focus:ring-red-400 focus:ring-offset-2',
                          (!isValidEmail || isInviteLoading) ? 'opacity-50 cursor-not-allowed' : ''
                        ]"
                        type="button"
                      >
                        <CoreLoader
                          v-if="isInviteLoading"
                          color="white"
                          height="16"
                          width="16"
                          class="mr-2"
                        />
                        <span v-if="!isInviteLoading">Send</span>
                        <span v-else>Sending...</span>
                      </button>
                    </div>
                  </div>

                  <div v-if="isLoading" class="flex justify-center py-8">
                    <CoreLoader :width="32" :height="32" />
                  </div>

                  <div v-else class="space-y-3">
                    <div
                      v-for="invitation in eventInvitations"
                      :key="invitation.email"
                      class="flex items-center gap-3 py-2"
                    >
                      <div
                        v-if="invitation.avatar"
                        class="w-10 h-10 rounded-full overflow-hidden"
                      >
                        <img
                          :src="invitation.avatar"
                          :alt="invitation.name"
                          class="w-full h-full object-cover"
                        />
                      </div>
                      <div
                        v-else
                        class="w-10 h-10 rounded-full bg-gray-200 dark:bg-zinc-700 flex items-center justify-center text-gray-600 dark:text-zinc-300"
                      >
                        {{ invitation.email[0].toUpperCase() }}
                      </div>
                      <div class="flex-1">
                        <div v-if="invitation.name" class="font-medium text-gray-900 dark:text-white">
                          {{ invitation.name }}
                        </div>
                        <div class="text-gray-600 dark:text-zinc-400">{{ invitation.email }}</div>
                      </div>
                      <div class="flex items-center gap-2">
                        <span
                          :class="{
                            'text-orange-600':
                              invitation.status === 'pending' ||
                              invitation.status === 'sent',
                            'text-green-600': invitation.status === 'accepted',
                            'text-red-600': invitation.status === 'declined',
                          }"
                        >
                          {{ getStatusText(invitation.status) }}
                        </span>
                        <button
                          v-if="
                            invitation.status === 'pending' ||
                            invitation.status === 'sent'
                          "
                          @click.stop="resendInvitation(invitation.id!)"
                          type="button"
                          class="text-xs text-blue-600 dark:text-blue-400 hover:underline focus:outline-none focus:ring-1 focus:ring-blue-500 dark:focus:ring-blue-400 px-1 py-0.5"
                        >
                          Resend
                        </button>
                        <button
                          @click.stop="deleteInvitation(invitation.id!)"
                          type="button"
                          class="text-xs text-red-600 dark:text-red-400 hover:underline focus:outline-none focus:ring-1 focus:ring-red-500 dark:focus:ring-red-400 px-1 py-0.5"
                        >
                          Remove
                        </button>
                      </div>
                    </div>

                    <div
                      v-if="eventInvitations.length === 0"
                      class="text-center py-8 text-gray-500 dark:text-zinc-400"
                    >
                      No invitations sent yet. Start by adding email addresses
                      above.
                    </div>
                  </div>

                  <div class="mt-6 p-4 bg-gray-50 dark:bg-zinc-800 border-gray-200 dark:border-zinc-700">
                    <div class="flex justify-between items-center">
                      <div>
                        <div class="font-medium text-gray-900 dark:text-white">
                          {{ totalInvitations }} invitations sent
                        </div>
                        <div class="text-gray-600 dark:text-zinc-400">
                          {{ pendingInvitations }} pending responses
                        </div>
                      </div>
                      <button
                        @click.stop="fetchEventInvitations"
                        type="button"
                        class="text-red-600 dark:text-red-400 hover:underline focus:outline-none focus:ring-1 focus:ring-red-500 dark:focus:ring-red-400 px-1 py-0.5 rounded"
                      >
                        Refresh
                      </button>
                    </div>
                    <div class="mt-4 bg-gray-200 dark:bg-zinc-600 rounded-full h-2">
                      <div
                        class="bg-red-600 dark:bg-red-500 rounded-full h-2"
                        :style="{
                          width:
                            totalInvitations > 0
                              ? `${
                                  ((totalInvitations - pendingInvitations) /
                                    totalInvitations) *
                                  100
                                }%`
                              : '0%',
                        }"
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from "@headlessui/vue";
import QrcodeVue from "qrcode.vue";
import type { PropType } from "vue";
import type { EventItem } from "@/types";
import type {
  EventInvitation,
  InviteUserRequest,
  EventInvitationsResponse,
  EventInvitationResponse,
} from "@/types/invitation";
import { useHttpClient } from "@/composables/useHttpClient";
import { ENDPOINTS } from "@/utils/api";

const props = defineProps({
  event: {
    type: Object as PropType<EventItem>,
    required: true,
  },
});

const isOpen = ref<boolean>(false);
const currentUrl = useRequestURL();
const eventLink = ref(`${currentUrl.origin}/events/${props.event?.slug}`);
const newEmail = ref<string>("");
const isLoading = ref<boolean>(false);
const isInviteLoading = ref<boolean>(false);
const isCopyLoading = ref<boolean>(false);
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const eventInvitations = ref<EventInvitation[]>([]);
const totalInvitations = ref<number>(0);
const pendingInvitations = ref<number>(0);

const closeModal = (): void => {
  isOpen.value = false;
  navigateTo("/dashboard/manage-events");
};

const openModal = async (): Promise<void> => {
  isOpen.value = true;
  if (isOpen.value) {
    eventLink.value = `${currentUrl.origin}/events/${props.event?.slug}`;
    await fetchEventInvitations();
  }
};

const fetchEventInvitations = async (): Promise<void> => {
  try {
    isLoading.value = true;
    const response = await httpClient.get<EventInvitationsResponse>(
      `${ENDPOINTS.EVENTS.INVITATIONS.LIST}/${props.event.id}`
    );

    if (response.data) {
      eventInvitations.value = response.data;
      totalInvitations.value = response.data.length;
      pendingInvitations.value = response.data.filter(
        (inv) => inv.status === "pending" || inv.status === "sent"
      ).length;
    }
  } catch (error) {
    console.error("Error fetching event invitations:", error);
    $toast.error("Failed to load event invitations");
  } finally {
    isLoading.value = false;
  }
};

const isValidEmail = computed(() => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(newEmail.value);
});

const getStatusText = (status: string): string => {
  switch (status) {
    case "pending":
    case "sent":
      return "Invite sent";
    case "accepted":
      return "Accepted";
    case "declined":
      return "Declined";
    default:
      return status;
  }
};

const copyLink = async (): Promise<void> => {
  try {
    isCopyLoading.value = true;
    await navigator.clipboard.writeText(eventLink.value);
    $toast.success("Event link copied to clipboard!");
  } catch (error) {
    console.error("Error copying to clipboard:", error);
    $toast.error("Failed to copy link");
  } finally {
    isCopyLoading.value = false;
  }
};

const sendInvite = async (): Promise<void> => {
  if (!isValidEmail.value || isInviteLoading.value) return;

  try {
    isInviteLoading.value = true;

    const inviteData: InviteUserRequest = {
      event_id: props.event.id,
      email: newEmail.value.trim(),
    };

    const response = await httpClient.post<EventInvitationResponse>(
      ENDPOINTS.EVENTS.INVITATIONS.SEND,
      inviteData
    );

    if (response.data) {
      eventInvitations.value.unshift(response.data);
      totalInvitations.value = eventInvitations.value.length;
      pendingInvitations.value = eventInvitations.value.filter(
        (inv) => inv.status === "pending" || inv.status === "sent"
      ).length;
      newEmail.value = "";
      $toast.success("Invitation sent successfully!");
    }
  } catch (error: any) {
    console.error("Error sending invitation:", error);
    if (error?.errors) {
      Object.keys(error.errors).forEach((key) => {
        if (Array.isArray(error.errors[key])) {
          error.errors[key].forEach((message: string) => {
            $toast.error(message);
          });
        } else if (typeof error.errors[key] === 'string') {
          $toast.error(error.errors[key]);
        }
      });
    } else if (error?.message) {
      $toast.error(error.message);
    } else {
      $toast.error("Failed to send invitation");
    }
  } finally {
    isInviteLoading.value = false;
  }
};

const resendInvitation = async (invitationId: number): Promise<void> => {
  try {
    await httpClient.post(
      `${ENDPOINTS.EVENTS.INVITATIONS.RESEND}/${invitationId}`
    );
    $toast.success("Invitation resent successfully!");
    await fetchEventInvitations();
  } catch (error) {
    console.error("Error resending invitation:", error);
    $toast.error("Failed to resend invitation");
  }
};

const deleteInvitation = async (invitationId: number): Promise<void> => {
  try {
    await httpClient.delete(
      `${ENDPOINTS.EVENTS.INVITATIONS.DELETE}/${invitationId}`
    );

    eventInvitations.value = eventInvitations.value.filter(
      (inv) => inv.id !== invitationId
    );
    totalInvitations.value = eventInvitations.value.length;
    pendingInvitations.value = eventInvitations.value.filter(
      (inv) => inv.status === "pending" || inv.status === "sent"
    ).length;

    $toast.success("Invitation removed successfully!");
  } catch (error) {
    console.error("Error deleting invitation:", error);
    $toast.error("Failed to remove invitation");
  }
};

defineExpose({
  openModal,
  closeModal,
});
</script>
