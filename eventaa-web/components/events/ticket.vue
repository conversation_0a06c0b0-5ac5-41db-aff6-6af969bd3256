<template>
  <div
    class="bg-white dark:bg-zinc-800 border-t border-gray-200 dark:border-zinc-700 transition-all duration-200 hover:shadow-lg group cursor-pointer"
    @click="showTicketDetails"
  >
    <div class="p-6 border-b border-gray-100 dark:border-zinc-700">
      <div class="flex items-start justify-between">
        <div class="flex-1">
          <div class="flex items-center space-x-3 mb-2">
            <div
              class="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 flex items-center justify-center text-white font-bold text-lg"
            >
              {{ ticket?.event?.title?.charAt(0)?.toUpperCase() || "T" }}
            </div>
            <div>
              <h3
                class="text-lg font-semibold text-gray-900 dark:text-white group-hover:text-red-600 dark:group-hover:text-red-400 transition-colors"
              >
                {{ ticket?.event?.title || "Event Ticket" }}
              </h3>
              <p class="text-sm text-gray-500 dark:text-zinc-400">
                {{ ticket?.ticket?.name || "General Admission" }}
              </p>
            </div>
          </div>

          <div
            class="flex items-center space-x-4 text-sm text-gray-600 dark:text-zinc-400"
          >
            <div
              v-if="ticket?.event?.start"
              class="flex items-center space-x-1"
            >
              <Icon icon="heroicons:calendar-days" class="w-4 h-4" />
              <span>{{ formatEventDate(ticket.event.start) }}</span>
            </div>
            <div
              v-if="ticket?.event?.location"
              class="flex items-center space-x-1"
            >
              <Icon icon="heroicons:map-pin" class="w-4 h-4" />
              <span class="truncate max-w-[200px]">{{
                ticket.event.location
              }}</span>
            </div>
          </div>
        </div>

        <div class="flex items-center space-x-2">
          <div
            v-if="ticket?.status"
            class="flex rounded-full items-center space-x-1 px-1.5 py-1 text-xs font-medium border"
            :class="getStatusBadgeClasses(ticket.status)"
          >
            <Icon :icon="getStatusIcon(ticket.status)" class="w-3 h-3" />
            <span class="text-xs">{{ getStatusText(ticket.status) }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="p-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Purchase Details -->
        <div class="space-y-4">
          <div>
            <div class="flex items-center space-x-2 mb-2">
              <Icon
                icon="heroicons:ticket"
                class="w-4 h-4 text-gray-400 dark:text-zinc-500"
              />
              <span
                class="text-xs font-medium text-gray-500 dark:text-zinc-400 uppercase tracking-wide"
                >Quantity</span
              >
            </div>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              {{ ticket?.quantity || 1 }}
            </p>
            <p class="text-xs text-gray-500 dark:text-zinc-400">
              {{ ticket?.quantity === 1 ? "Ticket" : "Tickets" }}
            </p>
          </div>
        </div>

        <!-- Payment Information -->
        <div class="space-y-4">
          <div>
            <div class="flex items-center space-x-2 mb-2">
              <Icon
                icon="heroicons:currency-dollar"
                class="w-4 h-4 text-gray-400 dark:text-zinc-500"
              />
              <span
                class="text-xs font-medium text-gray-500 dark:text-zinc-400 uppercase tracking-wide"
                >Total Paid</span
              >
            </div>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              {{
                formatPriceWithCurrency(
                  ticket?.total_amount || ticket?.unit_price || 0
                )
              }}
            </p>
            <p class="text-xs text-gray-500 dark:text-zinc-400">
              {{
                formatPriceWithCurrency(
                  (ticket?.total_amount || ticket?.unit_price || 0) /
                    (ticket?.quantity || 1)
                )
              }}
              per ticket
            </p>
          </div>
        </div>

        <!-- Actions -->
        <div class="flex flex-col space-y-3">
          <button
            @click.stop="downloadTicket"
            class="flex items-center justify-center space-x-2 px-4 py-3 bg-red-600 hover:bg-red-700 text-white font-medium transition-colors duration-200 group/btn"
          >
            <Icon
              icon="heroicons:arrow-down-tray"
              class="w-4 h-4 group-hover/btn:scale-110 transition-transform"
            />
            <span>Download PDF</span>
          </button>

          <TicketsRefundRequestDialog
            v-if="canRequestRefund(ticket)"
            :ticket="ticket || null"
            :is-open="isRefundDialogOpen"
            @close="closeRefundDialog"
            @refund-requested="handleRefundRequested"
          />
        </div>
      </div>

      <div
        v-if="ticket?.purchase_reference"
        class="mt-6 pt-4 border-t border-gray-100 dark:border-zinc-700"
      >
        <div class="flex items-center justify-between">
          <div>
            <span
              class="text-xs font-medium text-gray-500 dark:text-zinc-400 uppercase tracking-wide"
              >Reference</span
            >
            <p class="text-sm font-mono text-gray-900 dark:text-white">
              {{ ticket.purchase_reference }}
            </p>
          </div>
          <div class="text-right">
            <span
              class="text-xs font-medium text-gray-500 dark:text-zinc-400 uppercase tracking-wide"
              >Purchased</span
            >
            <p class="text-sm text-gray-900 dark:text-white">
              {{ formatPurchaseDate(ticket.created_at) }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Ticket Details Dialog -->
  <TransitionRoot appear :show="isTicketDetailsOpen" as="template">
    <Dialog as="div" @close="closeTicketDetails" class="relative z-10">
      <TransitionChild
        as="template"
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-black/25" />
      </TransitionChild>
      <div class="fixed inset-0 overflow-y-auto">
        <div class="flex min-h-full items-center justify-center p-4">
          <TransitionChild
            as="template"
            enter="duration-300 ease-out"
            enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95"
          >
            <DialogPanel
              class="w-full max-w-2xl transform overflow-hidden bg-white dark:bg-zinc-800 shadow-xl text-left align-middle transition-all"
            >
              <!-- Header -->
              <div class="bg-gradient-to-r from-red-500 to-red-600 px-6 py-4">
                <DialogTitle
                  as="h3"
                  class="text-xl font-semibold text-white flex justify-between items-center"
                >
                  <div class="flex items-center space-x-3">
                    <div
                      class="w-10 h-10 bg-white/20 flex items-center justify-center text-white font-bold"
                    >
                      {{
                        ticket?.event?.title?.charAt(0)?.toUpperCase() || "T"
                      }}
                    </div>
                    <span>Ticket Details</span>
                  </div>
                  <button
                    @click="closeTicketDetails"
                    class="text-white/80 hover:text-white transition-colors"
                  >
                    <Icon icon="heroicons:x-mark" class="w-6 h-6" />
                  </button>
                </DialogTitle>
              </div>

              <div class="p-6">
                <div class="bg-gray-50 dark:bg-zinc-700/50 p-4 mb-6">
                  <h4
                    class="text-lg font-semibold text-gray-900 dark:text-white mb-2"
                  >
                    {{ ticket?.event?.title || "Event Ticket" }}
                  </h4>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div
                      v-if="ticket?.event?.start"
                      class="flex items-center space-x-2"
                    >
                      <Icon
                        icon="heroicons:calendar-days"
                        class="w-4 h-4 text-gray-500 dark:text-zinc-400"
                      />
                      <span class="text-gray-600 dark:text-zinc-300">{{
                        formatEventDate(ticket.event.start)
                      }}</span>
                    </div>
                    <div
                      v-if="ticket?.event?.location"
                      class="flex items-center space-x-2"
                    >
                      <Icon
                        icon="heroicons:map-pin"
                        class="w-4 h-4 text-gray-500 dark:text-zinc-400"
                      />
                      <span class="text-gray-600 dark:text-zinc-300">{{
                        ticket.event.location
                      }}</span>
                    </div>
                  </div>
                </div>

                <div
                  class="flex items-center text-sm justify-between p-4 border border-gray-200 dark:border-zinc-600 mb-6"
                  :class="getStatusBadgeClasses(ticket?.status)"
                >
                  <div class="flex items-center space-x-3">
                    <Icon
                      :icon="getStatusIcon(ticket?.status)"
                      class="w-6 h-6"
                    />
                    <div>
                      <span class="font-semibold text-sm">{{
                        getStatusText(ticket?.status)
                      }}</span>
                      <p
                        v-if="ticket?.status === 'refunded'"
                        class="text-xs opacity-75"
                      >
                        Refund processed
                      </p>
                    </div>
                  </div>
                  <div
                    v-if="
                      ticket?.status === 'refunded' && ticket?.refund_amount
                    "
                    class="text-right"
                  >
                    <p class="text-xs opacity-75">Refunded Amount</p>
                    <p class="font-bold">
                      {{ formatPriceWithCurrency(ticket.refund_amount) }}
                    </p>
                  </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                  <div class="text-center p-4 bg-gray-50 dark:bg-zinc-700/50">
                    <Icon
                      icon="heroicons:ticket"
                      class="w-8 h-8 mx-auto mb-2 text-red-600 dark:text-red-400"
                    />
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">
                      {{ ticket?.quantity || 1 }}
                    </p>
                    <p
                      class="text-xs text-gray-500 dark:text-zinc-400 uppercase tracking-wide"
                    >
                      {{ ticket?.quantity === 1 ? "Ticket" : "Tickets" }}
                    </p>
                  </div>
                  <div class="text-center p-4 bg-gray-50 dark:bg-zinc-700/50">
                    <Icon
                      icon="heroicons:currency-dollar"
                      class="w-8 h-8 mx-auto mb-2 text-red-600 dark:text-red-400"
                    />
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">
                      {{
                        formatPriceWithCurrency(
                          ticket?.total_amount || ticket?.unit_price || 0
                        )
                      }}
                    </p>
                    <p
                      class="text-xs text-gray-500 dark:text-zinc-400 uppercase tracking-wide"
                    >
                      Total Paid
                    </p>
                  </div>
                  <div class="text-center p-4 bg-gray-50 dark:bg-zinc-700/50">
                    <Icon
                      icon="heroicons:calculator"
                      class="w-8 h-8 mx-auto mb-2 text-red-600 dark:text-red-400"
                    />
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">
                      {{
                        formatPriceWithCurrency(
                          (ticket?.total_amount || ticket?.unit_price || 0) /
                            (ticket?.quantity || 1)
                        )
                      }}
                    </p>
                    <p
                      class="text-xs text-gray-500 dark:text-zinc-400 uppercase tracking-wide"
                    >
                      Per Ticket
                    </p>
                  </div>
                </div>

                <div
                  v-if="ticket?.purchase_reference"
                  class="bg-gray-50 dark:bg-zinc-700/50 p-4 mb-6"
                >
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p
                        class="text-xs font-medium text-gray-500 dark:text-zinc-400 uppercase tracking-wide mb-1"
                      >
                        Purchase Reference
                      </p>
                      <p
                        class="font-mono text-sm text-gray-900 dark:text-white"
                      >
                        {{ ticket.purchase_reference }}
                      </p>
                    </div>
                    <div>
                      <p
                        class="text-xs font-medium text-gray-500 dark:text-zinc-400 uppercase tracking-wide mb-1"
                      >
                        Purchase Date
                      </p>
                      <p class="text-sm text-gray-900 dark:text-white">
                        {{ formatPurchaseDate(ticket.created_at) }}
                      </p>
                    </div>
                  </div>
                </div>

                <div
                  class="flex flex-col sm:flex-row gap-3 pt-4 border-t border-gray-200 dark:border-zinc-700"
                >
                  <button
                    v-if="canRequestRefund(ticket)"
                    @click="openRefundDialog"
                    class="flex-1 inline-flex items-center justify-center px-4 py-3 border border-red-300 dark:border-red-600 text-red-700 dark:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 font-medium transition-colors duration-200"
                  >
                    <Icon
                      icon="heroicons:arrow-uturn-left"
                      class="w-4 h-4 mr-2"
                    />
                    Request Refund
                  </button>
                  <button
                    @click="downloadTicket"
                    class="flex-1 inline-flex items-center justify-center px-4 py-3 bg-red-600 hover:bg-red-700 text-white font-medium transition-colors duration-200"
                  >
                    <Icon
                      icon="heroicons:arrow-down-tray"
                      class="w-4 h-4 mr-2"
                    />
                    Download PDF
                  </button>
                </div>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import {
  Dialog,
  DialogPanel,
  DialogTitle,
  TransitionChild,
  TransitionRoot,
} from "@headlessui/vue";
import type { TicketPurchase } from "@/types/ticket";
import { useRefundHelpers } from "@/services/refundService";

const props = defineProps<{
  ticket?: TicketPurchase | null;
}>();

const emit = defineEmits(["ticket-updated"]);

const isTicketDetailsOpen = ref(false);
const isRefundDialogOpen = ref(false);
const { canRequestRefund } = useRefundHelpers();

const formatPriceWithCurrency = (amount: number): string => {
  const currency = props.ticket?.payment?.currency;
  const symbol = currency?.symbol || "$";

  const formattedAmount = Number(amount).toLocaleString("en-US", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });

  return `${symbol}${formattedAmount}`;
};

const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const showTicketDetails = () => {
  isTicketDetailsOpen.value = true;
};

const closeTicketDetails = () => {
  isTicketDetailsOpen.value = false;
};

const downloadTicket = async (): Promise<void> => {
  if (!props.ticket?.id) {
    console.error("Ticket information not available");
    return;
  }

  try {
    const response = await httpClient.get<any>(
      `/tickets/download/${props.ticket.id}`,
      {
        responseType: "blob",
      }
    );

    if (response) {
      const blob =
        response instanceof Blob
          ? response
          : new Blob([response], { type: "application/pdf" });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `ticket-${props.ticket.id}-${new Date().getTime()}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      $toast.success("Ticket downloaded successfully");
    } else {
      throw new Error("Failed to download ticket");
    }
  } catch (error) {
    console.error("Error downloading ticket:", error);
  }
};

const openRefundDialog = () => {
  isRefundDialogOpen.value = true;
};

const closeRefundDialog = () => {
  isRefundDialogOpen.value = false;
};

const handleRefundRequested = (data: any) => {
  // Update the ticket status locally
  if (props.ticket) {
    props.ticket.status = "refunded";
    props.ticket.refund_amount = data.refundData?.refund_amount;
    props.ticket.refunded_at = new Date().toISOString();
    props.ticket.cancellation_reason = data.refundData?.reason;
  }

  // Emit event to parent component to refresh data
  emit("ticket-updated", props.ticket);

  closeRefundDialog();
};

const getStatusText = (status?: string): string => {
  switch (status?.toLowerCase()) {
    case "completed":
      return "Purchased";
    case "pending":
      return "Pending Payment";
    case "refunded":
      return "Refunded";
    case "cancelled":
      return "Cancelled";
    default:
      return "Unknown";
  }
};

const getStatusIcon = (status?: string): string => {
  switch (status?.toLowerCase()) {
    case "completed":
      return "heroicons:check-circle";
    case "pending":
      return "heroicons:clock";
    case "refunded":
      return "heroicons:arrow-uturn-left";
    case "cancelled":
      return "heroicons:x-circle";
    default:
      return "heroicons:question-mark-circle";
  }
};

const getStatusBadgeClasses = (status?: string): string => {
  switch (status?.toLowerCase()) {
    case "completed":
      return "bg-green-50 dark:bg-green-900/30 text-green-700 dark:text-green-300 border-green-200 dark:border-green-700";
    case "pending":
      return "bg-yellow-50 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 border-yellow-200 dark:border-yellow-700";
    case "refunded":
      return "bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-700";
    case "cancelled":
      return "bg-red-50 dark:bg-red-900/30 text-red-700 dark:text-red-300 border-red-200 dark:border-red-700";
    default:
      return "bg-gray-50 dark:bg-gray-900/30 text-gray-700 dark:text-gray-300 border-gray-200 dark:border-gray-700";
  }
};

const formatEventDate = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      weekday: "short",
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  } catch {
    return "Date TBA";
  }
};

const formatPurchaseDate = (dateString?: string): string => {
  if (!dateString) return "Unknown";
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  } catch {
    return "Unknown";
  }
};
</script>

<style lang="sass" scoped></style>
