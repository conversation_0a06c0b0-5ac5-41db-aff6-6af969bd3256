<template>
    <div @click="fetchEventDetails" class="bg-red-100 px-4 py-1 border-l-4 border-red-600 rounded-none">
        {{ calendarEvent.title }}
    </div>
    <TransitionRoot appear :show="isOpen" as="template">
        <Dialog as="div" class="relative z-10" @close="handleClose">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
                leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                <div class="fixed inset-0 bg-black/25" />
            </TransitionChild>
            <div class="fixed inset-0 overflow-y-auto">
                <div class="flex min-h-full items-center justify-center p-4">
                    <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                        enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
                        leave-to="opacity-0 scale-95">
                        <DialogPanel
                            class="w-full max-w-2xl transform overflow-hidden rounded-none bg-white align-middle shadow-xl transition-all">
                            <DialogTitle class="w-full flex border-b items-center justify-between px-4 py-2.5">
                                <h3 class="text-xl font-semibold">{{ props.calendarEvent?.title || 'View Event' }}</h3>
                                <button @click="handleClose">
                                    <Icon icon="mingcute:close-line" class="w-6 h-6" />
                                </button>
                            </DialogTitle>
                            <DialogDescription class="p-4">
                                <div v-if="loading" class="w-full flex flex-col items-center justify-center py-20 space-y-2">
                                    <CoreLoader/>
                                    <p class="text-black">Loading event details, please wait <span class="animate-pulse">...</span></p>
                                </div>
                                <div class="w-full flex flex-col space-y-2.5" v-else>
                                    <div class="w-full flex flex-col space-y-2">
                                        <h3 class="text-lg font-semibold">Description</h3>
                                        <div class="text-gray-500 font-normal border-2 border-dotted px-2 py-2" v-html="eventItem?.description">
                                        </div>
                                    </div>
                                    <div class="w-full flex flex-col space-y-2">
                                        <h3 class="text-lg font-semibold">Location</h3>
                                        <p>{{ eventItem?.location }}</p>
                                    </div>
                                    <div class="w-full flex flex-col space-y-2">
                                        <h3 class="text-lg font-semibold">Visibility</h3>
                                        <p>{{ eventItem?.visibility?.name }}</p>
                                    </div>
                                    <div class="w-full flex flex-col space-y-2">
                                        <h3 class="text-lg font-semibold">Category</h3>
                                        <p>{{ eventItem?.category?.name }}</p>
                                    </div>
                                    <div class="w-full flex flex-col space-y-2">
                                        <h3 class="text-lg font-semibold">Start & End date</h3>
                                        <p>{{ eventItem?.start }} - {{ eventItem?.end }}</p>
                                    </div>
                                    <div class="w-full flex flex-col space-y-2">
                                        <h3 class="text-lg font-semibold">Ratings</h3>
                                        <p>{{  }}</p>
                                    </div>
                                    <div class="space-y-4">
                                                <h2 class="text-lg font-bold mb-4">Ticket Packages</h2>
                                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                    <div v-for="tier in eventItem?.tiers" :key="tier.id"
                                                        class="border rounded-none p-6 hover:shadow-lg transition-shadow">
                                                        <div class="flex justify-between items-start mb-4">
                                                            <div>
                                                                <h3 class="text-xl font-semibold">{{ tier.name }}</h3>
                                                                <p class="text-gray-600">{{ tier.description }}</p>
                                                            </div>
                                                            <span
                                                                class="bg-blue-100 text-blue-800 text-sm font-medium px-3 py-1 rounded-full">
                                                                ${{ tier.price }}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                </div>
                            </DialogDescription>
                            <div class="w-full border-t mt-4 flex items-center justify-end px-5 py-2.5">
                                <div class="flex items-center space-x-3">
                                    <button class="flex text-gray-500 font-medium items-center space-x-3 border px-4 py-2">
                                        <Icon icon="line-md:edit" class="w-5 h-5 mr-2" />
                                        Edit
                                    </button>
                                </div>
                            </div>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>
</template>

<script setup lang="ts">
import { Dialog, DialogPanel, TransitionRoot, TransitionChild, DialogTitle, DialogDescription } from '@headlessui/vue';
import type { EventItem } from '@/types';

interface Props {
    calendarEvent: any;
}

const props = defineProps<Props>();
const isOpen = ref<boolean>(false);
const loading = ref<boolean>(false);
const { $toast }: any = useNuxtApp();
const eventItem = ref<EventItem>();
const httpClient = useHttpClient();

const handleClose = (): void => {
    isOpen.value = false;
};

const fetchEventDetails = async (): Promise<void> => {
    isOpen.value = true;
    loading.value = true;
    const response: { event: EventItem } = await httpClient.get(`${ENDPOINTS.EVENTS.READ}/${props.calendarEvent.id}`);
    if (response) {
        eventItem.value = response.event;
        loading.value = false;
    }
    if (!response) {
        $toast.error('Error fetching event details');
        loading.value = false;
    }
};

</script>