<template>
    <button @click="openModal"
        class="flex items-center justify-center text-sm space-x-2 text-red-600 font-regular px-2 py-1.5">
        <Icon icon="ic:outline-rate-review" class="w-4 h-4 mr-2" />
        Add a review
    </button>
    <TransitionRoot appear :show="isOpen" as="template">
        <Dialog as="div" @close="closeModal" class="relative z-10">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
                leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                <div class="fixed inset-0 bg-black/25" />
            </TransitionChild>

            <div class="fixed inset-0 overflow-y-auto">
                <div class="flex min-h-full items-center justify-center p-4 text-center">
                    <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                        enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
                        leave-to="opacity-0 scale-95">
                        <DialogPanel
                            class="w-full max-w-xl transform overflow-hidden bg-white dark:bg-zinc-800 text-left align-middle shadow-xl transition-all">
                            <DialogTitle class="w-full flex justify-between items-center px-4 py-2 border-b dark:border-zinc-700">
                                <h3 class="text-xl font-semibold leading-6 text-gray-900 dark:text-zinc-50">Add Review</h3>
                                <button type="button">
                                    <Icon icon="material-symbols:close" class="w-5 h-5 dark:text-zinc-100" @click="closeModal" />
                                </button>
                            </DialogTitle>
                            <FormKit id="createPackageForm" @submit="postReview" type="form" submit-label="Update"
                                :actions="false" #default="{ state: { valid } }">

                                <div class="w-full flex flex-col space-y-2 py-2 bg-gray-50 border-b dark:bg-zinc-800 dark:border-zinc-700 px-4">
                                    <div>
                                        <h2 class="text-2xl font-bold mb-1 dark:text-zinc-50">Overall Rating</h2>
                                        <CoreStarRating v-model="overallRating"/>
                                        <div class="flex items-center mb-4">
                                            <span class="text-5xl font-bold text-red-600">{{ overallRating.toFixed(1)
                                                }}</span>
                                            <span class="text-sm text-gray-500 ml-2 dark:text-zinc-100">/5</span>
                                        </div>

                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div>
                                                <h3 class="text-lg font-medium mb-2 dark:text-zinc-50">Organization</h3>
                                                <div class="flex items-center">
                                                    <div class="w-full">
                                                        <div class="flex items-center justify-between">
                                                            <span class="text-4xl font-bold dark:text-zinc-50">{{
                                                                organizationRating.toFixed(1) }}</span>
                                                            <span class="text-sm text-gray-500 dark:text-zinc-100">(5)</span>
                                                        </div>
                                                        <input v-model.number="organizationRating" type="range" min="0"
                                                            max="5" step="0.1"
                                                            class="w-full h-2 bg-gray-200 dark:bg-zinc-700 dark:text-zinc-50 rounded-full appearance-none cursor-pointer focus:outline-none focus:ring-2 focus:ring-red-500" />
                                                    </div>
                                                </div>
                                            </div>

                                            <div>
                                                <h3 class="text-lg font-medium mb-2 dark:text-zinc-50">Content</h3>
                                                <div class="flex items-center">
                                                    <div class="w-full">
                                                        <div class="flex items-center justify-between">
                                                            <span class="text-4xl font-bold dark:text-zinc-50">{{ contentRating.toFixed(1)
                                                                }}</span>
                                                            <span class="text-sm text-gray-500 dark:text-zinc-100">(5)</span>
                                                        </div>
                                                        <input v-model.number="contentRating" type="range" min="0"
                                                            max="5" step="0.1"
                                                            class="w-full h-2 bg-gray-200 dark:bg-zinc-700 dark:text-zinc-50 rounded-full appearance-none cursor-pointer focus:outline-none focus:ring-2 focus:ring-red-500" />
                                                    </div>
                                                </div>
                                            </div>

                                            <div>
                                                <h3 class="text-lg font-medium mb-2 dark:text-zinc-50">Engagement</h3>
                                                <div class="flex items-center">
                                                    <div class="w-full">
                                                        <div class="flex items-center justify-between">
                                                            <span class="text-4xl font-bold dark:text-zinc-50">{{
                                                                engagementRating.toFixed(1) }}</span>
                                                            <span class="text-sm text-gray-500 dark:text-zinc-100">(5)</span>
                                                        </div>
                                                        <input v-model.number="engagementRating" type="range" min="0"
                                                            max="5" step="0.1"
                                                            class="w-full h-2 bg-gray-200 dark:bg-zinc-700 dark:text-zinc-50 rounded-full appearance-none cursor-pointer focus:outline-none focus:ring-2 focus:ring-red-500" />
                                                    </div>
                                                </div>
                                            </div>

                                            <div>
                                                <h3 class="text-lg font-medium mb-2 dark:text-zinc-50">Technical</h3>
                                                <div class="flex items-center">
                                                    <div class="w-full">
                                                        <div class="flex items-center justify-between">
                                                            <span class="text-4xl font-bold dark:text-zinc-50">{{
                                                                technicalRating.toFixed(1) }}</span>
                                                            <span class="text-sm text-gray-500 dark:text-zinc-100">(5)</span>
                                                        </div>
                                                        <input v-model.number="technicalRating" type="range" min="0"
                                                            max="5" step="0.1"
                                                            class="w-full h-2 bg-gray-200 dark:bg-zinc-700 dark:text-zinc-50 rounded-full appearance-none cursor-pointer focus:outline-none focus:ring-2 focus:ring-red-500" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div>
                                        <h3 class="text-lg font-semibold mb-2 dark:text-zinc-50">Comment</h3>
                                        <RichTextEditor theme="snow" class="editor" required v-model:content="comment"
                                            contentType="html" :editor="ClassicEditor" v-model="comment"
                                            :config="editorConfig">
                                        </RichTextEditor>
                                    </div>
                                </div>

                                <div class="w-full py-2.5 px-4">
                                    <div class="flex items-center space-x-3 justify-end">
                                        <button class="border border-gray-100 dark:border-zinc-700 dark:text-zinc-100 text-gray-500 px-2 py-1.5"
                                            @click="closeModal">
                                            Cancel
                                        </button>
                                        <CoreSubmitButton :loading="loading" />
                                    </div>
                                </div>
                            </FormKit>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>
</template>

<script setup lang="ts">
import { ClassicEditor, Bold, Essentials, Heading, BlockQuote, Table, MediaEmbed, Font, FontColor, FontSize, Italic, Mention, Paragraph, Undo, Link, InputNumberView, List, } from 'ckeditor5';
import {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
} from '@headlessui/vue'
import { computed } from 'vue'

const props = defineProps({
    eventId: {
        type: Number,
        required: true
    }
})
const emits = defineEmits(["update:rating"]);
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();
const isOpen = ref<boolean>(false);
const loading = ref<boolean>(false);
const organizationRating = ref(1);
const contentRating = ref(1);
const engagementRating = ref(1);
const technicalRating = ref(1);
const comment = ref<string>('');

const overallRating = computed(() => {
    return (organizationRating.value +
        contentRating.value +
        engagementRating.value +
        technicalRating.value) / 4
});

const editorConfig = {
    plugins: [Bold, Essentials, Heading, Italic, BlockQuote, Table, Font, FontColor, FontSize, MediaEmbed, Mention, Paragraph, Undo, Link, InputNumberView, List],
    toolbar: ['heading', '|', 'bold', 'italic', 'link', 'bulletedList', 'numberedList', 'blockQuote', 'insertTable', 'mediaEmbed', 'undo', 'redo', 'fontSize', 'fontColor'],
}

const closeModal = (): void => {
    isOpen.value = false
}

const openModal = (): void => {
    isOpen.value = true
    isOpen.value = true
}

const postReview = async (): Promise<void> => {
    loading.value = true;
    try {
        const formData = new FormData();
        formData.append('organization', String(Math.ceil(organizationRating.value)));
        formData.append('content', String(Math.ceil(contentRating.value)));
        formData.append('engagement', String(Math.ceil(engagementRating.value)));
        formData.append('technical', String(Math.ceil(technicalRating.value)));
        formData.append('rating', String(Math.ceil(overallRating.value)));
        formData.append('review', comment.value);
        formData.append('event_id', String(props.eventId));

        const response = await httpClient.post<{ message: string; }>(ENDPOINTS.RATING.CREATE, formData);
        if (response) {
            $toast.success(response.message);
            emits('update:rating', true);
            closeModal();
        }
    } catch (error: any) {
        const errors = error.errors;
        Object.keys(errors).forEach((key) => {
            if (Array.isArray(errors[key])) {
                errors[key].forEach((message: string) => {
                    $toast.error(message);
                });
            } else if (typeof errors[key] === 'string') {
                $toast.error(errors[key]);
            }
        });
    } finally {
        loading.value = false;
    }
}
</script>