<template>
    <div class="w-full shadow-red-50 shadow-xl border-t border-gray-100 rounded-none">
        <div class="flex flex-col md:flex-row">
            <div class="w-full md:w-[500px] lg:w-[400px] xl:w-[500px] h-auto relative group">
                <img :src="`${runtimeConfig.public.baseUrl}storage/events/${event.cover_art}`" :alt="`${props.event.slug}-image`"
                    class="w-full h-48 md:h-48 lg:h-40 xl:h-48 object-cover group-hover:opacity-100 transition duration-300 ease-in-out" />
                <div
                    class="absolute inset-0 bg-black bg-opacity-10 transition-opacity duration-300 group-hover:opacity-0">
                </div>
                <div
                    class="absolute top-0 left-0 flex flex-col transition-opacity duration-300">
                    <button @click="toggleLike" class="bg-red-600 bg-opacity-75 text-white px-2 py-2 flex flex-col items-center justify-center">
                        <Icon :icon="liked ? 'mingcute:heart-fill' : 'mingcute:heart-line'"
                            class="w-4 h-4 md:w-5 md:h-5 mr-2 transition-all duration-300" :class="liked ? 'fill-animation' : ''" />
                        <span class="text-xs md:text-sm">{{ props.event.likes_count }}</span>
                    </button>
                </div>
            </div>
            <div class="flex flex-col space-y-1 p-3 md:ml-3 md:p-0 flex-1">
                <NuxtLink :to="`/events/${props.event.slug}`">
                    <h3 class="text-lg md:text-xl lg:text-2xl tracking-wide font-semibold mt-2 hover:text-sky-500 transition duration-150">
                        {{ props.event.title }}
                    </h3>
                </NuxtLink>
                <div v-html="props.event.description" class="text-sm md:text-base bg-gray-50 border-l-4 px-3 py-2 mr-0 md:mr-5 text-gray-600 font-normal line-clamp-2">
                </div>
            </div>
        </div>
        <div class="w-full border-t border-gray-100">
            <div class="w-full grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 px-3 md:px-5 py-3">
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 md:w-10 md:h-10 flex items-center justify-center bg-red-100 rounded-full">
                        <Icon icon="ion:calendar-sharp" class="w-4 h-4 md:w-5 md:h-5 text-red-600" />
                    </div>
                    <div class="flex-1 min-w-0">
                        <h3 class="text-sm md:text-lg font-semibold truncate">Event Starts On</h3>
                        <p class="text-xs md:text-base text-gray-600 truncate">{{ dayjs(props.event.start).format("DD/MM/YYYY HH:mm") }}</p>
                    </div>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 md:w-10 md:h-10 flex items-center justify-center bg-red-100 rounded-full">
                        <Icon icon="dashicons:tickets-alt" class="w-4 h-4 md:w-5 md:h-5 text-red-600" />
                    </div>
                    <div class="flex-1 min-w-0">
                        <h3 class="text-sm md:text-lg font-semibold truncate">Tickets Remaining</h3>
                        <p class="text-xs md:text-base font-medium text-gray-600">5</p>
                    </div>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 md:w-10 md:h-10 flex items-center justify-center bg-red-100 rounded-full">
                        <IconsGameIconsCash class="w-4 h-4 md:w-5 md:h-5 text-red-600" />
                    </div>
                    <div class="flex-1 min-w-0">
                        <h3 class="text-sm md:text-lg font-semibold truncate">Price Starts At</h3>
                        <p class="text-xs md:text-base font-medium text-gray-600">5,000</p>
                    </div>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 md:w-10 md:h-10 flex items-center justify-center bg-red-100 rounded-full">
                        <Icon icon="dashicons:location-alt" class="w-4 h-4 md:w-5 md:h-5 text-red-600" />
                    </div>
                    <div class="flex-1 min-w-0">
                        <h3 class="text-sm md:text-lg font-semibold truncate">Where?</h3>
                        <p class="text-xs md:text-base font-medium text-gray-600 truncate">{{ props.event.location }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { EventItem } from '@/types'
import dayjs from 'dayjs';
const props = defineProps({
    event: {
        type: Object as PropType<EventItem>,
        required: true
    }
});
const liked = ref(false);
const likesCount = ref(120);
const runtimeConfig = useRuntimeConfig();

const toggleLike = (): void => {
    liked.value = !liked.value;
    liked.value ? likesCount.value++ : likesCount.value--;
};
</script>

<style lang="scss" scoped>
.fill-animation {
  animation: fillAnimation 0.3s ease-in-out forwards;
}

@keyframes fillAnimation {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.3);
  }
  100% {
    transform: scale(1);
  }
}
</style>
