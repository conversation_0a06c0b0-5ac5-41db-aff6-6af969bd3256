<template>
  <div class="max-w-4xl mx-auto space-y-2">
    <div class="bg-white px-4 border-t border-b py-2">
      <div class="flex items-center justify-between">
        <h1 class="text-2xl font-bold">Rating & Highlights</h1>
        <div class="flex flex-col items-end">
          <div class="flex items-center gap-2">
            <span class="text-4xl font-bold text-red-600">{{ Math.round(eventData.overall) }}</span>
            <div class="flex">
              <template v-for="i in 5" :key="i">
                <span class="text-2xl" :class="eventData.overall >= i ? 'text-yellow-400' : 'text-gray-300'">
                  ★
                </span>
              </template>
            </div>
          </div>
          <span class="text-sm text-gray-500">Overall Rating</span>
        </div>
      </div>
    </div>

    <div class="bg-white px-4 py-4 border-b">
      <h2 class="text-2xl font-bold mb-6"> Ratings</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div v-for="(rating, aspect) in eventData.aspects" :key="aspect" class="relative">
          <div class="flex justify-between mb-2">
            <span class="text-lg capitalize">{{ aspect }}</span>
            <span class="font-medium">{{ Math.ceil(rating) }}/5</span>
          </div>
          <div class="h-3 bg-gray-200 rounded-full overflow-hidden">
            <div class="h-full bg-red-600 rounded-full" :style="{ width: `${(rating / 5) * 100}%` }"></div>
          </div>
        </div>
      </div>
    </div>

    <div class="bg-white p-8">
      <h2 class="text-2xl font-bold mb-6">Event Highlights</h2>

      <div class="grid grid-cols-3 gap-4 mb-8">
        <div v-for="category in ['positive', 'neutral', 'negative']" :key="category" class="p-4" :class="{
          'bg-green-50': category === 'positive',
          'bg-gray-50': category === 'neutral',
          'bg-red-50': category === 'negative'
        }">
          <div class="text-2xl font-bold mb-1">
            {{
              Object.keys(highlightsByCategory)[0] == category ? highlightsByCategory[category].length : 0
            }}
          </div>
          <div class="text-sm capitalize">{{ category }} Highlights</div>
        </div>
      </div>

      <div class="space-y-4">
        <div v-for="highlight in sortedHighlights" :key="highlight.id" class="flex gap-4">
          <div class="relative flex flex-col items-center">
            <div class="w-3 h-3 mt-2" :class="{
              'bg-green-400': highlight.category === 'positive',
              'bg-gray-400': highlight.category === 'neutral',
              'bg-red-400': highlight.category === 'negative'
            }"></div>
            <div class="w-px h-full bg-gray-200"></div>
          </div>

          <div class="flex-1 text-left pb-6">
            <div class="p-4" :class="{
              'bg-green-50': highlight.category === 'positive',
              'bg-gray-50': highlight.category === 'neutral',
              'bg-red-50': highlight.category === 'negative'
            }">
              <div class="flex items-center gap-2">
                <img src="/assets/images/user.jpg" alt="reviewer-alt" class="w-6 h-6 rounded-full" />
                <p class="text-lbase font-medium mb-2">{{ highlight.text }}</p>
              </div>
              <span class="text-sm text-gray-500">
                {{ formatTime(highlight.timestamp) }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { EventItem, Highlight } from '@/types';


const props = defineProps({
  event: {
    type: Object as PropType<EventItem>,
    required: true
  }
});

interface EventRating {
  overall: number;
  aspects: {
    organization: number;
    content: number;
    engagement: number;
    technical: number;
  };
  highlights: Highlight[];
}

const eventData: EventRating = {
  overall: props.event.ratings_avg_rating,
  aspects: {
    organization: props.event.ratings_avg_organization,
    content: props.event.ratings_avg_content,
    engagement: props.event.ratings_avg_engagement,
    technical: props.event.ratings_avg_technical
  },
  highlights: props.event.highlights || []
}

const sortedHighlights = computed(() => {
  return [...eventData.highlights].sort((a, b) =>
    new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
  )
})

const highlightsByCategory = computed(() => {
  return eventData.highlights ? eventData.highlights.reduce((acc, highlight) => {
    if (!acc[highlight.category]) {
      acc[highlight.category] = []
    }
    acc[highlight.category].push(highlight)
    return acc
  }, {} as Record<string, Highlight[]>) : {}
})

const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString()
}
</script>
