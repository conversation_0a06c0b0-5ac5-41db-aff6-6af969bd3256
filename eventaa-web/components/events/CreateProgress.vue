<template>
    <div class="flex items-center space-x-2">
        <div class="w-24 h-2 bg-gray-100 rounded-full overflow-hidden">
            <div class="h-full animate-pulse rounded-full bg-gradient-to-r from-green-500 to-emerald-600 transition-all duration-300 ease-out"
                :style="{ width: `${progressPercentage}%` }"></div>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
    steps: {
        type: Array,
        required: true,
        default: () => []
    }
})

const progressPercentage = computed(() => {
    if (!props.steps.length) return 0
    const completedSteps = props.steps.filter(step => step.completed).length
    return Math.round((completedSteps / props.steps.length) * 100)
})
</script>