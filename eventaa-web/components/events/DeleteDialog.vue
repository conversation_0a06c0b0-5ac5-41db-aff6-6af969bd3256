<template>
  <button @click="openModal" class="px-2 py-2 text-red-600">
    <Icon icon="heroicons:trash" class="w-5 h-5" />
  </button>
  <TransitionRoot appear :show="isOpen" as="template">
    <Dialog as="div" @close="closeModal" class="relative z-[9999]">
      <TransitionChild
        as="template"
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-black/25" />
      </TransitionChild>

      <div class="fixed inset-0 overflow-y-auto">
        <div
          class="flex min-h-full items-center justify-center p-4 text-center"
        >
          <TransitionChild
            as="template"
            enter="duration-300 ease-out"
            enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95"
          >
            <DialogPanel
              class="w-full max-w-xl transform overflow-hidden bg-white text-left align-middle shadow-xl transition-all"
            >
              <DialogTitle
                as="h3"
                class="w-full flex justify-between items-center px-4 py-2 border-b"
              >
                <h3 class="text-xl font-semibold leading-6">Delete Event</h3>
                <button>
                  <Icon
                    icon="mingcute:close-line"
                    class="w-5 h-5"
                    @click="closeModal"
                  />
                </button>
              </DialogTitle>
              <div class="px-4 py-2">
                <p class="text-base font-light text-gray-500">
                  Do you really want to delete this event
                  <b class="font-normal">`{{ event?.title }}`</b>? Note that
                  once this action is successfull, it cannot be reversed.
                </p>
              </div>

              <div class="px-4 py-2 border-t flex justify-end">
                <div class="w-32 inline-flex justify-end">
                  <CoreSubmitButton
                    text="Delete"
                    @click="deleteEvent"
                    :loading="loading"
                  />
                </div>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from "@headlessui/vue";
import type { EventItem } from "@/types";

const props = defineProps({
  event: {
    type: Object as PropType<EventItem>,
    required: true,
  },
});
const emits = defineEmits(["onEventDeleted"]);
const { $toast }: any = useNuxtApp();
const httpClient = useHttpClient();
const isOpen = ref<boolean>(false);
const loading = ref<boolean>(false);

const closeModal = () => {
  isOpen.value = false;
};
const openModal = () => {
  isOpen.value = true;
};

const deleteEvent = async (): Promise<void> => {
  loading.value = true;
  try {
    const response = await httpClient.delete<{ message: string }>(
      `${ENDPOINTS.EVENTS.DELETE}/${props.event.id}`
    );
    if (response) {
      $toast.success(response.message);
      closeModal();
      emits("onEventDeleted", true);
    }
  } catch (error) {
    console.error(error);
    $toast.error("Error deleting event");
  } finally {
    loading.value = false;
  }
};
</script>
