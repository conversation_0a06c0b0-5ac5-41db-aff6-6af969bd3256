<template>
  <TransitionRoot appear :show="isOpen" as="template">
    <Dialog as="div" @close="closeModal" class="relative z-10">
      <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
        leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
        <div class="fixed inset-0 bg-black/25" />
      </TransitionChild>
      <div class="fixed inset-0 overflow-y-auto">
        <div class="flex min-h-full items-center justify-center p-4">
          <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95">
            <DialogPanel class="w-full max-w-2xl transform overflow-hidden bg-white dark:bg-zinc-800 p-6 text-left align-middle shadow-xl transition-all">
              <div v-if="loading" class="flex justify-center items-center py-10">
                <CoreLoader />
              </div>
              <div v-else>
                <DialogTitle as="h3" class="text-xl font-semibold leading-6 text-gray-900 dark:text-white flex justify-between items-center">
                  Transaction Details
                  <button @click="closeModal" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <Icon icon="heroicons:x-mark" class="w-6 h-6" />
                  </button>
                </DialogTitle>

                <div v-if="!transaction" class="mt-4 text-gray-500 dark:text-gray-400">
                  No transaction details available
                </div>

                <div v-else class="mt-6 space-y-6">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-4 col-span-2">
                      <div>
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Transaction Information</h4>
                        <div class="mt-2 space-y-2">
                          <div class="flex justify-between bg-gray-50 p-2">
                            <span class="text-sm text-gray-600 dark:text-gray-300">Transaction ID:</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white">{{ transaction.transaction_id || transaction.transactionId }}</span>
                          </div>
                          <div class="flex justify-between p-2">
                            <span class="text-sm text-gray-600 dark:text-gray-300">Amount:</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white">{{ formatCurrency(parseFloat(transaction.amount), transaction.currency) }}</span>
                          </div>
                          <div class="flex justify-between bg-gray-50 p-2">
                            <span class="text-sm text-gray-600 dark:text-gray-300">Status:</span>
                            <span class="px-2 inline-flex rounded-full text-xs leading-5 font-semibold"
                              :class="{
                                'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200': transaction.status === 'completed',
                                'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200': transaction.status === 'pending',
                                'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200': transaction.status === 'failed',
                                'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200': transaction.status === 'refunded'
                              }">
                              {{ formatStatus(transaction.status) }}
                            </span>
                          </div>
                          <div class="flex justify-between p-2">
                            <span class="text-sm text-gray-600 dark:text-gray-300">Payment Method:</span>
                            <div class="flex items-center">
                              <img
                                :src="getPaymentMethodLogo(transaction)"
                                :alt="getPaymentMethodName(transaction)"
                                class="w-4 h-4 mr-2 object-contain"
                              />
                              <span class="text-sm font-medium text-gray-900 dark:text-white">{{ getPaymentMethodName(transaction) }}</span>
                            </div>
                          </div>
                          <div class="flex justify-between bg-gray-50 p-2">
                            <span class="text-sm text-gray-600 dark:text-gray-300">Date:</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white">{{ formatDate(transaction.date || transaction.created_at || '') }}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                  </div>

                  <div v-if="transaction.metadata?.tickets && transaction.metadata.tickets.length > 0" class="border-t border-gray-200 dark:border-gray-700 pt-6">
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-4">Tickets Purchased</h4>
                    <div class="space-y-2">
                      <div v-for="ticket in transaction.metadata.tickets" :key="ticket.ticket_id" class="flex justify-between items-center p-3 bg-gray-50 dark:bg-zinc-700">
                        <div>
                          <div class="text-sm font-medium text-gray-900 dark:text-white">{{ ticket.ticket_name }}</div>
                          <div class="text-sm text-gray-500 dark:text-gray-400">Quantity: {{ ticket.quantity }}</div>
                        </div>
                        <div class="text-sm font-medium text-gray-900 dark:text-white">{{ formatCurrency(parseFloat(ticket.unit_price), transaction.currency) }}</div>
                      </div>
                    </div>
                  </div>

                  <div class="border-t border-gray-200 dark:border-gray-700 pt-6 flex justify-end space-x-3">
                    <CorePrimaryButton @click="downloadReceipt" text="Download Receipt" :loading="loading" start-icon="vscode-icons:file-type-pdf2"/>
                    <button v-if="transaction.status === 'completed'" @click="$emit('refund', transaction)" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                      <Icon icon="heroicons:arrow-path" class="w-4 h-4 mr-2" />
                      Issue Refund
                    </button>
                  </div>
                </div>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from '@headlessui/vue';
import { ref, watch } from 'vue';
import type { Transaction } from '@/types';

const props = defineProps<{
  transaction: Transaction | null;
  isOpen: boolean;
}>();

const emit = defineEmits(['close', 'refund']);

const loading = ref(false);
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const isOpen = ref(false);

watch(() => props.isOpen, (newValue) => {
  isOpen.value = newValue;
});

const closeModal = () => {
  isOpen.value = false;
  emit('close');
};

const downloadReceipt = async () => {
  if (!props.transaction) return;
  loading.value = true;

  try {
    const response = await httpClient.get(`/payments/receipt/${props.transaction.id}`, {
      responseType: 'blob'
    }) as Blob;

    const url = window.URL.createObjectURL(response);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `receipt-${props.transaction.transactionId || props.transaction.transaction_id}.pdf`);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);

    $toast.success(`Receipt downloaded successfully`);
  } catch (error) {
    console.error('Error downloading receipt:', error);
    $toast.error('Failed to download receipt');
  } finally{
    loading.value = false;
    closeModal();
  }
};

function formatCurrency(amount: number, currency = "MWK") {
  const currencyCode = currency || "MWK";

  // Map currency codes to locales
  const localeMap: Record<string, string> = {
    "MWK": "en-MW",
    "USD": "en-US",
    "GBP": "en-GB",
    "EUR": "en-EU",
    "CAD": "en-CA",
    "ZAR": "en-ZA",
    "MK": "en-MW" // Handle PayChangu's MK code for Malawi Kwacha
  };

  // Default to en-US if no specific locale is found
  const locale = localeMap[currencyCode] || "en-US";

  // Convert MK to MWK for display (PayChangu uses MK)
  const displayCurrency = currencyCode === "MK" ? "MWK" : currencyCode;

  return new Intl.NumberFormat(locale, {
    style: "currency",
    currency: displayCurrency,
    minimumFractionDigits: ["MWK", "MK"].includes(currencyCode) ? 0 : 2,
  }).format(amount);
}

function formatDate(dateString: string | number | Date) {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}

function formatStatus(status: string) {
  return status.charAt(0).toUpperCase() + status.slice(1);
}

function getPaymentMethodLogo(transaction: Transaction) {
  if (!transaction.gateway_response?.data?.data?.mobile_money) {
    return "/assets/icons/credit-card.png";
  }

  const provider = transaction.gateway_response.data.data.mobile_money.name;

  switch (provider.toLowerCase()) {
    case "tnm mpamba":
      return "/assets/images/tnm.jpg";
    case "airtel money":
      return "/assets/images/airtel.png";
    default:
      return "/assets/icons/credit-card.png";
  }
}

function getPaymentMethodName(transaction: Transaction) {
  if (transaction.gateway_response?.data?.data?.mobile_money) {
    return transaction.gateway_response.data.data.mobile_money.name;
  }

  if (typeof transaction.payment_method === 'string') {
    return transaction.payment_method;
  }

  return "Unknown";
}

</script>
