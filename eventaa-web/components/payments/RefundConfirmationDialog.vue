<template>
  <TransitionRoot appear :show="isOpen" as="template">
    <Dialog as="div" @close="closeModal" class="relative z-50">
      <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
        leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
        <div class="fixed inset-0 bg-black/25" />
      </TransitionChild>

      <div class="fixed inset-0 overflow-y-auto">
        <div class="flex min-h-full items-center justify-center p-4 text-center">
          <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95">
            <DialogPanel class="w-full max-w-md transform overflow-hidden bg-white dark:bg-zinc-800 p-6 text-left align-middle shadow-xl transition-all">
              <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900 dark:text-white">
                Confirm Refund
              </DialogTitle>

              <div class="mt-2">
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  Are you sure you want to issue a refund for this transaction? This action cannot be undone.
                </p>
              </div>

              <div v-if="transaction" class="mt-4 p-4 bg-gray-50 dark:bg-zinc-700 space-y-2">
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-600 dark:text-gray-300">Transaction ID:</span>
                  <span class="text-sm text-gray-900 dark:text-white">{{ transaction.transaction_id || transaction.transactionId }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-600 dark:text-gray-300">Amount:</span>
                  <span class="text-sm text-gray-900 dark:text-white">{{ formatCurrency(parseFloat(transaction.amount), transaction.currency) }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-600 dark:text-gray-300">Customer:</span>
                  <span class="text-sm text-gray-900 dark:text-white">{{ transaction.user?.name }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-600 dark:text-gray-300">Event:</span>
                  <span class="text-sm text-gray-900 dark:text-white">{{ transaction.event?.title || transaction.event_name }}</span>
                </div>
              </div>

              <div class="mt-6 flex justify-end space-x-3">
                <button @click="closeModal" :disabled="loading" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-zinc-700 hover:bg-gray-50 dark:hover:bg-zinc-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed">
                  Cancel
                </button>
                <button @click="confirmRefund" :disabled="loading" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed">
                  <span v-if="loading" class="flex flex-col justify-center items-center">
                    <CoreLoader :height="12" :width="12"/>
                    Processing...
                  </span>
                  <span v-else class="flex items-center">
                    <Icon icon="heroicons:arrow-path" class="w-4 h-4 mr-2" />
                    Issue Refund
                  </span>
                </button>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from '@headlessui/vue';
import { ref, watch } from 'vue';
import type { Transaction } from '@/types';

const props = defineProps<{
  transaction: Transaction | null;
  isOpen: boolean;
}>();

const emit = defineEmits(['close', 'confirm']);

const loading = ref(false);
const isOpen = ref(false);

watch(() => props.isOpen, (newValue) => {
  isOpen.value = newValue;
});

const closeModal = () => {
  if (loading.value) return;
  isOpen.value = false;
  emit('close');
};

const confirmRefund = async () => {
  loading.value = true;
  emit('confirm', props.transaction);
};

const setLoading = (value: boolean) => {
  loading.value = value;
};

const closeAfterRefund = () => {
  loading.value = false;
  isOpen.value = false;
};

function formatCurrency(amount: number, currency = "MWK") {
  const currencyCode = currency || "MWK";

  // Map currency codes to locales
  const localeMap: Record<string, string> = {
    "MWK": "en-MW",
    "USD": "en-US",
    "GBP": "en-GB",
    "EUR": "en-EU",
    "CAD": "en-CA",
    "ZAR": "en-ZA",
    "MK": "en-MW" // Handle PayChangu's MK code for Malawi Kwacha
  };

  // Default to en-US if no specific locale is found
  const locale = localeMap[currencyCode] || "en-US";

  // Convert MK to MWK for display (PayChangu uses MK)
  const displayCurrency = currencyCode === "MK" ? "MWK" : currencyCode;

  return new Intl.NumberFormat(locale, {
    style: "currency",
    currency: displayCurrency,
    minimumFractionDigits: ["MWK", "MK"].includes(currencyCode) ? 0 : 2,
  }).format(amount);
}

defineExpose({
  setLoading,
  closeAfterRefund
});
</script>
