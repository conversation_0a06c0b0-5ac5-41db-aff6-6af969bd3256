<template>
  <div class="bg-white dark:bg-zinc-900 p-3 text-center space-y-6">
    <div class="flex justify-center">
      <div
        v-if="status === 'pending'"
        class="w-16 h-16 rounded-full bg-yellow-100 dark:bg-yellow-900/20 flex items-center justify-center"
      >
        <CoreLoader :height="30" :width="30" color="orange" />
      </div>
      <div
        v-else-if="status === 'completed'"
        class="w-16 h-16 bg-green-100 dark:bg-green-900/20 flex items-center justify-center"
      >
        <Icon
          icon="heroicons:check-circle-20-solid"
          class="w-8 h-8 text-green-600 dark:text-green-400"
        />
      </div>
      <div
        v-else-if="status === 'failed'"
        class="w-16 h-16 bg-red-100 dark:bg-red-900/20 flex items-center justify-center"
      >
        <Icon
          icon="heroicons:x-circle-20-solid"
          class="w-8 h-8 text-red-600 dark:text-red-400"
        />
      </div>
      <div
        v-else
        class="w-16 h-16 bg-gray-100 dark:bg-gray-800 flex items-center justify-center"
      >
        <Icon
          icon="heroicons:clock"
          class="w-8 h-8 text-gray-600 dark:text-gray-400"
        />
      </div>
    </div>

    <div class="space-y-2">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
        {{ statusTitle }}
      </h3>
      <p class="text-sm text-gray-600 dark:text-gray-400">
        {{ props.status !== 'failed' ? statusMessage : 'Something went wrong' }}
      </p>
    </div>

    <div
      v-if="paymentData"
      class="bg-gray-50 border border-gray-300 dark:border-zinc-700 border-dotted rounded dark:bg-zinc-800 p-4 space-y-2 text-left"
    >
      <div class="flex justify-between text-sm">
        <span class="text-gray-600 dark:text-gray-400">Reference:</span>
        <span
          class="font-mono text-gray-900 dark:text-white truncate pt-1 pl-3"
          >{{ paymentData.reference }}</span
        >
      </div>
      <div class="flex justify-between text-sm">
        <span class="text-gray-600 dark:text-gray-400">Amount:</span>
        <span class="font-medium text-gray-900 dark:text-white">{{
          formatCurrency(paymentData.amount)
        }}</span>
      </div>
      <div
        v-if="paymentData.operator_name"
        class="flex justify-between text-sm"
      >
        <span class="text-gray-600 dark:text-gray-400">Payment Method:</span>
        <span class="font-medium text-gray-900 dark:text-white">{{
          paymentData.operator_name
        }}</span>
      </div>
      <div v-if="paymentData.phone_number" class="flex justify-between text-sm">
        <span class="text-gray-600 dark:text-gray-400">Phone Number:</span>
        <span class="font-medium text-gray-900 dark:text-white">{{
          paymentData.phone_number
        }}</span>
      </div>
      <div
        v-else-if="paymentData.payment_method"
        class="flex justify-between text-sm"
      >
        <span class="text-gray-600 dark:text-gray-400">Method:</span>
        <span class="font-medium text-gray-900 dark:text-white">{{
          formatPaymentMethod(paymentData.payment_method)
        }}</span>
      </div>
    </div>

    <div v-if="status === 'pending'" class="space-y-3">
      <div
        class="text-xs text-left text-gray-500 dark:text-gray-400 uppercase tracking-wide"
      >
        Payment Steps
      </div>
      <div class="space-y-2">
        <div class="flex items-center space-x-3">
          <div
            class="w-4 h-4 bg-green-500 flex items-center justify-center rounded-full"
          >
            <Icon icon="heroicons:check" class="w-3 h-3 text-white" />
          </div>
          <span class="text-sm text-gray-900 dark:text-white"
            >Payment request sent</span
          >
        </div>
        <div class="flex items-center space-x-3">
          <div
            class="w-4 h-4 bg-yellow-100 flex items-center justify-center rounded-full"
          >
            <CoreLoader :height="10" :width="10" color="orange" />
          </div>
          <span class="text-sm text-gray-900 dark:text-white"
            >Waiting for authorization on your phone</span
          >
        </div>
        <div class="flex items-center space-x-3">
          <div
            class="w-4 h-4 bg-gray-300 dark:bg-gray-600 flex items-center justify-center rounded-full"
          >
            <Icon icon="heroicons:clock" class="w-3 h-3 text-gray-500" />
          </div>
          <span class="text-sm text-gray-500 dark:text-gray-400"
            >Payment confirmation</span
          >
        </div>
      </div>

      <div
        class="bg-sky-50 dark:bg-sky-900/20 rounded border border-sky-300 border-dotted p-3"
      >
        <div class="flex items-start space-x-2">
          <div class="text-sm dark:text-zinc-100">
            <p class="font-medium">Check your phone</p>
            <p class="mt-1">
              You should receive a payment request on
              <strong>{{
                `+265${paymentData?.phone_number}` || "your mobile device"
              }}</strong
              >. Please authorize the payment to complete your purchase.
            </p>
          </div>
        </div>
      </div>
    </div>

    <div class="flex space-x-3">
      <button
        v-if="status === 'failed'"
        @click="$emit('retry')"
        type="button"
        class="flex-1 px-4 py-2 bg-red-600 text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors"
      >
        Try Again
      </button>
      <button
        v-if="status === 'completed'"
        @click="$emit('continue')"
        type="button"
        class="flex-1 px-4 py-2 bg-green-600 text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors"
      >
        Continue
      </button>
      <button
        v-if="status === 'pending'"
        @click="$emit('cancel')"
        type="button"
        class="flex-1 px-4 py-2 border border-gray-300 dark:border-zinc-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-800 hover:bg-gray-50 dark:hover:bg-zinc-700 focus:outline-none focus:ring-0 focus:ring-red-500 transition-colors"
      >
        Cancel
      </button>
    </div>

    <div
      v-if="status === 'pending'"
      class="flex items-center justify-center space-x-2 text-xs text-gray-500 dark:text-gray-400"
    >
      <CoreLoader :height="20" :width="20" />
      <span>Listening for payment updates...</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";

interface PaymentData {
  reference: string;
  amount: number;
  payment_method?: string;
  transaction_id?: string;
  charge_id?: string;
  operator_name?: string;
  phone_number?: string;
}

interface Props {
  status: "pending" | "completed" | "failed" | "cancelled";
  paymentData?: PaymentData;
  error?: string;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  retry: [];
  continue: [];
  cancel: [];
  statusUpdate: [status: string];
  ticketPurchaseCompleted: [event: any];
}>();

const statusTitle = computed(() => {
  switch (props.status) {
    case "pending":
      return "Processing Payment";
    case "completed":
      return "Payment Successful";
    case "failed":
      return "Payment Failed";
    case "cancelled":
      return "Payment Cancelled";
    default:
      return "Payment Status";
  }
});

const statusMessage = computed(() => {
  switch (props.status) {
    case "pending":
      return "Please wait while we process your payment. This may take a few moments.";
    case "completed":
      return "Your payment has been processed successfully. Your tickets have been sent to your email and will download automatically.";
    case "failed":
      return (
        props.error ||
        "Your payment could not be processed. Please try again or use a different payment method."
      );
    case "cancelled":
      return "The payment was cancelled. You can try again or choose a different payment method.";
    default:
      return "Checking payment status...";
  }
});

const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat("en-MW", {
    style: "currency",
    currency: "MWK",
    minimumFractionDigits: 0,
  }).format(amount);
};

const formatPaymentMethod = (method: string): string => {
  switch (method) {
    case "mpamba":
      return "TNM Mpamba";
    case "airtel_money":
      return "Airtel Money";
    case "bank_transfer":
      return "Bank Transfer";
    default:
      return method;
  }
};
</script>
