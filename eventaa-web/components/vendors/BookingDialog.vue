<template>
  <div>
    <CorePrimaryButton @click="openModal" text="Book Now" startIcon="heroicons:calendar" />
  </div>
  <TransitionRoot appear :show="isOpen" as="template">
    <Dialog as="div" @close="closeModal" class="relative z-10">
      <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
        leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
        <div class="fixed inset-0 bg-black/25" />
      </TransitionChild>
      <div class="fixed inset-0 overflow-y-auto">
        <div class="flex min-h-full items-center justify-center p-4">
          <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95">
            <DialogPanel class="w-full max-w-md transform overflow-hidden bg-white shadow-xl transition-all">
              <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900 p-4 border-b">
                Book Service
              </DialogTitle>
              <div v-if="loading" class="p-6 flex justify-center">
                <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-red-500"></div>
              </div>
              <div v-else class="p-6">
                <FormKit type="form" id="bookingForm" @submit="submitForm" :actions="false" class="space-y-4">
                  <div v-if="services.length > 0">
                    <FormKit type="select" label="Service" name="vendorServiceId"
                      :value="form.vendorServiceId === null ? undefined : form.vendorServiceId"
                      @input="(val) => form.vendorServiceId = val === undefined ? null : Number(val)"
                      validation="required" :options="serviceOptions" placeholder="Select a service" />
                  </div>

                  <div class="relative mb-2">
                    <label class="block text-base font-medium mb-1">Date and Time</label>
                    <datepicker v-model="dateTimeRange" format="dd/MM/yyyy HH:mm"
                      input-class-name="mt-1 block w-full border border-gray-300 px-3 py-2 focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm"
                      :min-date="new Date()" :enable-time-picker="true" :range="true" :time-picker-inline="true"
                      :disabled-dates="disabledDates"
                      highlight-disabled-days
                      :highlight="{ options: { highlightDisabled: true } }"
                      teleport placeholder="Select date and time range"/>
                  </div>

                  <FormKit type="number" label="Number of Guests" name="numberOfGuests"
                    :value="String(form.numberOfGuests)" @input="(val) => form.numberOfGuests = Number(val || 1)"
                    validation="required|min:1" min="1" placeholder="Enter number of guests" />

                  <FormKit type="textarea" label="Message (Optional)" name="message" v-model="form.message"
                    placeholder="Add any special requests or information" rows="3" />

                  <div class="flex justify-end space-x-3 mt-6">
                    <button type="button"
                      class="px-4 py-2 bg-white text-gray-700 border border-gray-300 hover:bg-gray-50"
                      @click="closeModal">
                      Cancel
                    </button>
                    <CoreSubmitButton text="Book Now" :loading="actionLoading" />
                  </div>
                </FormKit>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from '@headlessui/vue';
import { ref, computed } from 'vue';
import dayjs from 'dayjs';
import { ENDPOINTS } from '@/utils/api';
import { handleError } from '@/utils/errorHandler';

interface BookingForm {
  vendorServiceId: number | null;
  numberOfGuests: number;
  message: string;
}

const props = defineProps<{
  vendorId: number;
  vendorSlug: string;
}>();

const emit = defineEmits(['booking-added']);

const isOpen = ref<boolean>(false);
const loading = ref<boolean>(false);
const actionLoading = ref<boolean>(false);
const services = ref<Array<{ id: number; service: { id: number; name: string } }>>([]);
const disabledDates = ref<Date[]>([]);
const { $toast }: any = useNuxtApp();
const httpClient = useHttpClient();

const tomorrow = dayjs().add(1, 'day').toDate();
const tomorrowPlusHour = dayjs().add(1, 'day').add(1, 'hour').toDate();

const dateTimeRange = ref<Date[]>([tomorrow, tomorrowPlusHour]);

const form = ref<BookingForm>({
  vendorServiceId: null,
  numberOfGuests: 1,
  message: ''
});

const serviceOptions = computed(() => {
  return services.value.map(service => ({
    value: service.id,
    label: service.service.name
  }));
});

const fetchBookedDates = async (): Promise<void> => {
  try {
    const response: any = await httpClient.get(`${ENDPOINTS.VENDORS.BASE}/bookings/get`, {
      params: {
        vendor_id: props.vendorId,
        status: ['pending', 'approved', 'completed']
      }
    });

    if (response && response.data) {
      const bookedDates: Date[] = [];
      response.data.forEach((booking: any) => {
        const startDate = dayjs(booking.booking_from).startOf('day');
        const endDate = dayjs(booking.booking_to).startOf('day');

        let currentDate = startDate;
        while (currentDate.isSame(endDate) || currentDate.isBefore(endDate)) {
          bookedDates.push(currentDate.toDate());
          currentDate = currentDate.add(1, 'day');
        }
      });

      disabledDates.value = bookedDates;
    }
  } catch (error: any) {
    console.error('Failed to fetch booked dates:', error);
  }
};

const openModal = async (): Promise<void> => {
  loading.value = true;
  await Promise.all([
    fetchVendorServices(),
    fetchBookedDates()
  ]);
  isOpen.value = true;
  loading.value = false;
};

const closeModal = (): void => {
  isOpen.value = false;
  resetForm();
};

const resetForm = (): void => {
  const newTomorrow = dayjs().add(1, 'day').toDate();
  const newTomorrowPlusHour = dayjs().add(1, 'day').add(1, 'hour').toDate();

  dateTimeRange.value = [newTomorrow, newTomorrowPlusHour];

  form.value = {
    vendorServiceId: null,
    numberOfGuests: 1,
    message: ''
  };
};

const fetchVendorServices = async (): Promise<void> => {
  try {
    const response: any = await httpClient.get(`${ENDPOINTS.VENDORS.READ}/${props.vendorSlug}`);
    if (response && response.services) {
      services.value = response.services;
    }
  } catch (error: any) {
    $toast.error('Failed to load vendor services');
  }
};

const submitForm = async (): Promise<void> => {
  actionLoading.value = true;

  if (!form.value.vendorServiceId) {
    $toast.error('Please select a service');
    actionLoading.value = false;
    return;
  }

  if (!dateTimeRange.value || dateTimeRange.value.length !== 2) {
    $toast.error('Please select a date and time range');
    actionLoading.value = false;
    return;
  }

  const bookingData = {
    vendor_id: props.vendorId,
    vendor_service_id: form.value.vendorServiceId,
    category_id: services.value.find(s => s.id === form.value.vendorServiceId)?.service?.id || 1,
    booking_from: dayjs(dateTimeRange.value[0]).format('YYYY-MM-DD HH:mm:ss'),
    booking_to: dayjs(dateTimeRange.value[1]).format('YYYY-MM-DD HH:mm:ss'),
    number_of_guests: form.value.numberOfGuests,
    message: form.value.message || '',
    total_price: 0
  };

  try {
    const response = await httpClient.post(`${ENDPOINTS.VENDORS.BASE}/bookings/create`, bookingData);
    if (response) {
      emit('booking-added', response);
      $toast.success('Booking created successfully');
      closeModal();
    } else {
      $toast.error('Failed to create booking');
    }
  } catch (error: any) {
    if (error.response?.data?.message === 'The vendor is already booked during this time period') {
      $toast.error('The vendor is already booked during this time period. Please select a different time.');
    } else {
      handleError(error, $toast, 'Failed to create booking');
    }
  } finally {
    actionLoading.value = false;
  }
};

defineExpose({
  openModal
});
</script>
