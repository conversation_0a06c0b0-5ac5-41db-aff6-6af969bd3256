<template>
    <div>
        <Popover v-slot="{ open }" class="relative">
            <PopoverButton :class="open ? 'text-gray-800 dark:text-white' : 'text-gray-700 dark:text-gray-300'"
                class="focus:outline-none focus:ring-0 w-full">
                <button class="flex items-center justify-center py-2 border border-zinc-200 dark:border-zinc-700
                    hover:bg-gray-50 dark:hover:bg-zinc-700 transition duration-150 w-full">
                    <svg class="h-5 w-5 mr-2 text-gray-500 dark:text-gray-200" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
                    </svg>
                    <span class="text-gray-700 dark:text-gray-300">Share</span>
                </button>
            </PopoverButton>

            <transition enter-active-class="transition duration-200 ease-out" enter-from-class="translate-y-1 opacity-0"
                enter-to-class="translate-y-0 opacity-100" leave-active-class="transition duration-150 ease-in"
                leave-from-class="translate-y-0 opacity-100" leave-to-class="translate-y-1 opacity-0">
                <PopoverPanel
                    class="absolute z-10 w-64 mt-2 transform -translate-x-1/2 left-1/2 sm:px-0">
                    <div class="bg-white dark:bg-zinc-800 shadow-lg overflow-hidden">
                        <div class="p-4">
                            <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Share this vendor</h3>
                            <div class="grid grid-cols-3 gap-3">
                                <button @click="shareOnWhatsApp"
                                    class="flex flex-col items-center justify-center space-y-1 hover:bg-gray-100 dark:hover:bg-zinc-700 p-2 transition duration-150">
                                    <Icon icon="logos:whatsapp-icon" class="w-7 h-7" />
                                    <span class="text-sm text-gray-600 dark:text-gray-200">WhatsApp</span>
                                </button>
                                <button @click="shareOnFacebook"
                                    class="flex flex-col items-center justify-center space-y-1 hover:bg-gray-100 dark:hover:bg-zinc-700 p-2 transition duration-150">
                                    <Icon icon="devicon:facebook" class="w-7 h-7" />
                                    <span class="text-sm text-gray-600 dark:text-gray-200">Facebook</span>
                                </button>
                                <button @click="shareOnTwitter"
                                    class="flex flex-col items-center justify-center space-y-1 hover:bg-gray-100 dark:hover:bg-zinc-700 p-2 transition duration-150">
                                    <Icon icon="devicon:twitter" class="w-7 h-7" />
                                    <span class="text-sm text-gray-600 dark:text-gray-200">Twitter</span>
                                </button>
                                <button @click="shareOnInstagram"
                                    class="flex flex-col items-center justify-center space-y-1 hover:bg-gray-100 dark:hover:bg-zinc-700 p-2 transition duration-150">
                                    <Icon icon="skill-icons:instagram" class="w-7 h-7" />
                                    <span class="text-sm text-gray-600 dark:text-gray-200">Instagram</span>
                                </button>
                            </div>
                            <div class="mt-4">
                                <button @click="shareViaEmail"
                                    class="w-full flex items-center justify-center space-x-2 hover:bg-gray-100 dark:hover:bg-zinc-700 p-2 transition duration-150">
                                    <Icon icon="clarity:email-outline-alerted" class="w-5 h-5 text-gray-600 dark:text-gray-200" />
                                    <span class="text-sm text-gray-600 dark:text-gray-200">Email</span>
                                </button>
                                <button @click="copyLink"
                                    class="w-full flex items-center justify-center space-x-2 hover:bg-gray-100 dark:hover:bg-zinc-700 p-2 mt-2 transition duration-150">
                                    <Icon icon="mynaui:copy" class="w-5 h-5 text-gray-600 dark:text-gray-200" />
                                    <span class="text-sm text-gray-600 dark:text-gray-200">Copy Link</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </PopoverPanel>
            </transition>
        </Popover>
    </div>
</template>

<script setup lang="ts">
import { Popover, PopoverButton, PopoverPanel } from '@headlessui/vue'
import type { PropType } from 'vue';
import type { ApiVendor } from '@/types/vendor';
import { ENDPOINTS } from '@/utils/api';

const props = defineProps({
    vendor: {
        type: Object as PropType<ApiVendor>,
        required: true,
    }
});

const { $toast }: any = useNuxtApp();
const runtimeConfig = useRuntimeConfig();

const vendorLink = computed(() => {
    return `${window.location.origin}/vendors/${props.vendor.slug}`
});

const shareMessage = computed(() => {
    return `🎯 Check out this vendor: ${props.vendor.name}
    📍 Location: ${props.vendor.location}
    ${props.vendor.bio ? props.vendor.bio.substring(0, 200) + (props.vendor.bio.length > 200 ? '...' : '') : ''}
    More details: ${vendorLink.value}`
});

const shareOnWhatsApp = () => {
    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(shareMessage.value)}`
    window.open(whatsappUrl, '_blank');
    trackShare('whatsapp');
}

const shareOnFacebook = () => {
    const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(vendorLink.value.toString())}`
    window.open(facebookUrl, '_blank');
    trackShare('facebook');
}

const shareOnTwitter = () => {
    const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareMessage.value)}`
    window.open(twitterUrl, '_blank');
    trackShare('twitter');
}

const shareOnInstagram = () => {
    navigator.clipboard.writeText(shareMessage.value);
    $toast.success('Vendor details copied to clipboard. You can now paste on Instagram.');
    trackShare('instagram');
}

const shareViaEmail = () => {
    const subject = `Check out this vendor: ${props.vendor.name}`;
    const emailUrl = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(shareMessage.value)}`;
    window.open(emailUrl, '_blank');
    trackShare('email');
}

const copyLink = () => {
    navigator.clipboard.writeText(vendorLink.value);
    $toast.info('Vendor link copied to clipboard');
    trackShare('copy');
}

const trackShare = async (platform: string) => {
    try {
        const httpClient = useHttpClient();
        await httpClient.post(ENDPOINTS.VENDORS.SHARE, {
            vendor_id: props.vendor.id,
            platform: platform
        });
    } catch (error) {
        console.error('Error tracking share:', error);
    }
}
</script>
