<template>
    <div>
        <div class="bg-gray-50 dark:bg-zinc-900 p-4">
            <div class="flex justify-between items-center mb-4">
                <button @click="previousMonth" class="p-1 rounded-full bg-gray-200 hover:bg-gray-200 dark:bg-zinc-800 focus:outline-none">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600 dark:text-zinc-200" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                    </svg>
                </button>
                <h3 class="text-lg font-medium text-gray-800 dark:text-white">{{ currentMonthName }} {{ currentYear }}</h3>
                <button @click="nextMonth" class="p-1 rounded-full bg-gray-200 hover:bg-gray-200 dark:bg-zinc-800 focus:outline-none">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600 dark:text-zinc-200" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </button>
            </div>

            <div class="grid grid-cols-7 gap-1 text-center">
                <div v-for="day in weekDays" :key="day" class="text-base font-medium text-gray-500 dark:text-zinc-100 py-2">
                    {{ day }}
                </div>

                <div v-for="_ in startingDayOfWeek" :key="'empty-start-' + _" class="p-2 text-sm"></div>

                <div v-for="date in calendarDays" :key="`${currentMonth}-${date.day}`" :class="[
                    'flex items-center justify-center p-2 text-sm transition-colors duration-150',
                    date.available ? 'bg-green-600 text-green-100 cursor-pointer hover:bg-green-700' : 'bg-gray-200 dark:bg-zinc-800 text-gray-600 dark:text-zinc-200',
                    isSelectedDate(date) ? 'bg-blue-500 text-white hover:bg-blue-600' : ''
                ]" @click="date.available ? selectDate(date) : null">
                    {{ date.day }}
                </div>

                <div v-for="_ in endingDayOfWeek" :key="'empty-end-' + _" class="p-2 text-sm"></div>
            </div>

            <div class="mt-4 flex items-center space-x-3">
                <div class="flex items-center">
                    <span class="w-3 h-3 bg-green-600 border border-green-500 rounded-full mr-2"></span>
                    <span class="text-sm text-gray-700 dark:text-zinc-100">Available</span>
                </div>
                <div class="flex items-center">
                    <span class="w-3 h-3 bg-gray-200 dark:bg-zinc-800 dark:border-zinc-700 border border-gray-100 rounded-full mr-2"></span>
                    <span class="text-sm text-gray-700 dark:text-zinc-100">Booked/Unavailable</span>
                </div>
                <div v-if="anySelected" class="flex items-center">
                    <span class="w-3 h-3 bg-blue-500 rounded-full mr-2"></span>
                    <span class="text-sm text-gray-700 dark:text-zinc-100">Selected</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';

interface CalendarDay {
  day: number;
  date: Date;
  available: boolean;
  isToday: boolean;
}

const props = defineProps({
    modelValue: {
        type: Object as PropType<CalendarDay | null>,
        default: null,
    },
    availabilityData: {
        type: Object as PropType<Record<string, boolean>>,
        default: () => ({}),
    },
    minDate: {
        type: Date,
        default: () => new Date(),
    },
    maxDate: {
        type: Date,
        default: () => {
            const date = new Date();
            date.setMonth(date.getMonth() + 6);
            return date;
        }
    }
});

const emit = defineEmits(['update:modelValue']);

const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
const currentMonth = ref(new Date().getMonth());
const currentYear = ref(new Date().getFullYear());
const selectedDate = ref(props.modelValue);
const calendarDays = ref<CalendarDay[]>([]);

const currentMonthName = computed(() => {
    return new Date(currentYear.value, currentMonth.value).toLocaleString('default', { month: 'long' });
});

const startingDayOfWeek = computed(() => {
    return new Date(currentYear.value, currentMonth.value, 1).getDay();
});

const endingDayOfWeek = computed(() => {
    const daysInGrid = startingDayOfWeek.value + calendarDays.value.length;
    const remainder = daysInGrid % 7;
    return remainder === 0 ? 0 : 7 - remainder;
});

const anySelected = computed(() => {
    return selectedDate.value !== null;
});

const formatDateKey = (date: Date): string => {
    return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
};

const generateCalendarDays = () => {
    const days: CalendarDay[] = [];
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const daysInMonth = new Date(currentYear.value, currentMonth.value + 1, 0).getDate();

    for (let i = 1; i <= daysInMonth; i++) {
        const currentDate = new Date(currentYear.value, currentMonth.value, i);
        currentDate.setHours(0, 0, 0, 0);
        
        const dateKey = formatDateKey(currentDate);
        const isInRange = currentDate >= props.minDate && currentDate <= props.maxDate;
        
        const isAvailable = isInRange && (dateKey in props.availabilityData ? props.availabilityData[dateKey] : true);
        
        days.push({
            day: i,
            date: currentDate,
            available: isAvailable,
            isToday: currentDate.getTime() === today.getTime()
        });
    }
    
    calendarDays.value = days;
};

const isSelectedDate = (date: CalendarDay): boolean => {
    if (!selectedDate.value) return false;

    const selected = selectedDate.value.date;
    return selected.getDate() === date.day &&
        selected.getMonth() === currentMonth.value &&
        selected.getFullYear() === currentYear.value;
};

const selectDate = (date: CalendarDay): void => {
    if (date.available) {
        selectedDate.value = date;
        emit('update:modelValue', date);
    }
};

const previousMonth = () => {
    if (currentMonth.value === 0) {
        currentMonth.value = 11;
        currentYear.value--;
    } else {
        currentMonth.value--;
    }
};

const nextMonth = () => {
    if (currentMonth.value === 11) {
        currentMonth.value = 0;
        currentYear.value++;
    } else {
        currentMonth.value++;
    }
};

watch(() => props.modelValue, (newVal) => {
    selectedDate.value = newVal;
});

watch([currentMonth, currentYear], () => {
    generateCalendarDays();
});

onMounted(() => {
    generateCalendarDays();
});
</script>