<template>
    <div class="max-w-sm overflow-hidden shadow bg-white dark:bg-zinc-800">
        <div class="relative overflow-hidden">
            <transition-group name="fade" tag="div">
                <img v-for="(img, index) in [currentImage]" :key="index" :src="venue.images[currentImage]"
                    :alt="venue.slug" class="w-full h-32 object-cover" />
            </transition-group>

            <div class="absolute inset-y-0 left-0 flex items-center">
                <button @click="prevImage" class="bg-black bg-opacity-25 hover:bg-opacity-50 rounded-r p-1 text-white"
                    aria-label="Previous image">
                    <ChevronLeftIcon class="w-5 h-5" />
                </button>
            </div>

            <div class="absolute inset-y-0 right-0 flex items-center">
                <button @click="nextImage" class="bg-black bg-opacity-25 hover:bg-opacity-50 rounded-l p-1 text-white"
                    aria-label="Next image">
                    <ChevronRightIcon class="w-5 h-5" />
                </button>
            </div>

            <div class="absolute bottom-2 left-0 right-0 flex justify-center space-x-2">
                <button v-for="(_, index) in venue.images" :key="index" @click="currentImage = index"
                    class="w-2 h-2 rounded-full transition-all duration-300"
                    :class="currentImage === index ? 'bg-white scale-125' : 'bg-white bg-opacity-50'"
                    :aria-label="`Go to slide ${index + 1}`"></button>
            </div>
        </div>

        <div class="p-4 space-y-3">
            <div class="flex justify-between items-center">
                <NuxtLink :to="`/venues/${venue.slug}`" class="flex-grow">
                    <h2
                        class="text-xl font-semibold hover:text-sky-500 transition-all duration-150 text-zinc-900 dark:text-zinc-100">
                        {{ venue.title }}
                    </h2>
                </NuxtLink>
                <div class="flex items-center gap-1 text-orange-500">
                    <StarIcon class="w-4 h-4" />
                    <span class="text-sm">{{ Number(venue.rating == null ? 0 : venue.rating).toFixed(1) }}</span>
                </div>
            </div>

            <div class="flex items-center gap-2 text-zinc-600 dark:text-zinc-400 text-sm">
                <MapPinIcon class="w-4 h-4" />
                <span>{{ venue.street }}, {{ venue.address }}</span>
            </div>

            <div class="flex flex-wrap gap-2 text-xs">
                <span v-for="(tag, i) in venue.tags.slice(0, 4)" :key="i"
                    class="bg-zinc-100 dark:bg-zinc-700 rounded-full px-2 py-1 text-gray-700 dark:text-gray-300 flex items-center">
                    <img :src="tag.icon" alt="tag icon" class="w-3 h-3 mr-1" />
                    {{ tag.name }}
                </span>
            </div>

            <div class="text-sm font-medium text-zinc-700 dark:text-zinc-300">
                Price starts:
                <span class="font-bold text-zinc-900 dark:text-zinc-100">
                    {{
                        new Intl.NumberFormat('en-MW', {
                            style: 'currency',
                            currency: venue.prices[0].currency.name,
                        }).format(Number(venue.prices[0].price))
                    }}
                </span>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ChevronLeftIcon, ChevronRightIcon, MapPinIcon, StarIcon } from "@heroicons/vue/24/outline";
import { ref, onMounted, onBeforeUnmount } from "vue";
import type { VenuePrice } from "@/types";

interface Venue {
    id: number;
    title: string;
    description: string;
    slug: string;
    images: string[];
    status: string;
    rating: string;
    street: string;
    address: string;
    tags: { name: string, icon: string; }[];
    prices: VenuePrice[];
    replies: number;
    timeAgo: string;
}

const props = defineProps<{
    venue: Venue;
}>();

const currentImage = ref(0);
let intervalId: number;

const nextImage = () => {
    currentImage.value = (currentImage.value + 1) % props.venue.images.length;
    resetCarouselTimer();
};

const prevImage = () => {
    currentImage.value = (currentImage.value - 1 + props.venue.images.length) % props.venue.images.length;
    resetCarouselTimer();
};

const resetCarouselTimer = () => {
    clearInterval(intervalId);
    startCarouselTimer();
};

const startCarouselTimer = () => {
    intervalId = window.setInterval(() => {
        nextImage();
    }, 5000);
};

onMounted(() => {
    startCarouselTimer();
});

onBeforeUnmount(() => {
    clearInterval(intervalId);
});
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.8s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}
</style>