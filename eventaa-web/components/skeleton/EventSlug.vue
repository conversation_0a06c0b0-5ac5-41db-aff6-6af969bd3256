<template>
    <div class="w-full bg-gray-50 animate-pulse">
        <div class="px-5 mx-auto py-5">
            <div class="grid grid-cols-1 lg:grid-cols-5 gap-6">
                <div class="lg:col-span-3 space-y-6">
                    <div class="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                        <div class="w-16 bg-gray-200 h-24 flex-shrink-0"></div>
                        <div class="space-y-2 w-full">
                            <div class="h-8 bg-gray-200 w-3/4 mb-2"></div>
                            <div class="h-4 bg-gray-200 w-1/2"></div>
                        </div>
                    </div>

                    <div class="relative">
                        <div class="w-full h-48 sm:h-72 bg-gray-200"></div>
                    </div>

                    <div class="border-l-4 border-gray-100 bg-white px-4 py-2">
                        <div class="space-y-2">
                            <div class="h-4 bg-gray-200 w-full"></div>
                            <div class="h-4 bg-gray-200 w-5/6"></div>
                            <div class="h-4 bg-gray-200 w-4/5"></div>
                        </div>
                    </div>

                    <div>
                        <div class="h-6 bg-gray-200 w-1/4 mb-4"></div>
                        <div class="grid grid-cols-3 gap-4">
                            <div class="h-16 bg-gray-200"></div>
                            <div class="h-16 bg-gray-200"></div>
                            <div class="h-16 bg-gray-200"></div>
                        </div>
                    </div>
                </div>

                <div class="lg:col-span-2">
                    <div class="bg-white shadow-md">
                        <div class="border-b border-gray-100 py-2 px-4">
                            <div class="h-6 bg-gray-200 w-1/2"></div>
                        </div>

                        <div class="py-4 flex justify-center">
                            <div class="h-12 bg-gray-200 w-3/4"></div>
                        </div>

                        <div class="h-64 sm:h-72 bg-gray-200"></div>

                        <div class="px-4 py-2 space-y-4">
                            <div class="h-6 bg-gray-200 w-1/3 mb-2"></div>
                            <div v-for="n in 3" :key="n" class="bg-white px-4 py-4 border border-gray-100">
                                <div class="flex items-center justify-between">
                                    <div class="space-y-2 w-2/3">
                                        <div class="h-6 bg-gray-200 w-1/2"></div>
                                        <div class="h-4 bg-gray-200 w-3/4"></div>
                                    </div>
                                    <div class="flex space-x-3">
                                        <div class="w-8 h-8 bg-gray-200 rounded-full"></div>
                                        <div class="w-8 h-8 bg-gray-200 rounded-full"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-6 space-y-4">
                <div class="h-6 bg-gray-200 w-1/4"></div>
                <div class="h-10 bg-gray-200 w-1/3"></div>
                <div class="grid grid-cols-3 gap-4">
                    <div v-for="n in 3" :key="n" class="bg-gray-200 h-48"></div>
                </div>
                <div class="h-10 bg-gray-200 w-full"></div>
            </div>
        </div>
    </div>
</template>

<script setup>
</script>