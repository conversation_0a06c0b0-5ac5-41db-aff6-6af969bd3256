<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Profile Header Skeleton -->
    <div class="bg-white dark:bg-zinc-800 rounded-xl shadow-sm border border-gray-200 dark:border-zinc-700 mb-8 overflow-hidden">
      <div class="px-6 py-8">
        <div class="flex flex-col md:flex-row items-start md:items-center gap-6">
          <!-- Avatar Skeleton -->
          <div class="relative">
            <div class="w-24 h-24 rounded-xl bg-gray-200 dark:bg-zinc-700 animate-pulse"></div>
            <div class="absolute -bottom-2 -right-2 w-8 h-8 bg-gray-200 dark:bg-zinc-700 rounded-full animate-pulse"></div>
          </div>

          <!-- User Info Skeleton -->
          <div class="flex-1 min-w-0">
            <div class="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4">
              <div class="min-w-0 flex-1">
                <!-- Name and Badge -->
                <div class="flex items-center gap-3 mb-2">
                  <div class="h-8 w-48 bg-gray-200 dark:bg-zinc-700 rounded animate-pulse"></div>
                  <div class="h-6 w-16 bg-gray-200 dark:bg-zinc-700 rounded-full animate-pulse"></div>
                </div>

                <!-- Bio -->
                <div class="space-y-2 mt-2">
                  <div class="h-4 w-3/4 bg-gray-200 dark:bg-zinc-700 rounded animate-pulse"></div>
                  <div class="h-4 w-1/2 bg-gray-200 dark:bg-zinc-700 rounded animate-pulse"></div>
                </div>

                <!-- Rating -->
                <div class="flex items-center gap-2 mt-3">
                  <div class="h-4 w-24 bg-gray-200 dark:bg-zinc-700 rounded animate-pulse"></div>
                </div>

                <!-- Join Date -->
                <div class="flex items-center gap-1 mt-2">
                  <div class="h-4 w-4 bg-gray-200 dark:bg-zinc-700 rounded animate-pulse"></div>
                  <div class="h-4 w-32 bg-gray-200 dark:bg-zinc-700 rounded animate-pulse"></div>
                </div>
              </div>

              <!-- Action Buttons Skeleton -->
              <div class="flex items-center gap-3 flex-shrink-0">
                <div class="h-10 w-10 bg-gray-200 dark:bg-zinc-700 rounded-md animate-pulse"></div>
                <div class="h-10 w-24 bg-gray-200 dark:bg-zinc-700 rounded-md animate-pulse"></div>
                <div class="h-10 w-10 bg-gray-200 dark:bg-zinc-700 rounded-md animate-pulse"></div>
              </div>
            </div>

            <!-- Stats Skeleton -->
            <div class="flex items-center gap-8 mt-6 pt-6 border-t border-gray-100 dark:border-zinc-700">
              <div v-for="i in 3" :key="i" class="text-center">
                <div class="h-8 w-12 bg-gray-200 dark:bg-zinc-700 rounded animate-pulse mx-auto mb-2"></div>
                <div class="h-4 w-16 bg-gray-200 dark:bg-zinc-700 rounded animate-pulse"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Social Links Skeleton -->
    <div class="bg-white dark:bg-zinc-800 rounded-xl shadow-sm border border-gray-200 dark:border-zinc-700 mb-8 overflow-hidden">
      <div class="px-6 py-6">
        <div class="flex items-center gap-2 mb-4">
          <div class="w-5 h-5 bg-gray-200 dark:bg-zinc-700 rounded animate-pulse"></div>
          <div class="h-6 w-32 bg-gray-200 dark:bg-zinc-700 rounded animate-pulse"></div>
        </div>
        <div class="flex items-center gap-4">
          <div v-for="i in 3" :key="i" class="h-10 w-24 bg-gray-200 dark:bg-zinc-700 rounded-lg animate-pulse"></div>
        </div>
      </div>
    </div>

    <!-- Events Section Skeleton -->
    <div class="bg-white dark:bg-zinc-800 rounded-xl shadow-sm border border-gray-200 dark:border-zinc-700 overflow-hidden">
      <div class="px-6 py-6 border-b border-gray-200 dark:border-zinc-700">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-2">
            <div class="w-5 h-5 bg-gray-200 dark:bg-zinc-700 rounded animate-pulse"></div>
            <div class="h-6 w-16 bg-gray-200 dark:bg-zinc-700 rounded animate-pulse"></div>
            <div class="h-6 w-8 bg-gray-200 dark:bg-zinc-700 rounded-full animate-pulse"></div>
          </div>
        </div>
      </div>

      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div v-for="i in 6" :key="i" class="border border-gray-200 dark:border-zinc-700 rounded-xl overflow-hidden">
            <!-- Image Skeleton -->
            <div class="w-full h-48 bg-gray-200 dark:bg-zinc-700 animate-pulse"></div>

            <!-- Content Skeleton -->
            <div class="p-4">
              <div class="h-5 w-3/4 bg-gray-200 dark:bg-zinc-700 rounded animate-pulse mb-2"></div>
              <div class="space-y-2 mb-3">
                <div class="h-4 w-full bg-gray-200 dark:bg-zinc-700 rounded animate-pulse"></div>
                <div class="h-4 w-2/3 bg-gray-200 dark:bg-zinc-700 rounded animate-pulse"></div>
              </div>
              <div class="flex items-center justify-between">
                <div class="h-4 w-24 bg-gray-200 dark:bg-zinc-700 rounded animate-pulse"></div>
                <div class="h-4 w-20 bg-gray-200 dark:bg-zinc-700 rounded animate-pulse"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// This is a skeleton loading component for the user profile page
</script>
