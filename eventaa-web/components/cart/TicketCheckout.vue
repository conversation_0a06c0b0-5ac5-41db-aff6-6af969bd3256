<template>
    <div class="w-full border-b dark:border-zinc-700 py-2 px-2">
        <div class="w-full items-center flex justify-between">
            <div class="flex items-center space-x-2">
                <Icon icon="dashicons:tickets-alt" class="h-16 w-16 text-gray-400 dark:text-zinc-500" />
                <div>
                    <h3 class="line-clamp-1 text-lg font-semibold dark:text-zinc-50">{{ props.ticket.event.title }}</h3>
                    <p class="text-base font-medium text-gray-600 dark:text-zinc-300">{{  props.ticket.tier.name }} ticket (x{{ props.ticket.quantity}})</p>
                </div>
            </div>
            <div class="flex items-center space-x-1">
                <h3 class="text-xl font-semibold dark:text-zinc-50">{{ getCurrencySymbol(props.ticket.tier) }}{{ (props.ticket.quantity * Number(props.ticket.tier.price)).toLocaleString() }}</h3>
                <div class="bg-gray-100 dark:bg-zinc-800 flex items-center px-2 py-2 hover:bg-red-100 dark:hover:bg-red-900/30 transition-colors duration-150">
                    <button>
                        <Icon icon="icon-park-outline:delete" class="w-5 h-5 text-gray-500 dark:text-zinc-400 hover:text-red-500 dark:hover:text-red-400 transition-colors duration-150" />
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
const defaultCurrencySymbol = ref<string>("MK"); // Default to Malawian Kwacha if no other currency specified

const props = defineProps({
    ticket: {
        required: true,
        type: Object
    }
});

const getCurrencySymbol = (tier: any): string => {
    // Check if tier has currency information, otherwise return default
    return tier?.currency?.symbol || defaultCurrencySymbol.value;
};
</script>
