<template>
  <TransitionRoot appear :show="isOpen" as="template">
    <Dialog as="div" @close="closeModal" class="relative z-50">
      <TransitionChild
        as="template"
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-black/25" />
      </TransitionChild>

      <div class="fixed inset-0 overflow-y-auto">
        <div class="flex min-h-full items-center justify-center p-4 text-center">
          <TransitionChild
            as="template"
            enter="duration-300 ease-out"
            enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95"
          >
            <DialogPanel class="w-full max-w-md transform overflow-hidden dashboard-bg-card dashboard-shadow transition-all p-6 text-left align-middle">
              <div class="flex items-center">
                <div v-if="icon" class="flex-shrink-0 mr-4">
                  <div :class="iconContainerClass">
                    <Icon :icon="icon" class="h-6 w-6" :class="iconClass" />
                  </div>
                </div>
                <div class="flex-1">
                  <DialogTitle as="h3" class="text-lg font-medium leading-6 dashboard-text-primary">
                    {{ title }}
                  </DialogTitle>
                </div>
              </div>

              <div class="mt-4">
                <p class="text-sm dashboard-text-secondary">
                  {{ message }}
                </p>
              </div>

              <div v-if="$slots.details" class="mt-4">
                <slot name="details" />
              </div>

              <div class="mt-6 flex justify-end space-x-3">
                <button
                  @click="closeModal"
                  :disabled="loading"
                  class="inline-flex items-center px-4 py-2 dashboard-border border shadow-sm text-sm font-medium dashboard-text-secondary dashboard-bg-input hover:dashboard-bg-hover focus:outline-none focus:ring-2 focus:ring-offset-2 focus:dashboard-focus-primary disabled:opacity-50 disabled:cursor-not-allowed dashboard-transition"
                >
                  {{ cancelText }}
                </button>
                <button
                  @click="confirmAction"
                  :disabled="loading"
                  class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed dashboard-transition"
                  :class="confirmButtonClass"
                >
                  <CoreLoader v-if="loading" :height="12" :width="12" class="mr-2" />
                  <Icon v-else-if="confirmIcon" :icon="confirmIcon" class="h-4 w-4 mr-2" />
                  {{ confirmText }}
                </button>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue';

export interface ConfirmationDialogProps {
  isOpen: boolean;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  confirmIcon?: string;
  icon?: string;
  variant?: 'danger' | 'warning' | 'info' | 'success';
  loading?: boolean;
}

interface ConfirmationDialogEmits {
  (e: 'close'): void;
  (e: 'confirm'): void;
}

const props = withDefaults(defineProps<ConfirmationDialogProps>(), {
  confirmText: 'Confirm',
  cancelText: 'Cancel',
  variant: 'danger',
  loading: false,
});

const emit = defineEmits<ConfirmationDialogEmits>();

const iconContainerClass = computed(() => {
  switch (props.variant) {
    case 'danger':
      return 'p-3 rounded-full bg-red-100 dark:bg-red-900/20';
    case 'warning':
      return 'p-3 rounded-full bg-yellow-100 dark:bg-yellow-900/20';
    case 'info':
      return 'p-3 rounded-full bg-blue-100 dark:bg-blue-900/20';
    case 'success':
      return 'p-3 rounded-full bg-green-100 dark:bg-green-900/20';
    default:
      return 'p-3 rounded-full bg-gray-100 dark:bg-gray-900/20';
  }
});

const iconClass = computed(() => {
  switch (props.variant) {
    case 'danger':
      return 'text-red-600 dark:text-red-400';
    case 'warning':
      return 'text-yellow-600 dark:text-yellow-400';
    case 'info':
      return 'text-blue-600 dark:text-blue-400';
    case 'success':
      return 'text-green-600 dark:text-green-400';
    default:
      return 'text-gray-600 dark:text-gray-400';
  }
});

const confirmButtonClass = computed(() => {
  switch (props.variant) {
    case 'danger':
      return 'bg-red-600 hover:bg-red-700 focus:ring-red-500';
    case 'warning':
      return 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500';
    case 'info':
      return 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500';
    case 'success':
      return 'bg-green-600 hover:bg-green-700 focus:ring-green-500';
    default:
      return 'bg-gray-600 hover:bg-gray-700 focus:ring-gray-500';
  }
});

const closeModal = () => {
  if (!props.loading) {
    emit('close');
  }
};

const confirmAction = () => {
  if (!props.loading) {
    emit('confirm');
  }
};
</script>
