<template>
  <div
    :class="[
      'newsletter-widget',
      variant === 'compact' ? 'p-4' : 'p-6',
      'bg-white dark:bg-zinc-800 rounded-lg shadow-sm border border-zinc-200 dark:border-zinc-700'
    ]"
  >
    <!-- Header -->
    <div :class="variant === 'compact' ? 'mb-3' : 'mb-4'">
      <h3 :class="[
        'font-semibold text-zinc-900 dark:text-zinc-100',
        variant === 'compact' ? 'text-lg' : 'text-xl'
      ]">
        <Icon icon="heroicons:envelope" class="w-5 h-5 inline mr-2" />
        {{ title }}
      </h3>
      <p :class="[
        'text-zinc-600 dark:text-zinc-400',
        variant === 'compact' ? 'text-sm' : 'text-base'
      ]">
        {{ description }}
      </p>
    </div>

    <!-- Success State -->
    <div v-if="showSuccess" class="text-center py-4">
      <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 dark:bg-green-900/20 mb-3">
        <Icon icon="heroicons:check" class="h-6 w-6 text-green-600 dark:text-green-400" />
      </div>
      <p class="text-green-600 dark:text-green-400 font-medium">
        Successfully subscribed!
      </p>
      <p class="text-sm text-zinc-600 dark:text-zinc-400 mt-1">
        Thank you for subscribing to our newsletter.
      </p>
    </div>

    <!-- Subscription Form -->
    <div v-else>
      <FormKit
        type="form"
        :actions="false"
        @submit="handleSubscribe"
        :disabled="loading"
      >
        <div :class="variant === 'compact' ? 'space-y-3' : 'space-y-4'">
          <!-- Email Input -->
          <div>
            <FormKit
              type="email"
              name="email"
              v-model="formData.email"
              required
              :placeholder="emailPlaceholder"
              label=""
              :classes="{
                input: 'w-full px-3 py-2 border border-zinc-300 dark:border-zinc-600 rounded-md bg-white dark:bg-zinc-700 text-zinc-900 dark:text-zinc-100 placeholder-zinc-500 dark:placeholder-zinc-400 focus:ring-2 focus:ring-red-500 focus:border-red-500'
              }"
            />
          </div>

          <!-- Name Input (optional) -->
          <div v-if="showNameField">
            <FormKit
              type="text"
              name="name"
              v-model="formData.name"
              placeholder="Your name (optional)"
              label=""
              :classes="{
                input: 'w-full px-3 py-2 border border-zinc-300 dark:border-zinc-600 rounded-md bg-white dark:bg-zinc-700 text-zinc-900 dark:text-zinc-100 placeholder-zinc-500 dark:placeholder-zinc-400 focus:ring-2 focus:ring-red-500 focus:border-red-500'
              }"
            />
          </div>

          <!-- Preferences -->
          <div v-if="showPreferences" :class="variant === 'compact' ? 'space-y-2' : 'space-y-3'">
            <h4 :class="[
              'font-medium text-zinc-900 dark:text-zinc-100',
              variant === 'compact' ? 'text-sm' : 'text-base'
            ]">
              What would you like to receive?
            </h4>
            <div :class="variant === 'compact' ? 'space-y-1' : 'space-y-2'">
              <label
                v-for="preference in availablePreferences"
                :key="preference.key"
                class="flex items-center"
              >
                <input
                  type="checkbox"
                  :value="preference.key"
                  v-model="formData.preferences"
                  class="w-4 h-4 text-red-600 bg-zinc-100 dark:bg-zinc-700 border-zinc-300 dark:border-zinc-600 rounded focus:ring-red-500"
                />
                <span :class="[
                  'ml-2 text-zinc-700 dark:text-zinc-300',
                  variant === 'compact' ? 'text-xs' : 'text-sm'
                ]">
                  {{ preference.label }}
                </span>
              </label>
            </div>
          </div>

          <!-- Submit Button -->
          <button
            type="submit"
            :disabled="loading || !formData.email"
            :class="[
              'w-full border border-transparent font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed',
              variant === 'compact' ? 'px-4 py-2 text-sm' : 'px-6 py-3 text-base'
            ]"
          >
            <span v-if="loading" class="flex items-center justify-center">
              <CoreLoader :width="16" :height="16" color="#ffffff" />
              <span class="ml-2">{{ loadingText }}</span>
            </span>
            <span v-else>{{ buttonText }}</span>
          </button>

          <!-- Privacy Notice -->
          <p :class="[
            'text-zinc-500 dark:text-zinc-400 text-center',
            variant === 'compact' ? 'text-xs' : 'text-sm'
          ]">
            {{ privacyText }}
            <NuxtLink
              to="/newsletter/preferences"
              class="text-red-600 dark:text-red-400 hover:underline"
            >
              Manage preferences
            </NuxtLink>
          </p>
        </div>
      </FormKit>

      <!-- Error Message -->
      <div v-if="errorMessage" :class="['mt-3 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md']">
        <div class="flex items-center">
          <Icon
            icon="heroicons:exclamation-triangle"
            class="w-4 h-4 text-red-600 dark:text-red-400 mr-2"
          />
          <p class="text-red-800 dark:text-red-200 text-sm">{{ errorMessage }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ENDPOINTS } from "@/utils/api";

interface Props {
  variant?: 'default' | 'compact';
  title?: string;
  description?: string;
  emailPlaceholder?: string;
  buttonText?: string;
  loadingText?: string;
  privacyText?: string;
  showNameField?: boolean;
  showPreferences?: boolean;
  defaultPreferences?: string[];
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  title: 'Subscribe to Newsletter',
  description: 'Get the latest updates on events and news from EventaHub Malawi.',
  emailPlaceholder: 'Enter your email address',
  buttonText: 'Subscribe',
  loadingText: 'Subscribing...',
  privacyText: 'By subscribing, you agree to receive marketing emails. You can unsubscribe at any time.',
  showNameField: false,
  showPreferences: true,
  defaultPreferences: () => ['latest_events', 'recommended_events'],
});

const emit = defineEmits<{
  (e: 'subscribe', data: { email: string; name?: string; preferences: string[] }): void;
  (e: 'success'): void;
  (e: 'error', message: string): void;
}>();

const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const loading = ref(false);
const showSuccess = ref(false);
const showPreferences = ref(false);
const errorMessage = ref("");

const formData = ref({
  email: "",
  name: "",
  preferences: [...props.defaultPreferences],
});

const availablePreferences = [
  { key: "latest_events", label: "Latest Events" },
  { key: "recommended_events", label: "Recommended Events" },
  { key: "new_venues", label: "New Venues" },
  { key: "event_updates", label: "Event Updates" },
];

const handleSubscribe = async (data: any) => {
  if (!data.email) return;

  loading.value = true;
  errorMessage.value = "";
  showSuccess.value = false;

  try {
    const payload = {
      email: data.email,
      name: formData.value.name || undefined,
      preferences: formData.value.preferences,
    };

    const response = await httpClient.post(ENDPOINTS.NEWSLETTER.SUBSCRIBE, payload);

    if (response) {
      showSuccess.value = true;
      emit('subscribe', payload);
      emit('success');

      // Reset form
      formData.value.email = "";
      formData.value.name = "";
      formData.value.preferences = [...props.defaultPreferences];
      showPreferences.value = false;

      // Hide success message after 5 seconds
      setTimeout(() => {
        showSuccess.value = false;
      }, 5000);

      $toast.success("Successfully subscribed to newsletter!");
    }
  } catch (error: any) {
    console.error("Newsletter subscription error:", error);

    let message = "Failed to subscribe. Please try again.";

    if (error.response?.data?.message) {
      message = error.response.data.message;
    } else if (error.response?.data?.errors?.email) {
      message = error.response.data.errors.email[0];
    }

    errorMessage.value = message;
    emit('error', message);

    // Hide error message after 5 seconds
    setTimeout(() => {
      errorMessage.value = "";
    }, 5000);
  } finally {
    loading.value = false;
  }
};

// Show preferences when user starts typing email
watch(
  () => formData.value.email,
  (newEmail) => {
    if (props.showPreferences && newEmail && newEmail.includes("@")) {
      showPreferences.value = true;
    }
  }
);

// Reset component when variant changes
watch(
  () => props.variant,
  () => {
    showSuccess.value = false;
    errorMessage.value = "";
    showPreferences.value = false;
  }
);
</script>

<style scoped>
.newsletter-widget {
  transition: all 0.2s ease-in-out;
}
</style>
