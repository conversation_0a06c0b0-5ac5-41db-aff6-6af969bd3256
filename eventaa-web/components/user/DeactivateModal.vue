<template>
    <div>
        <CorePrimaryButton @click="openModal" text="Deactivate" />
    </div>
    <TransitionRoot appear :show="isOpen" as="template">
        <Dialog as="div" @close="closeModal" class="relative z-10">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
                leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                <div class="fixed inset-0 bg-black/25 dark:bg-black/50" />
            </TransitionChild>

            <div class="fixed inset-0 overflow-y-auto">
                <div class="flex min-h-full items-center justify-center p-4 text-center">
                    <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                        enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
                        leave-to="opacity-0 scale-95">
                        <DialogPanel
                            class="w-full max-w-xl transform overflow-hidden bg-white dark:bg-zinc-800 text-left align-middle shadow-xl transition-all">
                            <DialogTitle as="h3" class="w-full border-b border-gray-200 dark:border-zinc-600 px-4 py-3 flex text-xl font-semibold leading-6 text-gray-900 dark:text-zinc-100">
                                <Icon icon="emojione-v1:warning" class="w-6 h-6 mr-2"/>
                                Delete Account
                            </DialogTitle>
                            <div class="mt-2 px-5">
                                <p class="sm:text-base text-sm text-gray-500 dark:text-zinc-400 bg-gray-50 dark:bg-zinc-700 px-2 py-2 border border-dotted border-gray-200 dark:border-zinc-600">
                                    Do you really want to delete your account? Your account will be deactivated for 14 days, it will be re-activated upon logging in. After this period your account will be permanently deleted.
                                </p>
                            </div>

                            <div class="w-full border-t border-gray-200 dark:border-zinc-600 mt-4 py-3 px-5 flex items-center justify-end space-x-3">
                                <button @click="deactivateAccount" :disabled="loading" class="px-4 py-2 border-2 border-red-600 dark:border-red-500 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 disabled:opacity-50 disabled:cursor-not-allowed">
                                    <span v-if="loading" class="inline-flex items-center">
                                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Deactivating...
                                    </span>
                                    <span v-else>Deactivate</span>
                                </button>
                                <div class="w-24" @click="gladYouStayed">
                                    <CorePrimaryButton text="Cancel"/>
                                </div>
                            </div>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>
</template>

<script setup lang="ts">
import {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
} from '@headlessui/vue'
import type { DeactivateAccountResponse } from '@/types/api'

const isOpen = ref<boolean>(false);
const loading = ref<boolean>(false);
const { $toast } = useNuxtApp();
const httpClient = useHttpClient();

const closeModal = (): void => {
    if (loading.value) return;
    isOpen.value = false
}

const openModal = (): void => {
    isOpen.value = true
}

const gladYouStayed = (): void => {
    closeModal();
    $toast.success("Pheww, We are glad you stayed!")
}

const deactivateAccount = async (): Promise<void> => {
    try {
        loading.value = true;
        const response = await httpClient.post<DeactivateAccountResponse>(ENDPOINTS.AUTH.DEACTIVATE_USER);

        if (response) {
            $toast.success(response.message || 'Account scheduled for deactivation');
            closeModal();

            setTimeout(() => {
                navigateTo('/auth/login');
            }, 2000);
        }
    } catch (error: any) {
        console.error('Error deactivating account:', error);
        $toast.error(error.message || 'Failed to deactivate account');
    } finally {
        loading.value = false;
    }
}
</script>
