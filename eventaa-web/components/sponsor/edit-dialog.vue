<template>
    <div>
        <button @click="openModal" class="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200">
            <Icon icon="heroicons:pencil" class="w-5 h-5" />
        </button>
    </div>

    <TransitionRoot appear :show="isOpen" as="template">
        <Dialog as="div" @close="closeModal" class="relative z-10">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
                leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                <div class="fixed inset-0 bg-black/25" />
            </TransitionChild>

            <div class="fixed inset-0 overflow-y-auto">
                <div class="flex min-h-full items-center justify-center">
                    <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                        enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
                        leave-to="opacity-0 scale-95">
                        <DialogPanel class="w-full max-w-xl transform overflow-hidden bg-white shadow-xl transition-all">
                            <DialogTitle class="w-full flex items-center justify-between px-4 py-2 border-b">
                                <h3 class="text-xl font-semibold">Edit sponsor</h3>
                                <button type="button" @click="closeModal"
                                    class="text-gray-500 hover:text-gray-700 transition-colors">
                                    <Icon icon="iconamoon:close" class="h-6 w-6" />
                                </button>
                            </DialogTitle>

                            <FormKit type="form" id="editSponsorForm" @submit="onFormSubmit" :actions="false"
                                #default="{ state: { valid } }">
                                <div class="space-y-4 px-5">
                                    <FormKit type="text" v-model="formData.name" label="Name" name="name"
                                        placeholder="Enter sponsor name" validation="required|string|length:3,100"
                                        :validation-messages="{
                                            required: 'Sponsor name is required',
                                            length: 'Name must be between 3 and 100 characters'
                                        }" />

                                    <div class="space-y-2">
                                        <label class="block text-sm font-medium text-gray-700">Current Logo</label>
                                        <div v-if="currentLogoUrl" class="flex items-center space-x-4">
                                            <img :src="currentLogoUrl" alt="Current logo" class="w-16 h-16 object-contain border rounded" />
                                            <button type="button" @click="removeLogo"
                                                class="text-red-600 hover:text-red-700 text-sm">
                                                Remove Logo
                                            </button>
                                        </div>
                                    </div>

                                    <EventsImagePicker
                                        v-if="!currentLogoUrl"
                                        label="Update Logo"
                                        :max-files="1"
                                        accept="image/*"
                                        @files-selected="onLogoPicked"
                                        @file-removed="onFileRemoved"
                                    />

                                    <div>
                                        <h3 class="text-lg font-semibold mb-2">Sponsor Headquarters</h3>
                                        <CoreLocationPicker
                                            @update:location="onUpdateLocation"
                                            :initial-latitude="formData.latitude"
                                            :initial-longitude="formData.longitude"
                                        />
                                    </div>

                                    <FormKit type="text" v-model="formData.address" label="Address" name="address"
                                        placeholder="Enter address" validation="required|string" />

                                    <div class="grid grid-cols-2 gap-4">
                                        <FormKit type="text" v-model="formData.city" label="City" name="city"
                                            placeholder="Enter city" validation="required|string" />
                                        <FormKit type="text" v-model="formData.country" label="Country" name="country"
                                            placeholder="Enter country" validation="required|string" />
                                    </div>
                                </div>

                                <div class="mt-6 flex justify-end space-x-3 px-4 py-3 border-t">
                                    <button type="button" @click="closeModal"
                                        class="px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 bg-white border border-gray-300 hover:bg-gray-50">
                                        Cancel
                                    </button>
                                    <button type="submit" :disabled="isSubmitting || !valid"
                                        class="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed">
                                        {{ isSubmitting ? 'Updating...' : 'Update Sponsor' }}
                                    </button>
                                </div>
                            </FormKit>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
} from '@headlessui/vue'

const props = defineProps<{
    sponsor: {
        id: number
        name: string
        logo: string
        latitude: string
        longitude: string
        address: string
        city: string
        country: string
    }
}>()

const emit = defineEmits(['sponsor-updated'])

const { $toast }: any = useNuxtApp()
const runtimeConfig = useRuntimeConfig()
const isOpen = ref(false)
const httpClient = useHttpClient()
const isSubmitting = ref(false)
const logoRemoved = ref(false)

const currentLogoUrl = computed(() => {
    if (logoRemoved.value) return null
    if (formData.logo instanceof File) {
        return URL.createObjectURL(formData.logo)
    }
    return props.sponsor.logo ? `${runtimeConfig.public.baseUrl}storage/sponsors/${props.sponsor.logo}` : null
})

const formData = reactive({
    name: props.sponsor.name,
    logo: props.sponsor.logo as any,
    latitude: props.sponsor.latitude,
    longitude: props.sponsor.longitude,
    address: props.sponsor.address,
    city: props.sponsor.city,
    country: props.sponsor.country
})

const openModal = () => {
    logoRemoved.value = false
    isOpen.value = true
}

const closeModal = () => {
    isOpen.value = false
    logoRemoved.value = false
    Object.assign(formData, {
        name: props.sponsor.name,
        logo: null,
        latitude: props.sponsor.latitude,
        longitude: props.sponsor.longitude,
        address: props.sponsor.address,
        city: props.sponsor.city,
        country: props.sponsor.country
    })
}

const removeLogo = () => {
    logoRemoved.value = true
    formData.logo = null
}

const onLogoPicked = (files: File[]) => {
    if (files.length > 0) {
        formData.logo = files[0]
        logoRemoved.value = false
    }
}

const onUpdateLocation = (e: any) => {
    formData.latitude = e.latitude || ''
    formData.longitude = e.longitude || ''
}

const onFileRemoved = () => {
    formData.logo = null
}

const onFormSubmit = async () => {
    try {
        isSubmitting.value = true

        const formDataObj = new FormData()
        Object.entries(formData).forEach(([key, value]) => {
            if (value !== null) {
                formDataObj.append(key, value)
            }
        });

        formDataObj.append('_method', 'PUT')

        const response = await httpClient.post(`${ENDPOINTS.SPONSORS.UPDATE}/${props.sponsor.id}`, formDataObj)
        if (response) {
            $toast.success('Sponsor updated successfully')
            emit('sponsor-updated')
            closeModal()
        }
    } catch (error: any) {
        if (error) {
            const errors = error.errors
            Object.keys(errors).forEach((key) => {
                if (Array.isArray(errors[key])) {
                    errors[key].forEach((message: string) => {
                        $toast.error(message)
                    })
                } else if (typeof errors[key] === 'string') {
                    $toast.error(errors[key])
                }
            })
        } else if (error.message) {
            $toast.error(error.message)
        } else {
            $toast.error('Something went wrong, please try again later')
        }
    } finally {
        isSubmitting.value = false
    }
}
</script>
