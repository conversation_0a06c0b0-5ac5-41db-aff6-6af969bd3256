<template>
    <div class="w-full">
        <label class="block text-lg font-medium text-black mb-2">Tags</label>
        <input type="text" v-model="inputValue" @keydown="handleKeyDown" @input="handleInput" placeholder="Add a tag" class="mt-1 block w-full rounded-none focus:outline-none focus:ring-0 border px-2 py-2" />
        <div class="mt-2 flex flex-wrap items-center gap-2">
            <div v-for="(tag, index) in modelValue" :key="index"
                class="inline-flex items-center px-3 py-1 rounded-full font-medium bg-gray-100 border">
                <p>{{ tag }}</p>
                <button @click="removeTag(index)" class="ml-2 focus:outline-none">
                    <Icon icon="material-symbols:close" class="w-4 h-4"/>
                </button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">

const props = defineProps({
    modelValue: {
        type: Array,
        default: () => [],
    }
})

const emit = defineEmits(['update:modelValue'])

const inputValue = ref('')

const createTag = () => {
    const tag = inputValue.value.trim()
    if (tag && !props.modelValue.includes(tag)) {
        emit('update:modelValue', [...props.modelValue, tag])
        inputValue.value = ''
    }
}

const handleKeyDown = (e: { key: string; preventDefault: () => void }) => {
    if (e.key === 'Enter') {
        e.preventDefault()
        createTag()
    }
}

const handleInput = (e: any) => {
    const value = e.target.value
    if (value.endsWith(',')) {
        inputValue.value = value.slice(0, -1);
        createTag()
    }
}

const removeTag = (indexToRemove: number) => {
    emit('update:modelValue', props.modelValue.filter((_, index) => index !== indexToRemove))
}
</script>