<template>
  <div class="h-full w-full">
    <div class="flex flex-col h-full">
      <!-- Y-axis labels -->
      <div class="flex justify-between h-full">
        <div class="w-12 flex flex-col justify-between py-4">
          <div v-for="i in 5" :key="i" class="h-4 w-8 bg-gray-200 rounded animate-pulse"></div>
        </div>
        <!-- Chart area -->
        <div class="flex-1 flex items-end">
          <div class="w-full h-[90%] flex items-end justify-between px-4">
            <div v-for="i in 12" :key="i" class="w-4 bg-gray-200 rounded-t animate-pulse" :style="{ height: `${Math.random() * 100}%` }"></div>
          </div>
        </div>
      </div>
      <!-- X-axis labels -->
      <div class="flex justify-between px-16 mt-2">
        <div v-for="i in 6" :key="i" class="h-4 w-12 bg-gray-200 rounded animate-pulse"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// No props or logic needed for this component
</script>

<style scoped>
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}
</style>
