<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h2 class="text-2xl font-bold text-zinc-900 dark:text-zinc-100">
          Newsletter Management
        </h2>
        <p class="text-zinc-600 dark:text-zinc-400">
          Manage newsletter subscriptions and send campaigns
        </p>
      </div>
      <div class="flex space-x-3">
        <button
          @click="refreshStats"
          :disabled="loading"
          class="px-4 py-2 border border-zinc-300 dark:border-zinc-600 text-sm font-medium rounded-md text-zinc-700 dark:text-zinc-300 hover:bg-zinc-50 dark:hover:bg-zinc-700 focus:outline-none focus:ring-2 focus:ring-red-500"
        >
          <Icon icon="heroicons:arrow-path" class="w-4 h-4 mr-2" />
          Refresh
        </button>
        <button
          @click="showSendCampaign = true"
          class="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
        >
          <Icon icon="heroicons:paper-airplane" class="w-4 h-4 mr-2" />
          Send Campaign
        </button>
      </div>
    </div>

    <!-- Stats Cards -->
    <div v-if="stats" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-sm border border-zinc-200 dark:border-zinc-700 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <Icon icon="heroicons:users" class="w-8 h-8 text-blue-500" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-zinc-600 dark:text-zinc-400">
              Total Subscribers
            </p>
            <p class="text-2xl font-semibold text-zinc-900 dark:text-zinc-100">
              {{ stats.total_subscribers.toLocaleString() }}
            </p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-sm border border-zinc-200 dark:border-zinc-700 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <Icon icon="heroicons:check-circle" class="w-8 h-8 text-green-500" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-zinc-600 dark:text-zinc-400">
              Active Subscribers
            </p>
            <p class="text-2xl font-semibold text-zinc-900 dark:text-zinc-100">
              {{ stats.active_subscribers.toLocaleString() }}
            </p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-sm border border-zinc-200 dark:border-zinc-700 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <Icon icon="heroicons:x-circle" class="w-8 h-8 text-red-500" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-zinc-600 dark:text-zinc-400">
              Inactive Subscribers
            </p>
            <p class="text-2xl font-semibold text-zinc-900 dark:text-zinc-100">
              {{ stats.inactive_subscribers.toLocaleString() }}
            </p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-sm border border-zinc-200 dark:border-zinc-700 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <Icon icon="heroicons:trending-up" class="w-8 h-8 text-purple-500" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-zinc-600 dark:text-zinc-400">
              This Week
            </p>
            <p class="text-2xl font-semibold text-zinc-900 dark:text-zinc-100">
              +{{ stats.recent_subscriptions.toLocaleString() }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Preferences Breakdown -->
    <div v-if="stats?.preferences_breakdown" class="bg-white dark:bg-zinc-800 rounded-lg shadow-sm border border-zinc-200 dark:border-zinc-700 p-6">
      <h3 class="text-lg font-semibold text-zinc-900 dark:text-zinc-100 mb-4">
        Preference Breakdown
      </h3>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div v-for="(count, preference) in stats.preferences_breakdown" :key="preference" class="text-center">
          <p class="text-2xl font-bold text-red-600 dark:text-red-400">
            {{ count }}
          </p>
          <p class="text-sm text-zinc-600 dark:text-zinc-400 capitalize">
            {{ String(preference).replace('_', ' ') }}
          </p>
        </div>
      </div>
    </div>

    <!-- Send Campaign Modal -->
    <CoreModal
      v-model="showSendCampaign"
      title="Send Newsletter Campaign"
      max-width="lg"
    >
      <div class="p-6">
        <FormKit
          type="form"
          :actions="false"
          @submit="handleSendCampaign"
          :disabled="sending"
        >
          <div class="space-y-6">
            <FormKit
              type="select"
              name="type"
              v-model="campaignForm.type"
              required
              label="Campaign Type"
              :options="campaignTypes"
              :classes="{
                input: 'w-full px-3 py-2 border border-zinc-300 dark:border-zinc-600 rounded-md bg-white dark:bg-zinc-700 text-zinc-900 dark:text-zinc-100'
              }"
            />

            <FormKit
              type="text"
              name="subject"
              v-model="campaignForm.subject"
              label="Email Subject (Optional)"
              placeholder="Leave empty to use default subject"
              :classes="{
                input: 'w-full px-3 py-2 border border-zinc-300 dark:border-zinc-600 rounded-md bg-white dark:bg-zinc-700 text-zinc-900 dark:text-zinc-100'
              }"
            />

            <div class="bg-zinc-50 dark:bg-zinc-700 rounded-lg p-4">
              <h4 class="text-sm font-medium text-zinc-900 dark:text-zinc-100 mb-2">
                Campaign Preview
              </h4>
              <div v-if="campaignForm.type" class="text-sm text-zinc-600 dark:text-zinc-400">
                <p><strong>Type:</strong> {{ getCampaignTypeName(campaignForm.type) }}</p>
                <p><strong>Recipients:</strong> {{ stats?.active_subscribers || 0 }} active subscribers</p>
                <p><strong>Subject:</strong> {{ campaignForm.subject || getDefaultSubject(campaignForm.type) }}</p>
              </div>
            </div>

            <div class="flex space-x-3 pt-4">
              <button
                type="button"
                @click="showSendCampaign = false"
                class="flex-1 px-4 py-2 border border-zinc-300 dark:border-zinc-600 text-sm font-medium rounded-md text-zinc-700 dark:text-zinc-300 hover:bg-zinc-50 dark:hover:bg-zinc-700 focus:outline-none focus:ring-2 focus:ring-red-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                :disabled="sending || !campaignForm.type"
                class="flex-1 px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50"
              >
                <span v-if="sending" class="flex items-center justify-center">
                  <CoreLoader :width="16" :height="16" color="#ffffff" />
                  <span class="ml-2">Sending...</span>
                </span>
                <span v-else>Send Campaign</span>
              </button>
            </div>
          </div>
        </FormKit>
      </div>
    </CoreModal>

    <!-- Loading State -->
    <div v-if="loading && !stats" class="text-center py-12">
      <CoreLoader :width="32" :height="32" color="#ef4444" />
      <p class="mt-4 text-zinc-600 dark:text-zinc-400">Loading newsletter statistics...</p>
    </div>

    <!-- Error State -->
    <div v-if="error" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
      <div class="flex items-center">
        <Icon icon="heroicons:exclamation-triangle" class="w-5 h-5 text-red-600 dark:text-red-400 mr-2" />
        <p class="text-red-800 dark:text-red-200 text-sm">{{ error }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ENDPOINTS } from "@/utils/api";

const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const loading = ref(false);
const sending = ref(false);
const error = ref("");
const stats = ref<any>(null);
const showSendCampaign = ref(false);

const campaignForm = ref({
  type: "",
  subject: "",
});

const campaignTypes = [
  { value: "latest_events", label: "Latest Events" },
  { value: "recommended_events", label: "Recommended Events" },
  { value: "new_venues", label: "New Venues" },
  { value: "weekly_digest", label: "Weekly Digest" },
];

const fetchStats = async () => {
  loading.value = true;
  error.value = "";

  try {
    const response = await httpClient.get(ENDPOINTS.NEWSLETTER.STATS) as any;
    if (response?.data) {
      stats.value = response.data;
    }
  } catch (err: any) {
    console.error("Fetch newsletter stats error:", err);
    error.value = "Failed to load newsletter statistics";
  } finally {
    loading.value = false;
  }
};

const refreshStats = () => {
  fetchStats();
};

const handleSendCampaign = async () => {
  if (!campaignForm.value.type) {
    $toast.error("Please select a campaign type");
    return;
  }

  sending.value = true;

  try {
    const payload: any = {
      type: campaignForm.value.type,
    };

    if (campaignForm.value.subject) {
      payload.subject = campaignForm.value.subject;
    }

    const response = await httpClient.post(ENDPOINTS.NEWSLETTER.SEND, payload);

    if (response) {
      $toast.success("Newsletter campaign sent successfully!");
      showSendCampaign.value = false;
      campaignForm.value = { type: "", subject: "" };

      // Refresh stats to show updated numbers
      setTimeout(() => {
        fetchStats();
      }, 1000);
    }
  } catch (err: any) {
    console.error("Send campaign error:", err);

    if (err.response?.data?.message) {
      $toast.error(err.response.data.message);
    } else {
      $toast.error("Failed to send campaign. Please try again.");
    }
  } finally {
    sending.value = false;
  }
};

const getCampaignTypeName = (type: string) => {
  const campaign = campaignTypes.find(c => c.value === type);
  return campaign?.label || type;
};

const getDefaultSubject = (type: string) => {
  switch (type) {
    case "latest_events":
      return "🎉 Latest Events This Week - EventaHub Malawi";
    case "recommended_events":
      return "⭐ Recommended Events Just for You - EventaHub Malawi";
    case "new_venues":
      return "🏢 New Venues Added - EventaHub Malawi";
    case "weekly_digest":
      return "📅 Your Weekly Event Digest - EventaHub Malawi";
    default:
      return "📧 Newsletter Update - EventaHub Malawi";
  }
};

// Fetch stats on component mount
onMounted(() => {
  fetchStats();
});
</script>
