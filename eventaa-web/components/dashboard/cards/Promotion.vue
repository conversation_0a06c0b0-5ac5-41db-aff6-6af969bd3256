<template>
  <div class="bg-red-50 p-4 shadow-sm rounded-md">
    <h2 class="text-xl font-bold text-gray-800 mb-1">
      {{ title }}
    </h2>
    <p class="text-gray-500 text-sm mb-3">
      {{ subtitle }}
    </p>

    <div class="relative h-1 mb-4">
      <div class="w-full h-full bg-gray-200"></div>
      <div
        class="absolute top-0 left-0 h-full bg-red-600"
        :style="{ width: `${progressValue}%` }"
      ></div>
    </div>

    <button
      class="w-full bg-red-600 text-white py-2 px-3 flex items-center justify-center font-medium hover:bg-red-700 transition-colors text-sm"
      @click="navigateTo('/dashboard/subscription-payments')"
    >
      <Icon icon="heroicons:sparkles" class="w-4 h-4 mr-1" />
      {{ buttonText }}
    </button>
  </div>
</template>

<script setup lang="ts">
import { navigateTo } from '#app';

defineProps({
  progressValue: {
    type: Number,
    default: 40,
  },
  title: {
    type: String,
    default: "Upgrade to a better plan.",
  },
  subtitle: {
    type: String,
    default: "Unlock additional features, enhanced capabilities",
  },
  buttonText: {
    type: String,
    default: "See plans",
  },
});
</script>
