<template>
  <div class="dashboard-bg-card p-6 dashboard-shadow flex flex-col items-center text-center group hover:dashboard-bg-hover dashboard-transition">
    <div class="p-3 rounded-full bg-red-100 dark:bg-red-900/20 mb-4 group-hover:scale-110 dashboard-transition">
      <Icon :icon="icon" class="w-8 h-8 text-red-600 dark:text-red-400" />
    </div>
    <h2 class="text-sm font-medium dashboard-text-muted uppercase tracking-wider mb-2">{{ title }}</h2>
    <p class="text-2xl font-bold dashboard-text-primary mb-1">{{ value }}</p>
    <p class="dashboard-text-muted text-sm">{{ subValue }}</p>

    <div v-if="growth !== undefined" class="mt-3 flex items-center">
      <Icon
        :icon="growth >= 0 ? 'mdi:trending-up-variant' : 'mdi:trending-down-variant'"
        class="w-5 h-5 mr-1"
        :class="growth >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'"
      />
      <span
        class="text-sm font-medium"
        :class="growth >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'"
      >
        {{ Math.abs(growth) }}%
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from "vue";

defineProps<{
  title: string;
  value: string;
  subValue: string;
  icon: string;
  growth?: number;
}>();
</script>

<style></style>
