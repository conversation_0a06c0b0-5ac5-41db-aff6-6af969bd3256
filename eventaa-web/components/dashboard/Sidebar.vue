<template>
  <div class="flex dashboard-bg-sidebar sm:border-r dashboard-border">
    <div
      :class="isOpen ? 'block' : 'hidden'"
      class="fixed inset-0 z-20 bg-red-200 bg-opacity-50 transition-opacity lg:hidden"
      @click="toggleSidebar"
    ></div>

    <div
      :class="[
        isOpen ? 'translate-x-0 ease-out' : '-translate-x-full ease-in',
        isMiniVariant ? 'lg:w-16' : 'lg:w-64',
      ]"
      class="fixed inset-y-0 left-0 z-30 flex flex-col transition duration-300 transform dashboard-bg-sidebar lg:translate-x-0 lg:static lg:inset-0"
    >
      <div class="flex items-center justify-between px-4 py-3 dashboard-border border-b">
        <div class="flex items-center">
          <img src="/icon.png" class="w-auto h-10" alt="app-icon"/>
          <span
            v-if="!isMiniVariant"
            class="mx-2 sm:text-2xl text-lg font-semibold dashboard-text-primary"
            >EventaHub</span
          >
        </div>
        <button v-if="!isMiniVariant" @click="toggleMini"
          class="p-1 rounded-full hover:dashboard-bg-hover lg:block hidden dashboard-text-secondary dashboard-transition">
          <Icon icon="heroicons:chevron-left" class="w-5 h-5" />
        </button>
      </div>

      <div class="flex-1 overflow-y-auto">
        <nav class="mt-5 px-2">
        <div v-for="(section, index) in sections" :key="index" class="mb-4">
          <div v-if="!isMiniVariant"
            class="px-3 mb-2 text-xs font-semibold dashboard-text-muted uppercase tracking-wider">
            {{ section.title }}
          </div>
          <div v-else class="dashboard-border border-b mb-2"></div>

          <router-link
            v-for="item in section.items"
            :key="item.title"
            class="flex items-center px-3 py-2 text-sm font-medium dashboard-transition"
            :class="[
              $route.path === item.link ? activeClass : inactiveClass,
              isMiniVariant ? 'justify-center' : '',
            ]"
            :to="item.link"
            @click="handleLinkClick(item.title)"
          >
            <Icon :icon="item.icon" class="w-5 h-5" />
            <span v-if="!isMiniVariant" class="ml-3">{{ item.title }}</span>
            <span v-if="!isMiniVariant && item.count && item.count > 0"
              class="ml-auto bg-red-600 text-white py-0.5 px-2 text-xs">
              {{ item.count }}
            </span>
          </router-link>
        </div>
      </nav>
      </div>

      <div class="w-full mt-auto" :class="isMiniVariant ? 'px-2' : 'px-4'">
        <p
          v-if="!isMiniVariant"
          class="dashboard-text-muted dashboard-border border-t py-3 px-4 text-sm"
        >
          &copy; {{ new Date().getFullYear() }} EventaHub. All rights reserved.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useSidebarStore } from "@/store/sidebar";
import { useSidebar } from "@/composables/useSidebar";
import { useAuthStore } from "@/store/auth";

const sidebarStore = useSidebarStore();
const authStore = useAuthStore();
const { active } = sidebarStore;
const sections = computed(() => sidebarStore.sections);
const { isOpen, isMiniVariant } = useSidebar();
const activeClass = ref("font-medium border-l-4 border-red-600 bg-red-100 dark:bg-red-900/20 dashboard-text-primary");
const inactiveClass = ref(
  "font-medium dashboard-text-primary hover:bg-red-600 hover:text-white hover:border-red-700 dashboard-transition"
);

const nuxtApp = useNuxtApp();
const $echo = nuxtApp.$echo as any;

const toggleSidebar = () => {
  isOpen.value = !isOpen.value;
};

const toggleMini = () => {
  isMiniVariant.value = !isMiniVariant.value;
};

const handleLinkClick = (title: string) => {
  sidebarStore.active = title;
  // Close sidebar on mobile when a link is clicked
  if (window.innerWidth < 1024) { // lg breakpoint
    isOpen.value = false;
  }
};

onMounted(async () => {
  // Fetch all communication counts when the component is mounted
  await Promise.all([
    sidebarStore.fetchEventCount(),
    sidebarStore.fetchAllCommunicationCounts()
  ]);

  // Set up real-time listeners if Echo is available
  if ($echo && authStore.user?.id && typeof $echo.private === 'function') {
    try {
      // Listen for new notifications
      $echo.private(`notifications.${authStore.user.id}`)
        .listen('App\\Events\\NotificationCreated', (event: any) => {
          if (!event.read_at) {
            sidebarStore.incrementUnreadNotifications();
          }
        });

      // Listen for new messages
      $echo.private(`user.${authStore.user.id}.messages`)
        .listen('MessageSent', () => {
          sidebarStore.incrementUnreadMessages();
        });

      // Listen for new reviews that need attention
      $echo.private(`user.${authStore.user.id}`)
        .listen('ReviewCreated', () => {
          sidebarStore.incrementPendingReviews();
        });
    } catch (error) {
      console.error('Failed to subscribe to real-time events:', error);
    }
  }
});

defineExpose({
  toggleMini,
});
</script>
