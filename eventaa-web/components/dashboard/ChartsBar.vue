<template>
  <div class="h-full w-full">
    <canvas ref="chartRef"></canvas>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, onUnmounted } from 'vue';
import { Chart } from 'chart.js/auto';
import type { ChartConfiguration, ChartOptions } from 'chart.js';

const props = defineProps<{
  data: {
    labels: string[];
    values: number[];
  };
  options?: ChartOptions;
}>();

const chartRef = ref<HTMLCanvasElement | null>(null);
let chart: Chart | null = null;

const createChart = () => {
  if (!chartRef.value) return;

  const ctx = chartRef.value.getContext('2d');
  if (!ctx) return;

  const config: ChartConfiguration = {
    type: 'bar',
    data: {
      labels: props.data.labels,
      datasets: [
        {
          label: 'Value',
          data: props.data.values,
          backgroundColor: 'rgba(239, 68, 68, 0.8)',
          borderColor: '#ef4444',
          borderWidth: 1,
          borderRadius: 4,
          maxBarThickness: 32
        }
      ]
    },
    options: {
      ...props.options,
      interaction: {
        intersect: false,
        mode: 'index'
      },
      plugins: {
        tooltip: {
          enabled: true,
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: '#ffffff',
          bodyColor: '#ffffff',
          padding: 12,
          displayColors: false
        },
        ...props.options?.plugins
      }
    }
  };

  chart = new Chart(ctx, config);
};

const updateChart = () => {
  if (!chart) return;

  chart.data.labels = props.data.labels;
  chart.data.datasets[0].data = props.data.values;
  chart.update();
};

watch(() => props.data, () => {
  if (chart) {
    updateChart();
  } else {
    createChart();
  }
}, { deep: true });

onMounted(() => {
  createChart();
});

onUnmounted(() => {
  if (chart) {
    chart.destroy();
    chart = null;
  }
});
</script>
