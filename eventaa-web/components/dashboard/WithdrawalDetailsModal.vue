<template>
  <TransitionRoot as="template" :show="isOpen">
    <Dialog class="relative z-50" @close="emit('close')">
      <TransitionChild
        as="template"
        enter="ease-out duration-300"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="ease-in duration-200"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
      </TransitionChild>

      <div class="fixed inset-0 z-10 overflow-y-auto">
        <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <TransitionChild
            as="template"
            enter="ease-out duration-300"
            enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            enter-to="opacity-100 translate-y-0 sm:scale-100"
            leave="ease-in duration-200"
            leave-from="opacity-100 translate-y-0 sm:scale-100"
            leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
          >
            <DialogPanel class="relative transform overflow-hidden dashboard-bg-card dashboard-shadow transition-all sm:my-8 sm:w-full sm:max-w-2xl">
              <div class="px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                  <div class="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full sm:mx-0 sm:h-10 sm:w-10"
                       :class="statusIconClasses">
                    <Icon :icon="statusIcon" class="h-6 w-6" :class="statusIconColor" />
                  </div>
                  <div class="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left w-full">
                    <DialogTitle as="h3" class="text-lg font-medium leading-6 dashboard-text-primary">
                      Withdrawal Details
                    </DialogTitle>

                    <div class="mt-4">
                      <!-- Status Badge -->
                      <div class="mb-6">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
                              :class="statusBadgeClasses">
                          <span class="w-2 h-2 rounded-full mr-2" :class="statusDotClasses"></span>
                          {{ formatStatus(withdrawal.status) }}
                        </span>
                      </div>

                      <!-- Withdrawal Information -->
                      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                        <div class="dashboard-bg-hover p-4 rounded-lg">
                          <h4 class="text-sm font-medium dashboard-text-secondary mb-3">Amount Details</h4>
                          <div class="space-y-2">
                            <div class="flex justify-between">
                              <span class="text-sm dashboard-text-muted">Requested Amount:</span>
                              <span class="text-sm font-medium dashboard-text-primary">{{ formatCurrency(withdrawal.amount) }}</span>
                            </div>
                            <div class="flex justify-between">
                              <span class="text-sm dashboard-text-muted">Processing Fee:</span>
                              <span class="text-sm font-medium dashboard-text-primary">{{ formatCurrency(withdrawal.fee) }}</span>
                            </div>
                            <div class="border-t dashboard-border pt-2">
                              <div class="flex justify-between">
                                <span class="text-sm font-medium dashboard-text-secondary">Net Amount:</span>
                                <span class="text-sm font-bold dashboard-text-primary">{{ formatCurrency(withdrawal.net_amount) }}</span>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div class="dashboard-bg-hover p-4 rounded-lg">
                          <h4 class="text-sm font-medium dashboard-text-secondary mb-3">Request Information</h4>
                          <div class="space-y-2">
                            <div class="flex justify-between">
                              <span class="text-sm dashboard-text-muted">Reference:</span>
                              <span class="text-sm font-mono dashboard-text-primary">{{ withdrawal.reference }}</span>
                            </div>
                            <div class="flex justify-between">
                              <span class="text-sm dashboard-text-muted">Requested:</span>
                              <span class="text-sm dashboard-text-primary">{{ formatDate(withdrawal.created_at) }}</span>
                            </div>
                            <div v-if="withdrawal.processed_at" class="flex justify-between">
                              <span class="text-sm dashboard-text-muted">Processed:</span>
                              <span class="text-sm dashboard-text-primary">{{ formatDate(withdrawal.processed_at) }}</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Payment Method -->
                      <div class="dashboard-bg-hover p-4 rounded-lg mb-6">
                        <h4 class="text-sm font-medium dashboard-text-secondary mb-3">Payment Method</h4>
                        <div class="space-y-2">
                          <div class="flex justify-between">
                            <span class="text-sm dashboard-text-muted">Method:</span>
                            <span class="text-sm dashboard-text-primary capitalize">{{ withdrawal.payment_method.replace('_', ' ') }}</span>
                          </div>

                          <!-- Mobile Money Details -->
                          <template v-if="withdrawal.payment_method === 'mobile_money'">
                            <div class="flex justify-between">
                              <span class="text-sm dashboard-text-muted">Operator:</span>
                              <span class="text-sm dashboard-text-primary uppercase">{{ withdrawal.payment_details?.operator }}</span>
                            </div>
                            <div class="flex justify-between">
                              <span class="text-sm dashboard-text-muted">Phone Number:</span>
                              <span class="text-sm dashboard-text-primary">{{ withdrawal.payment_details?.phone_number }}</span>
                            </div>
                          </template>

                          <!-- Bank Transfer Details -->
                          <template v-if="withdrawal.payment_method === 'bank_transfer'">
                            <div class="flex justify-between">
                              <span class="text-sm dashboard-text-muted">Account Name:</span>
                              <span class="text-sm dashboard-text-primary">{{ withdrawal.payment_details?.account_name }}</span>
                            </div>
                            <div class="flex justify-between">
                              <span class="text-sm dashboard-text-muted">Account Number:</span>
                              <span class="text-sm dashboard-text-primary">{{ withdrawal.payment_details?.account_number }}</span>
                            </div>
                            <div class="flex justify-between">
                              <span class="text-sm dashboard-text-muted">Bank:</span>
                              <span class="text-sm dashboard-text-primary">{{ withdrawal.payment_details?.bank_name }}</span>
                            </div>
                            <div class="flex justify-between">
                              <span class="text-sm dashboard-text-muted">Branch:</span>
                              <span class="text-sm dashboard-text-primary">{{ withdrawal.payment_details?.branch }}</span>
                            </div>
                          </template>
                        </div>
                      </div>

                      <!-- Transaction Details -->
                      <div v-if="withdrawal.paychangu_reference || withdrawal.admin_notes" class="dashboard-bg-hover p-4 rounded-lg mb-6">
                        <h4 class="text-sm font-medium dashboard-text-secondary mb-3">Transaction Details</h4>
                        <div class="space-y-2">
                          <div v-if="withdrawal.paychangu_reference" class="flex justify-between">
                            <span class="text-sm dashboard-text-muted">PayChangu Reference:</span>
                            <span class="text-sm font-mono dashboard-text-primary">{{ withdrawal.paychangu_reference }}</span>
                          </div>
                          <div v-if="withdrawal.admin_notes" class="mt-3">
                            <span class="text-sm dashboard-text-muted block mb-1">Admin Notes:</span>
                            <p class="text-sm dashboard-text-primary">{{ withdrawal.admin_notes }}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="dashboard-bg-hover px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                <!-- Cancel Button (only for pending status) -->
                <button
                  v-if="withdrawal.status === 'pending' && !isCancelling"
                  type="button"
                  @click="cancelWithdrawal"
                  class="inline-flex w-full justify-center bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 sm:ml-3 sm:w-auto"
                >
                  Cancel Withdrawal
                </button>

                <button
                  v-if="withdrawal.status === 'pending' && isCancelling"
                  type="button"
                  disabled
                  class="inline-flex w-full justify-center bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm opacity-50 cursor-not-allowed sm:ml-3 sm:w-auto"
                >
                  <CoreLoader class="mr-2 h-4 w-4" />
                  Cancelling...
                </button>

                <button
                  type="button"
                  @click="emit('close')"
                  class="mt-3 inline-flex w-full justify-center dashboard-bg-card px-3 py-2 text-sm font-semibold dashboard-text-primary dashboard-border hover:dashboard-bg-hover sm:mt-0 sm:w-auto"
                >
                  Close
                </button>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue';
import { useHttpClient } from '@/composables/useHttpClient';
import { ENDPOINTS } from '@/utils/api';
import type { Withdrawal } from '@/types/api';

interface Props {
  isOpen: boolean;
  withdrawal: Withdrawal;
}

interface WithdrawalDetailsModalEmits {
  (e: 'close'): void;
  (e: 'cancelled', id: number): void;
}

const props = defineProps<Props>();
const emit = defineEmits<WithdrawalDetailsModalEmits>();

const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const isCancelling = ref<boolean>(false);

const statusIcon = computed(() => {
  switch (props.withdrawal.status) {
    case 'pending':
      return 'heroicons:clock';
    case 'processing':
      return 'line-md:downloading-loop';
    case 'completed':
      return 'heroicons:check-circle';
    case 'failed':
      return 'heroicons:x-circle';
    case 'cancelled':
      return 'heroicons:x-circle';
    default:
      return 'heroicons:question-mark-circle';
  }
});

const statusIconClasses = computed(() => {
  switch (props.withdrawal.status) {
    case 'pending':
      return 'bg-yellow-100 dark:bg-yellow-900/20';
    case 'processing':
      return 'bg-blue-100 dark:bg-blue-900/20';
    case 'completed':
      return 'bg-green-100 dark:bg-green-900/20';
    case 'failed':
    case 'cancelled':
      return 'bg-red-100 dark:bg-red-900/20';
    default:
      return 'bg-gray-100 dark:bg-gray-900/20';
  }
});

const statusIconColor = computed(() => {
  switch (props.withdrawal.status) {
    case 'pending':
      return 'text-yellow-600 dark:text-yellow-400';
    case 'processing':
      return 'text-blue-600 dark:text-blue-400';
    case 'completed':
      return 'text-green-600 dark:text-green-400';
    case 'failed':
    case 'cancelled':
      return 'text-red-600 dark:text-red-400';
    default:
      return 'text-gray-600 dark:text-gray-400';
  }
});

const statusBadgeClasses = computed(() => {
  switch (props.withdrawal.status) {
    case 'pending':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
    case 'processing':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
    case 'completed':
      return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
    case 'failed':
      return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
    case 'cancelled':
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
  }
});

const statusDotClasses = computed(() => {
  switch (props.withdrawal.status) {
    case 'pending':
      return 'bg-yellow-400';
    case 'processing':
      return 'bg-blue-400';
    case 'completed':
      return 'bg-green-400';
    case 'failed':
      return 'bg-red-400';
    case 'cancelled':
      return 'bg-gray-400';
    default:
      return 'bg-gray-400';
  }
});

const formatCurrency = (amount: number): string => {
  return `MK ${Number(amount).toLocaleString()}`;
};

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

const formatStatus = (status: string): string => {
  return status.charAt(0).toUpperCase() + status.slice(1);
};

const cancelWithdrawal = async (): Promise<void> => {
  if (props.withdrawal.status !== 'pending') return;

  try {
    isCancelling.value = true;

    await httpClient.patch(`${ENDPOINTS.WITHDRAWALS.CANCEL}/${props.withdrawal.id}/cancel`);

    emit('cancelled', props.withdrawal.id);
    $toast.success('Withdrawal cancelled successfully');

  } catch (error: any) {
    console.error('Error cancelling withdrawal:', error);
    $toast.error(error.response?.data?.message || 'Failed to cancel withdrawal');
  } finally {
    isCancelling.value = false;
  }
};
</script>
