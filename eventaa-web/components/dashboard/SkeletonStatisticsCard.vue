<template>
  <div class="bg-white p-4 shadow rounded-lg">
    <div class="flex items-center justify-between mb-4">
      <div class="h-6 w-24 bg-gray-200 rounded animate-pulse"></div>
      <div class="h-8 w-8 bg-gray-200 rounded-full animate-pulse"></div>
    </div>
    <div class="mb-2">
      <div class="h-8 w-32 bg-gray-200 rounded animate-pulse"></div>
    </div>
    <div class="mb-2">
      <div class="h-4 w-40 bg-gray-200 rounded animate-pulse"></div>
    </div>
    <div class="flex items-center">
      <div class="h-4 w-16 bg-gray-200 rounded animate-pulse"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
// No props or logic needed for this component
</script>

<style scoped>
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}
</style>
