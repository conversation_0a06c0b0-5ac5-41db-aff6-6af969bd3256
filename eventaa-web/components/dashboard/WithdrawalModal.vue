<template>
  <TransitionRoot as="template" :show="isOpen">
    <Dialog class="relative z-50" @close="emit('close')">
      <TransitionChild
        as="template"
        enter="ease-out duration-300"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="ease-in duration-200"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div
          class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
        />
      </TransitionChild>

      <div class="fixed inset-0 z-10 overflow-y-auto">
        <div
          class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0"
        >
          <TransitionChild
            as="template"
            enter="ease-out duration-300"
            enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            enter-to="opacity-100 translate-y-0 sm:scale-100"
            leave="ease-in duration-200"
            leave-from="opacity-100 translate-y-0 sm:scale-100"
            leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
          >
            <DialogPanel
              class="relative transform overflow-hidden dashboard-bg-card dashboard-shadow transition-all sm:my-8 sm:w-full sm:max-w-lg"
            >
              <form @submit.prevent="submitWithdrawal">
                <div class="px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <div class="sm:flex sm:items-start">
                    <div
                      class="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/20 sm:mx-0 sm:h-10 sm:w-10"
                    >
                      <Icon
                        icon="ph:hand-withdraw-duotone"
                        class="h-10 w-10 text-green-600 dark:text-green-400"
                      />
                    </div>
                    <div
                      class="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left w-full"
                    >
                      <DialogTitle
                        as="h3"
                        class="text-lg font-medium leading-6 dashboard-text-primary"
                      >
                        Request Withdrawal
                      </DialogTitle>

                      <div class="mt-4 space-y-4">
                        <div class="dashboard-bg-hover p-3">
                          <div class="flex justify-between items-center">
                            <span class="text-lg dashboard-text-secondary"
                              >Available Balance:</span
                            >
                            <span class="font-medium dashboard-text-primary"
                              ><strong>{{
                                formatCurrency(balance.available_balance)
                              }}</strong></span
                            >
                          </div>
                        </div>

                        <div class="formkit-container">
                          <FormKit
                            v-model="form.amount"
                            type="number"
                            name="amount"
                            label="Withdrawal Amount"
                            :min="minimumWithdrawal"
                            :max="balance.available_balance"
                            step="1"
                            validation="required|min:1"
                            prefix-icon="currency-dollar"
                            :help="`Minimum withdrawal: ${formatCurrency(
                              minimumWithdrawal
                            )}`"
                            placeholder="Enter amount"
                            outer-class="w-full"
                            input-class="w-full"
                          />
                        </div>

                        <!-- Fee Calculation -->
                        <div
                          v-if="
                            form.amount &&
                            parseFloat(form.amount) >= minimumWithdrawal
                          "
                          class="dashboard-bg-hover p-3 rounded-lg space-y-2"
                        >
                          <div class="flex justify-between text-sm">
                            <span class="dashboard-text-secondary"
                              >Withdrawal Amount:</span
                            >
                            <span class="dashboard-text-primary">{{
                              formatCurrency(parseFloat(form.amount))
                            }}</span>
                          </div>
                          <div class="flex justify-between text-sm">
                            <span class="dashboard-text-secondary"
                              >Processing Fee ({{ feePercentage }}%):</span
                            >
                            <span class="dashboard-text-primary">{{
                              formatCurrency(calculatedFee)
                            }}</span>
                          </div>
                          <div class="border-t dashboard-border pt-2">
                            <div
                              class="flex justify-between text-sm font-medium"
                            >
                              <span class="dashboard-text-primary"
                                >Net Amount:</span
                              >
                              <span class="dashboard-text-primary">{{
                                formatCurrency(netAmount)
                              }}</span>
                            </div>
                          </div>
                        </div>

                        <div class="space-y-4">
                          <h4
                            class="text-sm font-medium dashboard-text-secondary"
                          >
                            Choose Payment Method
                          </h4>

                          <div class="space-y-3">
                            <h5
                              class="text-sm font-medium dashboard-text-muted"
                            >
                              Mobile Money
                            </h5>

                            <div class="grid grid-cols-1 gap-3">
                              <label
                                v-for="operator in mobileOperators"
                                :key="operator.value"
                                class="relative flex items-center p-4 cursor-pointer hover:dashboard-bg-hover transition-colors"
                                :class="{
                                  'border-red-500 bg-red-50 dark:bg-red-900/10':
                                    form.payment_method === 'mobile_money' &&
                                    form.payment_details.operator ===
                                      operator.value,
                                  'border-gray-200 dark:border-gray-700':
                                    form.payment_method !== 'mobile_money' ||
                                    form.payment_details.operator !==
                                      operator.value,
                                }"
                              >
                                <input
                                  v-model="form.payment_method"
                                  type="radio"
                                  value="mobile_money"
                                  @change="
                                    form.payment_details.operator =
                                      operator.value
                                  "
                                  class="sr-only"
                                />
                                <div class="flex items-center space-x-3 flex-1">
                                  <div
                                    class="w-16 h-16 flex items-center justify-center"
                                  >
                                    <img
                                      v-if="getOperatorLogo(operator.name)"
                                      :src="getOperatorLogo(operator.name)"
                                      class="object-cover w-16 h-16"
                                      :alt="`${operator.name} logo`"
                                    />
                                    <Icon
                                      v-else
                                      icon="heroicons:device-phone-mobile"
                                      class="w-8 h-8 text-gray-400"
                                    />
                                  </div>
                                  <div>
                                    <div
                                      class="text-base font-medium dashboard-text-primary"
                                    >
                                      {{ operator.name }}
                                    </div>
                                    <div class="text-sm dashboard-text-muted">
                                      {{ operator.description }}
                                    </div>
                                  </div>
                                </div>
                                <div
                                  v-if="
                                    form.payment_method === 'mobile_money' &&
                                    form.payment_details.operator ===
                                      operator.value
                                  "
                                  class="w-5 h-5 text-red-600"
                                >
                                  <Icon
                                    icon="heroicons:check-circle-20-solid"
                                  />
                                </div>
                              </label>
                            </div>

                            <div
                              v-if="form.payment_method === 'mobile_money'"
                              class="mt-3"
                            >
                              <FormKit
                                v-model="form.payment_details.phone_number"
                                type="tel"
                                name="phone_number"
                                label="Phone Number"
                                validation="required"
                                prefix-icon="phone"
                                help="Enter your mobile money number e.g., ********** or +************"
                                placeholder="e.g., **********"
                                outer-class="w-full"
                                input-class="w-full"
                              />
                            </div>
                          </div>

                          <div class="space-y-3">
                            <h5
                              class="text-md font-medium dashboard-text-muted"
                            >
                              Bank Transfer
                            </h5>

                            <label
                              class="relative flex items-center p-4 cursor-pointer hover:dashboard-bg-hover transition-colors"
                              :class="{
                                'border-sky-500 bg-sky-50 dark:bg-sky-900/10':
                                  form.payment_method ===
                                  'bank_transfer',
                                'border-gray-200 dark:border-gray-700':
                                  form.payment_method !==
                                  'bank_transfer',
                              }"
                            >
                              <input
                                v-model="form.payment_method"
                                type="radio"
                                value="bank_transfer"
                                class="sr-only"
                              />
                              <div class="flex items-center space-x-3 flex-1">
                                <div
                                  class="w-12 h-12 flex ml-2 items-center justify-center bg-blue-100 dark:bg-blue-900/20 rounded-lg"
                                >
                                  <Icon
                                    icon="emojione-monotone:bank"
                                    class="w-6 h-6 text-blue-600 dark:text-blue-400"
                                  />
                                </div>
                                <div>
                                  <div
                                    class="text-base font-medium dashboard-text-primary"
                                  >
                                    Bank Transfer
                                  </div>
                                  <div class="text-sm dashboard-text-muted">
                                    Transfer to your bank account
                                  </div>
                                </div>
                              </div>
                              <div
                                v-if="
                                  form.payment_method === 'bank_transfer'
                                "
                                class="w-5 h-5 text-sky-600"
                              >
                                <Icon icon="heroicons:check-circle-20-solid" />
                              </div>
                            </label>

                            <div
                              v-if="form.payment_method === 'bank_transfer'"
                              class="space-y-3 mt-3"
                            >
                              <FormKit
                                v-model="form.payment_details.account_name"
                                type="text"
                                name="account_name"
                                label="Account Holder Name"
                                validation="required"
                                prefix-icon="user"
                                placeholder="Account holder name"
                                help="Name as it appears on your bank account"
                                outer-class="w-full"
                                input-class="w-full"
                              />

                              <FormKit
                                v-model="form.payment_details.account_number"
                                type="text"
                                name="account_number"
                                label="Account Number"
                                validation="required"
                                prefix-icon="hashtag"
                                placeholder="Your bank account number"
                                outer-class="w-full"
                                input-class="w-full"
                              />

                              <FormKit
                                v-model="form.payment_details.bank_name"
                                type="text"
                                name="bank_name"
                                label="Bank Name"
                                validation="required"
                                prefix-icon="building-library"
                                placeholder="e.g., Standard Bank, NBS Bank"
                                outer-class="w-full"
                                input-class="w-full"
                              />

                              <FormKit
                                v-model="form.payment_details.branch"
                                type="text"
                                name="branch"
                                label="Branch"
                                validation="required"
                                prefix-icon="map-pin"
                                placeholder="Branch name or code"
                                outer-class="w-full"
                                input-class="w-full"
                              />
                            </div>
                          </div>


                        </div>

                        <!-- reCAPTCHA verification -->
                        <div class="mt-4">
                          <div class="flex flex-col items-center justify-center space-y-2">
                            <div class="text-sm dashboard-text-muted text-center">
                              This action is protected by reCAPTCHA
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div
                  class="dashboard-bg-hover px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6"
                >
                  <CoreSubmitButton :disabled="!isFormValid || isSubmitting" :loading="isSubmitting" text="Request Withdrawal"/>
                  <button
                    type="button"
                    @click="emit('close')"
                    :disabled="isSubmitting"
                    class="mt-3 inline-flex w-full justify-center dashboard-bg-card px-3 py-2 text-sm font-semibold dashboard-text-primary dashboard-border hover:dashboard-bg-hover sm:mt-0 sm:w-auto disabled:opacity-50"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import {
  ref,
  reactive,
  computed,
  watch,
  onMounted,
  onUnmounted,
  nextTick,
} from "vue";
import {
  Dialog,
  DialogPanel,
  DialogTitle,
  TransitionChild,
  TransitionRoot,
} from "@headlessui/vue";
import { useReCaptcha } from 'vue-recaptcha-v3';
import { useHttpClient } from "@/composables/useHttpClient";
import { ENDPOINTS } from "@/utils/api";
import { handleErrorWithToast } from "@/utils/errors";
import type { WithdrawalRequest, ApiResponse } from "@/types/api";

interface Props {
  isOpen: boolean;
  balance: {
    available_balance: number;
  };
  minimumWithdrawal: number;
  feePercentage: number;
}

interface MobileOperator {
  value: string;
  name: string;
  description: string;
  logo: string;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  close: [];
  success: [];
}>();

const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();
const recaptchaInstance = useReCaptcha();

const isSubmitting = ref<boolean>(false);

const mobileOperators: MobileOperator[] = [
  {
    value: "tnm",
    name: "TNM Mpamba",
    description: "Pay with your TNM Mpamba wallet",
    logo: "/assets/images/tnm.jpg",
  },
  {
    value: "airtel",
    name: "Airtel Money",
    description: "Pay with your Airtel Money wallet",
    logo: "/assets/images/airtel.png",
  },
];

const getOperatorLogo = (operatorName: string): string | undefined => {
  const name = operatorName.toLowerCase();
  if (name.includes("airtel")) {
    return "/assets/images/airtel.png";
  } else if (name.includes("mpamba") || name.includes("tnm")) {
    return "/assets/images/tnm.jpg";
  }
  return undefined;
};

const form = reactive<{
  amount: string;
  payment_method: string;
  payment_details: {
    // Mobile Money fields
    phone_number: string;
    operator: string;
    // Bank Transfer fields
    account_name: string;
    account_number: string;
    bank_name: string;
    branch: string;
  };
}>({
  amount: "",
  payment_method: "",
  payment_details: {
    // Mobile Money fields
    phone_number: "",
    operator: "",
    // Bank Transfer fields
    account_name: "",
    account_number: "",
    bank_name: "",
    branch: "",
  },
});

const calculatedFee = computed<number>(() => {
  const amount = parseFloat(form.amount);
  if (!amount) return 0;
  return (amount * props.feePercentage) / 100;
});

const netAmount = computed<number>(() => {
  const amount = parseFloat(form.amount);
  if (!amount) return 0;
  return amount - calculatedFee.value;
});

const isFormValid = computed<boolean>(() => {
  const amount = parseFloat(form.amount);
  if (
    !amount ||
    amount < props.minimumWithdrawal ||
    amount > props.balance.available_balance
  ) {
    return false;
  }

  if (!form.payment_method) {
    return false;
  }

  if (form.payment_method === "mobile_money") {
    return !!(
      form.payment_details.phone_number && form.payment_details.operator
    );
  }

  if (form.payment_method === "bank_transfer") {
    return !!(
      form.payment_details.account_name &&
      form.payment_details.account_number &&
      form.payment_details.bank_name &&
      form.payment_details.branch
    );
  }

  return false;
});

const formatCurrency = (amount: number): string => {
  return `MK ${Number(amount).toLocaleString()}`;
};

const resetForm = (): void => {
  form.amount = "";
  form.payment_method = "";
  form.payment_details = {
    phone_number: "",
    operator: "",
    account_name: "",
    account_number: "",
    bank_name: "",
    branch: "",
  };
};

const submitWithdrawal = async (): Promise<void> => {
  if (!isFormValid.value) {
    return;
  }

  try {
    isSubmitting.value = true;

    if (!recaptchaInstance) {
      throw new Error('reCAPTCHA not available');
    }

    await recaptchaInstance.recaptchaLoaded();
    const recaptchaToken = await recaptchaInstance.executeRecaptcha('withdrawal_request');

    let paymentDetails: any = {};

    if (form.payment_method === "mobile_money") {
      paymentDetails = {
        phone_number: form.payment_details.phone_number,
        operator: form.payment_details.operator,
      };
    } else if (form.payment_method === "bank_transfer") {
      paymentDetails = {
        account_name: form.payment_details.account_name,
        account_number: form.payment_details.account_number,
        bank_name: form.payment_details.bank_name,
        branch: form.payment_details.branch,
      };
    }

    const payload: WithdrawalRequest = {
      amount: parseFloat(form.amount),
      payment_method: form.payment_method,
      payment_details: paymentDetails,
      recaptcha_token: recaptchaToken,
    };

    await httpClient.post<ApiResponse<any>>(
      ENDPOINTS.WITHDRAWALS.CREATE,
      payload
    );

    resetForm();
    emit("success");
    $toast.success("Withdrawal request submitted successfully");
  } catch (error: any) {
    console.error("Error submitting withdrawal:", error);
    handleErrorWithToast(error, $toast);
  } finally {
    isSubmitting.value = false;
  }
};

watch(
  () => props.isOpen,
  async (newValue: boolean) => {
    if (!newValue) {
      resetForm();
    }
  }
);
</script>

<style scoped>
.formkit-container {
  width: 100%;
}

.formkit-container :deep(.formkit-outer) {
  width: 100%;
  margin-left: 0 !important;
  padding-left: 0 !important;
}

.formkit-container :deep(.formkit-wrapper) {
  width: 100%;
  margin-left: 0 !important;
  padding-left: 0 !important;
}

.formkit-container :deep(.formkit-inner) {
  width: 100%;
  margin-left: 0 !important;
  padding-left: 0 !important;
}

.formkit-container :deep(.formkit-input) {
  width: 100%;
  margin-left: 0 !important;
  padding-left: 0.75rem !important;
}

.formkit-container :deep(.formkit-label) {
  margin-left: 0 !important;
  padding-left: 0 !important;
}

.formkit-container :deep(.formkit-help) {
  margin-left: 0 !important;
  padding-left: 0 !important;
}

.formkit-container :deep(.formkit-messages) {
  margin-left: 0 !important;
  padding-left: 0 !important;
}
</style>
