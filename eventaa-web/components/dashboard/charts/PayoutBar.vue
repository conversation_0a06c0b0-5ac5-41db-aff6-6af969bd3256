<template>
  <div class="relative">
    <div class="flex justify-between items-center mb-4">
      <h2 class="font-semibold">Payouts</h2>
      <div class="text-gray-500">
        <span class="text-xl">→</span>
      </div>
    </div>
    <div class="h-48" ref="chartContainer"></div>
    <div class="flex justify-between mt-4 text-lg border-t pt-4">
      <div>
        <span class="flex items-center">
          <span class="w-2 h-2 rounded-full bg-red-600 mr-2"></span>
          Paid
        </span>
        <span class="font-semibold">${{ paidAmount }}</span>
      </div>
      <div>
        <span class="flex items-center">
          <span class="w-2 h-2 rounded-full bg-pink-300 mr-2"></span>
          Remaining
        </span>
        <span class="font-semibold">${{ remainingAmount }}</span>
      </div>
      <div>
        <span class="flex items-center">
          <span class="w-2 h-2 rounded-full bg-gray-300 mr-2"></span>
          Adjustments
        </span>
        <span class="font-semibold">${{ adjustmentsAmount }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import * as d3 from 'd3';

const chartContainer = ref(null);
const paidAmount = ref('55.00');
const remainingAmount = ref('920.50');
const adjustmentsAmount = ref('0.00');

onMounted(() => {
  if (chartContainer.value) {
    const width = chartContainer.value.clientWidth;
    const height = chartContainer.value.clientHeight;
    const margin = { top: 20, right: 20, bottom: 30, left: 20 };
    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    // Create SVG
    const svg = d3.select(chartContainer.value)
      .append("svg")
      .attr("width", width)
      .attr("height", height)
      .append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    // Sample data for the bar chart
    const data = [
      { month: 1, paid: 20, remaining: 5, adjustments: 0 },
      { month: 2, paid: 28, remaining: 8, adjustments: 3 },
      { month: 3, paid: 22, remaining: 5, adjustments: 2 },
      { month: 4, paid: 30, remaining: 10, adjustments: 5 },
      { month: 5, paid: 45, remaining: 15, adjustments: 10 },
      { month: 6, paid: 28, remaining: 10, adjustments: 5 },
      { month: 7, paid: 22, remaining: 5, adjustments: 2 },
      { month: 8, paid: 28, remaining: 8, adjustments: 3 },
      { month: 9, paid: 26, remaining: 5, adjustments: 2 },
      { month: 10, paid: 28, remaining: 8, adjustments: 3 },
    ];

    // Scales
    const x = d3.scaleBand()
      .domain(data.map(d => d.month))
      .range([0, innerWidth])
      .padding(0.3);

    const y = d3.scaleLinear()
      .domain([0, 70])
      .range([innerHeight, 0]);

    // Draw bars
    data.forEach(d => {
      // Background pattern for adjustments
      if (d.adjustments > 0) {
        svg.append("rect")
          .attr("x", x(d.month))
          .attr("y", y(d.paid + d.remaining + d.adjustments))
          .attr("width", x.bandwidth())
          .attr("height", innerHeight - y(d.paid + d.remaining + d.adjustments))
          .attr("fill", "url(#diagonalHatch)");
      }

      // Remaining bars (light pink)
      svg.append("rect")
        .attr("x", x(d.month))
        .attr("y", y(d.paid + d.remaining))
        .attr("width", x.bandwidth())
        .attr("height", innerHeight - y(d.remaining))
        .attr("fill", "#f8bdc7");

      // Paid bars (dark red)
      svg.append("rect")
        .attr("x", x(d.month))
        .attr("y", y(d.paid))
        .attr("width", x.bandwidth())
        .attr("height", innerHeight - y(d.paid))
        .attr("fill", "#e63946");
    });

    // Create diagonal pattern for adjustments
    const defs = svg.append("defs");
    const pattern = defs.append("pattern")
      .attr("id", "diagonalHatch")
      .attr("patternUnits", "userSpaceOnUse")
      .attr("width", 8)
      .attr("height", 8);

    pattern.append("path")
      .attr("d", "M-2,2 l4,-4 M0,8 l8,-8 M6,10 l4,-4")
      .attr("stroke", "#e5e5e5")
      .attr("stroke-width", 1);
  }
});
</script>