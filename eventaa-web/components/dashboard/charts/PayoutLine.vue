<template>
  <div class="relative">
    <div class="flex justify-between items-center mb-4">
      <h2 class="font-semibold">Available for payout</h2>
      <button class="bg-pink-100 text-pink-500 px-4 py-2 rounded-md text-sm">Get paid instantly</button>
    </div>
    <div class="h-48" ref="chartContainer"></div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import * as d3 from 'd3';

const chartContainer = ref(null);

onMounted(() => {
  if (chartContainer.value) {
    const width = chartContainer.value.clientWidth;
    const height = chartContainer.value.clientHeight;
    const margin = { top: 20, right: 20, bottom: 30, left: 50 };
    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    // Create SVG
    const svg = d3.select(chartContainer.value)
      .append("svg")
      .attr("width", width)
      .attr("height", height)
      .append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    // Sample data
    const blueLineData = [
      { date: '01', value: 30000 },
      { date: '02', value: 38000 },
      { date: '03', value: 30000 },
      { date: '04', value: 38000 },
      { date: '05', value: 35000 },
      { date: '06', value: 32000 },
      { date: '07', value: 35000 },
    ];

    const redLineData = [
      { date: '01', value: 10000 },
      { date: '02', value: 20000 },
      { date: '03', value: 18000 },
      { date: '04', value: 30000 },
      { date: '05', value: 35000 },
      { date: '06', value: 45000 },
      { date: '07', value: 38000 },
    ];

    // Scales
    const x = d3.scalePoint()
      .domain(blueLineData.map(d => d.date))
      .range([0, innerWidth]);

    const y = d3.scaleLinear()
      .domain([0, 60000])
      .range([innerHeight, 0]);

    // Y-axis labels
    const yLabels = ['$0K', '$10K', '$20K', '$30K', '$40K', '$50K', '$60K'];
    svg.selectAll(".y-label")
      .data(yLabels)
      .enter()
      .append("text")
      .attr("class", "y-label")
      .attr("x", -45)
      .attr("y", (d, i) => y(i * 10000))
      .attr("text-anchor", "start")
      .attr("font-size", "12px")
      .attr("fill", "#666")
      .text(d => d);

    // X-axis labels
    svg.selectAll(".x-label")
      .data(blueLineData)
      .enter()
      .append("text")
      .attr("class", "x-label")
      .attr("x", d => x(d.date))
      .attr("y", innerHeight + 20)
      .attr("text-anchor", "middle")
      .attr("font-size", "12px")
      .attr("fill", "#666")
      .text(d => d.date);

    // Line generator
    const line = d3.line()
      .x(d => x(d.date))
      .y(d => y(d.value))
      .curve(d3.curveMonotoneX);

    // Add blue line
    svg.append("path")
      .datum(blueLineData)
      .attr("fill", "none")
      .attr("stroke", "#4cc9f0")
      .attr("stroke-width", 2)
      .attr("d", line);

    // Add red line and area
    svg.append("path")
      .datum(redLineData)
      .attr("fill", "none")
      .attr("stroke", "#e63946")
      .attr("stroke-width", 2)
      .attr("d", line);

    // Add area fill under red line
    const area = d3.area()
      .x(d => x(d.date))
      .y0(innerHeight)
      .y1(d => y(d.value))
      .curve(d3.curveMonotoneX);

    svg.append("path")
      .datum(redLineData)
      .attr("fill", "#f8bdc7")
      .attr("opacity", 0.6)
      .attr("d", area);

    // Add vertical dotted line at date '05'
    svg.append("line")
      .attr("x1", x('05'))
      .attr("y1", 0)
      .attr("x2", x('05'))
      .attr("y2", innerHeight)
      .attr("stroke", "#e63946")
      .attr("stroke-dasharray", "4")
      .attr("stroke-width", 1);

    // Add dot at intersection
    svg.append("circle")
      .attr("cx", x('05'))
      .attr("cy", y(35000))
      .attr("r", 4)
      .attr("fill", "#e63946");
  }
});
</script>
