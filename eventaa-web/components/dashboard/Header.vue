<template>
  <header
    class="dashboard-bg-header flex items-center justify-between px-4 py-3 dashboard-border border-b"
  >
    <div class="flex items-center space-x-4">
      <button
        class="focus:outline-none lg:hidden dashboard-text-secondary dashboard-transition"
        @click="toggleSidebar"
      >
        <svg
          class="w-6 h-6"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M4 6H20M4 12H20M4 18H11"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>

      <button
        class="focus:outline-none hidden lg:block dashboard-text-secondary dashboard-transition"
        @click="toggleMiniVariant"
      >
        <svg
          class="w-6 h-6"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M4 6H20M4 12H20M4 18H11"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>

      <div class="relative mx-4 lg:mx-0">
        <span class="absolute inset-y-0 left-0 flex items-center pl-3">
          <Icon
            icon="heroicons:magnifying-glass"
            class="w-5 h-5 dashboard-text-muted"
          />
        </span>

        <input
          v-model="searchQuery"
          @input="handleSearch"
          @focus="showSearchResults = true"
          class="w-32 dashboard-bg-input dashboard-text-secondary pl-10 py-1.5 pr-4 dashboard-border rounded sm:w-64 dashboard-input"
          type="text"
          :placeholder="getSearchPlaceholder()"
        />

        <div
          v-if="
            showSearchResults &&
            (searchResults.events.length > 0 ||
              searchResults.users.length > 0 ||
              searchQuery.length > 0)
          "
          class="absolute top-full left-0 right-0 mt-1 dashboard-bg-card dashboard-shadow border dashboard-border z-50 max-h-96 overflow-y-auto"
        >
          <div
            v-if="searchLoading"
            class="p-4 flex items-center justify-center text-center"
          >
            <CoreLoader :width="16" :height="16" />
            <p class="text-sm dashboard-text-muted mt-2">Searching...</p>
          </div>

          <div
            v-else-if="
              searchQuery.length > 0 &&
              searchResults.events.length === 0 &&
              (searchResults.users.length === 0 || !hasAdminRole)
            "
            class="p-4 text-center"
          >
            <Icon
              icon="heroicons:magnifying-glass"
              class="w-8 h-8 dashboard-text-muted mx-auto mb-2"
            />
            <p class="text-sm dashboard-text-muted">
              No results found for "{{ searchQuery }}"
            </p>
            <p
              v-if="!hasAdminRole && !hasHostRole"
              class="text-xs dashboard-text-muted mt-1"
            >
              Searching public events only
            </p>
          </div>

          <!-- Events Results -->
          <div v-if="searchResults.events.length > 0">
            <div class="px-3 py-2 dashboard-border-light border-b">
              <h3
                class="text-xs font-semibold dashboard-text-muted uppercase tracking-wider"
              >
                Events
              </h3>
            </div>
            <NuxtLink
              v-for="event in searchResults.events.slice(0, 3)"
              :key="event.id"
              :to="getEventLink(event)"
              @click="closeSearch"
              class="flex items-center px-3 py-2 hover:dashboard-bg-hover dashboard-transition"
            >
              <Icon
                icon="heroicons:calendar"
                class="w-4 h-4 dashboard-text-muted mr-3"
              />
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium dashboard-text-primary truncate">
                  {{ event.title }}
                </p>
                <p class="text-xs dashboard-text-muted">
                  {{ formatDate(event.start) }}
                </p>
              </div>
            </NuxtLink>
          </div>

          <!-- Users Results (Admin only) -->
          <div v-if="hasAdminRole && searchResults.users.length > 0">
            <div class="px-3 py-2 dashboard-border-light border-b">
              <h3
                class="text-xs font-semibold dashboard-text-muted uppercase tracking-wider"
              >
                Users
              </h3>
            </div>
            <NuxtLink
              v-for="user in searchResults.users.slice(0, 3)"
              :key="user.id"
              :to="`/dashboard/users`"
              @click="closeSearch"
              class="flex items-center px-3 py-2 hover:dashboard-bg-hover dashboard-transition"
            >
              <Icon
                icon="heroicons:user"
                class="w-4 h-4 dashboard-text-muted mr-3"
              />
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium dashboard-text-primary truncate">
                  {{ user.name }}
                </p>
                <p class="text-xs dashboard-text-muted">{{ user.email }}</p>
              </div>
            </NuxtLink>
          </div>

          <!-- View All Results -->
          <div
            v-if="searchQuery.length > 0"
            class="px-3 py-2 dashboard-border-light border-t"
          >
            <button
              @click="viewAllResults"
              class="w-full text-left text-sm dashboard-text-brand hover:dashboard-text-primary dashboard-transition"
            >
              View all results for "{{ searchQuery }}"
            </button>
          </div>
        </div>

        <!-- Click outside to close -->
        <div
          v-if="showSearchResults"
          @click="closeSearch"
          class="fixed inset-0 z-40"
        ></div>
      </div>
    </div>

    <div class="flex items-center space-x-4">
      <!-- Dark Mode Toggle -->
      <div class="ml-4 flow-root lg:ml-6">
        <button
          class="group -m-2 flex items-center p-2"
          @click="toggleColorMode"
        >
          <Icon
            v-if="colorMode.value === 'dark'"
            icon="line-md:sunny-filled-loop"
            class="h-6 w-6 flex-shrink-0 text-yellow-400 group-hover:text-yellow-300"
            aria-hidden="true"
          />
          <Icon
            v-else
            icon="line-md:moon-filled"
            class="h-6 w-6 flex-shrink-0 text-gray-500 group-hover:text-gray-600"
            aria-hidden="true"
          />
        </button>
      </div>

      <!-- Vendor Requests (Admin Only) -->
      <div v-if="hasAdminRole" class="relative">
        <button
          @click="vendorRequestsOpen = !vendorRequestsOpen"
          class="relative p-2 dashboard-text-secondary hover:dashboard-text-primary dashboard-transition"
        >
          <Icon
            icon="fluent-mdl2:feedback-request-solid"
            class="w-6 h-6 text-gray-500 group-hover:text-gray-600 dark:text-zinc-300"
          />
          <span
            v-if="pendingVendorRequests > 0"
            class="absolute -top-1 -right-1 bg-red-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center"
          >
            {{ pendingVendorRequests }}
          </span>
        </button>

        <!-- Vendor Requests Dropdown -->
        <div
          v-if="vendorRequestsOpen"
          class="absolute right-0 mt-2 w-80 dashboard-bg-card dashboard-shadow rounded border dashboard-border z-50"
        >
          <div class="px-4 py-3 dashboard-border-light border-b">
            <h3 class="text-sm font-semibold dashboard-text-primary">
              Vendor Requests
            </h3>
            <p class="text-xs dashboard-text-muted">
              {{ pendingVendorRequests }} pending approval
            </p>
          </div>

          <div class="max-h-64 overflow-y-auto">
            <div v-if="loadingVendorRequests" class="p-4 text-center">
              <Icon
                icon="heroicons:arrow-path"
                class="w-5 h-5 animate-spin dashboard-text-muted mx-auto"
              />
            </div>

            <div
              v-else-if="vendorRequests.length === 0"
              class="p-4 text-center"
            >
              <Icon
                icon="heroicons:check-circle"
                class="w-8 h-8 dashboard-text-muted mx-auto mb-2"
              />
              <p class="text-sm dashboard-text-muted">No pending requests</p>
            </div>

            <div v-else>
              <div
                v-for="request in vendorRequests.slice(0, 5)"
                :key="request.id"
                class="px-4 py-3 dashboard-border-light border-b hover:dashboard-bg-hover"
              >
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3">
                    <img
                      :src="`${runtimeConfig.public.baseUrl}storage/avatars/${request.user?.avatar}`"
                      class="w-8 h-8 rounded-full"
                      :alt="request.name.toLowerCase().replaceAll(' ', '-')"
                    />
                    <div>
                      <p class="text-sm font-medium dashboard-text-primary">
                        {{ request.name }}
                      </p>
                      <p class="text-xs dashboard-text-muted">
                        {{ request.user?.name }}
                      </p>
                    </div>
                  </div>
                  <div class="flex space-x-1">
                    <button
                      @click="approveVendorRequest(request.id)"
                      class="p-1 text-green-600 hover:text-green-700"
                    >
                      <Icon icon="heroicons:check" class="w-4 h-4" />
                    </button>
                    <button
                      @click="rejectVendorRequest(request.id)"
                      class="p-1 text-red-600 hover:text-red-700"
                    >
                      <Icon icon="heroicons:x-mark" class="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="px-4 py-3 dashboard-border-light border-t">
            <NuxtLink
              to="/dashboard/vendor-requests"
              @click="vendorRequestsOpen = false"
              class="text-sm dashboard-text-brand hover:dashboard-text-primary dashboard-transition"
            >
              View all requests
            </NuxtLink>
          </div>
        </div>

        <!-- Click outside to close -->
        <div
          v-if="vendorRequestsOpen"
          @click="vendorRequestsOpen = false"
          class="fixed inset-0 z-40"
        ></div>
      </div>

      <!-- Messages Popover -->
      <div class="relative">
        <button
          @click="messagesOpen = !messagesOpen"
          class="relative p-2 dashboard-text-secondary hover:dashboard-text-primary dashboard-transition"
        >
          <Icon icon="duo-icons:message-2" class="w-6 h-6" />
          <span
            v-if="unreadMessagesCount > 0"
            class="absolute -top-1 -right-1 bg-red-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center"
          >
            {{ unreadMessagesCount }}
          </span>
        </button>

        <!-- Messages Dropdown -->
        <div
          v-if="messagesOpen"
          class="absolute right-0 mt-2 w-80 dashboard-bg-card dashboard-shadow rounded border dashboard-border z-50"
        >
          <div class="px-4 py-3 dashboard-border-light border-b">
            <h3 class="text-sm font-semibold dashboard-text-primary">
              Recent Messages
            </h3>
            <p class="text-xs dashboard-text-muted">
              {{ unreadMessagesCount }} unread
            </p>
          </div>

          <div class="max-h-64 overflow-y-auto">
            <div v-if="loadingMessages" class="p-4 text-center">
              <Icon
                icon="heroicons:arrow-path"
                class="w-5 h-5 animate-spin dashboard-text-muted mx-auto"
              />
            </div>

            <div
              v-else-if="recentMessages.length === 0"
              class="p-4 text-center"
            >
              <Icon
                icon="heroicons:chat-bubble-left-right"
                class="w-8 h-8 dashboard-text-muted mx-auto mb-2"
              />
              <p class="text-sm dashboard-text-muted">No messages yet</p>
            </div>

            <div v-else>
              <NuxtLink
                v-for="message in recentMessages.slice(0, 5)"
                :key="message.id"
                :to="`/dashboard/messages?conversation=${message.conversationId}`"
                @click="messagesOpen = false"
                class="block px-4 py-3 dashboard-border-light border-b hover:dashboard-bg-hover"
                :class="{ 'dashboard-bg-primary-light': !message.read }"
              >
                <div class="flex items-center space-x-3">
                  <div class="relative">
                    <img
                      :src="message.avatar"
                      class="w-8 h-8 rounded-full"
                      :alt="message.sender"
                    />
                    <div
                      v-if="!message.read"
                      class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"
                    ></div>
                  </div>
                  <div class="flex-1 min-w-0">
                    <div class="flex justify-between items-start">
                      <p
                        class="text-sm font-medium dashboard-text-primary truncate"
                      >
                        {{ message.sender }}
                      </p>
                      <span class="text-xs dashboard-text-muted">{{
                        message.time
                      }}</span>
                    </div>
                    <p class="text-sm dashboard-text-muted truncate">
                      {{ message.text }}
                    </p>
                  </div>
                </div>
              </NuxtLink>
            </div>
          </div>

          <div class="px-4 py-3 dashboard-border-light border-t">
            <NuxtLink
              to="/dashboard/messages"
              @click="messagesOpen = false"
              class="text-sm dashboard-text-brand hover:dashboard-text-primary dashboard-transition"
            >
              View all messages
            </NuxtLink>
          </div>
        </div>

        <!-- Click outside to close -->
        <div
          v-if="messagesOpen"
          @click="messagesOpen = false"
          class="fixed inset-0 z-40"
        ></div>
      </div>

      <UserNotificationDrawer />

      <div class="relative" v-if="isAuthenticated">
        <div class="flex items-center">
          <button
            class="relative z-10 block w-8 h-8 overflow-hidden rounded-full shadow focus:outline-none mr-3"
            @click="dropdownOpen = !dropdownOpen"
          >
            <img
              class="object-cover w-full h-full"
              :src="`${runtimeConfig.public.baseUrl}storage/avatars/${user?.avatar}`"
              :alt="user?.name.toLowerCase().replaceAll(' ', '-')"
            />
          </button>

          <div class="hidden md:block text-left">
            <div class="text-sm font-medium dashboard-text-primary">
              {{ user?.name }}
            </div>
            <div class="text-xs dashboard-text-muted">
              {{ userRoleDisplay }}
            </div>
          </div>
        </div>

        <div
          v-show="dropdownOpen"
          class="fixed inset-0 z-10 w-full h-full"
          @click="dropdownOpen = false"
        />

        <transition
          enter-active-class="transition duration-150 ease-out transform"
          enter-from-class="scale-95 opacity-0"
          enter-to-class="scale-100 opacity-100"
          leave-active-class="transition duration-150 ease-in transform"
          leave-from-class="scale-100 opacity-100"
          leave-to-class="scale-95 opacity-0"
        >
          <div
            v-show="dropdownOpen"
            class="absolute right-0 z-20 w-48 py-2 mt-2 dashboard-bg-card dashboard-shadow rounded"
          >
            <div class="px-4 py-2 dashboard-border-light border-b">
              <div class="text-sm font-medium dashboard-text-primary">
                {{ user?.name }}
              </div>
              <div class="text-xs dashboard-text-muted">{{ user?.email }}</div>
            </div>
            <nuxt-link
              to="/dashboard/profile"
              class="block px-4 py-2 dashboard-text-secondary hover:bg-red-600 hover:text-white dashboard-transition"
            >
              <div class="flex items-center">
                <Icon icon="heroicons:user" class="w-4 h-4 mr-2" />
                Profile
              </div>
            </nuxt-link>
            <nuxt-link
              to="/dashboard/settings"
              class="block px-4 py-2 dashboard-text-secondary hover:bg-red-600 hover:text-white dashboard-transition"
            >
              <div class="flex items-center">
                <Icon icon="heroicons:cog-6-tooth" class="w-4 h-4 mr-2" />
                Settings
              </div>
            </nuxt-link>
            <nuxt-link
              to="/"
              class="block px-4 py-2 dashboard-text-secondary hover:bg-red-600 hover:text-white dashboard-transition"
            >
              <div class="flex items-center">
                <Icon icon="ri:globe-line" class="w-4 h-4 mr-2" />
                Visit site
              </div>
            </nuxt-link>
            <button
              @click="authStore.logout()"
              class="w-full text-left block px-4 py-2 dashboard-text-secondary hover:bg-red-600 hover:text-white dashboard-transition"
            >
              <div class="flex items-center">
                <Icon
                  icon="heroicons:arrow-right-on-rectangle"
                  class="w-4 h-4 mr-2"
                />
                Log out
              </div>
            </button>
          </div>
        </transition>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import { useAuthStore } from "@/store/auth";
import { ENDPOINTS } from "@/utils/api";

interface SearchEvent {
  id: number;
  title: string;
  start: string;
  end?: string;
  published_at?: string;
}

interface SearchUser {
  id: number;
  name: string;
  email: string;
  avatar?: string;
  role?: string;
}

interface VendorRequest {
  id: number;
  name: string;
  status: string;
  user?: {
    id: number;
    name: string;
    avatar?: string;
  };
}

interface RecentMessage {
  id: number;
  conversationId: number;
  sender: string;
  avatar: string;
  text: string;
  time: string;
  read: boolean;
}

interface SearchResults {
  events: SearchEvent[];
  users: SearchUser[];
}

const emit = defineEmits<{
  "toggle-dark-mode": [];
}>();

const dropdownOpen = ref(false);
const runtimeConfig = useRuntimeConfig();
const { isOpen, isMiniVariant } = useSidebar();
const authStore = useAuthStore();
const { user, isAuthenticated } = authStore;
const colorMode = useColorMode();
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const searchQuery = ref("");
const showSearchResults = ref(false);
const searchLoading = ref(false);
const searchResults = ref<SearchResults>({
  events: [],
  users: [],
});

const vendorRequestsOpen = ref(false);
const vendorRequests = ref<VendorRequest[]>([]);
const pendingVendorRequests = ref(0);
const loadingVendorRequests = ref(false);

const messagesOpen = ref(false);
const recentMessages = ref<RecentMessage[]>([]);
const unreadMessagesCount = ref(0);
const loadingMessages = ref(false);

const userRoles = computed(() => {
  return authStore.user?.roles || [];
});

const hasAdminRole = computed(() => {
  return userRoles.value.includes("admin");
});

const hasHostRole = computed(() => {
  return userRoles.value.includes("host");
});

const userRoleDisplay = computed(() => {
  if (hasAdminRole.value) return "Administrator";
  if (hasHostRole.value) return "Event Host";
  return "User";
});

const getSearchPlaceholder = () => {
  if (hasAdminRole.value) {
    return "Search events, users, content...";
  } else if (hasHostRole.value) {
    return "Search your events...";
  } else {
    return "Search events...";
  }
};

let searchTimeout: NodeJS.Timeout;

const handleSearch = () => {
  if (searchTimeout) clearTimeout(searchTimeout);

  if (searchQuery.value.trim().length === 0) {
    searchResults.value = { events: [], users: [] };
    return;
  }

  searchTimeout = setTimeout(async () => {
    await performSearch();
  }, 300);
};

const performSearch = async () => {
  if (searchQuery.value.trim().length < 2) return;

  searchLoading.value = true;

  try {
    const promises: Promise<any>[] = [];
    if (hasAdminRole.value || hasHostRole.value) {
      promises.push(
        httpClient
          .get(
            `${ENDPOINTS.EVENTS.SEARCH}?title=${encodeURIComponent(
              searchQuery.value
            )}&per_page=5`
          )
          .then((response: any) => response?.events?.data || [])
          .catch(() => [])
      );
    } else {
      promises.push(
        httpClient
          .get(
            `${ENDPOINTS.EVENTS.GET}?search=${encodeURIComponent(
              searchQuery.value
            )}&per_page=5`
          )
          .then((response: any) => response?.events?.data || [])
          .catch(() => [])
      );
    }

    if (hasAdminRole.value) {
      promises.push(
        httpClient
          .get(
            `${ENDPOINTS.USERS.GET}?search=${encodeURIComponent(
              searchQuery.value
            )}&limit=5`
          )
          .then((response: any) => response?.data?.users || [])
          .catch(() => [])
      );
    } else {
      promises.push(Promise.resolve([]));
    }

    const [events, users] = await Promise.all(promises);

    searchResults.value = {
      events: (events || []) as SearchEvent[],
      users: (users || []) as SearchUser[],
    };
  } catch (error) {
    console.error("Search error:", error);
    searchResults.value = { events: [], users: [] };
  } finally {
    searchLoading.value = false;
  }
};

const closeSearch = () => {
  showSearchResults.value = false;
  searchQuery.value = "";
  searchResults.value = { events: [], users: [] };
};

const viewAllResults = () => {
  navigateTo(
    `/dashboard/manage-events?search=${encodeURIComponent(searchQuery.value)}`
  );
  closeSearch();
};

const formatDate = (dateString: string) => {
  if (!dateString) return "";
  return new Date(dateString).toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
  });
};

const getEventLink = (event: SearchEvent) => {
  if (hasAdminRole.value || hasHostRole.value) {
    return `/dashboard/manage-events/${event.id}`;
  } else {
    return `/events/${event.id}`;
  }
};

const fetchVendorRequests = async () => {
  if (!hasAdminRole.value) return;

  loadingVendorRequests.value = true;
  try {
    const response: any = await httpClient.get(
      `${ENDPOINTS.VENDORS.BASE}/pending`
    );
    if (response && response.data) {
      vendorRequests.value = response.data as VendorRequest[];
      pendingVendorRequests.value = response.total || response.data.length;
    }
  } catch (error) {
    console.error("Error fetching vendor requests:", error);
    vendorRequests.value = [];
    pendingVendorRequests.value = 0;
  } finally {
    loadingVendorRequests.value = false;
  }
};

const approveVendorRequest = async (vendorId: number) => {
  try {
    await httpClient.post(`${ENDPOINTS.VENDORS.BASE}/approve/${vendorId}`);
    $toast.success("Vendor request approved successfully");
    await fetchVendorRequests();
  } catch (error) {
    console.error("Error approving vendor request:", error);
    $toast.error("Failed to approve vendor request");
  }
};

const rejectVendorRequest = async (vendorId: number) => {
  try {
    const reason = prompt("Please provide a reason for rejection:");
    if (!reason) return;

    await httpClient.post(`${ENDPOINTS.VENDORS.BASE}/reject/${vendorId}`, {
      rejection_reason: reason,
    });
    $toast.success("Vendor request rejected");
    await fetchVendorRequests();
  } catch (error) {
    console.error("Error rejecting vendor request:", error);
    $toast.error("Failed to reject vendor request");
  }
};

const fetchRecentMessages = async () => {
  loadingMessages.value = true;
  try {
    const response: any = await httpClient.get(
      `${ENDPOINTS.MESSAGES.GET_CONVERSATIONS}?limit=5`
    );

    if (response && response.data) {
      recentMessages.value = response.data.map(
        (conversation: any): RecentMessage => {
          const otherUser = conversation.vendor?.user || conversation.user;
          const latestMessage =
            conversation.latest_message || conversation.latestMessage;

          return {
            id: conversation.id,
            conversationId: conversation.id,
            sender: otherUser?.name || "Unknown",
            avatar: otherUser?.avatar
              ? `${runtimeConfig.public.baseUrl}storage/avatars/${otherUser.avatar}`
              : `https://ui-avatars.com/api/?name=${encodeURIComponent(
                  otherUser?.name || "User"
                )}`,
            text: latestMessage?.content || "No messages yet",
            time: latestMessage?.created_at
              ? formatTimeAgo(latestMessage.created_at)
              : "",
            read: conversation.unread_count === 0,
          };
        }
      );

      unreadMessagesCount.value = response.data.reduce(
        (sum: number, conv: any) => sum + (conv.unread_count || 0),
        0
      );
    }
  } catch (error) {
    console.error("Error fetching recent messages:", error);
    recentMessages.value = [];
    unreadMessagesCount.value = 0;
  } finally {
    loadingMessages.value = false;
  }
};

const formatTimeAgo = (dateString: string) => {
  const now = new Date();
  const date = new Date(dateString);
  const diffInMinutes = Math.floor(
    (now.getTime() - date.getTime()) / (1000 * 60)
  );

  if (diffInMinutes < 1) return "Just now";
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
  if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
  return `${Math.floor(diffInMinutes / 1440)}d ago`;
};

const toggleSidebar = () => {
  isOpen.value = !isOpen.value;
};

const toggleMiniVariant = () => {
  isMiniVariant.value = !isMiniVariant.value;
};

const toggleColorMode = () => {
  colorMode.preference = colorMode.value === "dark" ? "light" : "dark";
  emit("toggle-dark-mode");
};

watch(vendorRequestsOpen, (isOpen) => {
  if (isOpen && hasAdminRole.value) {
    fetchVendorRequests();
  }
});

watch(messagesOpen, (isOpen) => {
  if (isOpen) {
    fetchRecentMessages();
  }
});

onMounted(() => {
  if (hasAdminRole.value) {
    fetchVendorRequests();
  }
  fetchRecentMessages();
});
</script>
