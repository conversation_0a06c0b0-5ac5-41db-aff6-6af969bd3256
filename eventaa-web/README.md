# EventaHub Malawi Web App

A modern, responsive web application built with Nuxt.js 3, providing a seamless event management experience.

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- npm/yarn/pnpm/bun

### Getting Started

1. **Clone the Repository**
   ```bash
   git clone https://github.com/KellsWorks/eventaa-web.git
   cd eventaa-web
   ```

2. **Install Dependencies**
   ```bash
   # npm
   npm install

   # pnpm
   pnpm install

   # yarn
   yarn install

   # bun
   bun install
   ```

3. **Environment Setup**
   ```bash
   # Copy environment file
   cp .env.example .env
   # Configure your environment variables
   ```

4. **Start Development Server**
   ```bash
   # npm
   npm run dev

   # pnpm
   pnpm run dev

   # yarn
   yarn dev

   # bun
   bun run dev
   ```

5. **Access the Application**
   - Development: `http://localhost:3000`

## 🏃‍♂️ Development Server

Start the development server on `http://localhost:3000`:

```bash
# npm
npm run dev

# pnpm
pnpm run dev

# yarn
yarn dev

# bun
bun run dev
```

## 🏗 Build & Production

### Development Build
```bash
npm run build
```

### Production Deployment

Use the included deployment scripts:

```bash
# Frontend PM2 setup
chmod +x frontend-pm2-setup.sh
./frontend-pm2-setup.sh

# Management script
chmod +x manage-frontend.sh
./manage-frontend.sh start
```

### Manual Production Setup
```bash
# Build for production
npm run build

# Preview production build locally
npm run preview

# For static hosting (Netlify, Vercel, etc.)
npm run generate
# Deploy dist/ folder to your hosting provider
```

## 🔧 Configuration

### Environment Variables

Configure your environment in `.env` file:

```env
# API Configuration
API_BASE_URL=http://localhost:8080/api
NUXT_PUBLIC_API_URL=http://localhost:8080/api

# Application Settings
NUXT_HOST=0.0.0.0
NUXT_PORT=3000

# Features
NUXT_PUBLIC_APP_NAME="EventaHub Malawi"
NUXT_PUBLIC_APP_DESCRIPTION="Event management platform for Malawi"
```

### Nuxt Configuration

Key configurations in `nuxt.config.ts`:

- **SSR**: Disabled for SPA mode
- **Modules**: Tailwind CSS, FormKit, Pinia, Color Mode
- **Build**: Transpilation for oh-vue-icons
- **Assets**: Custom SCSS and CSS files

## 🎨 Tech Stack

### Frontend Framework
- **Nuxt.js 3** - Vue.js framework
- **Vue 3** - Composition API
- **TypeScript** - Type safety

### UI & Styling
- **Tailwind CSS** - Utility-first CSS framework
- **Headless UI** - Unstyled UI components
- **Heroicons** - SVG icons
- **Oh Vue Icons** - Icon library

### Forms & Data
- **FormKit** - Form building framework
- **Pinia** - State management
- **VueForm** - Advanced form components
- **Vue Quill** - Rich text editor

### Additional Features
- **Chart.js** - Data visualization
- **Vue Datepicker** - Date selection
- **Schedule-X** - Calendar components
- **CKEditor 5** - Advanced text editing

## 🏗 Development

### Project Structure
```
eventaa-web/
├── app/                    # Application pages and layouts
├── assets/                 # Static assets (SCSS, CSS)
├── components/             # Vue components
├── composables/            # Vue composables
├── layouts/                # Application layouts
├── middleware/             # Route middleware
├── pages/                  # File-based routing
├── plugins/                # Nuxt plugins
├── public/                 # Public static files
├── server/                 # Server-side code
├── services/               # API services
├── store/                  # Pinia stores
├── types/                  # TypeScript types
└── utils/                  # Utility functions
```

### Code Quality
```bash
# Lint code
npm run lint

# Format code
npm run format

# Type check
npm run type-check
```

### Testing
```bash
# Run tests
npm run test

# Run tests with coverage
npm run test:coverage
```

## 🔗 Integration

### API Integration

The web app integrates with the EventaHub API:

```typescript
// services/api.ts
const config = useRuntimeConfig()
const baseURL = config.public.apiUrl

// Example API call
const { data: events } = await $fetch('/events', {
  baseURL,
  headers: {
    'Authorization': `Bearer ${token}`
  }
})
```

### Full Stack Setup

To run both frontend and backend together:

```bash
# Start API server (Terminal 1)
cd eventaa-api
php artisan serve

# Start web app (Terminal 2)
cd eventaa-web
npm run dev

# This provides:
# - API at http://localhost:8000
# - Web at http://localhost:3000
```

## 🚀 Deployment

### Production Server Setup

Use the included deployment scripts:

```bash
# Setup PM2 and production environment
chmod +x frontend-pm2-setup.sh
./frontend-pm2-setup.sh

# Use management commands
./manage-frontend.sh deploy
./manage-frontend.sh start
```

### Static Generation

For static hosting (Netlify, Vercel, etc.):

```bash
# Generate static files
npm run generate

# Deploy dist/ folder to your hosting provider
```

## 🛠 Troubleshooting

### Common Issues

**Port Already in Use**
```bash
# Kill process on port 3000
sudo kill -9 $(sudo lsof -t -i:3000)

# Or use different port
NUXT_PORT=3001 npm run dev
```

**Module Resolution Issues**
```bash
# Clear Nuxt cache
rm -rf .nuxt node_modules/.cache

# Reinstall dependencies
npm ci
```

**API Connection Issues**
- Verify API is running on correct port
- Check API_BASE_URL in environment
- Ensure CORS is configured in API

## 📚 Documentation

### Component Usage

```vue
<template>
  <div>
    <!-- Using themed components -->
    <ThemedText type="title">Welcome to Eventaa</ThemedText>

    <!-- Using form components -->
    <FormKit type="text" name="eventName" label="Event Name" />

    <!-- Using navigation -->
    <HapticTab />
  </div>
</template>
```

### State Management

```typescript
// stores/events.ts
export const useEventsStore = defineStore('events', () => {
  const events = ref([])

  const fetchEvents = async () => {
    const { data } = await $fetch('/events')
    events.value = data
  }

  return { events, fetchEvents }
})
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Follow Vue.js style guide
- Use TypeScript for type safety
- Write meaningful commit messages
- Add tests for new features
- Update documentation

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support, email <EMAIL> or join our Slack channel.

## 📋 Changelog

See [CHANGELOG.md](CHANGELOG.md) for details on releases and updates.

---

**Built with ❤️ by the EventaHub Team**
