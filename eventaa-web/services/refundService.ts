import { ENDPOINTS } from '@/utils/api';

export interface RefundEligibilityResponse {
  eligible: boolean;
  reason?: string;
  refund_amount?: number;
  processing_fee?: number;
  refund_deadline?: string;
}

export interface RefundRequestData {
  purchase_id: number;
  reason: string;
}

export interface RefundResponse {
  message: string;
  data: {
    refund_amount: number;
    original_amount: number;
    refund_fee: number;
    refund_id?: string;
    estimated_processing_time?: string;
  };
}

export class RefundService {
  private httpClient: any;

  constructor(httpClient: any) {
    this.httpClient = httpClient;
  }

  /**
   * Check if a ticket purchase is eligible for refund
   */
  async checkRefundEligibility(purchaseId: number): Promise<RefundEligibilityResponse> {
    try {
      const response = await this.httpClient.get(
        `${ENDPOINTS.TICKETS.CHECK_REFUND_ELIGIBILITY}/${purchaseId}`
      );
      return response.data || response;
    } catch (error: any) {
      console.error('Error checking refund eligibility:', error);
      throw new Error(error.response?.data?.message || 'Failed to check refund eligibility');
    }
  }

  /**
   * Request a refund for a ticket purchase
   */
  async requestRefund(refundData: RefundRequestData): Promise<RefundResponse> {
    try {
      const response = await this.httpClient.post(
        ENDPOINTS.TICKETS.REQUEST_REFUND,
        refundData
      );
      return response.data || response;
    } catch (error: any) {
      console.error('Error requesting refund:', error);
      throw new Error(error.response?.data?.message || 'Failed to request refund');
    }
  }

  /**
   * Calculate refund amount with fees
   */
  calculateRefundAmount(originalAmount: number, feePercentage: number = 5): {
    refundAmount: number;
    processingFee: number;
    feePercentage: number;
  } {
    const processingFee = (originalAmount * feePercentage) / 100;
    const refundAmount = Math.max(0, originalAmount - processingFee);

    return {
      refundAmount,
      processingFee,
      feePercentage
    };
  }

  /**
   * Check if refund is still possible based on event date
   */
  isRefundTimeValid(eventDate: string, hoursBeforeEvent: number = 24): boolean {
    const eventDateTime = new Date(eventDate);
    const cutoffTime = new Date(eventDateTime.getTime() - (hoursBeforeEvent * 60 * 60 * 1000));
    const now = new Date();

    return now < cutoffTime;
  }

  /**
   * Get refund policy text based on ticket type
   */
  getRefundPolicyText(isRefundable: boolean, feePercentage: number = 5): string {
    if (!isRefundable) {
      return 'This ticket is non-refundable.';
    }

    return `Refunds are available up to 24 hours before the event. A ${feePercentage}% processing fee applies.`;
  }

  /**
   * Format refund status for display
   */
  formatRefundStatus(status: string): {
    text: string;
    color: string;
    icon: string;
  } {
    switch (status.toLowerCase()) {
      case 'completed':
        return {
          text: 'Purchased',
          color: 'text-green-600 dark:text-green-400',
          icon: 'heroicons:check-circle'
        };
      case 'pending':
        return {
          text: 'Pending',
          color: 'text-yellow-600 dark:text-yellow-400',
          icon: 'heroicons:clock'
        };
      case 'refunded':
        return {
          text: 'Refunded',
          color: 'text-blue-600 dark:text-blue-400',
          icon: 'heroicons:arrow-uturn-left'
        };
      case 'cancelled':
        return {
          text: 'Cancelled',
          color: 'text-red-600 dark:text-red-400',
          icon: 'heroicons:x-circle'
        };
      default:
        return {
          text: 'Unknown',
          color: 'text-gray-600 dark:text-gray-400',
          icon: 'heroicons:question-mark-circle'
        };
    }
  }
}

// Composable for using refund service
export const useRefundService = () => {
  const httpClient = useHttpClient();
  return new RefundService(httpClient);
};

// Helper functions for components
export const useRefundHelpers = () => {
  const refundService = useRefundService();

  const formatPrice = (amount: number, currency: string = 'USD', symbol: string = '$'): string => {
    const formattedAmount = Number(amount).toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });

    return `${symbol}${formattedAmount}`;
  };

  const canRequestRefund = (ticket: any): boolean => {
    if (!ticket) return false;

    const isRefundable = ticket.ticket?.tier?.is_refundable ?? ticket.ticket?.is_refundable;
    if (isRefundable === false) return false;

    if (['refunded', 'cancelled'].includes(ticket.status?.toLowerCase())) return false;

    if (ticket.event?.start) {
      const eventDate = new Date(ticket.event.start);
      const now = new Date();
      if (eventDate < now) return false;
    }

    if (ticket.event?.start) {
      return refundService.isRefundTimeValid(ticket.event.start, 24);
    }

    return true;
  };

  const getRefundDeadline = (eventDate: string, hoursBeforeEvent: number = 24): string => {
    const eventDateTime = new Date(eventDate);
    const deadline = new Date(eventDateTime.getTime() - (hoursBeforeEvent * 60 * 60 * 1000));

    return deadline.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return {
    refundService,
    formatPrice,
    canRequestRefund,
    getRefundDeadline
  };
};
