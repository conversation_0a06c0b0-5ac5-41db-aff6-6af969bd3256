<template>
  <div class="flex h-screen relative dashboard-bg-main">
    <VendorDashboardSidebar />
    <div class="flex-1 flex flex-col overflow-hidden">
      <VendorDashboardHeader @toggle-dark-mode="toggleDarkMode" />
      <NuxtLoadingIndicator />
      <main class="flex-1 overflow-x-hidden overflow-y-auto dashboard-bg-main dashboard-text-primary">
        <NuxtPage />
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';

definePageMeta({
  middleware: ['vendor', 'auth']
});

const isDarkMode = ref<boolean>(false);

const toggleDarkMode = (): void => {
  isDarkMode.value = !isDarkMode.value;

  if (isDarkMode.value) {
    document.documentElement.classList.add('dark');
  } else {
    document.documentElement.classList.remove('dark');
  }

  localStorage.setItem('darkMode', isDarkMode.value ? 'true' : 'false');
};

onMounted(() => {
  const savedDarkMode = localStorage.getItem('darkMode');
  if (savedDarkMode === 'true') {
    isDarkMode.value = true;
    document.documentElement.classList.add('dark');
  }
});
</script>
