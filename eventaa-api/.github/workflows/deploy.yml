name: Deploy to VPS

on:
  push:
    branches: [main]
  workflow_dispatch: # Allow manual trigger

jobs:
  deploy:
    name: Deploy EventaHub API to VPS
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.2'
          extensions: mbstring, xml, ctype, iconv, intl, pdo_mysql, dom, filter, gd, json, bcmath, redis
          tools: composer:v2
          coverage: none

      - name: Validate composer.json and composer.lock
        run: composer validate --strict

      - name: Get composer cache directory
        id: composer-cache
        run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

      - name: Cache composer dependencies
        uses: actions/cache@v3
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-

      - name: Install composer dependencies
        run: composer install --no-dev --optimize-autoloader --no-interaction --prefer-dist

      - name: Deploy to VPS (git pull)
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USERNAME }}
          key: ${{ secrets.VPS_SSH_KEY }}
          port: ${{ secrets.VPS_PORT || 22 }}
          script_stop: true

          script: |
            set -e
            set -x

            APP_DIR="/var/www/eventahub/eventaa-api"
            BACKUP_DIR="/var/backups/eventahub"
            TIMESTAMP=$(date +%Y%m%d_%H%M%S)

            echo "🚀 Starting deployment process..."

            # Create backup directory if it doesn't exist
            sudo mkdir -p $BACKUP_DIR

            # Create backup of current application
            if [ -d "$APP_DIR" ]; then
              echo "📦 Creating backup of current application..."
              sudo tar -czf $BACKUP_DIR/eventahub-backup-$TIMESTAMP.tar.gz -C $(dirname $APP_DIR) $(basename $APP_DIR)

              # Keep only last 5 backups
              sudo find $BACKUP_DIR -name "eventahub-backup-*.tar.gz" -type f | sort -r | tail -n +6 | sudo xargs rm -f
            fi

            # Stop services
            echo "⏹️  Stopping EventaHub services..."
            sudo systemctl stop eventahub-app || true
            sudo systemctl stop eventahub-reverb || true

            # Always update repo (assume $APP_DIR is a git repo)
            echo "[DEBUG] Pulling latest changes in $APP_DIR..."
            cd $APP_DIR
            git fetch --all
            echo "[DEBUG] git fetch exit code $?"
            git reset --hard origin/main
            echo "[DEBUG] git reset exit code $?"

            # Restore environment file
            if [ -f "$APP_DIR/.env.production" ]; then
              echo "🔧 Restoring production environment file..."
              sudo cp $APP_DIR/.env.production $APP_DIR/.env
            elif [ -f "/var/backups/eventahub/.env" ]; then
              echo "🔧 Restoring environment file from backup..."
              sudo cp /var/backups/eventahub/.env $APP_DIR/.env
            else
              echo "⚠️  Warning: No environment file found. Please configure .env manually."
            fi

            # Set proper permissions
            echo "🔒 Setting proper permissions..."
            sudo chown -R www-data:www-data $APP_DIR
            sudo chmod -R 755 $APP_DIR
            sudo chmod -R 775 $APP_DIR/storage
            sudo chmod -R 775 $APP_DIR/bootstrap/cache

            # Install/update dependencies
            echo "📦 Installing composer dependencies..."
            cd $APP_DIR
            sudo -u www-data composer install --no-dev --optimize-autoloader --no-interaction

            # Run Laravel deployment tasks
            echo "🔧 Running Laravel deployment tasks..."

            # Clear and cache config
            sudo -u www-data php artisan config:clear
            sudo -u www-data php artisan config:cache

            # Clear and cache routes
            sudo -u www-data php artisan route:clear
            sudo -u www-data php artisan route:cache

            # Clear and cache views
            sudo -u www-data php artisan view:clear
            sudo -u www-data php artisan view:cache

            # Clear application cache
            sudo -u www-data php artisan cache:clear

            # Run database migrations
            echo "🗄️  Running database migrations..."
            sudo -u www-data php artisan migrate --force

            # Clear and cache events
            sudo -u www-data php artisan event:clear
            sudo -u www-data php artisan event:cache

            # Optimize autoloader
            sudo -u www-data php artisan optimize

            # Restart queue workers
            echo "🔄 Restarting queue workers..."
            sudo -u www-data php artisan queue:restart

            # Start services
            echo "▶️  Starting EventaHub services..."
            sudo systemctl start eventahub-app
            sudo systemctl start eventahub-reverb

            # Reload nginx
            sudo systemctl reload nginx

            # Check service status
            echo "✅ Checking service status..."
            sudo systemctl is-active eventahub-app
            sudo systemctl is-active eventahub-reverb

            echo "🎉 Deployment completed successfully!"

      - name: Health Check
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USERNAME }}
          key: ${{ secrets.VPS_SSH_KEY }}
          port: ${{ secrets.VPS_PORT || 22 }}
          script: |
            echo "🏥 Performing health check..."

            # Wait a moment for services to start
            sleep 10

            # Check if the application is responding
            if curl -f -s -o /dev/null http://localhost:8000/api/health || curl -f -s -o /dev/null http://localhost:8000; then
              echo "✅ Application is responding correctly"
            else
              echo "❌ Application health check failed"
              echo "Service status:"
              sudo systemctl status eventahub-app --no-pager
              exit 1
            fi

  notify:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: deploy
    if: always()

    steps:
      - name: Notify Success
        if: needs.deploy.result == 'success'
        run: |
          echo "✅ Deployment to VPS completed successfully!"
          echo "🔗 Application should be available at your configured domain"

      - name: Notify Failure
        if: needs.deploy.result == 'failure'
        run: |
          echo "❌ Deployment to VPS failed!"
          echo "Please check the workflow logs for details"
          exit 1
