{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.1", "abraham/twitteroauth": "^7.0", "barryvdh/laravel-dompdf": "^3.0", "chillerlan/php-qrcode": "^4.3", "guzzlehttp/guzzle": "^7.2", "intervention/image": "^2.7", "kreait/firebase-php": "^7.13", "laravel/framework": "^11.0.0", "laravel/reverb": "^1.0", "laravel/sanctum": "^4.0", "laravel/socialite": "^5.10", "laravel/tinker": "^2.9", "maatwebsite/excel": "^3.1", "paragonie/constant_time_encoding": "^2.6", "pusher/pusher-php-server": "^7.2", "spatie/laravel-permission": "^6.1", "stevebauman/location": "^7.4"}, "require-dev": {"fakerphp/faker": "^1.9.1", "knuckleswtf/scribe": "^4.25", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^10.1", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}