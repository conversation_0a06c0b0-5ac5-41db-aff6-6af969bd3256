{"authHost": "http://127.0.0.1:8001", "authEndpoint": "/broadcasting/auth", "clients": [{"appId": "1908574", "key": "c8ddef6745c937660552"}], "database": "redis", "databaseConfig": {"redis": {}, "sqlite": {"databasePath": "/database/laravel-echo-server.sqlite"}}, "devMode": true, "host": null, "port": "7001 ", "protocol": "http", "socketio": {}, "secureOptions": 67108864, "sslCertPath": "", "sslKeyPath": "", "sslCertChainPath": "", "sslPassphrase": "", "subscribers": {"http": true, "redis": true}, "apiOriginAllow": {"allowCors": false, "allowOrigin": "", "allowMethods": "", "allowHeaders": ""}}