<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;

class FixRolesSeeder extends Seeder
{
    /**
     * Run the database seeds to fix role assignments.
     */
    public function run(): void
    {
        // Find the admin user
        $user = User::where('email', '<EMAIL>')->first();
        
        if ($user) {
            // Log current roles
            Log::info('Current roles for admin user:', [
                'roles' => $user->getRoleNames()->toArray()
            ]);
            
            // Assign all roles
            $user->syncRoles(['user', 'host', 'admin', 'vendor']);
            
            // Log roles after assignment
            Log::info('Roles after assignment for admin user:', [
                'roles' => $user->getRoleNames()->toArray()
            ]);
            
            $this->command->info('Admin user roles have been fixed.');
        } else {
            $this->command->error('Admin user not found.');
        }
        
        // Fix roles for all users
        $users = User::all();
        $this->command->info('Found ' . $users->count() . ' users.');
        
        foreach ($users as $user) {
            if ($user->getRoleNames()->isEmpty()) {
                $user->assignRole('user');
                $this->command->info('Assigned "user" role to user ID: ' . $user->id);
            }
        }
    }
}
