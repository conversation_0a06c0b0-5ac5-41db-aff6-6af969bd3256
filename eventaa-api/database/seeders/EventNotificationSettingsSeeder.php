<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class EventNotificationSettingsSeeder extends Seeder
{
    /**
     * Add event notification settings to the settings table.
     */
    public function run(): void
    {
        $settings = [
            [
                'key' => 'email_event_reminders',
                'name' => 'Event reminders',
                'description' => 'Receive email notifications about upcoming events you\'re attending.',
                'type' => 'email',
                'enabled' => true
            ],
            [
                'key' => 'push_event_reminders',
                'name' => 'Event reminders',
                'description' => 'Receive push notifications about upcoming events you\'re attending.',
                'type' => 'push',
                'enabled' => true
            ],
            [
                'key' => 'sms_event_reminders',
                'name' => 'Event reminders',
                'description' => 'Receive SMS notifications about upcoming events you\'re attending.',
                'type' => 'sms',
                'enabled' => true
            ]
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }

        $this->command->info('Event notification settings created successfully.');
    }
}
