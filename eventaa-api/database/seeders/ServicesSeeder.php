<?php

namespace Database\Seeders;

use Carbon\Carbon;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ServicesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $services = [
            [
                'name' => 'Photography',
                'description' => 'Professional photography services for weddings, parties, and corporate events.',
            ],
            [
                'name' => 'Videography',
                'description' => 'High-quality video coverage including editing and post-production for various events.',
            ],
            [
                'name' => 'Catering',
                'description' => 'Event catering with a range of menu options tailored to your guests and theme.',
            ],
            [
                'name' => 'Decoration',
                'description' => 'Creative event decoration services for indoor and outdoor venues.',
            ],
            [
                'name' => 'Sound Engineering',
                'description' => 'Expert sound setup and live audio management to ensure flawless audio experiences.',
            ],
            [
                'name' => 'DJ Services',
                'description' => 'Professional DJ services to keep your event lively and entertaining.',
            ],
            [
                'name' => 'Makeup & Hair',
                'description' => 'On-site makeup and hairstyling for brides, grooms, and event attendees.',
            ],
            [
                'name' => 'Event Planning',
                'description' => 'Comprehensive event planning and coordination from start to finish.',
            ],
            [
                'name' => 'Transportation',
                'description' => 'Reliable transportation services for guests, staff, or logistics.',
            ],
            [
                'name' => 'Venue Rental',
                'description' => 'Wide range of venues available for weddings, conferences, and private events.',
            ],
            [
                'name' => 'Security Services',
                'description' => 'Event security services to ensure the safety and smooth operation of your event.',
            ],
        ];

        foreach ($services as $service) {
            DB::table('services')->updateOrInsert(
                ['name' => $service['name']],
                [
                    'description' => $service['description'],
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ]
            );
        }
    }
}
