<?php

namespace Database\Seeders;

use App\Models\PaymentGateway;
use App\Models\Vendor;
use App\Models\VendorPaymentMethod;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class VendorPaymentMethodSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $vendors = Vendor::all();
        $paychangu = PaymentGateway::where('slug', 'paychangu')->first();

        if ($vendors->isEmpty() || !$paychangu) {
            $this->command->info('No vendors or PayChangu gateway found. Skipping payment method seeding.');
            return;
        }

        DB::table('vendor_payment_methods')->truncate();
        $paymentMethods = $paychangu->config['payment_methods'] ?? [];

        foreach ($vendors as $vendor) {
            foreach ($paymentMethods as $methodKey => $methodDetails) {
                $accountDetails = $this->getAccountDetailsForMethod($methodKey);

                VendorPaymentMethod::create([
                    'vendor_id' => $vendor->id,
                    'payment_gateway_id' => $paychangu->id,
                    'is_default' => $methodKey === 'mpamba',
                    'account_details' => array_merge($accountDetails, [
                        'method' => $methodKey,
                        'method_name' => $methodDetails['name'] ?? ucfirst($methodKey)
                    ]),
                    'status' => 'active',
                    'last_used_at' => now()
                ]);
            }
        }

        $this->command->info('Vendor payment methods seeded successfully.');
    }

    /**
     * Get account details for a specific payment method
     */
    private function getAccountDetailsForMethod(string $methodKey): array
    {
        switch ($methodKey) {
            case 'mpamba':
                return [
                    'phone_number' => '+265 999 123 456',
                    'account_name' => 'Test Mpamba Account'
                ];
            case 'airtel_money':
                return [
                    'phone_number' => '+265 888 123 456',
                    'account_name' => 'Test Airtel Money Account'
                ];
            case 'visa':
                return [
                    
                    'card_number' => '4111 1111 1111 1111',
                    'expiry_date' => '12/25',
                    'cvv' => '123',
                    'card_holder_name' => 'Test Card Holder'
                ];
            default:
                return [
                    'account_name' => 'Test Account',
                    'account_number' => '**********'
                ];
        }
    }
}
