<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Rating;
use App\Models\VendorRating;
use App\Models\User;
use App\Models\Event;
use App\Models\Vendor;

class ReviewSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get some users, events, and vendors for testing
        $users = User::limit(5)->get();
        $events = Event::limit(3)->get();
        $vendors = Vendor::limit(3)->get();

        if ($users->isEmpty()) {
            $this->command->info('No users found. Creating test users...');
            $users = collect([
                User::create([
                    'name' => '<PERSON>',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'avatar' => 'avatar1.jpg',
                ]),
                User::create([
                    'name' => '<PERSON>',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'avatar' => 'avatar2.jpg',
                ]),
                User::create([
                    'name' => '<PERSON>',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'avatar' => 'avatar3.jpg',
                ]),
            ]);
        }

        if ($events->isEmpty()) {
            $this->command->info('No events found. Please create some events first or this seeder will skip event ratings.');
        } else {
            // Create event ratings
            foreach ($events as $event) {
                $numRatings = rand(2, 5);
                for ($i = 0; $i < $numRatings; $i++) {
                    $user = $users->random();

                    // Check if this user already rated this event
                    $existingRating = Rating::where('event_id', $event->id)
                        ->where('user_id', $user->id)
                        ->first();

                    if (!$existingRating) {
                        Rating::create([
                            'event_id' => $event->id,
                            'user_id' => $user->id,
                            'rating' => rand(3, 5),
                            'review' => $this->getRandomReview(),
                            'organization' => rand(3, 5),
                            'content' => rand(3, 5),
                            'technical' => rand(3, 5),
                            'engagement' => rand(3, 5),
                        ]);
                    }
                }
            }
        }

        if ($vendors->isEmpty()) {
            $this->command->info('No vendors found. Please create some vendors first or this seeder will skip vendor ratings.');
        } else {
            // Create vendor ratings
            foreach ($vendors as $vendor) {
                $numRatings = rand(2, 4);
                for ($i = 0; $i < $numRatings; $i++) {
                    $user = $users->random();

                    // Check if this user already rated this vendor
                    $existingRating = VendorRating::where('vendor_id', $vendor->id)
                        ->where('user_id', $user->id)
                        ->first();

                    if (!$existingRating) {
                        VendorRating::create([
                            'vendor_id' => $vendor->id,
                            'user_id' => $user->id,
                            'rating' => rand(3, 5),
                            'comment' => $this->getRandomReview(),
                        ]);
                    }
                }
            }
        }

        $this->command->info('Review seeder completed!');
    }

    private function getRandomReview(): string
    {
        $reviews = [
            'Great experience! Highly recommended.',
            'Amazing service and quality. Would definitely book again.',
            'Professional and reliable. Very satisfied with the outcome.',
            'Exceeded expectations in every way. Fantastic work!',
            'Good service overall. Some room for improvement but generally positive.',
            'Outstanding quality and attention to detail.',
            'Very professional and easy to work with.',
            'Delivered exactly what was promised. Great communication.',
            'Excellent value for money. Will recommend to others.',
            'Top-notch service from start to finish.',
            'Impressed with the level of professionalism and quality.',
            'Perfect for our event. Everyone was very happy with the service.',
            'Reliable and trustworthy. Made our event special.',
            'Innovative and creative approach. Loved the results.',
            'Smooth process and great final outcome.',
        ];

        return $reviews[array_rand($reviews)];
    }
}
