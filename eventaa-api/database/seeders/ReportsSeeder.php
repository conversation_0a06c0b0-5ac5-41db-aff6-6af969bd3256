<?php

namespace Database\Seeders;

use App\Models\Report;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ReportsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first user or create one
        $user = User::first();
        if (!$user) {
            $user = User::create([
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]);
        }

        $reports = [
            [
                'name' => 'Monthly Financial Report',
                'description' => 'Comprehensive financial overview for the current month',
                'type' => 'financial',
                'period' => 'month',
                'status' => 'completed',
                'user_id' => $user->id,
                'generated_at' => now()->subDays(1),
                'start_date' => now()->subMonth()->startOfMonth(),
                'end_date' => now()->subMonth()->endOfMonth(),
                'data' => [
                    'revenue' => 125000,
                    'transactions' => 450,
                    'refunds' => 2500,
                    'average_transaction' => 277.78
                ]
            ],
            [
                'name' => 'Event Attendance Analysis',
                'description' => 'Detailed attendance statistics and trends',
                'type' => 'attendance',
                'period' => 'quarter',
                'status' => 'completed',
                'user_id' => $user->id,
                'generated_at' => now()->subDays(2),
                'start_date' => now()->subQuarter()->startOfQuarter(),
                'end_date' => now()->subQuarter()->endOfQuarter(),
                'data' => [
                    'tickets_sold' => 2450,
                    'events_count' => 25,
                    'average_attendance' => 98
                ]
            ],
            [
                'name' => 'Sales Performance Report',
                'description' => 'Ticket sales performance and revenue analysis',
                'type' => 'sales',
                'period' => 'month',
                'status' => 'processing',
                'user_id' => $user->id,
                'start_date' => now()->startOfMonth(),
                'end_date' => now()->endOfMonth(),
                'data' => null
            ],
            [
                'name' => 'Vendor Revenue Summary',
                'description' => 'Revenue breakdown by vendor partnerships',
                'type' => 'vendor',
                'period' => 'quarter',
                'status' => 'completed',
                'user_id' => $user->id,
                'generated_at' => now()->subDays(3),
                'start_date' => now()->subQuarter()->startOfQuarter(),
                'end_date' => now()->subQuarter()->endOfQuarter(),
                'data' => [
                    'total_vendors' => 45,
                    'active_vendors' => 38,
                    'pending_vendors' => 7,
                    'vendor_revenue' => 45000
                ]
            ],
            [
                'name' => 'Annual Financial Summary',
                'description' => 'Complete financial overview for 2024',
                'type' => 'financial',
                'period' => 'year',
                'status' => 'failed',
                'user_id' => $user->id,
                'start_date' => now()->subYear()->startOfYear(),
                'end_date' => now()->subYear()->endOfYear(),
                'data' => null
            ],
            [
                'name' => 'Weekly Sales Report',
                'description' => 'Weekly ticket sales and performance metrics',
                'type' => 'sales',
                'period' => 'week',
                'status' => 'scheduled',
                'user_id' => $user->id,
                'start_date' => now()->startOfWeek(),
                'end_date' => now()->endOfWeek(),
                'data' => null
            ]
        ];

        foreach ($reports as $reportData) {
            Report::create($reportData);
        }
    }
}
