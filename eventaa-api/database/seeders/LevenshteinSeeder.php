<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class LevenshteinSeeder extends Seeder
{
    public function run()
    {
        DB::unprepared('DROP FUNCTION IF EXISTS levenshtein;');
        
        DB::unprepared('
            CREATE FUNCTION levenshtein(s1 VARCHAR(255), s2 VARCHAR(255))
            RETURNS INT
            DETERMINISTIC
            BEGIN
                DECLARE s1_len, s2_len, i, j, c, c_temp, cost INT;
                DECLARE s1_char CHAR;
                DECLARE cv0, cv1 VARBINARY(256);
                
                SET s1_len = CHAR_LENGTH(s1),
                    s2_len = CHAR_LENGTH(s2),
                    cv1 = 0x00,
                    j = 1,
                    i = 1,
                    c = 0;
                
                IF s1_len = 0 THEN
                    RETURN s2_len;
                ELSEIF s2_len = 0 THEN
                    RETURN s1_len;
                END IF;
                
                WHILE i <= s1_len DO
                    SET s1_char = SUBSTRING(s1, i, 1),
                        c = c + 1,
                        cv1 = CONCAT(cv1, CHAR(c));
                    SET i = i + 1;
                END WHILE;
                
                SET i = 1;
                WHILE i <= s1_len DO
                    SET j = 1,
                        c = 0,
                        cv0 = cv1,
                        cv1 = 0x00;
                    
                    WHILE j <= s2_len DO
                        SET c = c + 1,
                            c_temp = ORD(SUBSTRING(cv0, c, 1)),
                            cost = IF(SUBSTRING(s1, i, 1) = SUBSTRING(s2, j, 1), 0, 1);
                        
                        SET c_temp = LEAST(
                            c_temp + 1,
                            ORD(SUBSTRING(cv1, c, 1)) + 1,
                            c_temp + cost
                        );
                        
                        SET cv1 = CONCAT(cv1, CHAR(c_temp)),
                            j = j + 1;
                    END WHILE;
                    
                    SET i = i + 1;
                END WHILE;
                
                RETURN c_temp;
            END;
        ');
    }
}
