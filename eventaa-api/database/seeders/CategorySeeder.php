<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Seeder;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            ['name' => 'Music', 'icon' => 'concert.png', 'description' => 'Explore the world of musical events and concerts.'],
            ['name' => 'Sports', 'icon' => 'sports.png', 'description' => 'Engage in various sports activities and events.'],
            ['name' => 'Conferences', 'icon' => 'conference.png', 'description' => 'Participate in conferences and seminars.'],
            ['name' => 'Art and Theater', 'icon' => 'theater.png', 'description' => 'Experience art and theatrical performances.'],
            ['name' => 'Food and Drink', 'icon' => 'fast-food.png', 'description' => 'Discover culinary delights and beverages.'],
            ['name' => 'Charity', 'icon' => 'loving.png', 'description' => 'Support charitable causes and events.'],
            ['name' => 'Outdoor', 'icon' => 'table.png', 'description' => 'Enjoy outdoor activities and adventures.'],
            ['name' => 'Workshops', 'icon' => 'workshop.png', 'description' => 'Participate in educational workshops.'],
            ['name' => 'Family', 'icon' => 'family.png', 'description' => 'Events suitable for the whole family.'],
            ['name' => 'Comedy', 'icon' => 'stand-up.png', 'description' => 'Laugh and enjoy comedy performances.'],
            ['name' => 'Networking', 'icon' => 'networking.png', 'description' => 'Connect with professionals in networking events.'],
            ['name' => 'Movies', 'icon' => 'movies.png', 'description' => 'Watch movies and film-related events.'],
            ['name' => 'Fashion', 'icon' => 'fashion.png', 'description' => 'Explore fashion shows and events.'],
            ['name' => 'Science', 'icon' => 'atom.png', 'description' => 'Engage in scientific discussions and exhibitions.'],
            ['name' => 'Technology', 'icon' => 'cpu.png', 'description' => 'Explore the latest in technology and innovation.'],
            ['name' => 'Gaming', 'icon' => 'gaming.png', 'description' => 'Participate in gaming competitions and events.'],
            ['name' => 'Health and Wellness', 'icon' => 'meditation.png', 'description' => 'Focus on health and well-being activities.'],
            ['name' => 'Education', 'icon' => 'education.png', 'description' => 'Educational events and learning opportunities.'],
            ['name' => 'Religious', 'icon' => 'religion.png', 'description' => 'Religious and spiritual gatherings.'],
            ['name' => 'Other', 'icon' => 'other.png', 'description' => 'Events that do not fit into other categories.'],
        ];

        foreach ($categories as $category) {
            Category::create([
                'name' => $category['name'],
                'icon' => $category['icon'],
                'description' => $category['description']
            ]);
        }
    }
}
