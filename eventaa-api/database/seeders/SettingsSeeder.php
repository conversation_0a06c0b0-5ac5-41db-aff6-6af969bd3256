<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            [
                'key' => 'email_bookings',
                'name' => 'Booking notifications',
                'description' => 'Receive emails when you get new booking requests, confirmations, or cancellations.',
                'type' => 'email',
                'enabled' => true
            ],
            [
                'key' => 'email_messages',
                'name' => 'Message notifications',
                'description' => 'Receive emails when you get new messages from clients.',
                'type' => 'email',
                'enabled' => true
            ],
            [
                'key' => 'email_reviews',
                'name' => 'Review notifications',
                'description' => 'Receive emails when you get new reviews from clients.',
                'type' => 'email',
                'enabled' => true
            ],
            [
                'key' => 'email_marketing',
                'name' => 'Marketing emails',
                'description' => 'Receive emails about new features, tips, and promotions.',
                'type' => 'email',
                'enabled' => false
            ],
            [
                'key' => 'email_payment',
                'name' => 'Payment notifications',
                'description' => 'Receive emails about payments, invoices, and billing updates.',
                'type' => 'email',
                'enabled' => true
            ],

            [
                'key' => 'push_bookings',
                'name' => 'Booking notifications',
                'description' => 'Receive push notifications when you get new booking requests, confirmations, or cancellations.',
                'type' => 'push',
                'enabled' => true
            ],
            [
                'key' => 'push_messages',
                'name' => 'Message notifications',
                'description' => 'Receive push notifications when you get new messages from clients.',
                'type' => 'push',
                'enabled' => true
            ],
            [
                'key' => 'push_reviews',
                'name' => 'Review notifications',
                'description' => 'Receive push notifications when you get new reviews from clients.',
                'type' => 'push',
                'enabled' => true
            ],
            [
                'key' => 'push_payment',
                'name' => 'Payment notifications',
                'description' => 'Receive push notifications about payments, invoices, and billing updates.',
                'type' => 'push',
                'enabled' => true
            ],

            // SMS notifications
            [
                'key' => 'sms_bookings',
                'name' => 'Booking notifications',
                'description' => 'Receive SMS when you get new booking requests, confirmations, or cancellations.',
                'type' => 'sms',
                'enabled' => true
            ],
            [
                'key' => 'sms_urgent',
                'name' => 'Urgent messages',
                'description' => 'Receive SMS for urgent messages from clients.',
                'type' => 'sms',
                'enabled' => true
            ],
            [
                'key' => 'sms_reminders',
                'name' => 'Appointment reminders',
                'description' => 'Receive SMS reminders for upcoming appointments.',
                'type' => 'sms',
                'enabled' => true
            ],
            [
                'key' => 'sms_payment',
                'name' => 'Payment notifications',
                'description' => 'Receive SMS about payments, invoices, and billing updates.',
                'type' => 'sms',
                'enabled' => true
            ],
            [
                'key' => 'show_currency_symbol',
                'name' => 'Show currency symbol',
                'description' => 'Show the currency symbol in the app',
                'type' => 'preference',
                'enabled' => true
            ],
            [
                'key' => 'auto_convert_prices',
                'name' => 'Auto convert prices',
                'description' => 'Automatically convert prices to your preferred currency',
                'type' => 'preference',
                'enabled' => false
            ]
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
