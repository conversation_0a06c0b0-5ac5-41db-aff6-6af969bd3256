<?php

namespace Database\Seeders;

use App\Models\PaymentGateway;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PaymentGatewaySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // First, deactivate all existing gateways
        PaymentGateway::query()->update(['is_active' => false]);

        $gateways = [
            [
                'name' => 'PayChangu',
                'slug' => 'paychangu',
                'description' => 'PayChangu is a secure payment gateway supporting Mpamba, Airtel Money, and bank transfers.',
                'is_active' => true,
                'config' => [
                    'api_key' => env('PAYCHANGU_API_KEY', ''),
                    'secret_key' => env('PAYCHANGU_SECRET_KEY', ''),
                    'merchant_id' => env('PAYCHANGU_MERCHANT_ID', ''),
                    'webhook_secret' => env('PAYCHANGU_WEBHOOK_SECRET', ''),
                    'payment_methods' => [
                        'mpamba' => [
                            'name' => 'TNM Mpamba',
                            'operator' => 'tnm',
                            'logo' => 'mpamba-logo.png',
                            'is_active' => true,
                            'description' => 'Pay with your TNM Mpamba wallet'
                        ],
                        'airtel_money' => [
                            'name' => 'Airtel Money',
                            'operator' => 'airtel',
                            'logo' => 'airtel-money-logo.png',
                            'is_active' => true,
                            'description' => 'Pay with your Airtel Money wallet'
                        ],
                        'bank_transfer' => [
                            'name' => 'Bank Transfer',
                            'logo' => 'bank-logo.png',
                            'is_active' => true,
                            'description' => 'Pay with your bank account or card'
                        ]
                    ]
                ],
                'logo' => 'paychangu-logo.png',
                'supported_currencies' => ['MWK'],
                'test_mode' => env('PAYCHANGU_TEST_MODE', true)
            ]
        ];

        foreach ($gateways as $gateway) {
            PaymentGateway::updateOrCreate(
                ['slug' => $gateway['slug']],
                $gateway
            );
        }
    }
}
