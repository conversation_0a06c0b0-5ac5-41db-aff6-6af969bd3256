<?php

namespace Database\Seeders;

use App\Models\Visibility;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class VisibilitySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */

    public function run(): void
    {
        $items = [
            'public',
            'private',
        ];
        foreach ($items as $item) {
            Visibility::create([
                'name' => $item,
            ]);
        }
    }
}
