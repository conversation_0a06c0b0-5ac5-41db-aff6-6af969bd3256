<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class PermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = [
            // Event permissions
            'event.create' => 'Can create events',
            'event.update' => 'Can update events',
            'event.delete' => 'Can delete events',
            'event.view' => 'Can view events',
            'event.view.private' => 'Can view private events',
            'event.publish' => 'Can publish events',
            'event.add-sponsors' => 'Can add sponsors to events',
            'event.like' => 'Can like events',
            'event.share' => 'Can share events',
            'event.attend' => 'Can attend events',
            'event.enable-notifications' => 'Can enable notifications for events',

            // Ticket permissions
            'ticket.generate' => 'Can generate tickets',
            'ticket.scan' => 'Can scan tickets',
            'ticket.buy' => 'Can buy tickets',

            // Tier permissions
            'tier.create' => 'Can create tiers',
            'tier.update' => 'Can update tiers',
            'tier.delete' => 'Can delete tiers',
            'tier.view' => 'Can view tiers',

            // Venue permissions
            'venue.create' => 'Can create venues',
            'venue.update' => 'Can update venues',
            'venue.delete' => 'Can delete venues',
            'venue.view' => 'Can view venues',
            'venue.rate' => 'Can rate venues',

            // Venue booking permissions
            'venue-booking.create' => 'Can create venue bookings',
            'venue-booking.update' => 'Can update venue bookings',
            'venue-booking.delete' => 'Can delete venue bookings',
            'venue-booking.view' => 'Can view venue bookings',
            'venue-booking.update-status' => 'Can update venue booking status',

            // Vendor permissions
            'vendor.create' => 'Can create vendors',
            'vendor.update' => 'Can update vendors',
            'vendor.delete' => 'Can delete vendors',
            'vendor.view' => 'Can view vendors',

            // Vendor service permissions
            'vendor-service.create' => 'Can create vendor services',
            'vendor-service.update' => 'Can update vendor services',
            'vendor-service.delete' => 'Can delete vendor services',
            'vendor-service.view' => 'Can view vendor services',

            // Vendor booking permissions
            'vendor-booking.create' => 'Can create vendor bookings',
            'vendor-booking.update' => 'Can update vendor bookings',
            'vendor-booking.delete' => 'Can delete vendor bookings',
            'vendor-booking.view' => 'Can view vendor bookings',
            'vendor-booking.update-status' => 'Can update vendor booking status',

            // Vendor rating permissions
            'vendor-rating.create' => 'Can create vendor ratings',
            'vendor-rating.update' => 'Can update vendor ratings',
            'vendor-rating.delete' => 'Can delete vendor ratings',
            'vendor-rating.view' => 'Can view vendor ratings',

            // Messaging permissions
            'message.send' => 'Can send messages',
            'message.view' => 'Can view messages',
            'message.delete' => 'Can delete messages',
            'messages.view' => 'Can view messages dashboard',

            // Category permissions
            'category.create' => 'Can create categories',
            'category.update' => 'Can update categories',
            'category.delete' => 'Can delete categories',
            'category.view' => 'Can view categories',

            // Rating permissions
            'rating.create' => 'Can create ratings',
            'rating.update' => 'Can update ratings',
            'rating.delete' => 'Can delete ratings',
            'rating.view' => 'Can view ratings',
            'reviews.view' => 'Can view reviews dashboard',

            // Follower permissions
            'follower.create' => 'Can create followers',
            'follower.delete' => 'Can delete followers',
            'follower.view' => 'Can view followers',

            // Subscription permissions
            'subscription.create' => 'Can create subscriptions',
            'subscription.view' => 'Can view subscriptions',

            // Sponsor permissions
            'sponsor.create' => 'Can create sponsors',
            'sponsor.update' => 'Can update sponsors',
            'sponsor.delete' => 'Can delete sponsors',
            'sponsor.view' => 'Can view sponsors',

            // Retailer permissions
            'retailer.create' => 'Can create retailers',
            'retailer.update' => 'Can update retailers',
            'retailer.delete' => 'Can delete retailers',
            'retailer.view' => 'Can view retailers',

            // Verification permissions
            'verification.request' => 'Can request verification',
            'verification.verify' => 'Can verify users',
            'verification.check-status' => 'Can check verification status',

            // Blog permissions
            'blog.article.create' => 'Can create blog articles',
            'blog.article.update' => 'Can update blog articles',
            'blog.article.delete' => 'Can delete blog articles',
            'blog.article.view' => 'Can view blog articles',

            // Service permissions
            'service.create' => 'Can create services',
            'service.update' => 'Can update services',
            'service.delete' => 'Can delete services',
            'service.view' => 'Can view services',
            'services.view' => 'Can view services dashboard',
            'bookings.view' => 'Can view bookings dashboard',

            // Profile permissions
            'profile.update' => 'Can update profile',
            'profile.view' => 'Can view profile',
            'portfolio.view' => 'Can view portfolio',
            'pricing.view' => 'Can view pricing',

            // Settings permissions
            'settings.update' => 'Can update settings',
            'settings.view' => 'Can view settings',
            'settings.account.view' => 'Can view account settings',
            'settings.security.view' => 'Can view security settings',
            'settings.notifications.view' => 'Can view notification settings',
            'settings.payments.view' => 'Can view payment settings',

            // Notification permissions
            'notification.view' => 'Can view notifications',
            'notification.mark-as-read' => 'Can mark notifications as read',
            'notification.delete' => 'Can delete notifications',
            'notifications.view' => 'Can view notifications dashboard',

            // User permissions
            'user.deactivate' => 'Can deactivate user account',
            'user.manage' => 'Can manage users',
        ];

        foreach ($permissions as $name => $description) {
            Permission::updateOrCreate(
                ['name' => $name],
                ['guard_name' => 'web', 'description' => $description]
            );
        }

        $this->assignPermissionsToRoles($permissions);
    }

    /**
     * Assign permissions to roles
     */
    private function assignPermissionsToRoles($permissions): void
    {
        $adminRole = Role::findByName('admin');
        $userRole = Role::findByName('user');
        $hostRole = Role::findByName('host');
        $vendorRole = Role::findByName('vendor');

        $adminRole->syncPermissions(array_keys($permissions));

        $userPermissions = [
            'event.view', 'event.like', 'event.share', 'event.attend', 'event.enable-notifications',
            'ticket.buy',
            'tier.view',
            'venue.view', 'venue.rate',
            'venue-booking.create', 'venue-booking.view',
            'vendor.view',
            'vendor-service.view',
            'vendor-booking.create', 'vendor-booking.view', 'vendor-booking.update',
            'vendor-rating.create', 'vendor-rating.view',
            'message.send', 'message.view', 'message.delete',
            'category.view',
            'rating.create', 'rating.view',
            'follower.create', 'follower.delete', 'follower.view',
            'subscription.create', 'subscription.view',
            'sponsor.view',
            'retailer.view',
            'verification.request', 'verification.check-status',
            'blog.article.view',
            'service.view',
            'profile.update', 'profile.view',
            'settings.update', 'settings.view',
            'notification.view', 'notification.mark-as-read', 'notification.delete',
            'user.deactivate'
        ];
        $userRole->syncPermissions($userPermissions);

        $hostPermissions = array_merge($userPermissions, [
            'event.create', 'event.update', 'event.delete', 'event.publish', 'event.add-sponsors',
            'ticket.generate', 'ticket.scan',
            'tier.create', 'tier.update', 'tier.delete',
            'sponsor.create', 'sponsor.update', 'sponsor.delete',
            'retailer.create', 'retailer.update', 'retailer.delete',
            'blog.article.create', 'blog.article.update', 'blog.article.delete',
        ]);
        $hostRole->syncPermissions($hostPermissions);

        $vendorPermissions = array_merge($userPermissions, [
            'vendor.create', 'vendor.update',
            'vendor-service.create', 'vendor-service.update', 'vendor-service.delete',
            'vendor-booking.update-status',
            'venue.create', 'venue.update', 'venue.delete',
            'venue-booking.update-status',
            // Add dashboard-specific permissions
            'bookings.view', 'services.view', 'portfolio.view', 'pricing.view',
            'messages.view', 'reviews.view', 'notifications.view',
            'settings.account.view', 'settings.security.view', 'settings.notifications.view', 'settings.payments.view'
        ]);
        $vendorRole->syncPermissions($vendorPermissions);
    }
}
