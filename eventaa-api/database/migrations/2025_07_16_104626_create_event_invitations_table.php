<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('event_invitations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('event_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->string('email');
            $table->string('name')->nullable();
            $table->string('avatar')->nullable();
            $table->enum('status', ['pending', 'sent', 'accepted', 'declined'])->default('pending');
            $table->foreignId('invited_by')->constrained('users')->onDelete('cascade');
            $table->uuid('invitation_token')->unique();
            $table->text('message')->nullable();
            $table->timestamp('responded_at')->nullable();
            $table->timestamp('resent_at')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->index(['event_id', 'email']);
            $table->index(['event_id', 'status']);
            $table->index('invitation_token');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('event_invitations');
    }
};
