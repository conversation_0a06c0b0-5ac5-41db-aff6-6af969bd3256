<?php

use App\Models\Currency;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
{
    Schema::table('venue_prices', function (Blueprint $table) {
        if (!Schema::hasColumn('venue_prices', 'currency_id')) {
            $table->foreignIdFor(Currency::class)
                ->nullable()
                ->constrained()
                ->cascadeOnDelete();
        }
    });
}

public function down(): void
{
    Schema::table('venue_prices', function (Blueprint $table) {
        if (Schema::hasColumn('venue_prices', 'currency_id')) {
            $table->dropConstrainedForeignIdFor(Currency::class);
        }
    });
}
};
