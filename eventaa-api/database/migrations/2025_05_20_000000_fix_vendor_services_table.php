<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First check if service_id column already exists
        if (!Schema::hasColumn('vendor_services', 'service_id')) {
            // Add service_id column
            Schema::table('vendor_services', function (Blueprint $table) {
                $table->foreignId('service_id')
                    ->nullable()
                    ->after('vendor_id');
            });

            // Add foreign key constraint separately
            Schema::table('vendor_services', function (Blueprint $table) {
                $table->foreign('service_id')
                    ->references('id')
                    ->on('services')
                    ->cascadeOnDelete();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Only drop the column if it exists
        if (Schema::hasColumn('vendor_services', 'service_id')) {
            Schema::table('vendor_services', function (Blueprint $table) {
                $table->dropForeign(['service_id']);
                $table->dropColumn('service_id');
            });
        }
    }
};
