<?php

use App\Models\Currency;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            if (!Schema::hasColumn('users', 'currency_id')) {
                $table->foreignIdFor(Currency::class)
                    ->nullable()
                    ->after('last_active_at')
                    ->constrained()
                    ->nullOnDelete();
            }
        });

        // Set default currency to MWK for existing users
        $mwkCurrency = Currency::where('name', 'MWK')->first();
        if ($mwkCurrency) {
            DB::table('users')
                ->whereNull('currency_id')
                ->update(['currency_id' => $mwkCurrency->id]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            if (Schema::hasColumn('users', 'currency_id')) {
                $table->dropConstrainedForeignIdFor(Currency::class);
            }
        });
    }
};
