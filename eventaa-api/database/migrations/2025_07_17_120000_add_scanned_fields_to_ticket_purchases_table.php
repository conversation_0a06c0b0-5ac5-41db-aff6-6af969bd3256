<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ticket_purchases', function (Blueprint $table) {
            $table->boolean('scanned')->default(false)->after('metadata');
            $table->timestamp('scanned_at')->nullable()->after('scanned');
            $table->string('scanned_by')->nullable()->after('scanned_at')->comment('User who scanned the ticket');

            // Add index for scanning queries
            $table->index(['scanned', 'event_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ticket_purchases', function (Blueprint $table) {
            $table->dropIndex(['scanned', 'event_id']);
            $table->dropColumn(['scanned', 'scanned_at', 'scanned_by']);
        });
    }
};
