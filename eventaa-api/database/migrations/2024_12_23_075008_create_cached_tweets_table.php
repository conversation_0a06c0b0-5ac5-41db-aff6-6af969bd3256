<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('cached_tweets', function (Blueprint $table) {
            $table->id();
            $table->string('hashtag');
            $table->string('tweet_id');
            $table->text('content');
            $table->string('author_id')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamp('tweet_created_at');
            $table->timestamps();
            
            $table->index('hashtag');
            $table->unique(['tweet_id', 'hashtag']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('cached_tweets');
    }
};
