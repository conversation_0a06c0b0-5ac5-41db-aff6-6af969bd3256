<?php

use App\Models\Category;
use App\Models\District;
use App\Models\Type;
use App\Models\User;
use App\Models\Visibility;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('events', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(User::class);
            $table->foreignIdFor(Type::class);
            $table->foreignIdFor(Visibility::class)->default(1);
            $table->string('title');
            $table->text('description');
            $table->string('location');
            $table->foreignIdFor(Category::class);
            $table->float('latitude');
            $table->float('longitude');
            $table->string('cover_art')->default('cover_art');
            $table->timestamp('start');
            $table->timestamp('end')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('events');
    }
};
