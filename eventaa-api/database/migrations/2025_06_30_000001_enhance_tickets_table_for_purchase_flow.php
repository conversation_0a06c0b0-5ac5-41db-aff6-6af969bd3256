<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tickets', function (Blueprint $table) {
            // Add missing fields that are referenced in the controller
            // Note: name, price, banner, description, and status already exist

            // Add tier relationship
            if (!Schema::hasColumn('tickets', 'tier_id')) {
                $table->foreignId('tier_id')->nullable()->constrained()->nullOnDelete()->after('event_id');
            }

            // Add quantity management fields
            if (!Schema::hasColumn('tickets', 'quantity_available')) {
                $table->integer('quantity_available')->default(1)->after('status');
            }

            if (!Schema::hasColumn('tickets', 'quantity_sold')) {
                $table->integer('quantity_sold')->default(0)->after('quantity_available');
            }

            // Add sale period fields
            if (!Schema::hasColumn('tickets', 'sale_start_date')) {
                $table->datetime('sale_start_date')->nullable()->after('quantity_sold');
            }

            if (!Schema::hasColumn('tickets', 'sale_end_date')) {
                $table->datetime('sale_end_date')->nullable()->after('sale_start_date');
            }

            // Add refund fields
            if (!Schema::hasColumn('tickets', 'is_refundable')) {
                $table->boolean('is_refundable')->default(true)->after('sale_end_date');
            }

            if (!Schema::hasColumn('tickets', 'refund_fee_percentage')) {
                $table->decimal('refund_fee_percentage', 5, 2)->default(0)->after('is_refundable');
            }

            // Add metadata field
            if (!Schema::hasColumn('tickets', 'metadata')) {
                $table->json('metadata')->nullable()->after('refund_fee_percentage');
            }
        });

        // Add indexes separately to avoid conflicts
        try {
            Schema::table('tickets', function (Blueprint $table) {
                $table->index(['event_id', 'status'], 'tickets_event_id_status_idx');
            });
        } catch (\Exception $e) {
            // Index might already exist, ignore
        }

        try {
            Schema::table('tickets', function (Blueprint $table) {
                $table->index(['tier_id', 'status'], 'tickets_tier_id_status_idx');
            });
        } catch (\Exception $e) {
            // Index might already exist, ignore
        }

        try {
            Schema::table('tickets', function (Blueprint $table) {
                $table->index(['sale_start_date', 'sale_end_date'], 'tickets_sale_dates_idx');
            });
        } catch (\Exception $e) {
            // Index might already exist, ignore
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop indexes first
        try {
            Schema::table('tickets', function (Blueprint $table) {
                $table->dropIndex('tickets_event_id_status_idx');
            });
        } catch (\Exception $e) {
            // Index might not exist, ignore
        }

        try {
            Schema::table('tickets', function (Blueprint $table) {
                $table->dropIndex('tickets_tier_id_status_idx');
            });
        } catch (\Exception $e) {
            // Index might not exist, ignore
        }

        try {
            Schema::table('tickets', function (Blueprint $table) {
                $table->dropIndex('tickets_sale_dates_idx');
            });
        } catch (\Exception $e) {
            // Index might not exist, ignore
        }

        Schema::table('tickets', function (Blueprint $table) {
            // Drop foreign key constraint if it exists
            if (Schema::hasColumn('tickets', 'tier_id')) {
                $table->dropForeign(['tier_id']);
            }

            // Drop only the columns we added
            $columnsToRemove = [];

            if (Schema::hasColumn('tickets', 'tier_id')) {
                $columnsToRemove[] = 'tier_id';
            }

            if (Schema::hasColumn('tickets', 'quantity_available')) {
                $columnsToRemove[] = 'quantity_available';
            }

            if (Schema::hasColumn('tickets', 'quantity_sold')) {
                $columnsToRemove[] = 'quantity_sold';
            }

            if (Schema::hasColumn('tickets', 'sale_start_date')) {
                $columnsToRemove[] = 'sale_start_date';
            }

            if (Schema::hasColumn('tickets', 'sale_end_date')) {
                $columnsToRemove[] = 'sale_end_date';
            }

            if (Schema::hasColumn('tickets', 'is_refundable')) {
                $columnsToRemove[] = 'is_refundable';
            }

            if (Schema::hasColumn('tickets', 'refund_fee_percentage')) {
                $columnsToRemove[] = 'refund_fee_percentage';
            }

            if (Schema::hasColumn('tickets', 'metadata')) {
                $columnsToRemove[] = 'metadata';
            }

            if (!empty($columnsToRemove)) {
                $table->dropColumn($columnsToRemove);
            }
        });
    }
};
