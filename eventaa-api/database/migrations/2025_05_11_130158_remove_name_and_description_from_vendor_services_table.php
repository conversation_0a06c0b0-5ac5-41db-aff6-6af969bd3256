<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vendor_services', function (Blueprint $table) {
            if (Schema::hasColumn('vendor_services', 'name')) {
                $table->dropColumn('name');
            }
            if (Schema::hasColumn('vendor_services', 'description')) {
                $table->dropColumn('description');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vendor_services', function (Blueprint $table) {
            if (!Schema::hasColumn('vendor_services', 'name')) {
                $table->string('name')->nullable();
            }
            if (!Schema::hasColumn('vendor_services', 'description')) {
                $table->text('description')->nullable();
            }
        });
    }
};
