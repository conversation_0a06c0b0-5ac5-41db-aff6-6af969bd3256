<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up()
    {
        // Update existing users with null role to 'customer'
        DB::table('users')
            ->whereNull('role')
            ->update(['role' => 'customer']);

        // Update existing users with null status to 'active'
        DB::table('users')
            ->whereNull('status')
            ->update(['status' => 'active']);
    }

    public function down()
    {
        // No need for down migration as we don't want to revert these changes
    }
};
