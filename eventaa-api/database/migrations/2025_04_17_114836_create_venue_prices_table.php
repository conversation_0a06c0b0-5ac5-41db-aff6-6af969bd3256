<?php

use App\Models\Venue;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('venue_prices', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(Venue::class)->constrained()->cascadeOnDelete();
            $table->decimal('price', 10, 2);
            $table->text('ammenities');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('venue_prices');
    }
};
