<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payment_transactions', function (Blueprint $table) {
            // Drop the existing foreign key constraint
            $table->dropForeign(['vendor_payment_method_id']);
            
            // Make the column nullable
            $table->unsignedBigInteger('vendor_payment_method_id')->nullable()->change();
            
            // Re-add the foreign key constraint with nullable
            $table->foreign('vendor_payment_method_id')
                  ->references('id')
                  ->on('vendor_payment_methods')
                  ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payment_transactions', function (Blueprint $table) {
            // Drop the foreign key constraint
            $table->dropForeign(['vendor_payment_method_id']);
            
            // Make the column not nullable
            $table->unsignedBigInteger('vendor_payment_method_id')->nullable(false)->change();
            
            // Re-add the foreign key constraint
            $table->foreign('vendor_payment_method_id')
                  ->references('id')
                  ->on('vendor_payment_methods')
                  ->onDelete('cascade');
        });
    }
};
