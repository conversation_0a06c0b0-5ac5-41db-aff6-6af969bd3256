<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vendor_prices', function (Blueprint $table) {
            if (!Schema::hasColumn('vendor_prices', 'name')) {
                $table->string('name')->nullable();
            }
            if (!Schema::hasColumn('vendor_prices', 'duration')) {
                $table->string('duration')->nullable();
            }
            if (!Schema::hasColumn('vendor_prices', 'active')) {
                $table->boolean('active')->default(true);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vendor_prices', function (Blueprint $table) {
            $table->dropColumn(['name', 'duration', 'active']);
        });
    }
};
