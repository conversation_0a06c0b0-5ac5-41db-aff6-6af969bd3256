<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tickets', function (Blueprint $table) {
            // Add missing columns that are referenced in the controller and model

            if (!Schema::hasColumn('tickets', 'name')) {
                $table->string('name')->nullable()->after('uuid');
            }

            if (!Schema::hasColumn('tickets', 'price')) {
                $table->decimal('price', 10, 2)->default(0)->after('name');
            }

            if (!Schema::hasColumn('tickets', 'banner')) {
                $table->string('banner')->nullable()->after('price');
            }

            if (!Schema::hasColumn('tickets', 'description')) {
                $table->text('description')->nullable()->after('banner');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tickets', function (Blueprint $table) {
            $columnsToRemove = [];

            if (Schema::hasColumn('tickets', 'name')) {
                $columnsToRemove[] = 'name';
            }

            if (Schema::hasColumn('tickets', 'price')) {
                $columnsToRemove[] = 'price';
            }

            if (Schema::hasColumn('tickets', 'banner')) {
                $columnsToRemove[] = 'banner';
            }

            if (Schema::hasColumn('tickets', 'description')) {
                $columnsToRemove[] = 'description';
            }

            if (!empty($columnsToRemove)) {
                $table->dropColumn($columnsToRemove);
            }
        });
    }
};
