<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tiers', function (Blueprint $table) {
            $table->decimal('platform_fee_percentage', 5, 2)->default(2.50)->after('refund_fee_percentage');
            $table->decimal('platform_fee_amount', 10, 2)->nullable()->after('platform_fee_percentage');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tiers', function (Blueprint $table) {
            $table->dropColumn(['platform_fee_percentage', 'platform_fee_amount']);
        });
    }
};
