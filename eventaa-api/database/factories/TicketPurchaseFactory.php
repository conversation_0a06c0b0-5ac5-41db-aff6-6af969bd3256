<?php

namespace Database\Factories;

use App\Models\Event;
use App\Models\Payment;
use App\Models\Ticket;
use App\Models\TicketPurchase;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\TicketPurchase>
 */
class TicketPurchaseFactory extends Factory
{
    protected $model = TicketPurchase::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $unitPrice = $this->faker->randomFloat(2, 10, 500);
        $quantity = $this->faker->numberBetween(1, 5);
        $totalAmount = $unitPrice * $quantity;
        $fees = $totalAmount * 0.03;
        $taxes = $totalAmount * 0.05;

        return [
            'user_id' => User::factory(),
            'event_id' => Event::factory(),
            'ticket_id' => Ticket::factory(),
            'payment_id' => Payment::factory(),
            'purchase_reference' => 'PUR-' . strtoupper(Str::random(12)) . '-' . time(),
            'quantity' => $quantity,
            'unit_price' => $unitPrice,
            'total_amount' => $totalAmount,
            'fees' => $fees,
            'taxes' => $taxes,
            'status' => $this->faker->randomElement(['pending', 'completed', 'cancelled', 'refunded']),
            'purchased_at' => $this->faker->optional(0.7)->dateTimeBetween('-1 month', 'now'),
            'attendee_name' => $this->faker->name(),
            'attendee_email' => $this->faker->email(),
            'attendee_phone' => $this->faker->optional(0.6)->phoneNumber(),
            'attendee_details' => [
                'special_requirements' => $this->faker->optional(0.3)->sentence(),
                'dietary_restrictions' => $this->faker->optional(0.2)->words(3, true),
            ],
            'cancellation_reason' => $this->faker->optional(0.1)->sentence(),
            'cancelled_at' => $this->faker->optional(0.1)->dateTimeBetween('-1 week', 'now'),
            'refund_amount' => $this->faker->optional(0.1)->randomFloat(2, 0, $totalAmount),
            'refunded_at' => $this->faker->optional(0.1)->dateTimeBetween('-1 week', 'now'),
            'metadata' => [
                'source' => $this->faker->randomElement(['web', 'mobile', 'api']),
                'ip_address' => $this->faker->ipv4(),
            ],
        ];
    }

    /**
     * Indicate that the purchase is completed.
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
            'purchased_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
        ]);
    }

    /**
     * Indicate that the purchase is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'purchased_at' => null,
        ]);
    }

    /**
     * Indicate that the purchase is refunded.
     */
    public function refunded(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'refunded',
            'refund_amount' => $attributes['total_amount'] * 0.9, // 10% refund fee
            'refunded_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
            'cancellation_reason' => 'Customer requested refund',
        ]);
    }
}
