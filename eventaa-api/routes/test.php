<?php

Route::get('/test-ticket-filters', function () {
    $event = \App\Models\Event::first();

    if (!$event) {
        return response()->json(['error' => 'No events found']);
    }

    $allPurchases = $event->ticketPurchases()->count();
    $scannedPurchases = $event->ticketPurchases()->scanned()->count();
    $unscannedPurchases = $event->ticketPurchases()->unscanned()->count();
    $boughtPurchases = $event->ticketPurchases()->bought()->count();
    $notBoughtPurchases = $event->ticketPurchases()->notBought()->count();

    return response()->json([
        'event_id' => $event->id,
        'event_title' => $event->title,
        'total_purchases' => $allPurchases,
        'scanned_purchases' => $scannedPurchases,
        'unscanned_purchases' => $unscannedPurchases,
        'bought_purchases' => $boughtPurchases,
        'not_bought_purchases' => $notBoughtPurchases,
        'scope_methods_working' => true
    ]);
});
