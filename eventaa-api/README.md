
# EventaHub API

A robust Laravel-based API for the EventaHub platform, providing comprehensive event management capabilities.

## 🚀 Quick Start

### Prerequisites
- PHP 8.2+
- Composer
- MySQL 8.0+
- Redis
- Node.js & NPM

### Getting Started

1. **Clone the Repository**
   ```bash
   git clone https://github.com/KellsWorks/eventaa-api.git
   cd eventaa-api
   ```

2. **Install Dependencies**
   ```bash
   composer install
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env
   # Edit .env file with your database and other configurations
   ```

4. **Generate Application Key**
   ```bash
   php artisan key:generate
   ```

5. **Database Setup**
   ```bash
   php artisan migrate --seed
   ```

6. **Start Services**
   ```bash
   # Start the Laravel server
   php artisan serve

   # In another terminal, start the queue worker
   php artisan queue:work

   # In another terminal, run the scheduler (optional)
   php artisan schedule:work
   ```

7. **Access the API**
   - API: `http://localhost:8000`

## 🔧 Configuration1. **Clone the Repository**
   ```bash
   git clone https://github.com/KellsWorks/eventaa-api.git
   cd eventaa-api
   ```

2. **Install Dependencies**
   ```bash
   composer install
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env
   # Edit .env file with your database and other configurations
   ```

4. **Generate Application Key**
   ```bash
   php artisan key:generate
   ```

5. **Database Setup**
   ```bash
   php artisan migrate --seed
   ```

6. **Start Services**
   ```bash
   # Start the Laravel server
   php artisan serve

   # In another terminal, start the queue worker
   php artisan queue:work

   # In another terminal, run the scheduler (optional)
   php artisan schedule:work
   ```

7. **Access the API**
   - API: `http://localhost:8000`

## 🔧 Configuration

### Environment Variables

Key environment variables to configure in your `.env` file:

```env
# Application
APP_NAME="EventaHub API"
APP_ENV=local
APP_DEBUG=true
APP_URL=http://localhost:8000

# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=eventaadb
DB_USERNAME=root
DB_PASSWORD=your_password

# Cache & Sessions
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

# Redis
REDIS_HOST=127.0.0.1
REDIS_PORT=6379

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
```

## 🏗 Development

### Running Tests
```bash
php artisan test
```

### Code Quality
```bash
# Run PHP CS Fixer
./vendor/bin/php-cs-fixer fix

# Run PHPStan
./vendor/bin/phpstan analyse
```

### Database Operations
```bash
# Fresh migration with seeding
php artisan migrate:fresh --seed

# Create new migration
php artisan make:migration create_example_table

# Create new seeder
php artisan make:seeder ExampleSeeder
```

## 📦 Production Deployment

### Automated Deployment with GitHub Actions

EventaHub API includes automated deployment to your VPS using GitHub Actions:

1. **Pre-Deployment Setup**
   ```bash
   # Run the pre-deployment check
   ./pre-deploy-check.sh
   ```

2. **Configure GitHub Secrets**
   Add these secrets to your GitHub repository (Settings → Secrets and variables → Actions):
   - `VPS_HOST`: Your VPS IP address or domain
   - `VPS_USERNAME`: SSH username for your VPS
   - `VPS_SSH_KEY`: Private SSH key for VPS access
   - `VPS_PORT`: SSH port (optional, defaults to 22)

3. **Deploy**
   Push to the `main` branch to trigger automatic deployment, or manually trigger from GitHub Actions.

4. **Manual Deployment**
   Use the deployment helper script on your VPS:
   ```bash
   ./deploy.sh deploy    # Deploy application
   ./deploy.sh status    # Check service status
   ./deploy.sh health    # Run health check
   ./deploy.sh logs      # View logs
   ```

See [DEPLOYMENT.md](DEPLOYMENT.md) for detailed deployment instructions.

### Server Setup

Use the included deployment scripts for production setup:

1. **Server Setup Script**
   ```bash
   chmod +x server-setup.sh
   sudo ./server-setup.sh
   ```

2. **Management Script**
   ```bash
   chmod +x manage.sh
   ./manage.sh start
   ```

3. **Manual Production Setup**
   ```bash
   # Set environment to production
   cp .env.example .env.production
   # Configure production settings

   # Install production dependencies
   composer install --optimize-autoloader --no-dev

   # Cache configurations
   php artisan config:cache
   php artisan route:cache
   php artisan view:cache

   # Set up web server (Nginx/Apache)
   # Configure SSL certificates
   # Set up process manager (Supervisor)
   ```

## 🛠 Troubleshooting

### Common Issues

**Permission Issues**
```bash
sudo chown -R www-data:www-data storage/
sudo chown -R www-data:www-data bootstrap/cache/
sudo chmod -R 755 storage/
sudo chmod -R 755 bootstrap/cache/
```

**Clear Cache**
```bash
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear
```

**Database Connection Issues**
- Ensure MySQL service is running: `sudo systemctl status mysql`
- Check database credentials in `.env`
- Verify database exists
- Test connection: `php artisan tinker` then `DB::connection()->getPdo();`

**Queue Jobs Not Processing**
```bash
# Restart queue worker
php artisan queue:restart

# Start queue worker
php artisan queue:work

# Check failed jobs
php artisan queue:failed
```

## 📚 API Documentation

### Authentication
The API uses Laravel Sanctum for authentication. Include the Bearer token in your requests:

```bash
Authorization: Bearer your-auth-token
```

### Base URL
- Development: `http://localhost:8000/api`
- Production: `https://api.eventahub.com/api`

### Key Endpoints
- `POST /auth/login` - User authentication
- `GET /events` - List events
- `POST /events` - Create event
- `GET /events/{id}` - Get event details
- `PUT /events/{id}` - Update event
- `DELETE /events/{id}` - Delete event

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support, email <EMAIL> or join our Slack channel.

---

**Built with ❤️ by the EventaHub Team**
