<?php
namespace Tests\Feature;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CategoryTest extends TestCase
{
    use HasFactory;
    use RefreshDatabase;

    /** @test */
    public function it_returns_paginated_categories()
    {
        $response = $this->json('GET', '/api/category/index');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'current_page',
                'data' => [],
            ]);
    }

}
