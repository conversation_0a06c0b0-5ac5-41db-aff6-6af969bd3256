<?php

namespace Tests\Feature;

use App\Models\Event;
use App\Models\Ticket;
use App\Models\TicketPurchase;
use App\Models\User;
use App\Models\Tier;
use App\Models\Payment;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class TicketPurchaseTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $event;
    protected $ticket;
    protected $tier;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->event = Event::factory()->create(['status' => 'published']);
        $this->tier = Tier::factory()->create(['event_id' => $this->event->id]);
        $this->ticket = Ticket::factory()->create([
            'event_id' => $this->event->id,
            'tier_id' => $this->tier->id,
            'status' => 'available',
            'quantity_available' => 100,
            'quantity_sold' => 0,
            'price' => 50.00
        ]);
    }

    /** @test */
    public function user_can_get_available_tickets_for_event()
    {
        $response = $this->actingAs($this->user, 'sanctum')
            ->getJson("/api/tickets/event/{$this->event->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'message',
                'data' => [
                    'event',
                    'tickets' => [
                        '*' => [
                            'id',
                            'name',
                            'price',
                            'description',
                            'quantity_available',
                            'quantity_sold',
                            'remaining_quantity',
                            'is_available'
                        ]
                    ]
                ]
            ]);
    }

    /** @test */
    public function user_can_purchase_tickets()
    {
        $purchaseData = [
            'event_id' => $this->event->id,
            'tickets' => [
                [
                    'ticket_id' => $this->ticket->id,
                    'quantity' => 2
                ]
            ],
            'attendee_details' => [
                [
                    'name' => 'John Doe',
                    'email' => '<EMAIL>',
                    'phone' => '+1234567890'
                ]
            ],
            'payment_method' => 'paychangu'
        ];

        $response = $this->actingAs($this->user, 'sanctum')
            ->postJson('/api/tickets/purchase', $purchaseData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'message',
                'data' => [
                    'purchase_reference',
                    'payment',
                    'purchases',
                    'total_amount',
                    'breakdown'
                ]
            ]);

        // Verify database changes
        $this->assertDatabaseHas('ticket_purchases', [
            'user_id' => $this->user->id,
            'event_id' => $this->event->id,
            'ticket_id' => $this->ticket->id,
            'quantity' => 2,
            'status' => 'pending'
        ]);

        $this->assertDatabaseHas('payments', [
            'user_id' => $this->user->id,
            'type' => 'ticket_purchase',
            'status' => 'pending'
        ]);

        // Verify ticket quantity is reserved
        $this->ticket->refresh();
        $this->assertEquals(2, $this->ticket->quantity_sold);
    }

    /** @test */
    public function user_cannot_purchase_more_tickets_than_available()
    {
        // Set ticket to have only 1 available
        $this->ticket->update(['quantity_available' => 1]);

        $purchaseData = [
            'event_id' => $this->event->id,
            'tickets' => [
                [
                    'ticket_id' => $this->ticket->id,
                    'quantity' => 2 // Requesting more than available
                ]
            ],
            'payment_method' => 'paychangu'
        ];

        $response = $this->actingAs($this->user, 'sanctum')
            ->postJson('/api/tickets/purchase', $purchaseData);

        $response->assertStatus(500); // Should fail in transaction
    }

    /** @test */
    public function user_can_confirm_purchase_after_payment()
    {
        // Create a completed purchase
        $payment = Payment::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'pending'
        ]);

        $purchase = TicketPurchase::factory()->create([
            'user_id' => $this->user->id,
            'event_id' => $this->event->id,
            'ticket_id' => $this->ticket->id,
            'payment_id' => $payment->id,
            'status' => 'pending'
        ]);

        $confirmData = [
            'purchase_reference' => $purchase->purchase_reference,
            'payment_confirmation' => 'PAYMENT_CONFIRMED_123'
        ];

        $response = $this->actingAs($this->user, 'sanctum')
            ->postJson('/api/tickets/confirm-purchase', $confirmData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'message',
                'data' => [
                    'purchase_reference',
                    'tickets',
                    'event'
                ]
            ]);

        // Verify purchase is completed
        $purchase->refresh();
        $this->assertEquals('completed', $purchase->status);
        $this->assertNotNull($purchase->purchased_at);

        $payment->refresh();
        $this->assertEquals('completed', $payment->status);
    }

    /** @test */
    public function user_can_get_their_purchases()
    {
        TicketPurchase::factory()->count(3)->create([
            'user_id' => $this->user->id
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
            ->getJson('/api/tickets/my-purchases');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'message',
                'data' => [
                    'data' => [
                        '*' => [
                            'id',
                            'purchase_reference',
                            'quantity',
                            'total_amount',
                            'status',
                            'event',
                            'ticket'
                        ]
                    ]
                ]
            ]);
    }

    /** @test */
    public function user_can_request_refund_for_eligible_purchase()
    {
        $purchase = TicketPurchase::factory()->completed()->create([
            'user_id' => $this->user->id,
            'event_id' => $this->event->id,
            'ticket_id' => $this->ticket->id
        ]);

        // Set event date in future to make refund eligible
        $this->event->update(['start' => now()->addDays(10)]);

        $refundData = [
            'purchase_id' => $purchase->id,
            'reason' => 'Changed my mind'
        ];

        $response = $this->actingAs($this->user, 'sanctum')
            ->postJson('/api/tickets/request-refund', $refundData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'message',
                'data' => [
                    'refund_amount',
                    'original_amount',
                    'refund_fee'
                ]
            ]);

        // Verify refund processed
        $purchase->refresh();
        $this->assertEquals('refunded', $purchase->status);
        $this->assertNotNull($purchase->refunded_at);
    }

    /** @test */
    public function user_cannot_refund_past_event_tickets()
    {
        $purchase = TicketPurchase::factory()->completed()->create([
            'user_id' => $this->user->id,
            'event_id' => $this->event->id,
            'ticket_id' => $this->ticket->id
        ]);

        // Set event date in past
        $this->event->update(['start_date' => now()->subDays(1)]);

        $refundData = [
            'purchase_id' => $purchase->id,
            'reason' => 'Event has passed'
        ];

        $response = $this->actingAs($this->user, 'sanctum')
            ->postJson('/api/tickets/request-refund', $refundData);

        $response->assertStatus(400)
            ->assertJson([
                'message' => 'This purchase cannot be refunded'
            ]);
    }

    /** @test */
    public function validates_required_fields_for_purchase()
    {
        $response = $this->actingAs($this->user, 'sanctum')
            ->postJson('/api/tickets/purchase', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['event_id', 'tickets', 'payment_method']);
    }

    /** @test */
    public function cannot_purchase_tickets_for_unpublished_event()
    {
        $this->event->update(['status' => 'draft']);

        $purchaseData = [
            'event_id' => $this->event->id,
            'tickets' => [
                [
                    'ticket_id' => $this->ticket->id,
                    'quantity' => 1
                ]
            ],
            'payment_method' => 'paychangu'
        ];

        $response = $this->actingAs($this->user, 'sanctum')
            ->postJson('/api/tickets/purchase', $purchaseData);

        $response->assertStatus(400)
            ->assertJson([
                'message' => 'Event is not available for ticket purchase'
            ]);
    }
}
