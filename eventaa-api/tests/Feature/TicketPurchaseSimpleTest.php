<?php

namespace Tests\Feature;

use App\Models\Event;
use App\Models\Ticket;
use App\Models\TicketPurchase;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Schema;
use Tests\TestCase;

class TicketPurchaseSimpleTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Create only the tables we need for ticket purchase testing
        $this->createMinimalSchema();
    }

    private function createMinimalSchema(): void
    {
        // Create users table
        Schema::create('users', function ($table) {
            $table->id();
            $table->string('name');
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password');
            $table->rememberToken();
            $table->timestamps();
        });

        // Create categories table (needed for events)
        Schema::create('categories', function ($table) {
            $table->id();
            $table->string('name');
            $table->string('slug');
            $table->timestamps();
        });

        // Create events table
        Schema::create('events', function ($table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('category_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('location')->nullable();
            $table->dateTime('start_date');
            $table->dateTime('end_date');
            $table->string('status')->default('draft');
            $table->timestamps();
        });

        // Create tickets table with all needed columns
        Schema::create('tickets', function ($table) {
            $table->id();
            $table->foreignId('event_id')->constrained()->onDelete('cascade');
            $table->string('uuid')->unique();
            $table->string('name')->nullable();
            $table->decimal('price', 10, 2)->default(0);
            $table->string('banner')->nullable();
            $table->text('description')->nullable();
            $table->boolean('scanned')->default(false);
            $table->timestamp('scanned_at')->nullable();
            $table->string('status')->default('available');
            $table->integer('quantity_available')->default(1);
            $table->integer('quantity_sold')->default(0);
            $table->datetime('sale_start_date')->nullable();
            $table->datetime('sale_end_date')->nullable();
            $table->boolean('is_refundable')->default(true);
            $table->decimal('refund_fee_percentage', 5, 2)->default(0);
            $table->json('metadata')->nullable();
            $table->timestamps();
        });

        // Create ticket_purchases table
        Schema::create('ticket_purchases', function ($table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('event_id')->constrained()->onDelete('cascade');
            $table->foreignId('ticket_id')->constrained()->onDelete('cascade');
            $table->string('purchase_reference')->unique();
            $table->integer('quantity')->default(1);
            $table->decimal('unit_price', 10, 2);
            $table->decimal('total_amount', 10, 2);
            $table->decimal('fees', 10, 2)->default(0);
            $table->decimal('taxes', 10, 2)->default(0);
            $table->string('status')->default('pending');
            $table->datetime('purchased_at')->nullable();
            $table->string('attendee_name')->nullable();
            $table->string('attendee_email')->nullable();
            $table->string('attendee_phone')->nullable();
            $table->json('attendee_details')->nullable();
            $table->text('cancellation_reason')->nullable();
            $table->datetime('cancelled_at')->nullable();
            $table->decimal('refund_amount', 10, 2)->nullable();
            $table->datetime('refunded_at')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();
        });
    }

    private function createTestData()
    {
        // Create a category
        $category = [
            'id' => 1,
            'name' => 'Technology',
            'slug' => 'technology',
            'created_at' => now(),
            'updated_at' => now(),
        ];
        \DB::table('categories')->insert($category);

        // Create a user
        $user = [
            'id' => 1,
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'created_at' => now(),
            'updated_at' => now(),
        ];
        \DB::table('users')->insert($user);

        // Create an event
        $event = [
            'id' => 1,
            'user_id' => 1,
            'category_id' => 1,
            'name' => 'Test Event',
            'description' => 'A test event',
            'location' => 'Test Location',
            'start_date' => now()->addDays(30),
            'end_date' => now()->addDays(31),
            'status' => 'published',
            'created_at' => now(),
            'updated_at' => now(),
        ];
        \DB::table('events')->insert($event);

        // Create a ticket
        $ticket = [
            'id' => 1,
            'event_id' => 1,
            'uuid' => 'test-ticket-uuid',
            'name' => 'General Admission',
            'price' => 50.00,
            'description' => 'Standard ticket',
            'scanned' => false,
            'status' => 'available',
            'quantity_available' => 100,
            'quantity_sold' => 0,
            'sale_start_date' => now()->subDays(10),
            'sale_end_date' => now()->addDays(20),
            'is_refundable' => true,
            'refund_fee_percentage' => 5.00,
            'created_at' => now(),
            'updated_at' => now(),
        ];
        \DB::table('tickets')->insert($ticket);

        return (object) [
            'user' => (object) $user,
            'event' => (object) $event,
            'ticket' => (object) $ticket,
        ];
    }

    /** @test */
    public function user_can_get_available_tickets_for_event()
    {
        $data = $this->createTestData();

        $response = $this->actingAs(User::find(1))
            ->getJson("/api/events/{$data->event->id}/tickets");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'price',
                        'description',
                        'quantity_available',
                        'status'
                    ]
                ]
            ])
            ->assertJsonPath('data.0.name', 'General Admission')
            ->assertJsonPath('data.0.price', 50.00);
    }

    /** @test */
    public function user_can_purchase_tickets()
    {
        $data = $this->createTestData();

        $purchaseData = [
            'ticket_id' => 1,
            'quantity' => 2,
            'attendee_name' => 'John Doe',
            'attendee_email' => '<EMAIL>',
            'attendee_phone' => '+1234567890'
        ];

        $response = $this->actingAs(User::find(1))
            ->postJson("/api/events/{$data->event->id}/tickets/purchase", $purchaseData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'message',
                'data' => [
                    'purchase_reference',
                    'status',
                    'total_amount',
                    'quantity'
                ]
            ]);

        // Check if ticket_purchase was created in database
        $this->assertDatabaseHas('ticket_purchases', [
            'user_id' => 1,
            'event_id' => 1,
            'ticket_id' => 1,
            'quantity' => 2,
            'status' => 'pending'
        ]);
    }

    /** @test */
    public function user_cannot_purchase_more_tickets_than_available()
    {
        $data = $this->createTestData();

        $purchaseData = [
            'ticket_id' => 1,
            'quantity' => 150, // More than available (100)
            'attendee_name' => 'John Doe',
            'attendee_email' => '<EMAIL>'
        ];

        $response = $this->actingAs(User::find(1))
            ->postJson("/api/events/{$data->event->id}/tickets/purchase", $purchaseData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['quantity']);
    }

    /** @test */
    public function validates_required_fields_for_purchase()
    {
        $data = $this->createTestData();

        $response = $this->actingAs(User::find(1))
            ->postJson("/api/events/{$data->event->id}/tickets/purchase", []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['ticket_id', 'quantity', 'attendee_name', 'attendee_email']);
    }
}
