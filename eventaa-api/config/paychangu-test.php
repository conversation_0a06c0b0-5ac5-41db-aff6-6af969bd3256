<?php

return [
    /*
    |--------------------------------------------------------------------------
    | PayChangu Test Configuration
    |--------------------------------------------------------------------------
    |
    | This configuration file contains test data and settings for PayChangu
    | integration testing. Use this for development and testing purposes only.
    |
    */

    'test_credentials' => [
        'api_key' => env('PAYCHANGU_TEST_API_KEY', 'test_api_key'),
        'secret_key' => env('PAYCHANGU_TEST_SECRET_KEY', 'test_secret_key'),
        'merchant_id' => env('PAYCHANGU_TEST_MERCHANT_ID', 'test_merchant_id'),
        'webhook_secret' => env('PAYCHANGU_TEST_WEBHOOK_SECRET', 'test_webhook_secret'),
    ],

    'test_mobile_numbers' => [
        'mpamba' => [
            'success' => '+265999123456',
            'insufficient_funds' => '+265999123457',
            'invalid' => '+265999123458',
        ],
        'airtel_money' => [
            'success' => '+************',
            'insufficient_funds' => '+265888123457',
            'invalid' => '+265888123458',
        ],
    ],

    'test_cards' => [
        'visa' => [
            'success' => [
                'number' => '****************',
                'expiry_month' => '12',
                'expiry_year' => '2025',
                'cvv' => '123',
            ],
            'declined' => [
                'number' => '****************',
                'expiry_month' => '12',
                'expiry_year' => '2025',
                'cvv' => '123',
            ],
            'insufficient_funds' => [
                'number' => '****************',
                'expiry_month' => '12',
                'expiry_year' => '2025',
                'cvv' => '123',
            ],
        ],
    ],

    'test_amounts' => [
        'small' => 100,      // 100 MWK
        'medium' => 1000,    // 1,000 MWK
        'large' => 10000,    // 10,000 MWK
        'subscription' => 5000, // 5,000 MWK
        'ticket' => 2500,    // 2,500 MWK
    ],

    'webhook_test_payloads' => [
        'successful_payment' => [
            'event_type' => 'api.charge.payment',
            'reference' => 'TEST-SUCCESS-123',
            'status' => 'success',
            'amount' => 1000,
            'currency' => 'MWK',
            'customer' => [
                'email' => '<EMAIL>',
                'phone' => '+265999123456',
            ],
            'meta' => [
                'type' => 'test',
                'test_scenario' => 'success',
            ],
        ],
        'failed_payment' => [
            'event_type' => 'api.charge.payment',
            'reference' => 'TEST-FAILED-123',
            'status' => 'failed',
            'amount' => 1000,
            'currency' => 'MWK',
            'customer' => [
                'email' => '<EMAIL>',
                'phone' => '+265999123456',
            ],
            'meta' => [
                'type' => 'test',
                'test_scenario' => 'failed',
            ],
        ],
        'pending_payment' => [
            'event_type' => 'api.charge.payment',
            'reference' => 'TEST-PENDING-123',
            'status' => 'pending',
            'amount' => 1000,
            'currency' => 'MWK',
            'customer' => [
                'email' => '<EMAIL>',
                'phone' => '+265999123456',
            ],
            'meta' => [
                'type' => 'test',
                'test_scenario' => 'pending',
            ],
        ],
    ],

    'test_scenarios' => [
        'subscription_payment' => [
            'description' => 'Test subscription payment flow',
            'plan_id' => 1,
            'amount' => 5000,
            'currency' => 'MWK',
            'payment_method' => 'mpamba',
            'phone_number' => '+265999123456',
        ],
        'ticket_purchase' => [
            'description' => 'Test ticket purchase flow',
            'tickets' => [
                [
                    'ticket_id' => 1,
                    'quantity' => 2,
                ],
            ],
            'payment_method' => 'airtel_money',
            'phone_number' => '+************',
        ],
        'booking_payment' => [
            'description' => 'Test booking payment flow',
            'booking_id' => 1,
            'amount' => 10000,
            'currency' => 'MWK',
            'payment_method' => 'bank_transfer',
        ],
    ],

    'api_endpoints' => [
        'base_url' => env('PAYCHANGU_TEST_BASE_URL', 'https://api.paychangu.com'),
        'payment' => '/payment',
        'verify' => '/verify-payment',
        'charge' => '/charge',
        'operators' => '/operators',
    ],

    'timeout_settings' => [
        'payment_timeout_minutes' => 30,
        'verification_delay_minutes' => 2,
        'max_verification_attempts' => 5,
        'retry_delay_seconds' => 30,
    ],

    'notification_settings' => [
        'send_email_notifications' => true,
        'send_sms_notifications' => false,
        'send_push_notifications' => true,
        'webhook_retry_attempts' => 3,
    ],

    'logging' => [
        'log_requests' => true,
        'log_responses' => true,
        'log_webhooks' => true,
        'log_level' => 'debug',
        'mask_sensitive_data' => true,
    ],

    'rate_limiting' => [
        'payment_requests_per_minute' => 60,
        'verification_requests_per_minute' => 120,
        'webhook_requests_per_minute' => 300,
    ],

    'security' => [
        'require_https' => true,
        'verify_webhook_signatures' => true,
        'encrypt_sensitive_data' => true,
        'allowed_webhook_ips' => [
            // PayChangu webhook IPs (update with actual IPs)
            '127.0.0.1',
            '::1',
        ],
    ],

    'monitoring' => [
        'track_payment_metrics' => true,
        'alert_on_high_failure_rate' => true,
        'failure_rate_threshold' => 0.1, // 10%
        'alert_email' => env('PAYMENT_ALERT_EMAIL', '<EMAIL>'),
    ],
];
