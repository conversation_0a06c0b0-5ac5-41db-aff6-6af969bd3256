<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password Redirection</title>
</head>
<body>
    <a id="intentButton" href="eventaa://reset-password">
        Redirecting you to EventaHub app, click here if not automatically redirected...
    </a>
    <script>
        window.onload = function() {
            function getUrlParameter(name) {
                name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
                var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
                var results = regex.exec(location.search);
                return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
            }

            var email = getUrlParameter('email');
            var token = getUrlParameter('token');

            var intentButton = document.getElementById('intentButton');
            intentButton.href = 'eventaa://reset-password?email=' + encodeURIComponent(email) + '&token=' + encodeURIComponent(token);

            intentButton.click();
        };
    </script>
</body>
</html>
