<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Receipt - {{ $transaction_id }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
            line-height: 1.6;
        }

        .header {
            text-align: center;
            border-bottom: 2px solid #dc2626;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }

        .company-name {
            font-size: 28px;
            font-weight: bold;
            color: #dc2626;
            margin-bottom: 5px;
        }

        .company-details {
            font-size: 12px;
            color: #666;
        }

        .receipt-title {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            margin: 30px 0;
            color: #333;
        }

        .receipt-info {
            display: table;
            width: 100%;
            margin-bottom: 30px;
        }

        .receipt-info-row {
            display: table-row;
        }

        .receipt-info-label {
            display: table-cell;
            font-weight: bold;
            padding: 8px 0;
            width: 40%;
        }

        .receipt-info-value {
            display: table-cell;
            padding: 8px 0;
        }

        .status {
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status.completed {
            background-color: #dcfce7;
            color: #166534;
        }

        .status.pending {
            background-color: #fef3c7;
            color: #92400e;
        }

        .status.failed {
            background-color: #fee2e2;
            color: #991b1b;
        }

        .status.refunded {
            background-color: #f3f4f6;
            color: #374151;
        }

        .amount {
            font-size: 20px;
            font-weight: bold;
            color: #dc2626;
        }

        .ticket-details {
            background-color: #f9fafb;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .ticket-details h3 {
            margin-top: 0;
            color: #374151;
        }

        .footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            text-align: center;
            font-size: 12px;
            color: #666;
        }

        .thank-you {
            font-size: 16px;
            font-weight: bold;
            color: #dc2626;
            text-align: center;
            margin: 30px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">{{ $company_name }}</div>
        <div class="company-details">
            {{ $company_address }}<br>
            Email: {{ $company_email }} | Phone: {{ $company_phone }}
        </div>
    </div>

    <div class="receipt-title">PAYMENT RECEIPT</div>

    <div class="receipt-info">
        <div class="receipt-info-row">
            <div class="receipt-info-label">Transaction ID:</div>
            <div class="receipt-info-value">{{ $transaction_id }}</div>
        </div>

        <div class="receipt-info-row">
            <div class="receipt-info-label">Amount:</div>
            <div class="receipt-info-value amount">{{ $amount }}</div>
        </div>

        <div class="receipt-info-row">
            <div class="receipt-info-label">Status:</div>
            <div class="receipt-info-value">
                <span class="status {{ strtolower($status) }}">{{ $status }}</span>
            </div>
        </div>

        <div class="receipt-info-row">
            <div class="receipt-info-label">Payment Date:</div>
            <div class="receipt-info-value">{{ $payment_date }}</div>
        </div>

        <div class="receipt-info-row">
            <div class="receipt-info-label">Payment Method:</div>
            <div class="receipt-info-value">{{ $payment_method }}</div>
        </div>

        <div class="receipt-info-row">
            <div class="receipt-info-label">Payment Type:</div>
            <div class="receipt-info-value">{{ $payment_type }}</div>
        </div>

        <div class="receipt-info-row">
            <div class="receipt-info-label">Customer Name:</div>
            <div class="receipt-info-value">{{ $user_name }}</div>
        </div>

        <div class="receipt-info-row">
            <div class="receipt-info-label">Customer Email:</div>
            <div class="receipt-info-value">{{ $user_email }}</div>
        </div>
    </div>

    @if($payment_type === 'Ticket purchase' && $event_name !== 'N/A')
    <div class="ticket-details">
        <h3>Event Details</h3>
        <div class="receipt-info">
            <div class="receipt-info-row">
                <div class="receipt-info-label">Event Name:</div>
                <div class="receipt-info-value">{{ $event_name }}</div>
            </div>

            @if(!empty($ticket_details))
            <div class="receipt-info-row">
                <div class="receipt-info-label">Ticket Type:</div>
                <div class="receipt-info-value">{{ $ticket_details['name'] ?? 'N/A' }}</div>
            </div>

            <div class="receipt-info-row">
                <div class="receipt-info-label">Quantity:</div>
                <div class="receipt-info-value">{{ $ticket_details['quantity'] ?? 1 }}</div>
            </div>

            <div class="receipt-info-row">
                <div class="receipt-info-label">Unit Price:</div>
                <div class="receipt-info-value">{{ $ticket_details['formatted_price'] ?? ($currency_symbol . number_format($ticket_details['price'] ?? 0, 2)) }}</div>
            </div>
            @endif
        </div>
    </div>
    @endif

    <div class="thank-you">
        Thank you for your payment!
    </div>

    <div class="footer">
        <p>This is an automatically generated receipt.</p>
        <p>For any inquiries, please contact us at {{ $company_email }}</p>
        <p>Generated on {{ date('F d, Y h:i A') }}</p>
    </div>
</body>
</html>
