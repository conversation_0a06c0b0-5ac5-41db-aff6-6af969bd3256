@extends('emails.newsletter.base')

@section('newsletter-content')
    <div class="email-greeting">Hello there!</div>

    <div class="email-message">
        Exciting news! New venues have joined EventaHub and they're ready to host amazing events.
    </div>

    @if(!empty($content))
        <div class="email-info-box" style="border-left-color: #8b5cf6;">
            <div class="email-info-title">🏢 New Venues on EventaHub</div>

            @foreach($content as $venue)
                <div style="margin-bottom: 20px; padding-bottom: 20px; border-bottom: 1px solid #e5e7eb;">
                    <div style="font-size: 18px; font-weight: 600; color: #ef4444; margin-bottom: 8px;">
                        {{ $venue['name'] ?? 'Venue Name' }}
                    </div>

                    @if(!empty($venue['location']) || !empty($venue['city']))
                        <div class="email-info-item">
                            <span class="email-info-label">📍 Location:</span>
                            {{ $venue['location'] ?? '' }}{{ !empty($venue['location']) && !empty($venue['city']) ? ', ' : '' }}{{ $venue['city'] ?? '' }}
                        </div>
                    @endif

                    @if(!empty($venue['capacity']))
                        <div class="email-info-item">
                            <span class="email-info-label">👥 Capacity:</span> Up to {{ $venue['capacity'] }} people
                        </div>
                    @endif

                    @if(!empty($venue['description']))
                        <div style="margin-top: 8px; color: #6b7280; font-size: 14px; line-height: 1.6;">
                            {{ Str::limit(strip_tags($venue['description']), 120) }}
                        </div>
                    @endif

                    @if(!empty($venue['amenities']))
                        <div style="margin-top: 8px;">
                            <div style="font-size: 12px; color: #6b7280; margin-bottom: 4px;">Amenities:</div>
                            <div style="font-size: 14px; color: #374151;">
                                @if(is_array($venue['amenities']))
                                    @foreach(array_slice($venue['amenities'], 0, 3) as $amenity)
                                        <span style="background: #f3f4f6; padding: 2px 8px; border-radius: 12px; font-size: 12px; margin-right: 4px;">
                                            {{ $amenity }}
                                        </span>
                                    @endforeach
                                    @if(count($venue['amenities']) > 3)
                                        <span style="color: #6b7280; font-size: 12px;">+{{ count($venue['amenities']) - 3 }} more</span>
                                    @endif
                                @else
                                    {{ $venue['amenities'] }}
                                @endif
                            </div>
                        </div>
                    @endif

                    <div style="margin-top: 12px;">
                        <a href="{{ config('app.frontend_url', 'https://eventahub.com') }}/venues/{{ $venue['slug'] ?? $venue['id'] }}"
                           style="color: #ef4444; text-decoration: none; font-weight: 600; font-size: 14px;">
                            View Venue →
                        </a>
                    </div>
                </div>
            @endforeach
        </div>
    @else
        <div class="email-info-box">
            <div class="email-info-title">🔍 No New Venues This Week</div>
            <div style="color: #6b7280; font-size: 14px; line-height: 1.6;">
                But don't worry! Browse our existing amazing venues that are ready to host your next event.
            </div>
        </div>
    @endif

    <div class="email-info-box" style="border-left-color: #10b981;">
        <div class="email-info-title">🎪 Planning an Event?</div>
        <div style="color: #6b7280; font-size: 14px; line-height: 1.6;">
            These venues offer fresh opportunities for your next event. Whether you're planning a conference, wedding, or party, we have the perfect space for you.
        </div>
    </div>

    <div class="email-button-wrapper">
        <a href="{{ config('app.frontend_url', 'https://eventahub.com') }}/venues" class="email-button">
            Browse All Venues
        </a>
    </div>

    <div class="email-divider"></div>

    <div class="email-message">
        Do you own a venue?
        <a href="{{ config('app.frontend_url', 'https://eventahub.com') }}/vendors/request" style="color: #ef4444; text-decoration: none;">
            List your venue
        </a>
        and reach thousands of event organizers looking for the perfect space!
    </div>
@endsection
