@extends('emails.newsletter.base')

@section('newsletter-content')
    <div class="email-greeting">Hello there!</div>

    <div class="email-message">
        Here's your weekly roundup of the latest events, recommendations, and new venues on EventaHub!
    </div>

    @if(!empty($content['latest_events']))
        <div class="email-info-box">
            <div class="email-info-title">🆕 Latest Events</div>

            @foreach($content['latest_events'] as $event)
                <div style="margin-bottom: 20px; padding-bottom: 20px; border-bottom: 1px solid #e5e7eb;">
                    <div style="font-size: 18px; font-weight: 600; color: #ef4444; margin-bottom: 8px;">
                        {{ $event['title'] ?? 'Event Title' }}
                    </div>
                    <div class="email-info-item">
                        <span class="email-info-label">📅 Date:</span>
                        {{ isset($event['start_date']) ? \Carbon\Carbon::parse($event['start_date'])->format('l, F j, Y \a\t g:i A') : 'Date TBA' }}
                    </div>
                    @if(!empty($event['location']))
                        <div class="email-info-item">
                            <span class="email-info-label">📍 Location:</span> {{ $event['location'] }}
                        </div>
                    @endif
                    @if(!empty($event['description']))
                        <div style="margin-top: 8px; color: #6b7280; font-size: 14px;">
                            {{ Str::limit(strip_tags($event['description']), 100) }}
                        </div>
                    @endif
                    <div style="margin-top: 12px;">
                        <a href="{{ config('app.frontend_url', 'https://eventahub.com') }}/events/{{ $event['slug'] ?? $event['id'] }}"
                           style="color: #ef4444; text-decoration: none; font-weight: 600; font-size: 14px;">
                            View Event →
                        </a>
                    </div>
                </div>
            @endforeach
        </div>
    @endif

    @if(!empty($content['recommended_events']))
        <div class="email-info-box" style="border-left-color: #f59e0b;">
            <div class="email-info-title">⭐ Recommended for You</div>

            @foreach($content['recommended_events'] as $event)
                <div style="margin-bottom: 20px; padding-bottom: 20px; border-bottom: 1px solid #e5e7eb;">
                    <div style="font-size: 18px; font-weight: 600; color: #ef4444; margin-bottom: 8px;">
                        {{ $event['title'] ?? 'Event Title' }}
                    </div>
                    <div class="email-info-item">
                        <span class="email-info-label">📅 Date:</span>
                        {{ isset($event['start_date']) ? \Carbon\Carbon::parse($event['start_date'])->format('l, F j, Y \a\t g:i A') : 'Date TBA' }}
                    </div>
                    @if(!empty($event['location']))
                        <div class="email-info-item">
                            <span class="email-info-label">📍 Location:</span> {{ $event['location'] }}
                        </div>
                    @endif
                    <div style="margin-top: 12px;">
                        <a href="{{ config('app.frontend_url', 'https://eventahub.com') }}/events/{{ $event['slug'] ?? $event['id'] }}"
                           style="color: #ef4444; text-decoration: none; font-weight: 600; font-size: 14px;">
                            View Event →
                        </a>
                    </div>
                </div>
            @endforeach
        </div>
    @endif

    @if(!empty($content['new_venues']))
        <div class="email-info-box" style="border-left-color: #8b5cf6;">
            <div class="email-info-title">🏢 New Venues</div>

            @foreach($content['new_venues'] as $venue)
                <div style="margin-bottom: 15px;">
                    <div style="font-weight: 600; color: #111827;">{{ $venue['name'] ?? 'Venue Name' }}</div>
                    @if(!empty($venue['location']))
                        <div style="color: #6b7280; font-size: 14px;">📍 {{ $venue['location'] }}</div>
                    @endif
                    @if(!empty($venue['description']))
                        <div style="color: #6b7280; font-size: 14px; margin-top: 4px;">
                            {{ Str::limit(strip_tags($venue['description']), 80) }}
                        </div>
                    @endif
                </div>
            @endforeach
        </div>
    @endif

    <div class="email-button-wrapper">
        <a href="{{ config('app.frontend_url', 'https://eventahub.com') }}/events" class="email-button">
            Explore All Events
        </a>
    </div>

    <div class="email-divider"></div>

    <div class="email-message">
        Thanks for being part of the EventaHub community! 🎉
    </div>
@endsection

            @foreach($content['recommended_events'] as $event)
                <div class="event-card">
                    <div class="event-title">{{ $event['title'] ?? 'Event Title' }}</div>
                    <div class="event-date">
                        📅 {{ isset($event['start_date']) ? \Carbon\Carbon::parse($event['start_date'])->format('M d, Y \a\t g:i A') : 'Date TBA' }}
                    </div>
                    @if(!empty($event['location']))
                        <div class="event-date">📍 {{ $event['location'] }}</div>
                    @endif
                    @if(!empty($event['description']))
                        <div class="event-description">
                            {{ Str::limit(strip_tags($event['description']), 100) }}
                        </div>
                    @endif
                    <a href="{{ env('FRONTEND_URL') }}/events/{{ $event['slug'] ?? $event['id'] }}" class="btn">
                        Get Tickets
                    </a>
                </div>
            @endforeach
        </div>
    @endif

    @if(!empty($content['new_venues']))
        <div class="section">
            <h2 class="section-title">🏢 New Venues</h2>

            @foreach($content['new_venues'] as $venue)
                <div class="venue-card">
                    <div class="venue-name">{{ $venue['name'] ?? 'Venue Name' }}</div>
                    @if(!empty($venue['location']) || !empty($venue['city']))
                        <div class="venue-location">
                            📍 {{ $venue['location'] ?? '' }}{{ !empty($venue['location']) && !empty($venue['city']) ? ', ' : '' }}{{ $venue['city'] ?? '' }}
                        </div>
                    @endif
                    @if(!empty($venue['description']))
                        <div class="venue-description">
                            {{ Str::limit(strip_tags($venue['description']), 100) }}
                        </div>
                    @endif
                    <a href="{{ env('FRONTEND_URL') }}/venues/{{ $venue['slug'] ?? $venue['id'] }}" class="btn">
                        View Venue
                    </a>
                </div>
            @endforeach
        </div>
    @endif

    <div class="section">
        <h2 class="section-title">📊 This Week's Stats</h2>
        <p>
            @if(!empty($content['latest_events']))
                • {{ count($content['latest_events']) }} new events added<br>
            @endif
            @if(!empty($content['recommended_events']))
                • {{ count($content['recommended_events']) }} events recommended for you<br>
            @endif
            @if(!empty($content['new_venues']))
                • {{ count($content['new_venues']) }} new venues joined our platform<br>
            @endif
        </p>
    </div>

    <div class="section">
        <h2 class="section-title">🚀 Quick Actions</h2>
        <p>Make the most of your week with these quick actions:</p>
        <a href="{{ env('FRONTEND_URL') }}/events" class="btn" style="margin-right: 10px;">Browse Events</a>
        <a href="{{ env('FRONTEND_URL') }}/venues" class="btn">Find Venues</a>
    </div>
@endsection
