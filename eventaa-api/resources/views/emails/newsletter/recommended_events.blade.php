@extends('emails.newsletter.base')

@section('title', 'Recommended Events - EventaHub Malawi')

@extends('emails.newsletter.base')

@section('newsletter-content')
    <div class="email-greeting">Hello there!</div>

    <div class="email-message">
        Based on your interests and past events, here are some events we think you'll love!
    </div>

    @if(!empty($events))
        <div class="email-info-box" style="border-left-color: #f59e0b;">
            <div class="email-info-title">⭐ Events Recommended Just For You</div>

            @foreach($events as $event)
                <div style="margin-bottom: 20px; padding-bottom: 20px; border-bottom: 1px solid #e5e7eb;">
                    <div style="font-size: 18px; font-weight: 600; color: #ef4444; margin-bottom: 8px;">
                        {{ $event['title'] ?? 'Event Title' }}
                    </div>
                    <div class="email-info-item">
                        <span class="email-info-label">📅 Date:</span>
                        {{ isset($event['start_date']) ? \Carbon\Carbon::parse($event['start_date'])->format('l, F j, Y \a\t g:i A') : 'Date TBA' }}
                    </div>
                    @if(!empty($event['location']))
                        <div class="email-info-item">
                            <span class="email-info-label">📍 Location:</span> {{ $event['location'] }}
                        </div>
                    @endif
                    @if(!empty($event['price']))
                        <div class="email-info-item">
                            <span class="email-info-label">💰 Price:</span> {{ $event['price'] }}
                        </div>
                    @endif
                    @if(!empty($event['description']))
                        <div style="margin-top: 8px; color: #6b7280; font-size: 14px;">
                            {{ Str::limit(strip_tags($event['description']), 150) }}
                        </div>
                    @endif
                    <div style="margin-top: 12px;">
                        <a href="{{ config('app.frontend_url', 'https://eventahub.com') }}/events/{{ $event['slug'] ?? $event['id'] }}"
                           style="color: #ef4444; text-decoration: none; font-weight: 600; font-size: 14px;">
                            View Event →
                        </a>
                    </div>
                </div>
            @endforeach
        </div>
    @else
        <div class="email-info-box">
            <div class="email-info-title">🎯 Building Your Profile</div>
            <div style="color: #6b7280; font-size: 14px; line-height: 1.6;">
                We're still learning about your preferences. Check out all our events to help us recommend better matches for you!
            </div>
        </div>
    @endif

    <div class="email-button-wrapper">
        <a href="{{ config('app.frontend_url', 'https://eventahub.com') }}/events" class="email-button">
            Explore All Events
        </a>
    </div>

    <div class="email-divider"></div>

    <div class="email-message">
        Can't find what you're looking for?
        <a href="{{ config('app.frontend_url', 'https://eventahub.com') }}/search" style="color: #ef4444; text-decoration: none;">Search for specific events</a>
        or
        <a href="{{ config('app.frontend_url', 'https://eventahub.com') }}/profile/preferences" style="color: #ef4444; text-decoration: none;">update your preferences</a>
        for better recommendations.
    </div>
@endsection

@section('content')
    <div class="section">
        <p>Based on popular events and trending activities, here are our top recommendations for you this week!</p>
    </div>

    @if(!empty($content))
        <div class="section">
            <h2 class="section-title">⭐ Recommended for You</h2>

            @foreach($content as $event)
                <div class="event-card">
                    <div class="event-title">{{ $event['title'] ?? 'Event Title' }}</div>
                    <div class="event-date">
                        📅 {{ isset($event['start_date']) ? \Carbon\Carbon::parse($event['start_date'])->format('M d, Y \a\t g:i A') : 'Date TBA' }}
                    </div>
                    @if(!empty($event['location']))
                        <div class="event-date">📍 {{ $event['location'] }}</div>
                    @endif
                    @if(!empty($event['category']))
                        <div class="event-date">🏷️ {{ $event['category']['name'] ?? $event['category'] }}</div>
                    @endif
                    @if(!empty($event['description']))
                        <div class="event-description">
                            {{ Str::limit(strip_tags($event['description']), 150) }}
                        </div>
                    @endif
                    <a href="{{ env('FRONTEND_URL') }}/events/{{ $event['slug'] ?? $event['id'] }}" class="btn">
                        Get Tickets
                    </a>
                </div>
            @endforeach
        </div>
    @else
        <div class="section">
            <p>We're working on finding the perfect events for you. Check back soon!</p>
        </div>
    @endif

    <div class="section">
        <h2 class="section-title">💡 Pro Tip</h2>
        <p>Book your tickets early to secure the best prices and avoid disappointment. Many popular events sell out quickly!</p>
    </div>

    <div class="section">
        <h2 class="section-title">🎯 Personalize Your Experience</h2>
        <p>Want more personalized recommendations? Update your interests and preferences in your profile to get better suggestions.</p>
        <a href="{{ env('FRONTEND_URL') }}/my-profile" class="btn">Update Preferences</a>
    </div>
@endsection
