@extends('emails.newsletter.base')

@section('newsletter-content')
    <div class="email-greeting">Hello there!</div>

    <div class="email-message">
        Here are the latest events that have been added to our platform this week. Don't miss out on these exciting opportunities!
    </div>

    @if(!empty($content))
        <div class="email-info-box">
            <div class="email-info-title">🎉 Latest Events</div>

            @foreach($content as $event)
                <div style="margin-bottom: 20px; padding-bottom: 20px; border-bottom: 1px solid #e5e7eb;">
                    <div style="font-size: 18px; font-weight: 600; color: #ef4444; margin-bottom: 8px;">
                        {{ $event['title'] ?? 'Event Title' }}
                    </div>
                    <div class="email-info-item">
                        <span class="email-info-label">📅 Date:</span>
                        {{ isset($event['start_date']) ? \Carbon\Carbon::parse($event['start_date'])->format('l, F j, Y \a\t g:i A') : 'Date TBA' }}
                    </div>
                    @if(!empty($event['location']))
                        <div class="email-info-item">
                            <span class="email-info-label">📍 Location:</span> {{ $event['location'] }}
                        </div>
                    @endif
                    @if(!empty($event['description']))
                        <div style="margin-top: 8px; color: #6b7280; font-size: 14px;">
                            {{ Str::limit(strip_tags($event['description']), 150) }}
                        </div>
                    @endif
                    <div style="margin-top: 12px;">
                        <a href="{{ config('app.frontend_url', 'https://eventahub.com') }}/events/{{ $event['slug'] ?? $event['id'] }}"
                           style="color: #ef4444; text-decoration: none; font-weight: 600; font-size: 14px;">
                            View Event Details →
                        </a>
                    </div>
                </div>
            @endforeach
        </div>
    @else
        <div class="email-message">
            No new events were added this week, but stay tuned for exciting updates!
        </div>
    @endif

    <div class="email-button-wrapper">
        <a href="{{ config('app.frontend_url', 'https://eventahub.com') }}/events" class="email-button">
            Browse All Events
        </a>
    </div>

    <div class="email-divider"></div>

    <div class="email-message">
        Follow us on social media for real-time updates and behind-the-scenes content from your favorite events.
    </div>

    <div class="email-message">
        Thank you for being part of the EventaHub community! 🎉
    </div>
@endsection
    </div>
@endsection
