@extends('emails.layout.base')

@section('content')
    @yield('newsletter-content')
@endsection
            font-weight: bold;
            color: #ef4444;
            margin-bottom: 15px;
            border-bottom: 2px solid #ef4444;
            padding-bottom: 5px;
        }
        .event-card, .venue-card {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            background-color: #fafafa;
        }
        .event-title, .venue-name {
            font-size: 18px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 8px;
        }
        .event-date, .venue-location {
            color: #6b7280;
            font-size: 14px;
            margin-bottom: 8px;
        }
        .event-description, .venue-description {
            color: #4b5563;
            font-size: 14px;
            line-height: 1.5;
        }
        .btn {
            display: inline-block;
            background-color: #ef4444;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
            margin-top: 10px;
        }
        .btn:hover {
            background-color: #dc2626;
        }
        .footer {
            background-color: #f9fafb;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #e5e7eb;
        }
        .footer p {
            margin: 5px 0;
            font-size: 14px;
            color: #6b7280;
        }
        .unsubscribe {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #e5e7eb;
        }
        .unsubscribe a {
            color: #6b7280;
            text-decoration: none;
            font-size: 12px;
        }
        .social-links {
            margin: 15px 0;
        }
        .social-links a {
            display: inline-block;
            margin: 0 10px;
            color: #ef4444;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>EventaHub Malawi</h1>
            <p>@yield('header-subtitle', 'Your Event Discovery Platform')</p>
        </div>

        <div class="content">
            <div class="greeting">
                Hello {{ $subscription->name ?? 'Event Enthusiast' }}! 👋
            </div>

            @yield('content')
        </div>

        <div class="footer">
            <p><strong>EventaHub Malawi</strong></p>
            <p>Discover amazing events happening around you</p>

            <div class="social-links">
                <a href="#">Facebook</a>
                <a href="#">Twitter</a>
                <a href="#">Instagram</a>
                <a href="#">LinkedIn</a>
            </div>

            <div class="unsubscribe">
                <p>You're receiving this email because you subscribed to our newsletter.</p>
                <a href="{{ $unsubscribeUrl }}">Unsubscribe from this newsletter</a>
            </div>
        </div>
    </div>
</body>
</html>
