@extends('emails.layout.base')

@section('content')
    <div class="email-greeting">Hello {{ $user->name }}!</div>

    <div class="email-message">
        🎉 Great news! Your ticket purchase has been confirmed. We're excited to see you at the event!
    </div>

    <div class="email-info-box">
        <div class="email-info-title">Purchase Summary</div>
        <div class="email-info-item">
            <span class="email-info-label">Transaction ID:</span> {{ $transaction->transaction_id }}
        </div>
        <div class="email-info-item">
            <span class="email-info-label">Total Amount:</span> {{ $transaction->currency }}
            {{ number_format($transaction->amount, 2) }}
        </div>
        <div class="email-info-item">
            <span class="email-info-label">Payment Status:</span> <span
                style="color: #10b981; font-weight: 600;">{{ ucfirst($transaction->status) }}</span>
        </div>
        <div class="email-info-item">
            <span class="email-info-label">Purchase Date:</span> {{ $transaction->created_at->format('F j, Y \a\t g:i A') }}
        </div>
    </div>

    @foreach ($purchases as $purchase)
        <div class="email-info-box" style="border-left-color: #10b981;">
            <div class="email-info-title" style="color: #ef4444; font-size: 18px;">{{ $purchase->event->title }}</div>

            <div class="email-info-item">
                <span class="email-info-label">📅 Date & Time:</span>
                {{ \Carbon\Carbon::parse($purchase->event->start_date)->format('l, F j, Y') }}
                @if ($purchase->event->start_time)
                    at {{ \Carbon\Carbon::parse($purchase->event->start_time)->format('g:i A') }}
                @endif
            </div>

            @if ($purchase->event->venue)
                <div class="email-info-item">
                    <span class="email-info-label">📍 Venue:</span> {{ $purchase->event->venue->name }}
                    @if ($purchase->event->venue->address)
                        <br><span style="margin-left: 20px; color: #6b7280;">{{ $purchase->event->venue->address }}</span>
                    @endif
                </div>
            @endif

            <div class="email-info-item">
                <span class="email-info-label">🎫 Ticket Type:</span> {{ $purchase->ticket->name }}
            </div>

            <div class="email-info-item">
                <span class="email-info-label">🔢 Quantity:</span> {{ $purchase->quantity }}
            </div>

            <div class="email-info-item">
                <span class="email-info-label">💰 Price per ticket:</span> {{ $purchase->ticket->currency }}
                {{ number_format($purchase->ticket->price, 2) }}
            </div>

            <div class="email-divider"></div>

            <div class="email-info-item">
                <span class="email-info-label">💵 Subtotal:</span> {{ $purchase->ticket->currency }}
                {{ number_format($purchase->quantity * $purchase->ticket->price, 2) }}
            </div>
        </div>
    @endforeach

    <div class="email-button-wrapper">
        <a href="{{ config('FRONTEND_URL', 'https://eventahub.com') }}/my-profile?tab=Tickets" class="email-button">
            View My Tickets
        </a>
    </div>

    <div class="email-message">
        Your digital tickets have been generated and are available in your account dashboard. You can access them anytime
        and present them at the event for entry.
    </div>

    <div class="email-info-box" style="background-color: #fef3c7; border-left-color: #f59e0b;">
        <div class="email-info-title" style="color: #92400e;">Important Information:</div>
        <div class="email-info-item">• Please arrive at least 30 minutes before the event starts</div>
        <div class="email-info-item">• Bring a valid ID that matches the name on your ticket</div>
        <div class="email-info-item">• Screenshots of tickets are not accepted - please use the official ticket from your
            dashboard</div>
        <div class="email-info-item">• Contact the event organizer if you have any questions about the event</div>
    </div>

    <div class="email-divider"></div>

    <div class="email-message" style="font-size: 14px; color: #6b7280;">
        If you have any questions about your purchase or need assistance, please don't hesitate to contact our support team.
        We're here to help!
    </div>

    <div class="email-message">
        Thank you for choosing EventaHub. We hope you have an amazing time at the event! 🎉
    </div>
@endsection
