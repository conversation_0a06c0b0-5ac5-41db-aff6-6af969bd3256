@extends('emails.layout.base')

@section('content')
    <div class="email-greeting">Hello {{ $user->name }}!</div>

    <div class="email-message">
        🎉 Great news! Your event tickets have been successfully generated and are ready for distribution.
    </div>

    <div class="email-info-box">
        <div class="email-info-title">Event Information</div>
        <div class="email-info-item">
            <span class="email-info-label">Event:</span> {{ $eventName ?? $data['event_name'] ?? 'Your Event' }}
        </div>
        <div class="email-info-item">
            <span class="email-info-label">Tickets Generated:</span> <span style="color: #10b981; font-weight: 600;">{{ $ticketCount ?? $data['ticket_count'] ?? 'Multiple' }} ticket(s)</span>
        </div>
        @if(isset($data['ticket_type']))
        <div class="email-info-item">
            <span class="email-info-label">Ticket Type:</span> {{ $data['ticket_type'] }}
        </div>
        @endif
        @if(isset($data['generation_date']))
        <div class="email-info-item">
            <span class="email-info-label">Generated On:</span> {{ \Carbon\Carbon::parse($data['generation_date'])->format('l, F j, Y \a\t g:i A') }}
        </div>
        @endif
    </div>

    <div class="email-info-box" style="background-color: #fef3c7; border-left-color: #f59e0b;">
        <div class="email-info-title" style="color: #92400e;">What's Next?</div>
        <div class="email-info-item">• Your tickets are now available in your event dashboard</div>
        <div class="email-info-item">• You can view, download, and share ticket information</div>
        <div class="email-info-item">• Monitor ticket sales and attendee data in real-time</div>
        <div class="email-info-item">• Export attendee lists when needed</div>
    </div>

    <div class="email-button-wrapper">
        <a href="{{ config('app.frontend_url', 'https://eventahub.com') }}/dashboard/manage-events" class="email-button">
            View Event Dashboard
        </a>
    </div>

    <div class="email-message">
        Your tickets are now ready to be sold or distributed to attendees. You can manage everything from your event dashboard.
    </div>

    <div class="email-info-box" style="background-color: #dbeafe; border-left-color: #3b82f6;">
        <div class="email-info-title" style="color: #1e40af;">📋 Important Notes:</div>
        <div class="email-info-item">• Each ticket has a unique QR code for verification</div>
        <div class="email-info-item">• Tickets can be scanned at the event entrance</div>
        <div class="email-info-item">• You'll receive real-time notifications for ticket sales</div>
        <div class="email-info-item">• Attendee data is automatically updated</div>
    </div>

    <div class="email-divider"></div>

    <div class="email-message" style="font-size: 14px; color: #6b7280;">
        If you need assistance with managing your tickets or have any questions, our support team is here to help.
    </div>

    <div class="email-message">
        Thank you for choosing EventaHub for your event management needs! 🚀
    </div>
@endsection
            font-weight: bold;
            margin: 20px 0;
        }

        .important-info {
            background-color: #fef3c7;
            border-left: 4px solid #f59e0b;
            padding: 15px;
            margin: 20px 0;
        }

        .action-buttons {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            text-decoration: none;
            border-radius: 0px;
            font-weight: bold;
            text-align: center;
        }

        .btn-primary {
            background-color: #dc2626;
            color: white;
        }

        .btn-secondary {
            background-color: #6b7280;
            color: white;
        }

        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            color: #6b7280;
            font-size: 14px;
        }

        @media (max-width: 600px) {
            .btn {
                display: block;
                margin: 10px 0;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>{{ config('app.name') }}</h1>
            <p>{{ $title }}</p>
        </div>

        <div class="success-badge">
            ✅ Tickets Generated Successfully
        </div>

        <p>{{ $greeting }}</p>

        <p>{{ $line }}</p>

        <div class="important-info">
            <strong>📎 Your Event Tickets:</strong> Your tickets have been generated and are attached to this email as a PDF file. Please save them to your device for distribution to attendees or for event management purposes.
        </div>

        @if($action && $actionURL)
        <div class="action-buttons">
            <a href="{{ $actionURL }}" class="btn btn-primary">
                {{ $action }}
            </a>
            <a href="{{ config('app.frontend_url') }}/dashboard" class="btn btn-secondary">
                Go to Dashboard
            </a>
        </div>
        @endif

        <p>If you have any questions about your event or need assistance, please don't hesitate to contact our support team.</p>

        <p>Thank you for using {{ config('app.name')}} for your event management!</p>

        <div class="footer">
            <p><strong>{{ config('app.name')}}</strong></p>
            <p>Your premier event discovery and ticketing platform</p>
            <p>This is an automated email. Please do not reply to this message.</p>
            <p>© {{ date('Y') }} {{ config('app.name')}}. All rights reserved.</p>
        </div>
    </div>
</body>

</html>
