@extends('emails.layout.base')

@section('content')
<div class="email-content">
    <div class="content-section">
        <h2 class="section-title">New Contact Form Submission</h2>
        <p class="section-description">
            A new message has been submitted through the EventaHub contact form.
        </p>
    </div>

    <div class="content-section">
        <div class="info-grid">
            <div class="info-item">
                <span class="info-label">Name:</span>
                <span class="info-value">{{ $contactData['firstName'] }} {{ $contactData['lastName'] }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Email:</span>
                <span class="info-value">{{ $contactData['email'] }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Submitted:</span>
                <span class="info-value">{{ $contactData['submitted_at']->format('F j, Y \a\t g:i A') }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">IP Address:</span>
                <span class="info-value">{{ $contactData['ip_address'] }}</span>
            </div>
        </div>
    </div>

    <div class="content-section">
        <h3 class="message-title">Message:</h3>
        <div class="message-content">
            {{ $contactData['message'] }}
        </div>
    </div>

    <div class="content-section">
        <div class="action-buttons">
            <a href="mailto:{{ $contactData['email'] }}?subject=Re: Your message to EventaHub" class="btn btn-primary">
                Reply to {{ $contactData['firstName'] }}
            </a>
        </div>
    </div>

    <div class="content-section">
        <div class="footer-note">
            <p><strong>Note:</strong> This email was automatically generated from the EventaHub contact form. Please respond directly to the sender's email address.</p>
        </div>
    </div>
</div>

<style>
    .info-grid {
        display: table;
        width: 100%;
        border-collapse: collapse;
    }

    .info-item {
        display: table-row;
        border-bottom: 1px solid #e5e7eb;
    }

    .info-item:last-child {
        border-bottom: none;
    }

    .info-label,
    .info-value {
        display: table-cell;
        padding: 12px 0;
        vertical-align: top;
    }

    .info-label {
        font-weight: 600;
        color: #374151;
        width: 120px;
        padding-right: 20px;
    }

    .info-value {
        color: #111827;
    }

    .message-title {
        font-size: 18px;
        font-weight: 600;
        color: #111827;
        margin-bottom: 12px;
    }

    .message-content {
        background-color: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 20px;
        color: #374151;
        line-height: 1.6;
        white-space: pre-wrap;
        word-wrap: break-word;
    }

    .action-buttons {
        text-align: center;
        margin: 20px 0;
    }

    .btn {
        display: inline-block;
        padding: 12px 24px;
        text-decoration: none;
        border-radius: 6px;
        font-weight: 600;
        text-align: center;
        transition: all 0.2s;
    }

    .btn-primary {
        background-color: #ef4444;
        color: white;
        border: 2px solid #ef4444;
    }

    .btn-primary:hover {
        background-color: #dc2626;
        border-color: #dc2626;
    }

    .footer-note {
        background-color: #fef3c7;
        border: 1px solid #f59e0b;
        border-radius: 6px;
        padding: 16px;
        margin-top: 20px;
    }

    .footer-note p {
        margin: 0;
        color: #92400e;
        font-size: 14px;
    }
</style>
@endsection
