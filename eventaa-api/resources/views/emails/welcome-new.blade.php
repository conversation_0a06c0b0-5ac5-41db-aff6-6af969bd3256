@extends('emails.layout.base')

@section('content')
    <div style="text-align: center; margin-bottom: 30px;">
        <div style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); color: white; padding: 15px 30px; display: inline-block; font-size: 18px; font-weight: bold;">
            🎉 Welcome Aboard!
        </div>
    </div>

    <div class="email-greeting">Hello {{ $user->name ?? 'Friend' }}!</div>

    <div class="email-message">
        Welcome to EventaHub, your ultimate event management platform! We're thrilled to have you join our community of event organizers and attendees.
    </div>

    <div class="email-message">
        Whether you're planning your first event or you're a seasoned organizer, EventaHub provides all the tools you need to create memorable experiences.
    </div>

    <!-- Features Section -->
    <div class="email-info-box">
        <div class="email-info-title">🚀 What you can do with EventaHub:</div>

        <div style="margin: 15px 0; padding: 15px; background-color: white; border-left: 4px solid #10b981;">
            <div style="font-weight: 600; color: #111827; margin-bottom: 5px;">🎫 Discover Events</div>
            <div style="color: #6b7280; font-size: 14px;">Browse and book tickets for amazing events in your area</div>
        </div>

        <div style="margin: 15px 0; padding: 15px; background-color: white; border-left: 4px solid #3b82f6;">
            <div style="font-weight: 600; color: #111827; margin-bottom: 5px;">📅 Create Events</div>
            <div style="color: #6b7280; font-size: 14px;">Organize and manage your own events with our powerful tools</div>
        </div>

        <div style="margin: 15px 0; padding: 15px; background-color: white; border-left: 4px solid #8b5cf6;">
            <div style="font-weight: 600; color: #111827; margin-bottom: 5px;">💳 Secure Payments</div>
            <div style="color: #6b7280; font-size: 14px;">Process payments safely with multiple payment options</div>
        </div>

        <div style="margin: 15px 0; padding: 15px; background-color: white; border-left: 4px solid #f59e0b;">
            <div style="font-weight: 600; color: #111827; margin-bottom: 5px;">📊 Analytics</div>
            <div style="color: #6b7280; font-size: 14px;">Track your event performance with detailed analytics</div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="email-button-wrapper">
        <a href="{{ config('app.frontend_url', 'https://eventahub.com') }}/events"
           style="background-color: #ef4444; color: white; padding: 14px 28px; text-decoration: none; font-weight: 600; font-size: 16px; margin: 10px; display: inline-block;">
            Explore Events
        </a>
        <a href="{{ config('app.frontend_url', 'https://eventahub.com') }}/dashboard/events/create"
           style="background-color: #6b7280; color: white; padding: 14px 28px; text-decoration: none; font-weight: 600; font-size: 16px; margin: 10px; display: inline-block;">
            Create Your First Event
        </a>
    </div>

    <!-- Quick Tips -->
    <div class="email-info-box" style="background-color: #d1fae5; border-left-color: #10b981;">
        <div class="email-info-title" style="color: #065f46;">💡 Quick Tips to Get Started:</div>
        <div class="email-info-item">• Complete your profile to get personalized event recommendations</div>
        <div class="email-info-item">• Follow your favorite event organizers to stay updated</div>
        <div class="email-info-item">• Enable notifications to never miss an event you're interested in</div>
        <div class="email-info-item">• Join our community forum to connect with other event enthusiasts</div>
    </div>

    @if(isset($data['user_name']))
    <div class="email-info-box">
        <div class="email-info-title">Your Account Details</div>
        <div class="email-info-item">
            <span class="email-info-label">Name:</span> {{ $data['user_name'] }}
        </div>
        <div class="email-info-item">
            <span class="email-info-label">Email:</span> {{ $user->email ?? '' }}
        </div>
        <div class="email-info-item">
            <span class="email-info-label">Member Since:</span> {{ $data['registration_date'] ?? now()->format('F j, Y') }}
        </div>
    </div>
    @endif

    <div class="email-message">
        If you have any questions or need help getting started, our support team is here to help. Just reply to this email or visit our help center.
    </div>

    <div class="email-divider"></div>

    <div style="text-align: center; margin: 20px 0;">
        <div style="font-size: 14px; color: #6b7280; margin-bottom: 15px;">
            Connect with us:
        </div>
        <div>
            <a href="#" style="color: #ef4444; text-decoration: none; margin: 0 10px;">Help Center</a> |
            <a href="#" style="color: #ef4444; text-decoration: none; margin: 0 10px;">Community</a> |
            <a href="#" style="color: #ef4444; text-decoration: none; margin: 0 10px;">Contact Support</a>
        </div>
    </div>

    <div class="email-message">
        Thank you for choosing EventaHub. Let's make your event experiences unforgettable! 🚀
    </div>
@endsection
