<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ticket Purchase Confirmation</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }

        .container {
            background-color: white;
            border-radius: 0px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            border-bottom: 3px solid #dc2626;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #dc2626;
            margin: 0;
            font-size: 28px;
        }

        .header p {
            color: #666;
            margin: 5px 0 0 0;
            font-size: 16px;
        }

        .success-badge {
            background-color: #10b981;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            display: inline-block;
            font-weight: bold;
            margin: 20px 0;
        }

        .purchase-summary {
            background-color: #f3f4f6;
            padding: 20px;
            border-radius: 0px;
            margin: 20px 0;
        }

        .purchase-summary h3 {
            margin-top: 0;
            color: #374151;
        }

        .ticket-item {
            border: 1px solid #e5e7eb;
            border-radius: 0px;
            padding: 20px;
            margin: 15px 0;
            background-color: white;
        }

        .event-title {
            font-size: 18px;
            font-weight: bold;
            color: #dc2626;
            margin-bottom: 10px;
        }

        .event-details {
            color: #6b7280;
            margin-bottom: 15px;
        }

        .ticket-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 15px;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
        }

        .detail-label {
            font-weight: 600;
            color: #374151;
        }

        .detail-value {
            color: #6b7280;
        }

        .total-section {
            background-color: #dc2626;
            color: white;
            padding: 20px;
            border-radius: 0px;
            margin: 20px 0;
            text-align: center;
        }

        .total-amount {
            font-size: 24px;
            font-weight: bold;
        }

        .action-buttons {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            text-decoration: none;
            border-radius: 0px;
            font-weight: bold;
            text-align: center;
        }

        .btn-primary {
            background-color: #dc2626;
            color: white;
        }

        .btn-secondary {
            background-color: #6b7280;
            color: white;
        }

        .important-info {
            background-color: #fef3c7;
            border-left: 4px solid #f59e0b;
            padding: 15px;
            margin: 20px 0;
        }

        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            color: #6b7280;
            font-size: 14px;
        }

        @media (max-width: 600px) {
            .ticket-details {
                grid-template-columns: 1fr;
            }

            .btn {
                display: block;
                margin: 10px 0;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>{{ config('app.name') }}</h1>
            <p>Ticket Purchase Confirmation</p>
        </div>

        <div class="success-badge">
            ✅ Purchase Confirmed
        </div>

        <p>Hello <strong>{{ $user->name }}</strong>,</p>

        <p>Great news! Your ticket purchase has been confirmed and processed successfully. Here are the details of your
            purchase:</p>

        <div class="purchase-summary">
            <h3>Purchase Summary</h3>
            <div class="detail-item">
                <span class="detail-label">Event:</span>
                <span class="detail-value">{{ $eventName }}</span>
            </div>
            @if(isset($purchases[0]->event))
                @php $event = $purchases[0]->event; @endphp
                @if($event->start)
                    <div class="detail-item">
                        <span class="detail-label">Date:</span>
                        <span class="detail-value">{{ \Carbon\Carbon::parse($event->start)->format('F j, Y \a\t g:i A') }}</span>
                    </div>
                @endif
                @if($event->location)
                    <div class="detail-item">
                        <span class="detail-label">Location:</span>
                        <span class="detail-value">{{ $event->location }}</span>
                    </div>
                @endif
            @endif
            <div class="detail-item">
                <span class="detail-label">Total Tickets:</span>
                <span class="detail-value">{{ $ticketCount }}</span>
            </div>
        </div>

        @foreach ($purchases as $purchase)
            <div class="ticket-item">
                <div class="event-title">{{ $purchase->ticket->name ?? 'General Admission' }}</div>

                <div class="ticket-details">
                    <div class="detail-item">
                        <span class="detail-label">Quantity:</span>
                        <span class="detail-value">{{ $purchase->quantity }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Unit Price:</span>
                        <span class="detail-value">{{ $currencySymbol }}{{ number_format($purchase->ticket->price, 0) }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Subtotal:</span>
                        <span class="detail-value">{{ $currencySymbol }}{{ number_format($purchase->quantity * $purchase->ticket->price, 0) }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Attendee:</span>
                        <span class="detail-value">{{ $user->name }}</span>
                    </div>
                </div>
            </div>
        @endforeach

        <div class="total-section">
            <div>Total Amount Paid</div>
            <div class="total-amount">{{ $currencySymbol }}{{ number_format($totalAmount, 0) }}</div>
        </div>

        <div class="important-info">
            <strong>📎 Your Tickets:</strong> Your tickets are attached to this email as a PDF file. Please save them to your device and bring them (printed or on your phone) to the event for entry. You can also access your tickets anytime from your account dashboard.
        </div>

        <div class="action-buttons">
            <a href="{{ config('app.frontend_url') }}/my-profile?Tab=Tickets" class="btn btn-primary">
                View My Tickets
            </a>
            <a href="{{ config('app.frontend_url') }}/my-profile" class="btn btn-secondary">
                Go to Dashboard
            </a>
        </div>

        <p>If you have any questions about your purchase or need assistance, please don't hesitate to contact our
            support team.</p>

        <p>Thank you for choosing {{ config('app.name')}}!</p>

        <div class="footer">
            <p><strong>{{ config('app.name')}}</strong></p>
            <p>Your premier event discovery and ticketing platform</p>
            <p>This is an automated email. Please do not reply to this message.</p>
            <p>© {{ date('Y') }} {{ config('app.name')}}. All rights reserved.</p>
        </div>
    </div>
</body>

</html>
