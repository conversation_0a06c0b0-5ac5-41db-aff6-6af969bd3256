@extends('emails.layout.base')

@section('content')
    <div class="email-greeting">Hello {{ $user->name ?? 'There' }}!</div>

    <div class="email-message">
        {{ $message }}
    </div>

    @if(isset($data['download_link']))
        <div class="email-button-wrapper">
            <a href="{{ $data['download_link'] }}" class="email-button">Download</a>
        </div>
    @endif

    @if(isset($data) && count($data) > 0)
        <div class="email-info-box">
            <div class="email-info-title">Additional Information:</div>
            @foreach($data as $key => $value)
                @if(!in_array($key, ['download_link']))
                    <div class="email-info-item">
                        <span class="email-info-label">{{ ucfirst(str_replace('_', ' ', $key)) }}:</span>
                        @if(is_array($value))
                            {{ implode(', ', $value) }}
                        @else
                            {{ $value }}
                        @endif
                    </div>
                @endif
            @endforeach
        </div>
    @endif
@endsection
