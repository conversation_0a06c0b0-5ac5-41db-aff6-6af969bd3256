@extends('emails.layout.base')

@section('content')
    <div class="email-greeting">Hello there!</div>

    <div class="email-message">
        🎉 <strong>{{ $eventOwner->name }}</strong> has invited you to attend an exciting event!
    </div>

    <div class="email-info-box" style="border-left-color: #10b981;">
        <div class="email-info-title" style="color: #ef4444; font-size: 20px; margin-bottom: 16px;">
            {{ $event->title }}
        </div>

        <div class="email-info-item">
            <span class="email-info-label">📅 Date:</span>
            {{ $event->start ? \Carbon\Carbon::parse($event->start)->format('l, F j, Y') : 'Date TBD' }}
        </div>

        <div class="email-info-item">
            <span class="email-info-label">⏰ Time:</span>
            {{ $event->start ? \Carbon\Carbon::parse($event->start)->format('g:i A') : 'Time TBD' }}
        </div>

        <div class="email-info-item">
            <span class="email-info-label">📍 Location:</span>
            {{ $event->location ?: 'Location TBD' }}
        </div>

        @if($event->description)
        <div class="email-divider"></div>
        <div style="color: #6b7280; font-size: 14px; line-height: 1.6;">
            {{ Str::limit(strip_tags($event->description), 200) }}
        </div>
        @endif
    </div>

    @if($invitation->message)
    <div class="email-info-box" style="background-color: #fef3c7; border-left-color: #f59e0b;">
        <div class="email-info-title" style="color: #92400e;">Personal Message from {{ $eventOwner->name }}:</div>
        <div style="color: #92400e; font-style: italic;">
            "{{ $invitation->message }}"
        </div>
    </div>
    @endif

    <div class="email-button-wrapper">
        <a href="{{ $invitation->getAcceptanceUrl() }}"
           style="background-color: #10b981; color: white; padding: 14px 28px; text-decoration: none; font-weight: 600; font-size: 16px; margin: 10px; display: inline-block;">
            ✓ Accept Invitation
        </a>
        <a href="{{ $invitation->getDeclineUrl() }}"
           style="background-color: #6b7280; color: white; padding: 14px 28px; text-decoration: none; font-weight: 600; font-size: 16px; margin: 10px; display: inline-block;">
            ✗ Decline Invitation
        </a>
    </div>

    <div class="email-message">
        Click one of the buttons above to let {{ $eventOwner->name }} know if you'll be attending.
    </div>

    <div class="email-info-box">
        <div class="email-info-title">Event Host</div>
        <div class="email-info-item">
            <span class="email-info-label">Name:</span> {{ $eventOwner->name }}
        </div>
        @if($eventOwner->email)
        <div class="email-info-item">
            <span class="email-info-label">Email:</span> <a href="mailto:{{ $eventOwner->email }}" style="color: #ef4444;">{{ $eventOwner->email }}</a>
        </div>
        @endif
    </div>

    <div class="email-divider"></div>

    <div class="email-message" style="font-size: 14px; color: #6b7280;">
        This invitation was sent through EventaHub. If you have any questions about this event, please contact the host directly.
    </div>

    <div class="email-message">
        We hope to see you there! 🎉
    </div>
@endsection
