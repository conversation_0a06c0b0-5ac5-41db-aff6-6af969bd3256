@extends('emails.layout.base')

@section('content')
    <div class="email-greeting">Hello {{ $user->name }}!</div>

    @if ($booking->status === 'accepted')
        <div class="email-message">
            🎉 Great news! Your booking request at <strong>{{ $venue->name }}</strong> has been <span style="color: #10b981; font-weight: 600;">accepted</span>.
        </div>
    @elseif($booking->status === 'rejected')
        <div class="email-message">
            😔 We regret to inform you that your booking request at <strong>{{ $venue->name }}</strong> has been <span style="color: #ef4444; font-weight: 600;">rejected</span>.
        </div>
    @else
        <div class="email-message">
            📋 Your booking status at <strong>{{ $venue->name }}</strong> has been updated to: <span style="font-weight: 600;">{{ ucfirst($booking->status) }}</span>.
        </div>
    @endif

    <!-- Booking Details -->
    <div class="email-info-box">
        <div class="email-info-title">Booking Details</div>
        <div class="email-info-item">
            <span class="email-info-label">Booking ID:</span> #{{ $booking->id }}
        </div>
        <div class="email-info-item">
            <span class="email-info-label">Venue:</span> {{ $venue->name }}
        </div>
        <div class="email-info-item">
            <span class="email-info-label">From:</span> {{ date('l, F j, Y \a\t g:i A', strtotime($booking->booking_from)) }}
        </div>
        <div class="email-info-item">
            <span class="email-info-label">To:</span> {{ date('l, F j, Y \a\t g:i A', strtotime($booking->booking_to)) }}
        </div>
        @if ($booking->message)
        <div class="email-info-item">
            <span class="email-info-label">Message:</span> {{ $booking->message }}
        </div>
        @endif
        <div class="email-info-item">
            <span class="email-info-label">Status:</span>
            <span style="color: {{ $booking->status === 'accepted' ? '#10b981' : ($booking->status === 'rejected' ? '#ef4444' : '#6b7280') }}; font-weight: 600;">
                {{ ucfirst($booking->status) }}
            </span>
        </div>
    </div>

    <!-- Status-specific information -->
    @if ($booking->status === 'accepted')
        <div class="email-info-box" style="background-color: #d1fae5; border-left-color: #10b981;">
            <div class="email-info-title" style="color: #065f46;">🎉 Your booking is confirmed!</div>
            <div class="email-info-item">• The venue has been reserved for your specified dates</div>
            <div class="email-info-item">• You'll receive further details from the venue team</div>
            <div class="email-info-item">• Please arrive on time for your booking</div>
        </div>
    @elseif($booking->status === 'rejected')
        <div class="email-info-box" style="background-color: #fee2e2; border-left-color: #ef4444;">
            <div class="email-info-title" style="color: #991b1b;">Why was my booking rejected?</div>
            <div class="email-info-item">• The venue may not be available for your requested dates</div>
            <div class="email-info-item">• Your request might not meet the venue's requirements</div>
            <div class="email-info-item">• Consider contacting the venue directly for alternatives</div>
        </div>
    @endif

    <!-- Venue Contact Information -->
    <div class="email-info-box">
        <div class="email-info-title">Venue Contact Information</div>
        @if($venue->email)
        <div class="email-info-item">
            <span class="email-info-label">Email:</span> <a href="mailto:{{ $venue->email }}" style="color: #ef4444;">{{ $venue->email }}</a>
        </div>
        @endif
        @if($venue->phone)
        <div class="email-info-item">
            <span class="email-info-label">Phone:</span> <a href="tel:{{ $venue->phone }}" style="color: #ef4444;">{{ $venue->phone }}</a>
        </div>
        @endif
    </div>

    <div class="email-button-wrapper">
        <a href="{{ config('app.frontend_url', 'https://eventahub.com') }}/dashboard/bookings" class="email-button">
            View My Bookings
        </a>
    </div>

    <div class="email-message">
        @if ($booking->status === 'accepted')
            We look forward to hosting you at {{ $venue->name }}!
        @elseif($booking->status === 'rejected')
            We hope to serve you in the future with a more suitable availability.
        @else
            If you have any questions, please don't hesitate to contact the venue.
        @endif
    </div>

    <div class="email-divider"></div>

    <div class="email-message" style="font-size: 14px; color: #6b7280;">
        This is an automated email. Please do not reply to this message.
    </div>

    <div class="email-message">
        Thank you for using EventaHub! 🏢
    </div>
@endsection
