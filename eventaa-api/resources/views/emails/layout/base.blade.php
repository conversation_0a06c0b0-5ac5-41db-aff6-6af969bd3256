<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ $subject ?? 'EventaHub Notification' }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #111827;
            background-color: #f9fafb;
            padding: 0;
            margin: 0;
        }

        .email-wrapper {
            max-width: 600px;
            margin: 0 auto;
            background-color: #f9fafb;
            padding: 20px;
        }

        .email-container {
            background-color: #ffffff;
            margin: 0 auto;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        }

        .email-header {
            background-color: #ef4444;
            padding: 30px;
            text-align: center;
            color: white;
        }

        .email-logo {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 8px;
            letter-spacing: -0.5px;
        }

        .email-tagline {
            font-size: 14px;
            opacity: 0.9;
            font-weight: 400;
        }

        .email-content {
            padding: 40px 30px;
        }

        .email-greeting {
            font-size: 20px;
            font-weight: 600;
            color: #111827;
            margin-bottom: 24px;
        }

        .email-message {
            font-size: 16px;
            line-height: 1.7;
            color: #374151;
            margin-bottom: 24px;
        }

        .email-button {
            display: inline-block;
            background-color: #ef4444;
            color: white;
            padding: 14px 28px;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            margin: 20px 0;
            transition: background-color 0.2s;
        }

        .email-button:hover {
            background-color: #dc2626;
        }

        .email-button-wrapper {
            text-align: center;
            margin: 30px 0;
        }

        .email-info-box {
            background-color: #f9fafb;
            border-left: 4px solid #ef4444;
            padding: 20px;
            margin: 24px 0;
        }

        .email-info-title {
            font-weight: 600;
            color: #111827;
            margin-bottom: 12px;
            font-size: 14px;
        }

        .email-info-item {
            margin-bottom: 8px;
            font-size: 14px;
            color: #374151;
        }

        .email-info-label {
            font-weight: 600;
            color: #111827;
        }

        .email-code {
            background-color: #f3f4f6;
            border: 2px solid #e5e7eb;
            padding: 20px;
            text-align: center;
            margin: 24px 0;
            font-size: 28px;
            font-weight: bold;
            letter-spacing: 4px;
            color: #111827;
            font-family: 'Monaco', 'Consolas', monospace;
        }

        .email-footer {
            background-color: #f9fafb;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e5e7eb;
        }

        .email-footer-text {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.6;
            margin-bottom: 8px;
        }

        .email-footer-brand {
            font-weight: 600;
            color: #ef4444;
        }

        .email-divider {
            height: 1px;
            background-color: #e5e7eb;
            margin: 24px 0;
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            .email-container {
                background-color: #27272a;
                color: #f4f4f5;
            }

            .email-content {
                background-color: #27272a;
            }

            .email-greeting {
                color: #f4f4f5;
            }

            .email-message {
                color: #d4d4d8;
            }

            .email-info-box {
                background-color: #18181b;
            }

            .email-info-title {
                color: #f4f4f5;
            }

            .email-info-item {
                color: #d4d4d8;
            }

            .email-info-label {
                color: #f4f4f5;
            }

            .email-code {
                background-color: #3f3f46;
                border-color: #52525b;
                color: #f4f4f5;
            }

            .email-footer {
                background-color: #18181b;
            }

            .email-footer-text {
                color: #a1a1aa;
            }

            .email-divider {
                background-color: #52525b;
            }
        }

        /* Mobile responsive */
        @media only screen and (max-width: 600px) {
            .email-wrapper {
                padding: 10px;
            }

            .email-header {
                padding: 20px;
            }

            .email-logo {
                font-size: 24px;
            }

            .email-content {
                padding: 30px 20px;
            }

            .email-greeting {
                font-size: 18px;
            }

            .email-message {
                font-size: 15px;
            }

            .email-button {
                display: block;
                width: 100%;
                padding: 16px;
            }

            .email-code {
                font-size: 24px;
                letter-spacing: 2px;
            }
        }
    </style>
</head>
<body>
    <div class="email-wrapper">
        <div class="email-container">
            <div class="email-header">
                @if(function_exists('get_app_logo_url') && get_app_logo_url())
                    <img src="{{ get_app_logo_url() }}" alt="EventaHub Logo" style="height: 40px; margin-bottom: 10px;">
                @else
                    <div class="email-logo">EventaHub</div>
                @endif
                <div class="email-tagline">Your Event Management Platform</div>
            </div>

            <div class="email-content">
                @yield('content')
            </div>

            <div class="email-footer">
                <p class="email-footer-text">
                    &copy; {{ date('Y') }} <span class="email-footer-brand">EventaHub</span>. All rights reserved.
                </p>
                <p class="email-footer-text">
                    You're receiving this email because you have an account with EventaHub.
                </p>
                @if(isset($unsubscribeUrl))
                <p class="email-footer-text">
                    <a href="{{ $unsubscribeUrl }}" style="color: #6b7280; text-decoration: underline;">Unsubscribe</a>
                </p>
                @endif
            </div>
        </div>
    </div>
</body>
</html>
