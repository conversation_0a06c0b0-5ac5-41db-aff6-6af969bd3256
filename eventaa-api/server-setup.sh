#!/bin/bash

# EventaHub Server Setup Script
# This script sets up the Laravel application for production deployment
# without <PERSON><PERSON>, using systemd services and cron jobs

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration variables
APP_NAME="eventahub"
APP_DIR="/var/www/eventahub/eventaa-api"
APP_USER="www-data"
DOMAIN="api.eventahub.com"
PHP_VERSION="8.2"

echo -e "${BLUE}======================================${NC}"
echo -e "${BLUE}    EventaHub Server Setup Script    ${NC}"
echo -e "${BLUE}======================================${NC}"

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   print_error "This script must be run as root"
   exit 1
fi

print_status "Starting EventaHub server setup..."

# Update system packages
print_status "Updating system packages..."
apt update && apt upgrade -y

# Install required packages
print_status "Installing required packages..."
apt install -y nginx php${PHP_VERSION} php${PHP_VERSION}-fpm php${PHP_VERSION}-mysql \
    php${PHP_VERSION}-xml php${PHP_VERSION}-curl php${PHP_VERSION}-mbstring \
    php${PHP_VERSION}-zip php${PHP_VERSION}-bcmath php${PHP_VERSION}-gd \
    php${PHP_VERSION}-redis php${PHP_VERSION}-intl composer mysql-server \
    redis-server supervisor git curl unzip nodejs npm

# Remove Docker and Docker Compose if installed
print_status "Removing Docker configurations..."
if command -v docker &> /dev/null; then
    print_warning "Stopping and removing Docker containers..."
    docker-compose down 2>/dev/null || true
    systemctl stop docker 2>/dev/null || true
    systemctl disable docker 2>/dev/null || true
    apt remove -y docker docker-engine docker.io containerd runc docker-compose 2>/dev/null || true
fi

# Remove Docker files
rm -f ${APP_DIR}/docker-compose.yml
rm -f ${APP_DIR}/Dockerfile
rm -rf ${APP_DIR}/nginx/
print_status "Docker configurations removed"

# Create application directory if it doesn't exist
if [ ! -d "$APP_DIR" ]; then
    print_status "Creating application directory at $APP_DIR"
    mkdir -p $APP_DIR
fi

# Set proper ownership
chown -R $APP_USER:$APP_USER $APP_DIR

# Create systemd service for Laravel application
print_status "Creating Laravel application service..."
cat > /etc/systemd/system/${APP_NAME}-app.service << EOF
[Unit]
Description=EventaHub API
After=network.target mysql.service redis.service
Wants=mysql.service redis.service

[Service]
Type=exec
User=$APP_USER
Group=$APP_USER
WorkingDirectory=$APP_DIR
ExecStart=/usr/bin/php artisan serve --host=0.0.0.0 --port=8000
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=${APP_NAME}-app

Environment=APP_ENV=production
Environment=QUEUE_CONNECTION=redis

[Install]
WantedBy=multi-user.target
EOF

# Create systemd service for Laravel Reverb WebSocket server
print_status "Creating Laravel Reverb service..."
cat > /etc/systemd/system/${APP_NAME}-reverb.service << EOF
[Unit]
Description=EventaHub Reverb WebSocket Server
After=network.target redis.service
Wants=redis.service

[Service]
Type=exec
User=$APP_USER
Group=$APP_USER
WorkingDirectory=$APP_DIR
ExecStart=/usr/bin/php artisan reverb:start --host=0.0.0.0 --port=7001
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=${APP_NAME}-reverb

Environment=APP_ENV=production

[Install]
WantedBy=multi-user.target
EOF

# Create systemd service for Laravel Queue Worker
print_status "Creating Laravel Queue Worker service..."
cat > /etc/systemd/system/${APP_NAME}-queue.service << EOF
[Unit]
Description=EventaHub Laravel Queue Worker
After=network.target mysql.service redis.service
Wants=mysql.service redis.service

[Service]
Type=exec
User=$APP_USER
Group=$APP_USER
WorkingDirectory=$APP_DIR
ExecStart=/usr/bin/php artisan queue:work redis --sleep=3 --tries=3 --max-time=3600
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=${APP_NAME}-queue

Environment=APP_ENV=production

[Install]
WantedBy=multi-user.target
EOF

# Create Nginx configuration
print_status "Creating Nginx configuration..."
cat > /etc/nginx/sites-available/${APP_NAME} << EOF
server {
    listen 80;
    server_name $DOMAIN www.$DOMAIN;
    root $APP_DIR/public;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-XSS-Protection "1; mode=block";
    add_header X-Content-Type-Options "nosniff";

    index index.html index.htm index.php;

    charset utf-8;

    location / {
        try_files \$uri \$uri/ /index.php?\$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php${PHP_VERSION}-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME \$realpath_root\$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }

    # WebSocket proxy for Reverb
    location /app/ {
        proxy_pass http://127.0.0.1:7001 ;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
}

# Redirect API calls to Laravel app on port 8000
server {
    listen 80;
    server_name api.$DOMAIN;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF

# Enable Nginx site
ln -sf /etc/nginx/sites-available/${APP_NAME} /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# Test Nginx configuration
nginx -t

# Create supervisor configuration for additional workers
print_status "Creating Supervisor configuration..."
cat > /etc/supervisor/conf.d/${APP_NAME}-workers.conf << EOF
[program:${APP_NAME}-queue-default]
process_name=%(program_name)s_%(process_num)02d
command=php $APP_DIR/artisan queue:work redis --queue=default --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=$APP_USER
numprocs=2
redirect_stderr=true
stdout_logfile=$APP_DIR/storage/logs/worker.log
stopwaitsecs=3600

[program:${APP_NAME}-queue-high]
process_name=%(program_name)s_%(process_num)02d
command=php $APP_DIR/artisan queue:work redis --queue=high --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=$APP_USER
numprocs=1
redirect_stderr=true
stdout_logfile=$APP_DIR/storage/logs/worker-high.log
stopwaitsecs=3600

[program:${APP_NAME}-queue-low]
process_name=%(program_name)s_%(process_num)02d
command=php $APP_DIR/artisan queue:work redis --queue=low --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=$APP_USER
numprocs=1
redirect_stderr=true
stdout_logfile=$APP_DIR/storage/logs/worker-low.log
stopwaitsecs=3600
EOF

# Add Laravel cron jobs
print_status "Setting up cron jobs..."

# Create a temporary cron file
CRON_FILE="/tmp/${APP_NAME}_cron"

# Get current crontab for www-data user or create empty if none exists
crontab -u $APP_USER -l 2>/dev/null > $CRON_FILE || echo "" > $CRON_FILE

# Remove any existing EventaHub cron jobs
sed -i '/# EventaHub/d' $CRON_FILE
sed -i '/artisan schedule:run/d' $CRON_FILE
sed -i '/artisan queue:restart/d' $CRON_FILE
sed -i '/artisan subscriptions:remove-expired/d' $CRON_FILE
sed -i '/artisan notifications:send/d' $CRON_FILE
sed -i '/artisan users:delete-inactive/d' $CRON_FILE
sed -i '/artisan payments:check-stale/d' $CRON_FILE

# Add EventaHub cron jobs
cat >> $CRON_FILE << EOF

# EventaHub Cron Jobs
* * * * * cd $APP_DIR && php artisan schedule:run >> /dev/null 2>&1
0 2 * * * cd $APP_DIR && php artisan queue:restart >> /dev/null 2>&1
0 3 * * * cd $APP_DIR && php artisan subscriptions:remove-expired >> $APP_DIR/storage/logs/cron.log 2>&1
*/5 * * * * cd $APP_DIR && php artisan notifications:send >> $APP_DIR/storage/logs/cron.log 2>&1
0 4 * * * cd $APP_DIR && php artisan users:delete-inactive >> $APP_DIR/storage/logs/cron.log 2>&1
*/5 * * * * cd $APP_DIR && php artisan payments:check-stale >> $APP_DIR/storage/logs/cron.log 2>&1
0 1 * * * cd $APP_DIR && php artisan telescope:prune >> /dev/null 2>&1
0 0 * * * cd $APP_DIR && php artisan auth:clear-resets >> /dev/null 2>&1
0 5 * * 0 cd $APP_DIR && php artisan backup:clean >> $APP_DIR/storage/logs/backup.log 2>&1
0 6 * * 0 cd $APP_DIR && php artisan backup:run >> $APP_DIR/storage/logs/backup.log 2>&1
EOF

# Install the new crontab
crontab -u $APP_USER $CRON_FILE
rm $CRON_FILE

print_status "Cron jobs installed for user: $APP_USER"

# Create log rotation configuration
print_status "Setting up log rotation..."
cat > /etc/logrotate.d/${APP_NAME} << EOF
$APP_DIR/storage/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 0640 $APP_USER $APP_USER
    postrotate
        systemctl reload ${APP_NAME}-app ${APP_NAME}-reverb ${APP_NAME}-queue || true
    endscript
}
EOF

# Configure PHP-FPM
print_status "Configuring PHP-FPM..."
sed -i "s/;cgi.fix_pathinfo=1/cgi.fix_pathinfo=0/" /etc/php/${PHP_VERSION}/fpm/php.ini
sed -i "s/upload_max_filesize = 2M/upload_max_filesize = 50M/" /etc/php/${PHP_VERSION}/fpm/php.ini
sed -i "s/post_max_size = 8M/post_max_size = 50M/" /etc/php/${PHP_VERSION}/fpm/php.ini
sed -i "s/max_execution_time = 30/max_execution_time = 300/" /etc/php/${PHP_VERSION}/fpm/php.ini
sed -i "s/memory_limit = 128M/memory_limit = 512M/" /etc/php/${PHP_VERSION}/fpm/php.ini

# Configure Redis
print_status "Configuring Redis..."
sed -i "s/# maxmemory <bytes>/maxmemory 256mb/" /etc/redis/redis.conf
sed -i "s/# maxmemory-policy noeviction/maxmemory-policy allkeys-lru/" /etc/redis/redis.conf

# Reload systemd daemon
systemctl daemon-reload

# Enable and start services
print_status "Enabling and starting services..."
systemctl enable nginx
systemctl enable php${PHP_VERSION}-fpm
systemctl enable redis-server
systemctl enable mysql
systemctl enable supervisor
systemctl enable ${APP_NAME}-app
systemctl enable ${APP_NAME}-reverb
systemctl enable ${APP_NAME}-queue

# Start services
systemctl start nginx
systemctl start php${PHP_VERSION}-fpm
systemctl start redis-server
systemctl start mysql
systemctl start supervisor

# Create deployment script
print_status "Creating deployment script..."
cat > $APP_DIR/deploy.sh << 'EOF'
#!/bin/bash

# EventaHub Deployment Script

set -e

APP_DIR="/var/www/eventahub"
APP_USER="www-data"

echo "Starting deployment..."

# Pull latest changes
git pull origin main

# Install/update dependencies
composer install --no-dev --optimize-autoloader
npm ci --only=production

# Run migrations
php artisan migrate --force

# Clear and cache configurations
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Generate assets
npm run build

# Set permissions
chown -R $APP_USER:$APP_USER $APP_DIR
chmod -R 755 $APP_DIR
chmod -R 775 $APP_DIR/storage
chmod -R 775 $APP_DIR/bootstrap/cache

# Restart services
systemctl restart eventahub-app
systemctl restart eventahub-reverb
systemctl restart eventahub-queue
supervisorctl restart all

echo "Deployment completed successfully!"
EOF

chmod +x $APP_DIR/deploy.sh
chown $APP_USER:$APP_USER $APP_DIR/deploy.sh

# Set proper permissions
print_status "Setting file permissions..."
chown -R $APP_USER:$APP_USER $APP_DIR
chmod -R 755 $APP_DIR
chmod -R 775 $APP_DIR/storage $APP_DIR/bootstrap/cache

# Create systemd targets for easier management
print_status "Creating systemd targets..."
cat > /etc/systemd/system/${APP_NAME}.target << EOF
[Unit]
Description=EventaHub Application Stack
Wants=${APP_NAME}-app.service ${APP_NAME}-reverb.service ${APP_NAME}-queue.service
After=${APP_NAME}-app.service ${APP_NAME}-reverb.service ${APP_NAME}-queue.service

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl enable ${APP_NAME}.target

# Create firewall rules
print_status "Configuring firewall..."
if command -v ufw &> /dev/null; then
    ufw allow 80/tcp
    ufw allow 443/tcp
    ufw allow 8000/tcp
    ufw allow 7001 /tcp
    ufw --force enable
fi

# Start EventaHub services
print_status "Starting EventaHub services..."
systemctl start ${APP_NAME}-app
systemctl start ${APP_NAME}-reverb
systemctl start ${APP_NAME}-queue

# Update supervisor
supervisorctl reread
supervisorctl update
supervisorctl start all

# Restart web server
systemctl restart nginx

print_status "Server setup completed successfully!"
echo ""
echo -e "${GREEN}======================================${NC}"
echo -e "${GREEN}     Setup Summary                   ${NC}"
echo -e "${GREEN}======================================${NC}"
echo -e "Laravel App: ${BLUE}http://$DOMAIN:8000${NC} (or via nginx proxy)"
echo -e "Reverb WebSocket: ${BLUE}ws://$DOMAIN:7001 ${NC}"
echo -e "Web Interface: ${BLUE}http://$DOMAIN${NC}"
echo -e "API Endpoint: ${BLUE}http://api.$DOMAIN${NC}"
echo ""
echo -e "${YELLOW}Services:${NC}"
echo -e "  • ${APP_NAME}-app.service (Laravel on port 8000)"
echo -e "  • ${APP_NAME}-reverb.service (WebSocket on port 7001 )"
echo -e "  • ${APP_NAME}-queue.service (Queue Worker)"
echo -e "  • Supervisor workers (multiple queue workers)"
echo ""
echo -e "${YELLOW}Management Commands:${NC}"
echo -e "  • Start all: ${BLUE}systemctl start ${APP_NAME}.target${NC}"
echo -e "  • Stop all: ${BLUE}systemctl stop ${APP_NAME}.target${NC}"
echo -e "  • Status: ${BLUE}systemctl status ${APP_NAME}.target${NC}"
echo -e "  • Deploy: ${BLUE}cd $APP_DIR && ./deploy.sh${NC}"
echo ""
echo -e "${YELLOW}Next Steps:${NC}"
echo "1. Configure your .env file in $APP_DIR"
echo "2. Update the domain name in /etc/nginx/sites-available/${APP_NAME}"
echo "3. Set up SSL certificate (recommended: certbot)"
echo "4. Run initial migration: php artisan migrate"
echo "5. Configure database connection"
echo ""
echo -e "${GREEN}Docker configurations have been removed.${NC}"
echo -e "${GREEN}All services are now managed by systemd.${NC}"
