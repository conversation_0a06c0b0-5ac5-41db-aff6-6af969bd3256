<?php

namespace App\Exports;

use App\Models\Withdrawal;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Font;
use Illuminate\Support\Collection;

class WithdrawalsExport implements FromCollection, WithHeadings, WithMapping, WithStyles, WithColumnWidths
{
    protected $filters;

    public function __construct($filters = [])
    {
        $this->filters = $filters;
    }

    /**
     * @return Collection
     */
    public function collection()
    {
        $query = Withdrawal::with(['user']);

        if (!empty($this->filters['status'])) {
            $query->where('status', $this->filters['status']);
        }

        if (!empty($this->filters['payment_method'])) {
            $query->where('payment_method', $this->filters['payment_method']);
        }

        if (!empty($this->filters['gateway'])) {
            $query->where('gateway', $this->filters['gateway']);
        }

        if (!empty($this->filters['user_id'])) {
            $query->where('user_id', $this->filters['user_id']);
        }

        if (!empty($this->filters['date_from'])) {
            $query->whereDate('created_at', '>=', $this->filters['date_from']);
        }

        if (!empty($this->filters['date_to'])) {
            $query->whereDate('created_at', '<=', $this->filters['date_to']);
        }

        if (!empty($this->filters['search'])) {
            $search = $this->filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('reference', 'like', "%{$search}%")
                  ->orWhere('gateway_reference', 'like', "%{$search}%")
                  ->orWhere('notes', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        return $query->orderBy('created_at', 'desc')->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'ID',
            'Reference',
            'User Name',
            'User Email',
            'Amount (MWK)',
            'Fee (MWK)',
            'Net Amount (MWK)',
            'Payment Method',
            'Payment Details',
            'Status',
            'Gateway',
            'Gateway Reference',
            'Notes',
            'Created At',
            'Processed At',
        ];
    }

    /**
     * @param Withdrawal $withdrawal
     * @return array
     */
    public function map($withdrawal): array
    {
        $paymentDetails = '';
        if (is_array($withdrawal->payment_details)) {
            if ($withdrawal->payment_method === 'mobile_money') {
                $paymentDetails = ($withdrawal->payment_details['phone_number'] ?? '') .
                                ' (' . ($withdrawal->payment_details['network'] ?? '') . ')';
            } elseif ($withdrawal->payment_method === 'bank_transfer') {
                $paymentDetails = ($withdrawal->payment_details['account_name'] ?? '') . ' - ' .
                                ($withdrawal->payment_details['account_number'] ?? '') . ' (' .
                                ($withdrawal->payment_details['bank_name'] ?? '') . ')';
            }
        }

        return [
            $withdrawal->id,
            $withdrawal->reference,
            $withdrawal->user->name ?? 'N/A',
            $withdrawal->user->email ?? 'N/A',
            number_format((float) $withdrawal->amount, 2),
            number_format((float) $withdrawal->fee, 2),
            number_format((float) $withdrawal->net_amount, 2),
            ucfirst(str_replace('_', ' ', $withdrawal->payment_method)),
            $paymentDetails,
            ucfirst($withdrawal->status),
            $withdrawal->gateway ?? 'N/A',
            $withdrawal->gateway_reference ?? 'N/A',
            $withdrawal->notes ?? 'N/A',
            $withdrawal->created_at->format('Y-m-d H:i:s'),
            $withdrawal->processed_at ? $withdrawal->processed_at->format('Y-m-d H:i:s') : 'N/A',
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text
            1 => [
                'font' => [
                    'bold' => true,
                    'size' => 12,
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => [
                        'argb' => 'FFE2E8F0',
                    ],
                ],
            ],
        ];
    }

    /**
     * @return array
     */
    public function columnWidths(): array
    {
        return [
            'A' => 8,   // ID
            'B' => 20,  // Reference
            'C' => 25,  // User Name
            'D' => 30,  // User Email
            'E' => 15,  // Amount (MWK)
            'F' => 15,  // Fee (MWK)
            'G' => 15,  // Net Amount (MWK)
            'H' => 20,  // Payment Method
            'I' => 35,  // Payment Details
            'J' => 15,  // Status
            'K' => 15,  // Gateway
            'L' => 25,  // Gateway Reference
            'M' => 30,  // Notes
            'N' => 20,  // Created At
            'O' => 20,  // Processed At
        ];
    }
}
