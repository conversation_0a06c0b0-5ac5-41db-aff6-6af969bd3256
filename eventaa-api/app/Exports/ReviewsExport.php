<?php

namespace App\Exports;

use App\Models\Rating;
use App\Models\VendorRating;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Illuminate\Support\Collection;

class ReviewsExport implements FromCollection, WithHeadings, WithMapping, WithStyles, ShouldAutoSize
{
    protected $filters;

    public function __construct($filters = [])
    {
        $this->filters = $filters;
    }

    /**
     * @return Collection
     */
    public function collection()
    {
        $reviews = collect();

        // Get event ratings if type filter allows
        if (empty($this->filters['type']) || $this->filters['type'] === 'event') {
            $eventQuery = Rating::with(['user', 'event']);

            // Apply search filter at database level
            if (!empty($this->filters['search'])) {
                $search = $this->filters['search'];
                $eventQuery->where(function ($query) use ($search) {
                    $query->whereHas('user', function ($q) use ($search) {
                        $q->where('name', 'like', "%{$search}%")
                          ->orWhere('email', 'like', "%{$search}%");
                    })->orWhereHas('event', function ($q) use ($search) {
                        $q->where('title', 'like', "%{$search}%");
                    })->orWhere('review', 'like', "%{$search}%");
                });
            }

            // Apply rating filter at database level
            if (!empty($this->filters['rating']) && $this->filters['rating'] !== 'all') {
                $eventQuery->where('rating', $this->filters['rating']);
            }

            $eventRatings = $eventQuery->get()->map(function ($rating) {
                return [
                    'id' => $rating->id,
                    'reviewer_name' => $rating->user->name ?? 'Unknown User',
                    'reviewer_email' => $rating->user->email ?? 'No Email',
                    'rating' => $rating->rating ?? 0,
                    'content' => $rating->review ?? 'No Review Content',
                    'type' => 'event',
                    'for_name' => $rating->event->title ?? 'Unknown Event',
                    'for_id' => $rating->event_id ?? 0,
                    'date' => $rating->created_at ?? now(),
                    'status' => 'published',
                ];
            });

            $reviews = $reviews->merge($eventRatings);
        }

        // Get vendor ratings if type filter allows
        if (empty($this->filters['type']) || $this->filters['type'] === 'vendor') {
            $vendorQuery = VendorRating::with(['user', 'vendor']);

            if (!empty($this->filters['search'])) {
                $search = $this->filters['search'];
                $vendorQuery->where(function ($query) use ($search) {
                    $query->whereHas('user', function ($q) use ($search) {
                        $q->where('name', 'like', "%{$search}%")
                          ->orWhere('email', 'like', "%{$search}%");
                    })->orWhereHas('vendor', function ($q) use ($search) {
                        $q->where('name', 'like', "%{$search}%");
                    })->orWhere('comment', 'like', "%{$search}%");
                });
            }

            if (!empty($this->filters['rating']) && $this->filters['rating'] !== 'all') {
                $vendorQuery->where('rating', $this->filters['rating']);
            }

            $vendorRatings = $vendorQuery->get()->map(function ($rating) {
                return [
                    'id' => $rating->id,
                    'reviewer_name' => $rating->user->name ?? 'Unknown User',
                    'reviewer_email' => $rating->user->email ?? 'No Email',
                    'rating' => $rating->rating ?? 0,
                    'content' => $rating->comment ?? 'No Review Content',
                    'type' => 'vendor',
                    'for_name' => $rating->vendor->name ?? 'Unknown Vendor',
                    'for_id' => $rating->vendor_id ?? 0,
                    'date' => $rating->created_at ?? now(),
                    'status' => 'published',
                ];
            });

            $reviews = $reviews->merge($vendorRatings);
        }

        // Apply status filter - since we don't have status field, only published reviews are available
        if (!empty($this->filters['status']) && $this->filters['status'] !== 'all' && $this->filters['status'] !== 'published') {
            return collect(); // Return empty collection for non-published status filters
        }

        return $reviews->sortByDesc('date')->values();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'ID',
            'Reviewer Name',
            'Reviewer Email',
            'Rating',
            'Review Content',
            'Type',
            'For (Event/Vendor)',
            'Date',
            'Status',
        ];
    }

    /**
     * @param mixed $review
     * @return array
     */
    public function map($review): array
    {
        return [
            $review['id'],
            $review['reviewer_name'],
            $review['reviewer_email'],
            $review['rating'] . '/5',
            strip_tags($review['content']),
            ucfirst($review['type']),
            $review['for_name'],
            $review['date']->format('Y-m-d H:i:s'),
            ucfirst($review['status']),
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as header
            1 => [
                'font' => [
                    'bold' => true,
                    'size' => 12,
                ],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => [
                        'rgb' => 'E2E8F0',
                    ],
                ],
            ],
        ];
    }
}
