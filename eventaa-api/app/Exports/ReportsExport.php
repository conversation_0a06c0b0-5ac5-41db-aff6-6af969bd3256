<?php

namespace App\Exports;

use App\Models\Report;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Font;

class ReportsExport implements FromCollection, WithHeadings, WithMapping, WithStyles, WithColumnWidths
{
    protected $query;

    public function __construct($query)
    {
        $this->query = $query;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return $this->query->with('user:id,name,email')->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'ID',
            'Report Name',
            'Type',
            'Period',
            'Status',
            'Generated By',
            'Created Date',
            'Generated Date',
            'Start Date',
            'End Date',
            'Description'
        ];
    }

    /**
     * @param Report $report
     * @return array
     */
    public function map($report): array
    {
        return [
            $report->id,
            $report->name,
            ucfirst($report->type),
            ucfirst($report->period),
            ucfirst($report->status),
            $report->user->name ?? 'Unknown',
            $report->created_at ? $report->created_at->format('Y-m-d H:i:s') : 'N/A',
            $report->generated_at ? $report->generated_at->format('Y-m-d H:i:s') : 'N/A',
            $report->start_date ? $report->start_date->format('Y-m-d') : 'N/A',
            $report->end_date ? $report->end_date->format('Y-m-d') : 'N/A',
            $report->description ?? 'N/A'
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text
            1 => [
                'font' => [
                    'bold' => true,
                    'size' => 12,
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => [
                        'argb' => 'FFE2E8F0',
                    ],
                ],
            ],
        ];
    }

    /**
     * @return array
     */
    public function columnWidths(): array
    {
        return [
            'A' => 8,   // ID
            'B' => 30,  // Report Name
            'C' => 15,  // Type
            'D' => 15,  // Period
            'E' => 15,  // Status
            'F' => 20,  // Generated By
            'G' => 20,  // Created Date
            'H' => 20,  // Generated Date
            'I' => 15,  // Start Date
            'J' => 15,  // End Date
            'K' => 40,  // Description
        ];
    }
}
