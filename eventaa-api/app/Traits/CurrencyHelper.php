<?php

namespace App\Traits;

trait CurrencyHelper
{
    /**
     * Format currency amount with proper symbol
     */
    public function formatCurrency($amount, $user = null): string
    {
        $currency = $user?->currency ?? null;
        $currencyCode = $currency?->name ?? 'MWK';
        $symbol = $this->getCurrencySymbol($currencyCode);

        return $symbol . number_format((float)$amount, 2) . ' ' . $currencyCode;
    }

    /**
     * Get currency symbol for a given currency code
     */
    public function getCurrencySymbol(string $currencyCode): string
    {
        $symbols = [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'MWK' => 'MK',
            'ZAR' => 'R',
            'KES' => 'KSh',
            'UGX' => 'USh',
            'TZS' => 'TSh',
            'BWP' => 'P',
            'ZMW' => 'ZK',
            'NAD' => 'N$',
            'SZL' => 'L',
            'LSL' => 'L',
            'MZN' => 'MT',
            'AOA' => 'Kz',
            'XAF' => 'FCFA',
            'XOF' => 'CFA',
            'NGN' => '₦',
            'GHS' => 'GH₵',
            'ETB' => 'Br',
            'RWF' => 'RF',
            'BIF' => 'FBu',
            'DJF' => 'Fdj',
            'ERN' => 'Nfk',
            'SOS' => 'S',
            'SSP' => '£',
            'SDG' => 'ج.س.',
            'EGP' => 'E£',
            'LYD' => 'LD',
            'TND' => 'د.ت',
            'DZD' => 'دج',
            'MAD' => 'د.م.',
        ];

        return $symbols[$currencyCode] ?? $currencyCode . ' ';
    }

    /**
     * Format currency amount for display (without currency code)
     */
    public function formatAmount($amount, $user = null): string
    {
        $currency = $user?->currency ?? null;
        $currencyCode = $currency?->name ?? 'MWK';

        $symbol = $this->getCurrencySymbol($currencyCode);

        return $symbol . number_format((float)$amount, 2);
    }

    /**
     * Get user's preferred currency code
     */
    public function getUserCurrencyCode($user = null): string
    {
        $currency = $user?->currency ?? null;
        return $currency?->name ?? 'MWK';
    }
}
