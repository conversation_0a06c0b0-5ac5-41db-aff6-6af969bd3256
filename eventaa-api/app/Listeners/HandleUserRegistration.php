<?php

namespace App\Listeners;

use App\Events\UserRegistered;
use App\Jobs\SendEmailNotification;
use App\Mail\WelcomeEmail;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class HandleUserRegistration implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(UserRegistered $event): void
    {
        try {
            Log::info('Handling user registration event', [
                'user_id' => $event->user->id,
                'user_email' => $event->user->email
            ]);

            // Send welcome email
            dispatch(new SendEmailNotification(
                $event->user,
                'Welcome to EventaHub!',
                'Thank you for registering with EventaHub. We\'re excited to have you on board!',
                WelcomeEmail::class,
                [
                    'user_name' => $event->user->name,
                    'registration_date' => $event->user->created_at
                ]
            ));

            // Initialize user preferences
            $this->initializeUserPreferences($event->user);

            // Track registration analytics
            $this->trackRegistrationAnalytics($event->user);

            // Add user to mailing list (if opted in)
            if ($event->user->email_notifications ?? true) {
                $this->addToMailingList($event->user);
            }

            Log::info('User registration event handled successfully', [
                'user_id' => $event->user->id
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to handle user registration event', [
                'user_id' => $event->user->id,
                'error' => $e->getMessage()
            ]);

            // Don't rethrow the exception to prevent blocking user registration
        }
    }

    /**
     * Initialize default user preferences.
     */
    private function initializeUserPreferences($user): void
    {
        // Create default user preferences
        // This assumes you have a user_preferences table or similar
        $defaultPreferences = [
            'email_notifications' => true,
            'sms_notifications' => false,
            'marketing_emails' => true,
            'event_reminders' => true,
            'timezone' => config('app.timezone'),
            'language' => 'en'
        ];

        // Store preferences (you may need to create a UserPreference model)
        Log::info('User preferences initialized', [
            'user_id' => $user->id,
            'preferences' => $defaultPreferences
        ]);
    }

    /**
     * Track registration analytics.
     */
    private function trackRegistrationAnalytics($user): void
    {
        // Track user registration for analytics
        // You might want to dispatch this to a separate analytics job
        Log::info('User registration tracked for analytics', [
            'user_id' => $user->id,
            'registration_source' => request()->header('User-Agent'),
            'ip_address' => request()->ip(),
            'registered_at' => $user->created_at
        ]);
    }

    /**
     * Add user to mailing list.
     */
    private function addToMailingList($user): void
    {
        // Add user to your mailing list service (Mailchimp, SendGrid, etc.)
        Log::info('User added to mailing list', [
            'user_id' => $user->id,
            'email' => $user->email
        ]);
    }

    /**
     * Handle a job failure.
     */
    public function failed(UserRegistered $event, \Throwable $exception): void
    {
        Log::error('User registration listener failed', [
            'user_id' => $event->user->id,
            'error' => $exception->getMessage()
        ]);
    }
}
