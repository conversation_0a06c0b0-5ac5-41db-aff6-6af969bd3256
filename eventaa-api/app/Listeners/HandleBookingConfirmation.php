<?php

namespace App\Listeners;

use App\Events\BookingConfirmed;
use App\Jobs\SendEmailNotification;
use App\Jobs\SendTicketConfirmationEmail;
use App\Mail\BookingConfirmationEmail;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class HandleBookingConfirmation implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(BookingConfirmed $event): void
    {
        try {
            $ticketPurchase = $event->ticketPurchase;

            Log::info('Handling booking confirmation event', [
                'purchase_id' => $ticketPurchase->id,
                'user_id' => $ticketPurchase->user_id,
                'event_id' => $ticketPurchase->event_id
            ]);

            // Load relationships
            $ticketPurchase->load(['user', 'event', 'ticket']);

            /** @var \App\Models\User $user */
            $user = $ticketPurchase->user;

            dispatch(new SendEmailNotification(
                $user,
                'Booking Confirmed - ' . $ticketPurchase->event->title,
                'Your booking has been confirmed! Here are your ticket details.',
                BookingConfirmationEmail::class,
                [
                    'ticket_purchase' => $ticketPurchase,
                    'event' => $ticketPurchase->event,
                    'ticket' => $ticketPurchase->ticket
                ]
            ));

            dispatch(new \App\Jobs\ProcessTicketPurchase($ticketPurchase, $ticketPurchase->purchase_reference));

            $this->updateEventAnalytics($ticketPurchase);

            $this->notifyEventOrganizer($ticketPurchase);

            $this->updateTicketAvailability($ticketPurchase);

            $this->createCalendarEvent($ticketPurchase);

            Log::info('Booking confirmation event handled successfully', [
                'purchase_id' => $ticketPurchase->id
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to handle booking confirmation event', [
                'purchase_id' => $event->ticketPurchase->id,
                'error' => $e->getMessage()
            ]);

            throw $e; // Rethrow to retry the job
        }
    }

    /**
     * Update event analytics.
     */
    private function updateEventAnalytics($ticketPurchase): void
    {
        try {
            $ticket = $ticketPurchase->ticket;
            $ticket->increment('quantity_sold', $ticketPurchase->quantity);

            $event = $ticketPurchase->event;
            $event->increment('tickets_sold', $ticketPurchase->quantity);

            $totalRevenue = $event->ticket_purchases()
                ->where('status', 'completed')
                ->sum('total_amount');

            $event->update(['total_revenue' => $totalRevenue]);

            Log::info('Event analytics updated', [
                'event_id' => $event->id,
                'tickets_sold' => $ticketPurchase->quantity,
                'total_revenue' => $totalRevenue
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to update event analytics', [
                'purchase_id' => $ticketPurchase->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Notify event organizer.
     */
    private function notifyEventOrganizer($ticketPurchase): void
    {
        try {
            $organizer = $ticketPurchase->event->organizer;

            if ($organizer && $organizer->email) {
                dispatch(new SendEmailNotification(
                    $organizer,
                    'New Ticket Purchase - ' . $ticketPurchase->event->title,
                    "A new ticket has been purchased for your event. Ticket quantity: {$ticketPurchase->quantity}",
                    null,
                    [
                        'ticket_purchase' => $ticketPurchase,
                        'event' => $ticketPurchase->event,
                        'attendee_name' => $ticketPurchase->attendee_name,
                        'attendee_email' => $ticketPurchase->attendee_email
                    ]
                ));

                Log::info('Organizer notified of new ticket purchase', [
                    'organizer_id' => $organizer->id,
                    'purchase_id' => $ticketPurchase->id
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Failed to notify event organizer', [
                'purchase_id' => $ticketPurchase->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Update ticket availability.
     */
    private function updateTicketAvailability($ticketPurchase): void
    {
        try {
            $ticket = $ticketPurchase->ticket;
            $newAvailableQuantity = $ticket->quantity_available - $ticketPurchase->quantity;

            $ticket->update(['quantity_available' => max(0, $newAvailableQuantity)]);

            if ($newAvailableQuantity <= 0) {
                $ticket->update(['status' => 'sold_out']);

                Log::info('Ticket marked as sold out', [
                    'ticket_id' => $ticket->id,
                    'event_id' => $ticket->event_id
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Failed to update ticket availability', [
                'purchase_id' => $ticketPurchase->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Create calendar event for attendee.
     */
    private function createCalendarEvent($ticketPurchase): void
    {
        try {
            // Generate calendar event data
            $event = $ticketPurchase->event;

            $calendarData = [
                'title' => $event->title,
                'description' => $event->description,
                'start_date' => $event->start_date,
                'end_date' => $event->end_date,
                'location' => $event->location,
                'attendee_email' => $ticketPurchase->attendee_email
            ];

            Log::info('Calendar event data prepared', [
                'purchase_id' => $ticketPurchase->id,
                'event_title' => $event->title
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to create calendar event', [
                'purchase_id' => $ticketPurchase->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(BookingConfirmed $event, \Throwable $exception): void
    {
        Log::error('Booking confirmation listener failed', [
            'purchase_id' => $event->ticketPurchase->id,
            'error' => $exception->getMessage()
        ]);
    }
}
