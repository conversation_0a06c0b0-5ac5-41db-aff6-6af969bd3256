<?php

namespace App\Listeners;

use App\Events\TicketPurchaseCompleted;
use App\Models\HostBalance;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class ProcessTicketPurchaseEarnings implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(TicketPurchaseCompleted $event): void
    {
        try {
            Log::info('Processing ticket purchase earnings', [
                'purchase_count' => count($event->purchases),
                'total_amount' => $event->totalAmount,
                'user_id' => $event->user->id
            ]);

            foreach ($event->purchases as $purchase) {
                if (is_array($purchase)) {
                    $eventId = $purchase['event_id'] ?? null;
                    $ticketId = $purchase['ticket_id'] ?? null;
                    $totalAmount = $purchase['total_amount'] ?? 0;
                } else {
                    $eventId = $purchase->event_id ?? null;
                    $ticketId = $purchase->ticket_id ?? null;
                    $totalAmount = $purchase->total_amount ?? 0;
                }

                if (!$eventId || !$totalAmount) {
                    Log::warning('Missing event_id or total_amount for purchase', [
                        'purchase' => $purchase
                    ]);
                    continue;
                }

                $eventModel = \App\Models\Event::find($eventId);
                if (!$eventModel) {
                    Log::warning('Event not found', ['event_id' => $eventId]);
                    continue;
                }

                $organizerId = $eventModel->user_id;
                if (!$organizerId) {
                    Log::warning('Event has no organizer', ['event_id' => $eventId]);
                    continue;
                }

                $platformFeePercentage = 2.50;
                if ($ticketId) {
                    $ticket = \App\Models\Ticket::with('tier')->find($ticketId);
                    if ($ticket && $ticket->tier && $ticket->tier->platform_fee_percentage) {
                        $platformFeePercentage = $ticket->tier->platform_fee_percentage;
                    }
                }

                $platformFee = ($totalAmount * $platformFeePercentage) / 100;
                $organizerEarnings = $totalAmount - $platformFee;

                $hostBalance = HostBalance::getOrCreateForUser($organizerId);
                $hostBalance->addEarnings($organizerEarnings, $platformFee);

                Log::info('Added earnings to host balance', [
                    'organizer_id' => $organizerId,
                    'event_id' => $eventId,
                    'ticket_id' => $ticketId,
                    'total_amount' => $totalAmount,
                    'platform_fee_percentage' => $platformFeePercentage,
                    'platform_fee' => $platformFee,
                    'organizer_earnings' => $organizerEarnings,
                    'new_available_balance' => $hostBalance->fresh()->available_balance,
                    'new_total_earned' => $hostBalance->fresh()->total_earned
                ]);
            }

            Log::info('Ticket purchase earnings processed successfully', [
                'user_id' => $event->user->id,
                'purchases_processed' => count($event->purchases)
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to process ticket purchase earnings', [
                'user_id' => $event->user->id ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(TicketPurchaseCompleted $event, \Throwable $exception): void
    {
        Log::error('Ticket purchase earnings processing failed permanently', [
            'user_id' => $event->user->id ?? null,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
