<?php

namespace App\Notifications;

use App\Models\User;
use App\Models\TicketPurchase;
use App\Models\PaymentTransaction;
use App\Mail\TicketPurchaseConfirmation;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Collection;

class TicketPurchaseNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected Collection $purchases;
    protected PaymentTransaction $transaction;
    protected array $notificationData;

    /**
     * Create a new notification instance.
     */
    public function __construct(Collection $purchases, PaymentTransaction $transaction)
    {
        $this->purchases = $purchases;
        $this->transaction = $transaction;
        $this->notificationData = $this->prepareNotificationData();
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): TicketPurchaseConfirmation
    {
        return new TicketPurchaseConfirmation($notifiable, $this->purchases, $this->transaction);
    }

    /**
     * Get the array representation of the notification for database storage.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'ticket_purchase_confirmed',
            'title' => $this->notificationData['title'],
            'message' => $this->notificationData['message'],
            'data' => [
                'transaction_id' => $this->transaction->transaction_id,
                'total_amount' => $this->transaction->amount,
                'currency' => $this->transaction->currency,
                'total_tickets' => $this->purchases->sum('quantity'),
                'event_count' => $this->purchases->pluck('event_id')->unique()->count(),
                'event_names' => $this->purchases->pluck('event.name')->unique()->values()->toArray(),
                'purchase_date' => $this->transaction->payment_date ?? $this->transaction->created_at,
                'purchases' => $this->purchases->map(function ($purchase) {
                    return [
                        'id' => $purchase->id,
                        'purchase_reference' => $purchase->purchase_reference,
                        'event_title' => $purchase->event->name ?? 'Unknown Event',
                        'ticket_name' => $purchase->ticket->name ?? 'Unknown Ticket',
                        'quantity' => $purchase->quantity,
                        'total_amount' => $purchase->total_amount,
                        'attendee_name' => $purchase->attendee_name,
                    ];
                })->toArray(),
            ],
            'action_url' => '/dashboard/tickets',
            'action_text' => 'View My Tickets',
            'icon' => 'ticket',
            'priority' => 'high',
            'read_at' => null,
        ];
    }

    /**
     * Prepare notification data for display
     */
    private function prepareNotificationData(): array
    {
        // Ensure related data is loaded
        if (!$this->purchases->first()->relationLoaded('event') || !$this->purchases->first()->relationLoaded('ticket')) {
            $this->purchases->load(['event', 'ticket']);
        }

        $totalTickets = $this->purchases->sum('quantity');
        $eventNames = $this->purchases->pluck('event.name')->unique()->filter();
        $eventCount = $eventNames->count();

        if ($eventCount === 1) {
            $eventText = $eventNames->first();
        } elseif ($eventCount === 2) {
            $eventText = $eventNames->implode(' and ');
        } else {
            $eventText = $eventNames->take(2)->implode(', ') . ' and ' . ($eventCount - 2) . ' other event(s)';
        }

        $title = "Ticket Purchase Confirmed";
        $message = "Your purchase of {$totalTickets} ticket(s) for {$eventText} has been confirmed. Total amount: {$this->transaction->currency} " . number_format((float)$this->transaction->amount, 2);

        return [
            'title' => $title,
            'message' => $message,
            'event_text' => $eventText,
            'total_tickets' => $totalTickets,
        ];
    }

    /**
     * Get the notification's database type.
     */
    public function databaseType(object $notifiable): string
    {
        return 'ticket_purchase_confirmed';
    }
}
