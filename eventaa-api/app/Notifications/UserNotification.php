<?php
namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class UserNotification extends Notification
{
    use Queueable;

    public $data;
    public $channels;

    /**
     * Create a new notification instance.
     *
     * @param array $data
     * @param array|null $channels
     */
    public function __construct(array $data, array $channels = null)
    {
        $this->data = $data;
        $this->channels = $channels ?? ['database'];
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return $this->channels;
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->greeting($this->data['greeting'])
            ->subject($this->data['title'])
            ->line($this->data['line'])
            ->action($this->data['action'], url($this->data['actionURL']));
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return $this->data;
    }
}
