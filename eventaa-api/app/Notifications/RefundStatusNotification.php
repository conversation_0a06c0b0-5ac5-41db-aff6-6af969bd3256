<?php

namespace App\Notifications;

use App\Models\RefundRequest;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class RefundStatusNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected RefundRequest $refundRequest;

    /**
     * Create a new notification instance.
     */
    public function __construct(RefundRequest $refundRequest)
    {
        $this->refundRequest = $refundRequest;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $refund = $this->refundRequest;
        $event = $refund->ticketPurchase->event;
        $status = $refund->status;

        $mailMessage = (new MailMessage)
            ->greeting('Hello ' . $notifiable->name . ',');

        switch ($status) {
            case 'approved':
                $mailMessage
                    ->subject('Refund Approved - ' . $event->title)
                    ->line('Great news! Your refund request has been approved.')
                    ->line('**Event:** ' . $event->title)
                    ->line('**Refund Amount:** ' . $this->formatCurrency($refund->refund_amount, $refund->user))
                    ->line('**Reference:** ' . $refund->refund_reference)
                    ->line('Your refund is now being processed and will be credited back to your original payment method within 5-10 business days.');

                if ($refund->admin_notes) {
                    $mailMessage->line('**Admin Notes:** ' . $refund->admin_notes);
                }
                break;

            case 'rejected':
                $mailMessage
                    ->subject('Refund Request Declined - ' . $event->title)
                    ->line('We regret to inform you that your refund request has been declined.')
                    ->line('**Event:** ' . $event->title)
                    ->line('**Reference:** ' . $refund->refund_reference)
                    ->line('**Reason:** ' . $refund->rejection_reason);

                if ($refund->admin_notes) {
                    $mailMessage->line('**Additional Notes:** ' . $refund->admin_notes);
                }

                $mailMessage->line('If you believe this decision was made in error, please contact our support team.');
                break;

            case 'processing':
                $mailMessage
                    ->subject('Refund Processing - ' . $event->title)
                    ->line('Your approved refund is now being processed.')
                    ->line('**Event:** ' . $event->title)
                    ->line('**Refund Amount:** ' . $this->formatCurrency($refund->refund_amount, $refund->user))
                    ->line('**Reference:** ' . $refund->refund_reference)
                    ->line('The refund will be credited back to your original payment method within 5-10 business days.');
                break;

            case 'completed':
                $mailMessage
                    ->subject('Refund Completed - ' . $event->title)
                    ->line('Your refund has been successfully processed!')
                    ->line('**Event:** ' . $event->title)
                    ->line('**Refund Amount:** ' . $this->formatCurrency($refund->refund_amount, $refund->user))
                    ->line('**Reference:** ' . $refund->refund_reference);

                if ($refund->gateway_refund_id) {
                    $mailMessage->line('**Transaction ID:** ' . $refund->gateway_refund_id);
                }

                $mailMessage->line('The refund has been credited back to your original payment method. Please allow 5-10 business days for the amount to appear in your account.');
                break;

            case 'failed':
                $mailMessage
                    ->subject('Refund Processing Failed - ' . $event->title)
                    ->line('We encountered an issue while processing your refund.')
                    ->line('**Event:** ' . $event->title)
                    ->line('**Reference:** ' . $refund->refund_reference)
                    ->line('Our team has been notified and will resolve this issue promptly. You will receive another notification once the refund has been successfully processed.');

                if ($refund->rejection_reason) {
                    $mailMessage->line('**Error Details:** ' . $refund->rejection_reason);
                }
                break;
        }

        return $mailMessage
            ->action('View My Tickets', url('/my-profile?tab=tickets'))
            ->line('If you have any questions, please contact our support team.')
            ->salutation('Best regards, EventaHub Team');
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        $refund = $this->refundRequest;
        $event = $refund->ticketPurchase->event;
        $status = $refund->status;

        $baseData = [
            'refund_reference' => $refund->refund_reference,
            'refund_amount' => $refund->refund_amount,
            'event_id' => $event->id,
            'event_title' => $event->title,
            'status' => $status,
            'updated_at' => $refund->updated_at,
            'action_url' => '/my-profile?tab=tickets',
        ];

        switch ($status) {
            case 'approved':
                return array_merge($baseData, [
                    'type' => 'refund_approved',
                    'title' => 'Refund Approved',
                    'message' => 'Your refund request for ' . $event->title . ' has been approved',
                    'admin_notes' => $refund->admin_notes,
                ]);

            case 'rejected':
                return array_merge($baseData, [
                    'type' => 'refund_rejected',
                    'title' => 'Refund Request Declined',
                    'message' => 'Your refund request for ' . $event->title . ' has been declined',
                    'rejection_reason' => $refund->rejection_reason,
                    'admin_notes' => $refund->admin_notes,
                ]);

            case 'processing':
                return array_merge($baseData, [
                    'type' => 'refund_processing',
                    'title' => 'Refund Processing',
                    'message' => 'Your refund for ' . $event->title . ' is being processed',
                ]);

            case 'completed':
                return array_merge($baseData, [
                    'type' => 'refund_completed',
                    'title' => 'Refund Completed',
                    'message' => 'Your refund for ' . $event->title . ' has been completed',
                    'gateway_refund_id' => $refund->gateway_refund_id,
                ]);

            case 'failed':
                return array_merge($baseData, [
                    'type' => 'refund_failed',
                    'title' => 'Refund Processing Failed',
                    'message' => 'There was an issue processing your refund for ' . $event->title,
                    'error_details' => $refund->rejection_reason,
                ]);

            default:
                return array_merge($baseData, [
                    'type' => 'refund_status_update',
                    'title' => 'Refund Status Update',
                    'message' => 'Your refund status for ' . $event->title . ' has been updated',
                ]);
        }
    }

    /**
     * Format currency amount with proper symbol
     */
    private function formatCurrency($amount, $user = null): string
    {
        $currency = $user?->currency ?? null;
        $currencyCode = $currency?->name ?? 'MWK';

        // Get currency symbol based on currency code
        $symbol = $this->getCurrencySymbol($currencyCode);

        return $symbol . number_format((float)$amount, 2) . ' ' . $currencyCode;
    }

    /**
     * Get currency symbol for a given currency code
     */
    private function getCurrencySymbol(string $currencyCode): string
    {
        $symbols = [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'MWK' => 'MK',
            'ZAR' => 'R',
            'KES' => 'KSh',
            'UGX' => 'USh',
            'TZS' => 'TSh',
        ];

        return $symbols[$currencyCode] ?? $currencyCode . ' ';
    }
}
