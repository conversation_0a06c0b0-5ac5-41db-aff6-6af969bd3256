<?php

namespace App\Notifications;

use App\Models\Vendor;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class VendorRequestApprovedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The vendor instance.
     *
     * @var Vendor
     */
    protected $vendor;

    /**
     * Create a new notification instance.
     *
     * @param Vendor $vendor
     */
    public function __construct(Vendor $vendor)
    {
        $this->vendor = $vendor;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $vendorDashboardUrl = url('/vendor/dashboard');

        return (new MailMessage)
            ->subject('Congratulations! Your Vendor Request Has Been Approved')
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('We are pleased to inform you that your vendor request for "' . $this->vendor->name . '" has been approved.')
            ->line('You now have access to all vendor features and can start managing your services, bookings, and more.')
            ->line('As part of our welcome package, you have been given a 14-day trial subscription to explore all premium features.')
            ->action('Go to Vendor Dashboard', $vendorDashboardUrl)
            ->line('If you have any questions or need assistance, please don\'t hesitate to contact our support team.')
            ->line('Thank you for joining our platform as a vendor!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'vendor_id' => $this->vendor->id,
            'vendor_name' => $this->vendor->name,
            'message' => 'Your vendor request for "' . $this->vendor->name . '" has been approved.',
            'type' => 'vendor_request_approved',
            'url' => '/vendor/dashboard',
        ];
    }
}
