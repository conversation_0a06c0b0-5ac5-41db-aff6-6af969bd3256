<?php

namespace App\Notifications;

use App\Models\Vendor;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class NewVendorRequestNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The vendor instance.
     *
     * @var Vendor
     */
    protected $vendor;

    /**
     * Create a new notification instance.
     *
     * @param Vendor $vendor
     */
    public function __construct(Vendor $vendor)
    {
        $this->vendor = $vendor;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $url = url('/admin/vendors/' . $this->vendor->id);

        return (new MailMessage)
            ->subject('New Vendor Request: ' . $this->vendor->name)
            ->greeting('Hello Admin!')
            ->line('A new vendor request has been submitted and is pending approval.')
            ->line('Vendor Name: ' . $this->vendor->name)
            ->line('Business Email: ' . $this->vendor->business_email)
            ->line('Submitted by: ' . $this->vendor->user->name)
            ->action('Review Vendor Request', $url)
            ->line('Please review this request at your earliest convenience.')
            ->line('Thank you for using our application!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'vendor_id' => $this->vendor->id,
            'vendor_name' => $this->vendor->name,
            'user_id' => $this->vendor->user_id,
            'user_name' => $this->vendor->user->name,
            'message' => 'New vendor request from ' . $this->vendor->name . ' is pending approval.',
            'type' => 'vendor_request',
            'url' => '/admin/vendors/' . $this->vendor->id,
        ];
    }
}
