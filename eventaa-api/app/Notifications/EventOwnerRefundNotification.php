<?php

namespace App\Notifications;

use App\Models\RefundRequest;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class EventOwnerRefundNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected RefundRequest $refundRequest;
    protected string $notificationType;

    /**
     * Create a new notification instance.
     */
    public function __construct(RefundRequest $refundRequest, string $notificationType = 'request')
    {
        $this->refundRequest = $refundRequest;
        $this->notificationType = $notificationType; // 'request', 'approved', 'completed'
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $refund = $this->refundRequest;
        $event = $refund->ticketPurchase->event;
        $customer = $refund->user;

        $mailMessage = (new MailMessage)
            ->greeting('Hello ' . $notifiable->name . ',');

        switch ($this->notificationType) {
            case 'request':
                $mailMessage
                    ->subject('Refund Request for Your Event - ' . $event->title)
                    ->line('A customer has requested a refund for your event.')
                    ->line('**Event:** ' . $event->title)
                    ->line('**Customer:** ' . $customer->name . ' (' . $customer->email . ')')
                    ->line('**Refund Amount:** ' . $this->formatCurrency($refund->refund_amount, $refund->user))
                    ->line('**Processing Fee:** ' . $this->formatCurrency($refund->processing_fee, $refund->user))
                    ->line('**Reason:** ' . $refund->reason)
                    ->line('**Reference:** ' . $refund->refund_reference)
                    ->line('The refund request is currently pending review by our team.')
                    ->action('View Event Dashboard', url('/dashboard/events/' . $event->id))
                    ->line('You will be notified once the refund has been processed.');
                break;

            case 'approved':
                $mailMessage
                    ->subject('Refund Approved for Your Event - ' . $event->title)
                    ->line('A refund request for your event has been approved.')
                    ->line('**Event:** ' . $event->title)
                    ->line('**Customer:** ' . $customer->name)
                    ->line('**Refund Amount:** ' . $this->formatCurrency($refund->refund_amount, $refund->user))
                    ->line('**Reference:** ' . $refund->refund_reference)
                    ->line('The refund is now being processed and will be completed within 5-10 business days.')
                    ->action('View Event Dashboard', url('/dashboard/events/' . $event->id));
                break;

            case 'completed':
                $mailMessage
                    ->subject('Refund Completed for Your Event - ' . $event->title)
                    ->line('A refund for your event has been successfully completed.')
                    ->line('**Event:** ' . $event->title)
                    ->line('**Customer:** ' . $customer->name)
                    ->line('**Refund Amount:** ' . $this->formatCurrency($refund->refund_amount, $refund->user))
                    ->line('**Reference:** ' . $refund->refund_reference);

                if ($refund->gateway_refund_id) {
                    $mailMessage->line('**Transaction ID:** ' . $refund->gateway_refund_id);
                }

                $mailMessage
                    ->line('The refund has been processed and the customer has been notified.')
                    ->action('View Event Dashboard', url('/dashboard/events/' . $event->id));
                break;

            case 'rejected':
                $mailMessage
                    ->subject('Refund Request Rejected for Your Event - ' . $event->title)
                    ->line('A refund request for your event has been rejected.')
                    ->line('**Event:** ' . $event->title)
                    ->line('**Customer:** ' . $customer->name)
                    ->line('**Reference:** ' . $refund->refund_reference)
                    ->line('**Rejection Reason:** ' . $refund->rejection_reason)
                    ->line('The customer has been notified of this decision.')
                    ->action('View Event Dashboard', url('/dashboard/events/' . $event->id));
                break;
        }

        return $mailMessage
            ->line('Thank you for using EventaHub for your events.')
            ->salutation('Best regards, EventaHub Team');
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        $refund = $this->refundRequest;
        $event = $refund->ticketPurchase->event;
        $customer = $refund->user;

        $baseData = [
            'refund_reference' => $refund->refund_reference,
            'refund_amount' => $refund->refund_amount,
            'processing_fee' => $refund->processing_fee,
            'event_id' => $event->id,
            'event_title' => $event->title,
            'customer_id' => $customer->id,
            'customer_name' => $customer->name,
            'customer_email' => $customer->email,
            'status' => $refund->status,
            'reason' => $refund->reason,
            'created_at' => $refund->created_at,
            'action_url' => '/dashboard/events/' . $event->id,
        ];

        switch ($this->notificationType) {
            case 'request':
                return array_merge($baseData, [
                    'type' => 'event_owner_refund_request',
                    'title' => 'Refund Request for Your Event',
                    'message' => $customer->name . ' has requested a refund for ' . $event->title,
                    'priority' => 'normal',
                ]);

            case 'approved':
                return array_merge($baseData, [
                    'type' => 'event_owner_refund_approved',
                    'title' => 'Refund Approved',
                    'message' => 'Refund for ' . $event->title . ' has been approved',
                    'priority' => 'low',
                ]);

            case 'completed':
                return array_merge($baseData, [
                    'type' => 'event_owner_refund_completed',
                    'title' => 'Refund Completed',
                    'message' => 'Refund for ' . $event->title . ' has been completed',
                    'priority' => 'low',
                    'gateway_refund_id' => $refund->gateway_refund_id,
                ]);

            case 'rejected':
                return array_merge($baseData, [
                    'type' => 'event_owner_refund_rejected',
                    'title' => 'Refund Request Rejected',
                    'message' => 'Refund request for ' . $event->title . ' has been rejected',
                    'priority' => 'low',
                    'rejection_reason' => $refund->rejection_reason,
                ]);

            default:
                return array_merge($baseData, [
                    'type' => 'event_owner_refund_update',
                    'title' => 'Refund Update',
                    'message' => 'Refund status updated for ' . $event->title,
                    'priority' => 'low',
                ]);
        }
    }

    /**
     * Format currency amount with proper symbol
     */
    private function formatCurrency($amount, $user = null): string
    {
        $currency = $user?->currency ?? null;
        $currencyCode = $currency?->name ?? 'MWK';

        // Get currency symbol based on currency code
        $symbol = $this->getCurrencySymbol($currencyCode);

        return $symbol . number_format((float)$amount, 2) . ' ' . $currencyCode;
    }

    /**
     * Get currency symbol for a given currency code
     */
    private function getCurrencySymbol(string $currencyCode): string
    {
        $symbols = [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'MWK' => 'MK',
            'ZAR' => 'R',
            'KES' => 'KSh',
            'UGX' => 'USh',
            'TZS' => 'TSh',
        ];

        return $symbols[$currencyCode] ?? $currencyCode . ' ';
    }
}
