<?php

namespace App\Notifications;

use App\Models\Event;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Carbon\Carbon;

class EventReminderNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $event;

    /**
     * Create a new notification instance.
     */
    public function __construct(Event $event)
    {
        $this->event = $event;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $formattedStartTime = Carbon::parse($this->event->start)->format('F j, Y \a\t g:i A');
        $eventLocation = $this->event->location ?? 'Online';

        return (new MailMessage)
            ->subject("Reminder: Your event '{$this->event->title}' starts soon")
            ->greeting("Hello {$notifiable->name}!")
            ->line("This is a friendly reminder that an event you're attending starts in 30 minutes.")
            ->line("Event: {$this->event->title}")
            ->line("Time: {$formattedStartTime}")
            ->line("Location: {$eventLocation}")
            ->line("Don't forget to check the event details for any last-minute updates.")
            ->action('View Event Details', url("/events/{$this->event->slug}"))
            ->line('We look forward to seeing you there!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'event_reminder',
            'event_id' => $this->event->id,
            'event_title' => $this->event->title,
            'event_slug' => $this->event->slug,
            'event_start' => $this->event->start,
            'event_location' => $this->event->location ?? 'Online',
            'message' => "Your event '{$this->event->title}' starts in 30 minutes.",
        ];
    }
}
