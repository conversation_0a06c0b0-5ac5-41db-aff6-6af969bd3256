<?php

namespace App\Notifications;

use App\Models\Vendor;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class VendorRequestRejectedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The vendor instance.
     *
     * @var Vendor
     */
    protected $vendor;

    /**
     * Create a new notification instance.
     *
     * @param Vendor $vendor
     */
    public function __construct(Vendor $vendor)
    {
        $this->vendor = $vendor;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $supportUrl = url('/contact-support');

        return (new MailMessage)
            ->subject('Update on Your Vendor Request')
            ->greeting('Hello ' . $notifiable->name . ',')
            ->line('We have reviewed your vendor request for "' . $this->vendor->name . '".')
            ->line('Unfortunately, we are unable to approve your request at this time.')
            ->line('Reason: ' . ($this->vendor->rejection_reason ?: 'Your application does not meet our current requirements.'))
            ->line('You are welcome to submit a new request addressing the issues mentioned above.')
            ->action('Contact Support', $supportUrl)
            ->line('If you have any questions or need further clarification, please don\'t hesitate to contact our support team.')
            ->line('Thank you for your interest in becoming a vendor on our platform.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'vendor_id' => $this->vendor->id,
            'vendor_name' => $this->vendor->name,
            'message' => 'Your vendor request for "' . $this->vendor->name . '" has been rejected.',
            'reason' => $this->vendor->rejection_reason,
            'type' => 'vendor_request_rejected',
            'url' => '/contact-support',
        ];
    }
}
