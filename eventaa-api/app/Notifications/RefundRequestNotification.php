<?php

namespace App\Notifications;

use App\Models\RefundRequest;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class RefundRequestNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected RefundRequest $refundRequest;
    protected bool $isEventOwner;

    /**
     * Create a new notification instance.
     */
    public function __construct(RefundRequest $refundRequest, bool $isEventOwner = false)
    {
        $this->refundRequest = $refundRequest;
        $this->isEventOwner = $isEventOwner;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $refund = $this->refundRequest;
        $event = $refund->ticketPurchase->event;
        $user = $refund->user;

        if ($this->isEventOwner) {
            return (new MailMessage)
                ->subject('Refund Request for Your Event - ' . $event->title)
                ->greeting('Hello ' . $notifiable->name . ',')
                ->line('A refund request has been submitted for your event.')
                ->line('**Event:** ' . $event->title)
                ->line('**Customer:** ' . $user->name . ' (' . $user->email . ')')
                ->line('**Refund Amount:** ' . $this->formatCurrency($refund->refund_amount, $refund->user))
                ->line('**Reason:** ' . $refund->reason)
                ->line('**Reference:** ' . $refund->refund_reference)
                ->line('The refund request is currently pending review by our team.')
                ->action('View Event Dashboard', url('/dashboard/events/' . $event->id))
                ->line('You will be notified once the refund has been processed.')
                ->salutation('Best regards, EventaHub Team');
        }

        return (new MailMessage)
            ->subject('Refund Request Submitted - ' . $event->title)
            ->greeting('Hello ' . $notifiable->name . ',')
            ->line('Your refund request has been successfully submitted.')
            ->line('**Event:** ' . $event->title)
            ->line('**Refund Amount:** ' . $this->formatCurrency($refund->refund_amount, $refund->user))
            ->line('**Processing Fee:** ' . $this->formatCurrency($refund->processing_fee, $refund->user))
            ->line('**Reference:** ' . $refund->refund_reference)
            ->line('**Status:** Pending Review')
            ->line('Your refund request is currently being reviewed by our team. This process typically takes 3-5 business days.')
            ->action('View My Tickets', url('/my-profile?tab=tickets'))
            ->line('You will receive an email notification once your refund has been processed.')
            ->line('If you have any questions, please contact our support team.')
            ->salutation('Best regards, EventaHub Team');
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        $refund = $this->refundRequest;
        $event = $refund->ticketPurchase->event;

        if ($this->isEventOwner) {
            return [
                'type' => 'refund_request_event_owner',
                'title' => 'Refund Request for Your Event',
                'message' => 'A refund request has been submitted for ' . $event->title,
                'refund_reference' => $refund->refund_reference,
                'refund_amount' => $refund->refund_amount,
                'event_id' => $event->id,
                'event_title' => $event->title,
                'customer_name' => $refund->user->name,
                'customer_email' => $refund->user->email,
                'reason' => $refund->reason,
                'status' => $refund->status,
                'created_at' => $refund->created_at,
                'action_url' => '/dashboard/manage-events/' . $event->id,
            ];
        }

        return [
            'type' => 'refund_request_submitted',
            'title' => 'Refund Request Submitted',
            'message' => 'Your refund request for ' . $event->title . ' has been submitted',
            'refund_reference' => $refund->refund_reference,
            'refund_amount' => $refund->refund_amount,
            'processing_fee' => $refund->processing_fee,
            'event_id' => $event->id,
            'event_title' => $event->title,
            'status' => $refund->status,
            'estimated_processing_time' => '3-5 business days',
            'created_at' => $refund->created_at,
            'action_url' => '/my-profile?tab=Tickets',
        ];
    }

    /**
     * Format currency amount with proper symbol
     */
    private function formatCurrency($amount, $user = null): string
    {
        $currency = $user?->currency ?? null;
        $currencyCode = $currency?->name ?? 'MWK';

        // Get currency symbol based on currency code
        $symbol = $this->getCurrencySymbol($currencyCode);

        return $symbol . number_format((float)$amount, 2);
    }

    /**
     * Get currency symbol for a given currency code
     */
    private function getCurrencySymbol(string $currencyCode): string
    {
        $symbols = [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'MWK' => 'MK',
            'ZAR' => 'R',
            'KES' => 'KSh',
            'UGX' => 'USh',
            'TZS' => 'TSh',
        ];

        return $symbols[$currencyCode] ?? $currencyCode . ' ';
    }
}
