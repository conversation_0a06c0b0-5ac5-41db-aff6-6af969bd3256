<?php

namespace App\Notifications;

use App\Models\PaymentTransaction;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class PaymentTimeoutNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $transaction;

    /**
     * Create a new notification instance.
     */
    public function __construct(PaymentTransaction $transaction)
    {
        $this->transaction = $transaction;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $paymentType = $this->getPaymentTypeLabel();
        $amount = $this->formatCurrency($this->transaction->amount);

        return (new MailMessage)
            ->subject('Payment Timeout - ' . $paymentType)
            ->greeting('Hello ' . $notifiable->name . ',')
            ->line('Your payment for ' . $paymentType . ' has timed out.')
            ->line('**Payment Details:**')
            ->line('- Reference: ' . $this->transaction->transaction_id)
            ->line('- Amount: ' . $amount)
            ->line('- Type: ' . $paymentType)
            ->line('The payment was not completed within the allowed time frame and has been cancelled.')
            ->line('If you still wish to proceed, please initiate a new payment.')
            ->action('Try Again', $this->getRetryUrl())
            ->line('If you have any questions, please contact our support team.')
            ->salutation('Best regards, EventaHub Team');
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'payment_timeout',
            'title' => 'Payment Timeout',
            'message' => 'Your payment for ' . $this->getPaymentTypeLabel() . ' has timed out.',
            'transaction_id' => $this->transaction->transaction_id,
            'amount' => $this->transaction->amount,
            'currency' => $this->transaction->currency,
            'payment_type' => $this->transaction->payment_type,
            'timeout_at' => now()->toISOString(),
        ];
    }

    /**
     * Get payment type label
     */
    private function getPaymentTypeLabel(): string
    {
        switch ($this->transaction->payment_type) {
            case 'subscription':
                return 'Subscription';
            case 'ticket_purchase':
                return 'Ticket Purchase';
            case 'booking':
                return 'Booking';
            default:
                return 'Payment';
        }
    }

    /**
     * Format currency
     */
    private function formatCurrency(float $amount): string
    {
        return number_format($amount, 0) . ' ' . ($this->transaction->currency ?? 'MWK');
    }

    /**
     * Get retry URL based on payment type
     */
    private function getRetryUrl(): string
    {
        $baseUrl = config('app.frontend_url', 'http://localhost:3000');
        
        switch ($this->transaction->payment_type) {
            case 'subscription':
                return $baseUrl . '/subscription/plans';
            case 'ticket_purchase':
                // Try to get event ID from metadata
                $metadata = $this->transaction->metadata ?? [];
                if (isset($metadata['event_id'])) {
                    return $baseUrl . '/events/' . $metadata['event_id'];
                }
                return $baseUrl . '/events';
            case 'booking':
                return $baseUrl . '/bookings';
            default:
                return $baseUrl . '/dashboard';
        }
    }
}
