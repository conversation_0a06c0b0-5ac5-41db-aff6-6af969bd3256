<?php

namespace App\Notifications;

use App\Models\Event;
use App\Models\EventInvitation;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class EventInvitationNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $event;
    protected $invitation;

    /**
     * Create a new notification instance.
     */
    public function __construct(Event $event, EventInvitation $invitation)
    {
        $this->event = $event;
        $this->invitation = $invitation;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $eventOwner = $this->event->user;

        return (new MailMessage)
            ->subject("You're invited to {$this->event->title} - EventaHub")
            ->view('emails.event-invitation', [
                'event' => $this->event,
                'invitation' => $this->invitation,
                'eventOwner' => $eventOwner,
                'subject' => "You're invited to {$this->event->title} - EventaHub"
            ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'event_invitation',
            'event_id' => $this->event->id,
            'event_title' => $this->event->title,
            'event_slug' => $this->event->slug,
            'invitation_id' => $this->invitation->id,
            'invited_by' => $this->event->user->name,
            'invited_by_id' => $this->event->user_id,
            'invitation_token' => $this->invitation->invitation_token,
            'message' => $this->invitation->message,
            'acceptance_url' => $this->invitation->getAcceptanceUrl(),
            'decline_url' => $this->invitation->getDeclineUrl(),
        ];
    }

    /**
     * Get the notification's database type.
     *
     * @return string
     */
    public function databaseType(object $notifiable): string
    {
        return 'event_invitation';
    }
}
