<?php

namespace App\Console\Commands;

use App\Models\Withdrawal;
use App\Models\HostBalance;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixFailedWithdrawals extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'withdrawals:fix-failed {--dry-run : Run without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix withdrawals that are stuck in processing status but should be failed';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');

        $this->info('🔍 Scanning for failed withdrawals...');

        // Find failed withdrawals - ones that are still "processing" but without gateway_reference
        $failedWithdrawals = Withdrawal::where('status', 'processing')
            ->where(function ($query) {
                $query->whereNull('gateway_reference')
                      ->orWhere('gateway_reference', '');
            })
            ->get();

        if ($failedWithdrawals->isEmpty()) {
            $this->info('✅ No failed withdrawals found that need fixing.');
            return 0;
        }

        $this->info("Found {$failedWithdrawals->count()} withdrawals that need to be marked as failed:");

        $this->table(
            ['ID', 'Reference', 'User ID', 'Amount', 'Status', 'Created At', 'Notes'],
            $failedWithdrawals->map(function ($withdrawal) {
                return [
                    $withdrawal->id,
                    $withdrawal->reference,
                    $withdrawal->user_id,
                    'MK ' . number_format((float) $withdrawal->amount),
                    $withdrawal->status,
                    $withdrawal->created_at->format('Y-m-d H:i:s'),
                    $withdrawal->notes ?? 'No notes'
                ];
            })
        );

        if (!$dryRun) {
            if (!$this->confirm('Do you want to proceed with fixing these withdrawals?')) {
                $this->info('Operation cancelled.');
                return 0;
            }
        }

        $fixed = 0;
        $errors = 0;

        foreach ($failedWithdrawals as $withdrawal) {
            try {
                if (!$dryRun) {
                    DB::transaction(function () use ($withdrawal) {
                        // Update withdrawal status to failed
                        $withdrawal->update([
                            'status' => 'failed',
                            'notes' => 'Marked as failed by fix command - PayChangu API call failed',
                            'processed_at' => now(),
                        ]);

                        // Revert balance changes - add back the amount to available balance
                        $hostBalance = HostBalance::where('user_id', $withdrawal->user_id)->first();
                        if ($hostBalance) {
                            $hostBalance->increment('available_balance', (float) $withdrawal->amount);
                            // If there's a pending_balance field, decrement it
                            if (isset($hostBalance->pending_balance) && $hostBalance->pending_balance >= $withdrawal->amount) {
                                $hostBalance->decrement('pending_balance', (float) $withdrawal->amount);
                            }
                        }

                        Log::info('Fixed failed withdrawal', [
                            'withdrawal_id' => $withdrawal->id,
                            'reference' => $withdrawal->reference,
                            'user_id' => $withdrawal->user_id,
                            'amount' => $withdrawal->amount
                        ]);
                    });
                }

                $this->info("✓ Fixed withdrawal {$withdrawal->reference} (ID: {$withdrawal->id})");
                $fixed++;

            } catch (\Exception $e) {
                $this->error("✗ Failed to fix withdrawal {$withdrawal->reference}: {$e->getMessage()}");
                $errors++;

                Log::error('Failed to fix withdrawal', [
                    'withdrawal_id' => $withdrawal->id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        if ($dryRun) {
            $this->info("\n🔍 DRY RUN COMPLETE - No changes were made");
            $this->info("Would fix {$failedWithdrawals->count()} withdrawals");
        } else {
            $this->info("\n✅ FIX COMPLETE:");
            $this->info("✓ Successfully fixed: {$fixed} withdrawals");
            if ($errors > 0) {
                $this->error("✗ Errors: {$errors} withdrawals");
            }
        }

        return 0;
    }
}
