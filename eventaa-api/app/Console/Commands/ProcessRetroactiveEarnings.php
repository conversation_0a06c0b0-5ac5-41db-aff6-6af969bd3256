<?php

namespace App\Console\Commands;

use App\Models\TicketPurchase;
use App\Models\HostBalance;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ProcessRetroactiveEarnings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'earnings:process-retroactive {--dry-run : Run without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process earnings for existing completed ticket purchases that haven\'t been credited to host balances yet';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $isDryRun = $this->option('dry-run');

        if ($isDryRun) {
            $this->info('🔍 Running in DRY RUN mode - no changes will be made');
        }

        $this->info('🎫 Processing retroactive earnings for completed ticket purchases...');

        // Get all completed ticket purchases
        $completedPurchases = TicketPurchase::where('status', 'completed')
            ->with(['event', 'ticket.tier'])
            ->get();

        $this->info("📊 Found {$completedPurchases->count()} completed ticket purchases");

        $processedCount = 0;
        $skippedCount = 0;
        $errorCount = 0;
        $totalEarningsProcessed = 0;

        foreach ($completedPurchases as $purchase) {
            try {
                // Check if this purchase belongs to an event with an organizer
                if (!$purchase->event || !$purchase->event->user_id) {
                    $this->warn("⚠️  Purchase {$purchase->id}: No event or organizer found");
                    $skippedCount++;
                    continue;
                }

                $organizerId = $purchase->event->user_id;
                $totalAmount = $purchase->total_amount;

                // Get platform fee from the ticket tier, fallback to default
                $platformFeePercentage = 2.50; // Default platform fee
                if ($purchase->ticket && $purchase->ticket->tier && $purchase->ticket->tier->platform_fee_percentage) {
                    $platformFeePercentage = $purchase->ticket->tier->platform_fee_percentage;
                }

                $platformFee = ($totalAmount * $platformFeePercentage) / 100;
                $organizerEarnings = $totalAmount - $platformFee;

                if (!$isDryRun) {
                    // Get or create host balance for the event organizer
                    $hostBalance = HostBalance::getOrCreateForUser($organizerId);

                    // Add earnings to the host balance
                    $hostBalance->addEarnings($organizerEarnings, $platformFee);
                }

                $this->line("✅ Purchase {$purchase->id}: Event '{$purchase->event->title}' - Organizer {$organizerId} - Earnings: " . number_format($organizerEarnings, 2) . " (Fee: " . number_format($platformFee, 2) . ")");

                $processedCount++;
                $totalEarningsProcessed += $organizerEarnings;

            } catch (\Exception $e) {
                $this->error("❌ Error processing purchase {$purchase->id}: " . $e->getMessage());
                Log::error('Error processing retroactive earnings', [
                    'purchase_id' => $purchase->id,
                    'error' => $e->getMessage()
                ]);
                $errorCount++;
            }
        }

        $this->newLine();
        $this->info('📈 Processing Summary:');
        $this->info("   ✅ Processed: {$processedCount}");
        $this->info("   ⚠️  Skipped: {$skippedCount}");
        $this->info("   ❌ Errors: {$errorCount}");
        $this->info("   💰 Total earnings processed: " . number_format($totalEarningsProcessed, 2));

        if ($isDryRun) {
            $this->info('🔍 This was a DRY RUN - no changes were made. Run without --dry-run to apply changes.');
        } else {
            $this->info('✅ Retroactive earnings processing completed!');
        }

        return Command::SUCCESS;
    }
}
