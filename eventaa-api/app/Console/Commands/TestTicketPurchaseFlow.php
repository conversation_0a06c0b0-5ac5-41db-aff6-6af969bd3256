<?php

namespace App\Console\Commands;

use App\Services\PayChanguService;
use App\Models\User;
use App\Models\Ticket;
use Illuminate\Console\Command;

class TestTicketPurchaseFlow extends Command
{
    protected $signature = 'paychangu:test-ticket-purchase {--user-id=1} {--ticket-id=1}';
    protected $description = 'Test the complete ticket purchase flow with PayChangu';

    public function handle()
    {
        $this->info('Testing Ticket Purchase Flow with PayChangu...');
        
        $userId = $this->option('user-id');
        $ticketId = $this->option('ticket-id');
        
        // Find user and ticket
        $user = User::find($userId);
        $ticket = Ticket::find($ticketId);
        
        if (!$user) {
            $this->error("User with ID {$userId} not found");
            return 1;
        }
        
        if (!$ticket) {
            $this->error("Ticket with ID {$ticketId} not found");
            return 1;
        }
        
        $this->info("✓ User and ticket found");
        $this->line("  - User: {$user->name} ({$user->email})");
        $this->line("  - Ticket: {$ticket->name} - " . number_format($ticket->price) . " MWK");
        
        // Test PayChangu service
        $this->info("Testing PayChangu ticket purchase...");
        
        try {
            $tickets = [
                [
                    'ticket_id' => $ticket->id,
                    'quantity' => 1
                ]
            ];
            
            $service = new PayChanguService();
            $result = $service->processTicketPurchase(
                $tickets,
                $user,
                route('payments.webhook')
            );
            
            if ($result['status']) {
                $this->info("✓ Ticket purchase payment initialized successfully");
                $this->line("  - Reference: " . ($result['data']['reference'] ?? 'N/A'));
                $this->line("  - Payment URL: " . ($result['data']['payment_url'] ?? 'N/A'));
                
                if (isset($result['data']['payment_url'])) {
                    $this->info("✓ Payment URL generated - user can proceed to payment");
                }
                
                return 0;
            } else {
                $this->error("✗ Ticket purchase failed: " . ($result['message'] ?? 'Unknown error'));
                if (isset($result['data'])) {
                    $this->line("Error details: " . json_encode($result['data'], JSON_PRETTY_PRINT));
                }
                return 1;
            }
            
        } catch (\Exception $e) {
            $this->error("✗ Exception during ticket purchase: " . $e->getMessage());
            $this->line("Stack trace: " . $e->getTraceAsString());
            return 1;
        }
    }
}
