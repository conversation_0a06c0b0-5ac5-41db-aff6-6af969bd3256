<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class CheckScheduledTaskLogs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'logs:scheduled-tasks {task? : Specific task log to check} {--lines=50 : Number of lines to display}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check logs for scheduled tasks';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $task = $this->argument('task');
        $lines = $this->option('lines');

        $logFiles = [
            'notifications' => 'notifications-send.log',
            'users' => 'users-delete-inactive.log',
            'payments' => 'payments-check-stale.log',
            'tweets' => 'tweets-fetch.log',
            'earnings' => 'earnings-process.log',
            'withdrawals' => 'withdrawals-fix.log'
        ];

        if ($task && isset($logFiles[$task])) {
            $this->displayLog($logFiles[$task], $lines);
        } elseif ($task) {
            $this->error("Unknown task: $task");
            $this->info("Available tasks: " . implode(', ', array_keys($logFiles)));
        } else {
            $this->info("Available task logs:");

            foreach ($logFiles as $taskName => $logFile) {
                $path = storage_path('logs/' . $logFile);
                $exists = File::exists($path);
                $size = $exists ? File::size($path) : 0;
                $lastModified = $exists ? date('Y-m-d H:i:s', File::lastModified($path)) : 'N/A';

                $this->line(sprintf(
                    "- <info>%s</info>: %s (Size: %s, Last modified: %s)",
                    $taskName,
                    $exists ? 'EXISTS' : 'MISSING',
                    $exists ? $this->formatBytes($size) : 'N/A',
                    $lastModified
                ));
            }

            $this->line("\nUse <info>php artisan logs:scheduled-tasks {task}</info> to view a specific log.");
        }
    }

    private function displayLog($logFile, $lines)
    {
        $path = storage_path('logs/' . $logFile);

        if (!File::exists($path)) {
            $this->warn("Log file does not exist yet: $logFile");
            return;
        }

        $this->info("Showing last $lines lines of $logFile:");
        $this->line('');

        $file = new \SplFileObject($path, 'r');
        $file->seek(PHP_INT_MAX);
        $totalLines = $file->key();

        $startLine = max(0, $totalLines - $lines);
        $file->seek($startLine);

        while (!$file->eof()) {
            $line = $file->current();
            $this->line($line);
            $file->next();
        }
    }

    private function formatBytes($size, $precision = 2)
    {
        $base = log($size, 1024);
        $suffixes = ['B', 'KB', 'MB', 'GB', 'TB'];

        return round(pow(1024, $base - floor($base)), $precision) . ' ' . $suffixes[floor($base)];
    }
}
