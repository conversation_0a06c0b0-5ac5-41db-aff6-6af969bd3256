<?php

namespace App\Console\Commands;

use App\Services\PayChanguService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class TestPayChanguConfig extends Command
{
    protected $signature = 'paychangu:test-config';
    protected $description = 'Test PayChangu API configuration';

    public function handle()
    {
        $this->info('Testing PayChangu Configuration...');

        // Test environment variables
        $this->info('1. Checking environment variables:');
        $apiKey = env('PAYCHANGU_API_KEY');
        $secretKey = env('PAYCHANGU_SECRET_KEY');
        $merchantId = env('PAYCHANGU_MERCHANT_ID');
        $testMode = env('PAYCHANGU_TEST_MODE');

        $this->line("   API Key: " . ($apiKey ? substr($apiKey, 0, 20) . '...' : 'NOT SET'));
        $this->line("   Secret Key: " . ($secretKey ? substr($secretKey, 0, 20) . '...' : 'NOT SET'));
        $this->line("   Merchant ID: " . ($merchantId ?: 'NOT SET'));
        $this->line("   Test Mode: " . ($testMode ? 'true' : 'false'));

        if (!$apiKey || !$secretKey || !$merchantId) {
            $this->error('Missing required PayChangu configuration!');
            return 1;
        }

        // Test PayChangu service initialization
        $this->info('2. Testing PayChangu service initialization:');
        try {
            $service = new PayChanguService();
            $this->info('   ✓ PayChangu service initialized successfully');
        } catch (\Exception $e) {
            $this->error('   ✗ PayChangu service initialization failed: ' . $e->getMessage());
            return 1;
        }

        // Test API connectivity with a simple payment initialization
        $this->info('3. Testing API connectivity with payment endpoint:');
        try {
            $testPayload = [
                'amount' => 100,
                'currency' => 'MWK',
                'tx_ref' => 'TEST-CONFIG-' . time(),
                'callback_url' => route('payments.webhook'),
                'return_url' => route('payments.webhook'),
                'customization' => [
                    'title' => 'EventaHub Test',
                    'description' => 'Configuration test',
                ],
            ];

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $secretKey,
                'Content-Type' => 'application/json',
            ])->post('https://api.paychangu.com/payment', $testPayload);

            if ($response->successful()) {
                $this->info('   ✓ API connectivity and authentication successful');
                $this->line('   Response status: ' . $response->status());
                $data = $response->json();
                if (isset($data['data']['link'])) {
                    $this->line('   Payment URL generated successfully');
                }
            } else {
                $this->error('   ✗ API call failed');
                $this->line('   Status: ' . $response->status());
                $this->line('   Response: ' . $response->body());
            }
        } catch (\Exception $e) {
            $this->error('   ✗ API connectivity exception: ' . $e->getMessage());
        }

        // Test payment initialization (dry run)
        $this->info('4. Testing payment initialization (dry run):');
        try {
            $testPayload = [
                'amount' => 1000,
                'currency' => 'MWK',
                'tx_ref' => 'TEST-' . time(),
                'callback_url' => route('payments.webhook'),
                'return_url' => route('payments.webhook'),
                'customization' => [
                    'title' => 'EventaHub Test Payment',
                    'description' => 'Test payment for configuration verification',
                ],
                'meta' => [
                    'type' => 'test',
                    'test_mode' => true,
                ],
            ];

            $this->line('   Test payload prepared:');
            $this->line('   - Amount: ' . $testPayload['amount'] . ' ' . $testPayload['currency']);
            $this->line('   - Reference: ' . $testPayload['tx_ref']);
            $this->line('   - Callback URL: ' . $testPayload['callback_url']);

            // Note: We're not actually sending this to avoid creating test transactions
            $this->info('   ✓ Payment payload validation successful (dry run)');

        } catch (\Exception $e) {
            $this->error('   ✗ Payment initialization test failed: ' . $e->getMessage());
        }

        $this->info('PayChangu configuration test completed!');
        return 0;
    }
}
