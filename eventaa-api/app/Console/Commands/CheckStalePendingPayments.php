<?php

namespace App\Console\Commands;

use App\Models\PaymentTransaction;
use App\Jobs\VerifyPaymentJob;
use App\Jobs\HandlePaymentTimeoutJob;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CheckStalePendingPayments extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'payments:check-stale 
                            {--timeout=60 : Timeout in minutes for pending payments}
                            {--verify-delay=5 : Delay in minutes before verification}
                            {--dry-run : Show what would be done without executing}';

    /**
     * The console command description.
     */
    protected $description = 'Check for stale pending payments and trigger verification or timeout handling';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $timeoutMinutes = (int) $this->option('timeout');
        $verifyDelayMinutes = (int) $this->option('verify-delay');
        $dryRun = $this->option('dry-run');

        $this->info("Checking for stale pending payments...");
        $this->info("Timeout threshold: {$timeoutMinutes} minutes");
        $this->info("Verification delay: {$verifyDelayMinutes} minutes");
        
        if ($dryRun) {
            $this->warn("DRY RUN MODE - No actions will be taken");
        }

        // Find payments that are pending and older than verification delay
        $stalePendingPayments = PaymentTransaction::where('status', 'pending')
            ->where('created_at', '<=', now()->subMinutes($verifyDelayMinutes))
            ->get();

        if ($stalePendingPayments->isEmpty()) {
            $this->info("No stale pending payments found.");
            return 0;
        }

        $this->info("Found {$stalePendingPayments->count()} stale pending payments.");

        $verificationCount = 0;
        $timeoutCount = 0;

        foreach ($stalePendingPayments as $payment) {
            $ageMinutes = $payment->created_at->diffInMinutes(now());
            
            $this->line("Processing payment {$payment->transaction_id} (Age: {$ageMinutes} minutes)");

            if ($ageMinutes >= $timeoutMinutes) {
                // Payment has exceeded timeout threshold
                $this->warn("  → Payment exceeded timeout threshold, scheduling timeout handling");
                
                if (!$dryRun) {
                    HandlePaymentTimeoutJob::dispatch($payment->transaction_id, 0); // Immediate timeout
                }
                $timeoutCount++;
            } else {
                // Payment is stale but not timed out, trigger verification
                $this->info("  → Payment is stale, scheduling verification");
                
                if (!$dryRun) {
                    VerifyPaymentJob::dispatch($payment->transaction_id);
                }
                $verificationCount++;
            }
        }

        if (!$dryRun) {
            $this->info("Scheduled {$verificationCount} payments for verification");
            $this->info("Scheduled {$timeoutCount} payments for timeout handling");
            
            Log::info('Stale pending payments check completed', [
                'total_found' => $stalePendingPayments->count(),
                'verification_scheduled' => $verificationCount,
                'timeout_scheduled' => $timeoutCount,
                'timeout_threshold_minutes' => $timeoutMinutes,
                'verify_delay_minutes' => $verifyDelayMinutes,
            ]);
        } else {
            $this->info("Would schedule {$verificationCount} payments for verification");
            $this->info("Would schedule {$timeoutCount} payments for timeout handling");
        }

        return 0;
    }
}
