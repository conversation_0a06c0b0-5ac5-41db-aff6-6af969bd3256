<?php

namespace App\Console\Commands;

use App\Services\PayChanguService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class TestPayChanguConnection extends Command
{
    protected $signature = 'paychangu:test';
    protected $description = 'Test PayChangu API connection and configuration';

    public function handle()
    {
        $this->info('Testing PayChangu API connection...');

        // Test 1: Check environment variables
        $this->info('1. Checking environment variables...');
        $apiKey = env('PAYCHANGU_API_KEY');
        $secretKey = env('PAYCHANGU_SECRET_KEY');
        $merchantId = env('PAYCHANGU_MERCHANT_ID');
        $testMode = env('PAYCHANGU_TEST_MODE', true);

        $this->line("API Key: " . ($apiKey ? 'SET (' . strlen($apiKey) . ' chars)' : 'NOT SET'));
        $this->line("Secret Key: " . ($secretKey ? 'SET (' . strlen($secretKey) . ' chars)' : 'NOT SET'));
        $this->line("Merchant ID: " . ($merchantId ? 'SET (' . strlen($merchantId) . ' chars)' : 'NOT SET'));
        $this->line("Test Mode: " . ($testMode ? 'TRUE' : 'FALSE'));

        if (!$secretKey) {
            $this->error('Secret key is required for PayChangu API calls');
            return 1;
        }

        // Test 2: Test basic API connectivity
        $this->info('2. Testing API connectivity...');
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $secretKey,
                'Accept' => 'application/json',
            ])->timeout(10)->get('https://api.paychangu.com/verify-payment/test-reference');

            $this->line("API Response Status: " . $response->status());
            $this->line("API Response: " . $response->body());

            if ($response->status() === 404) {
                $this->info('✓ API is reachable (404 expected for test reference)');
            } elseif ($response->status() === 401) {
                $this->error('✗ Authentication failed - check your secret key');
                return 1;
            } else {
                $this->info('✓ API is reachable');
            }
        } catch (\Exception $e) {
            $this->error('✗ API connection failed: ' . $e->getMessage());
            return 1;
        }

        // Test 3: Test PayChanguService initialization
        $this->info('3. Testing PayChanguService...');
        try {
            $service = new PayChanguService();
            $this->info('✓ PayChanguService initialized successfully');
        } catch (\Exception $e) {
            $this->error('✗ PayChanguService initialization failed: ' . $e->getMessage());
            return 1;
        }

        // Test 4: Test mobile money operators
        $this->info('4. Testing mobile money operators...');
        try {
            $result = $service->getMobileMoneyOperators();

            if ($result['status']) {
                $this->info('✓ Mobile money operators fetched successfully');
                $this->line("Operators: " . count($result['data']) . " found");
                foreach ($result['data'] as $operator) {
                    $this->line("  - " . ($operator['name'] ?? 'Unknown') . " (" . ($operator['id'] ?? 'No ID') . ")");
                }
            } else {
                $this->error('✗ Failed to fetch mobile money operators');
                return 1;
            }
        } catch (\Exception $e) {
            $this->error('✗ Mobile money operators test failed: ' . $e->getMessage());
            return 1;
        }

        // Test 5: Test direct payment initialization (dry run)
        $this->info('5. Testing direct payment initialization...');
        try {
            $testReference = 'TEST-' . time();
            $testPhone = '999123456'; // 9 digits without country code

            // Get the first operator from the fetched list
            $operatorsResult = $service->getMobileMoneyOperators();
            if (!$operatorsResult['status'] || empty($operatorsResult['data'])) {
                $this->error('✗ No operators available for testing');
                return 1;
            }

            $testOperatorId = $operatorsResult['data'][0]['id']; // Use the first operator ID
            $this->line("Using operator: " . $operatorsResult['data'][0]['name'] . " (ID: $testOperatorId)");

            $result = $service->initializeDirectPayment(
                100.0,
                'MWK',
                $testReference,
                $testPhone,
                $testOperatorId,
                ['type' => 'test']
            );

            if ($result['status']) {
                $this->info('✓ Direct payment initialization successful');
                $this->line("Charge ID: " . ($result['charge_id'] ?? 'Not provided'));
            } else {
                $this->error('✗ Direct payment initialization failed: ' . ($result['message'] ?? 'Unknown error'));
                $this->line("Response: " . json_encode($result, JSON_PRETTY_PRINT));
                return 1;
            }
        } catch (\Exception $e) {
            $this->error('✗ Direct payment initialization failed: ' . $e->getMessage());
            return 1;
        }

        $this->info('All tests passed! PayChangu integration appears to be working correctly.');
        return 0;
    }
}
