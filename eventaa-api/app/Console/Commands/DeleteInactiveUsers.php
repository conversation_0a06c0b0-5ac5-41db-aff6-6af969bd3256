<?php

namespace App\Console\Commands;

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Console\Command;

class DeleteInactiveUsers extends Command
{
    protected $signature = 'users:delete-inactive';
    protected $description = 'Delete users who have been deactivated for more than 14 days';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $thresholdDate = Carbon::now()->subDays(14);

        $usersToDelete = User::where('deactivation_date', '<=', $thresholdDate)->get();

        foreach ($usersToDelete as $user) {
            $user->delete();
        }

        $this->info('Inactive users deleted successfully.');
    }
}
