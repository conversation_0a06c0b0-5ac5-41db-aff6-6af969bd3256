<?php

namespace App\Console\Commands;

use App\Services\PayChanguService;
use App\Models\PaymentGateway;
use App\Models\User;
use App\Models\Plan;
use App\Models\Ticket;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class TestPayChanguIntegration extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'paychangu:test 
                            {--scenario=all : Test scenario to run (all, connection, subscription, tickets, webhook)}
                            {--user-id=1 : User ID for testing}
                            {--plan-id=1 : Plan ID for subscription testing}
                            {--ticket-id=1 : Ticket ID for ticket testing}';

    /**
     * The console command description.
     */
    protected $description = 'Test PayChangu integration with various scenarios';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $scenario = $this->option('scenario');
        
        $this->info('PayChangu Integration Test Suite');
        $this->info('================================');

        switch ($scenario) {
            case 'all':
                return $this->runAllTests();
            case 'connection':
                return $this->testConnection();
            case 'subscription':
                return $this->testSubscriptionPayment();
            case 'tickets':
                return $this->testTicketPurchase();
            case 'webhook':
                return $this->testWebhookHandling();
            default:
                $this->error("Unknown scenario: {$scenario}");
                $this->info("Available scenarios: all, connection, subscription, tickets, webhook");
                return 1;
        }
    }

    /**
     * Run all test scenarios
     */
    private function runAllTests(): int
    {
        $this->info('Running all test scenarios...');
        
        $results = [
            'connection' => $this->testConnection(),
            'subscription' => $this->testSubscriptionPayment(),
            'tickets' => $this->testTicketPurchase(),
            'webhook' => $this->testWebhookHandling(),
        ];

        $this->info("\nTest Results Summary:");
        $this->info("====================");
        
        $passed = 0;
        $total = count($results);
        
        foreach ($results as $test => $result) {
            $status = $result === 0 ? '✅ PASSED' : '❌ FAILED';
            $this->line("{$test}: {$status}");
            if ($result === 0) $passed++;
        }

        $this->info("\nOverall: {$passed}/{$total} tests passed");
        
        return array_sum($results) === 0 ? 0 : 1;
    }

    /**
     * Test PayChangu connection
     */
    private function testConnection(): int
    {
        $this->info("\n1. Testing PayChangu Connection");
        $this->info("==============================");

        try {
            $gateway = PaymentGateway::where('slug', 'paychangu')->first();
            
            if (!$gateway) {
                $this->error("PayChangu gateway not found in database");
                return 1;
            }

            $this->info("✓ PayChangu gateway found");
            $this->line("  - Name: {$gateway->name}");
            $this->line("  - Active: " . ($gateway->is_active ? 'Yes' : 'No'));
            $this->line("  - Test Mode: " . ($gateway->test_mode ? 'Yes' : 'No'));

            $service = new PayChanguService($gateway);
            $result = $service->getMobileMoneyOperators();

            if ($result['status']) {
                $this->info("✓ API connection successful");
                $operators = $result['data'] ?? [];
                $this->line("  - Available operators: " . count($operators));
                return 0;
            } else {
                $this->error("✗ API connection failed: " . ($result['message'] ?? 'Unknown error'));
                return 1;
            }
        } catch (\Exception $e) {
            $this->error("✗ Connection test failed: " . $e->getMessage());
            return 1;
        }
    }

    /**
     * Test subscription payment
     */
    private function testSubscriptionPayment(): int
    {
        $this->info("\n2. Testing Subscription Payment");
        $this->info("===============================");

        try {
            $userId = $this->option('user-id');
            $planId = $this->option('plan-id');

            $user = User::find($userId);
            $plan = Plan::find($planId);

            if (!$user) {
                $this->error("User with ID {$userId} not found");
                return 1;
            }

            if (!$plan) {
                $this->error("Plan with ID {$planId} not found");
                return 1;
            }

            $this->info("✓ User and plan found");
            $this->line("  - User: {$user->name} ({$user->email})");
            $this->line("  - Plan: {$plan->name} - " . number_format($plan->price) . " {$plan->currency}");

            $service = new PayChanguService();
            $result = $service->processSubscriptionPayment(
                $plan,
                $user,
                'http://localhost:8000/api/payments/webhook'
            );

            if ($result['status']) {
                $this->info("✓ Subscription payment initialized");
                $this->line("  - Reference: " . $result['data']['reference']);
                $this->line("  - Payment URL: " . ($result['data']['payment_url'] ?? 'N/A'));
                return 0;
            } else {
                $this->error("✗ Subscription payment failed: " . $result['message']);
                return 1;
            }
        } catch (\Exception $e) {
            $this->error("✗ Subscription test failed: " . $e->getMessage());
            return 1;
        }
    }

    /**
     * Test ticket purchase
     */
    private function testTicketPurchase(): int
    {
        $this->info("\n3. Testing Ticket Purchase");
        $this->info("==========================");

        try {
            $userId = $this->option('user-id');
            $ticketId = $this->option('ticket-id');

            $user = User::find($userId);
            $ticket = Ticket::find($ticketId);

            if (!$user) {
                $this->error("User with ID {$userId} not found");
                return 1;
            }

            if (!$ticket) {
                $this->error("Ticket with ID {$ticketId} not found");
                return 1;
            }

            $this->info("✓ User and ticket found");
            $this->line("  - User: {$user->name} ({$user->email})");
            $this->line("  - Ticket: {$ticket->name} - " . number_format($ticket->price) . " MWK");

            $tickets = [
                [
                    'ticket_id' => $ticket->id,
                    'quantity' => 1
                ]
            ];

            $service = new PayChanguService();
            $result = $service->processTicketPurchase(
                $tickets,
                $user,
                'http://localhost:8000/api/payments/webhook'
            );

            if ($result['status']) {
                $this->info("✓ Ticket purchase initialized");
                $this->line("  - Reference: " . $result['data']['reference']);
                $this->line("  - Total Amount: " . number_format($result['data']['total_amount']) . " MWK");
                return 0;
            } else {
                $this->error("✗ Ticket purchase failed: " . $result['message']);
                return 1;
            }
        } catch (\Exception $e) {
            $this->error("✗ Ticket purchase test failed: " . $e->getMessage());
            return 1;
        }
    }

    /**
     * Test webhook handling
     */
    private function testWebhookHandling(): int
    {
        $this->info("\n4. Testing Webhook Handling");
        $this->info("===========================");

        try {
            $webhookUrl = 'http://localhost:8000/api/payments/webhook';
            
            $testPayload = [
                'event_type' => 'api.charge.payment',
                'reference' => 'TEST-' . uniqid(),
                'status' => 'success',
                'amount' => 1000,
                'currency' => 'MWK',
                'customer' => [
                    'email' => '<EMAIL>',
                    'phone' => '+265999123456',
                ],
                'meta' => [
                    'type' => 'test',
                    'test_scenario' => 'webhook_test',
                ],
            ];

            $this->info("✓ Test payload prepared");
            $this->line("  - Reference: " . $testPayload['reference']);
            $this->line("  - Status: " . $testPayload['status']);
            $this->line("  - Amount: " . number_format($testPayload['amount']) . " " . $testPayload['currency']);

            // Generate test signature
            $webhookSecret = config('paychangu-test.test_credentials.webhook_secret');
            $signature = hash_hmac('sha256', json_encode($testPayload), $webhookSecret);

            $this->info("✓ Webhook signature generated");

            // Test webhook endpoint (this would normally be called by PayChangu)
            $this->warn("Note: This test simulates webhook delivery but doesn't actually call the endpoint");
            $this->line("  - Webhook URL: {$webhookUrl}");
            $this->line("  - Signature: " . substr($signature, 0, 20) . "...");

            return 0;
        } catch (\Exception $e) {
            $this->error("✗ Webhook test failed: " . $e->getMessage());
            return 1;
        }
    }
}
