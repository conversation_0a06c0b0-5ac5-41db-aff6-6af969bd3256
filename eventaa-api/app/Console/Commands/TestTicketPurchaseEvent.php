<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Events\TicketPurchaseCompleted;
use App\Models\User;
use App\Models\TicketPurchase;

class TestTicketPurchaseEvent extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:ticket-purchase-event {user_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the ticket purchase completion event broadcasting';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userId = $this->argument('user_id');
        
        $user = User::find($userId);
        if (!$user) {
            $this->error("User with ID {$userId} not found");
            return 1;
        }

        $this->info("Testing ticket purchase event for user: {$user->name} (ID: {$user->id})");

        // Get some recent ticket purchases for this user or create mock data
        $purchases = TicketPurchase::where('user_id', $user->id)
            ->latest()
            ->take(1)
            ->get();

        if ($purchases->isEmpty()) {
            $this->warn("No ticket purchases found for this user. Creating mock data...");
            
            // Create mock purchase data
            $mockPurchases = collect([
                (object) [
                    'id' => 999,
                    'purchase_reference' => 'TEST-' . uniqid(),
                    'event_id' => 1,
                    'ticket_id' => 1,
                    'quantity' => 2,
                    'total_amount' => 50.00,
                    'status' => 'completed',
                ]
            ]);
            
            $purchases = $mockPurchases;
        }

        $this->info("Broadcasting event with " . count($purchases) . " purchase(s)");

        try {
            // Fire the event
            event(new TicketPurchaseCompleted(
                $purchases,
                $user,
                (float) $purchases->sum('total_amount')
            ));

            $this->info("✅ Event broadcasted successfully!");
            $this->info("Check the frontend console and Laravel logs for confirmation.");
            
            return 0;
        } catch (\Exception $e) {
            $this->error("❌ Failed to broadcast event: " . $e->getMessage());
            $this->error("Stack trace: " . $e->getTraceAsString());
            return 1;
        }
    }
}
