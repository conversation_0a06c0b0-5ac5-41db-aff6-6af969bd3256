<?php

namespace App\Console\Commands;

use App\Models\Event;
use App\Models\EventAttendee;
use App\Models\User;
use App\Notifications\EventReminderNotification;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SendEventNotification extends Command
{
    protected $signature = 'notifications:send';

    protected $description = 'Send notifications to event attendees 30 minutes before event starts';

    public function handle()
    {
        $this->info('Starting to process event reminders...');

        $currentTime = Carbon::now();
        $thirtyMinutesLater = $currentTime->copy()->addMinutes(30);

        $events = Event::where('start', '>', $currentTime)
            ->where('start', '<=', $thirtyMinutesLater)
            ->get();

        $this->info('Found ' . $events->count() . ' events starting in the next 30 minutes');

        $notificationCount = 0;

        foreach ($events as $event) {
            $eventTitle = $event['title'] ?? 'Unknown Event';
            $eventId = $event['id'] ?? 'Unknown ID';

            $attendees = $event->attendees()->with('user')->get();

            if ($attendees->isEmpty()) {
                $this->info("No attendees found for event: {$eventTitle} (ID: {$eventId})");
                continue;
            }

            $this->info("Processing event: {$eventTitle} with {$attendees->count()} attendees");

            foreach ($attendees as $attendee) {
                $user = $attendee->user;

                if (!$user) {
                    $attendeeId = $attendee['id'] ?? 'Unknown';
                    $this->warn("No user found for attendee (ID: {$attendeeId})");
                    continue;
                }

                // Check if user has enabled event notifications
                $userWantsNotifications = $this->userWantsEventNotifications($user);

                if (!$userWantsNotifications) {
                    $userName = $user['name'] ?? 'Unknown';
                    $this->line("Skipping notification for {$userName} - notifications disabled");
                    continue;
                }

                try {
                    $user->notify(new EventReminderNotification($event));
                    $notificationCount++;

                    $userName = $user['name'] ?? 'Unknown';
                    $userEmail = $user['email'] ?? 'Unknown';
                    $this->line("Sent notification to {$userName} ({$userEmail}) for event: {$eventTitle}");
                } catch (\Exception $e) {
                    $userId = $user['id'] ?? 'Unknown';
                    $this->error("Failed to send notification to user: " . ($user['email'] ?? 'Unknown') . ": " . $e->getMessage());
                    Log::error("Event notification error: " . $e->getMessage(), [
                        'event_id' => $eventId,
                        'user_id' => $userId,
                        'exception' => $e
                    ]);
                }
            }
        }

        $this->info("Event reminder notifications completed. Sent {$notificationCount} notifications.");
    }

    /**
     * Check if a user has enabled event notifications in their settings
     *
     * @param User $user The user to check
     * @return bool True if the user wants event notifications, false otherwise
     */
    private function userWantsEventNotifications($user): bool
    {
        // If user doesn't have a settings relationship, default to true
        if (!method_exists($user, 'settings')) {
            return true;
        }

        try {
            // First check for an 'event_reminders' setting specifically
            $hasEventReminders = $user->settings()
                ->where('key', 'email_event_reminders')
                ->exists();

            if ($hasEventReminders) {
                return true;
            }

            // Fall back to checking if they have bookings notifications enabled
            // since that's the closest setting we have to event notifications
            return $user->settings()
                ->where('key', 'email_bookings')
                ->exists();
        } catch (\Exception $e) {
            // Log the error but don't block notifications
            $this->warn("Error checking user notification settings: " . $e->getMessage());
            Log::warning("Error checking user notification settings: " . $e->getMessage(), [
                'user_id' => $user['id'] ?? 'Unknown',
                'exception' => $e
            ]);

            // Default to sending notifications if there's an error checking settings
            return true;
        }
    }
}
