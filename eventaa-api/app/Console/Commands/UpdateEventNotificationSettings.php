<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class UpdateEventNotificationSettings extends Command
{
    protected $signature = 'notifications:update-settings';
    protected $description = 'Update event notification settings in the database';

    public function handle()
    {
        $this->info('Updating event notification settings...');

        try {
            // Run the EventNotificationSettingsSeeder
            $this->call('db:seed', [
                '--class' => 'Database\\Seeders\\EventNotificationSettingsSeeder',
                '--force' => true
            ]);

            $this->info('Event notification settings updated successfully.');
        } catch (\Exception $e) {
            $this->error('Failed to update event notification settings: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
