<?php

namespace App\Console\Commands;

use App\Models\CachedTweet;
use <PERSON>\TwitterOAuth\TwitterOAuth;
use Carbon\Carbon;
use Illuminate\Console\Command;

class FetchTweetsCommand extends Command
{
    protected $signature = 'tweets:fetch {hashtag} {--limit=10}';
    protected $description = 'Fetch and cache tweets for a specific hashtag';

    public function handle()
    {
        $hashtag = $this->argument('hashtag');
        $limit = $this->option('limit');

        $this->info("Fetching tweets for #{$hashtag}...");

        $connection = new TwitterOAuth(
            config('services.twitter.api_key'),
            config('services.twitter.api_secret_key'),
            config('services.twitter.access_token'),
            config('services.twitter.access_token_secret')
        );

        $connection->setApiVersion('2');

        try {
            $tweets = $connection->get('tweets/search/recent', [
                'query' => '#' . $hashtag,
                'max_results' => $limit,
                'tweet.fields' => 'created_at,author_id,text'
            ]);

            if ($connection->getLastHttpCode() != 200) {
                $this->error('Failed to fetch tweets: ' . json_encode($tweets));
                return 1;
            }

            $saved = 0;
            foreach ($tweets->data as $tweet) {
                CachedTweet::updateOrCreate(
                    [
                        'tweet_id' => $tweet->id,
                        'hashtag' => $hashtag
                    ],
                    [
                        'content' => $tweet->text,
                        'author_id' => $tweet->author_id,
                        'tweet_created_at' => Carbon::parse($tweet->created_at),
                        'metadata' => json_encode($tweet)
                    ]
                );
                $saved++;
            }

            $this->info("Successfully saved {$saved} tweets.");
        } catch (\Exception $e) {
            $this->error('Error: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
