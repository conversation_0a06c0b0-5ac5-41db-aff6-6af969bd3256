<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        $schedule->command('notifications:send')
            ->everyThirtyMinutes()
            ->appendOutputTo(storage_path('logs/notifications-send.log'))
            ->emailOutputOnFailure(env('ADMIN_EMAIL'));

        $schedule->command('users:delete-inactive')
            ->daily()
            ->appendOutputTo(storage_path('logs/users-delete-inactive.log'));

        $schedule->command('payments:check-stale')
            ->everyFiveMinutes()
            ->appendOutputTo(storage_path('logs/payments-check-stale.log'));

        $schedule->command('tweets:fetch malawi-events --limit=10')
            ->daily()
            ->appendOutputTo(storage_path('logs/tweets-fetch.log'));

        $schedule->command('earnings:process-retroactive')
            ->daily()
            ->appendOutputTo(storage_path('logs/earnings-process.log'));

        $schedule->command('withdrawals:fix-failed')
            ->daily()
            ->appendOutputTo(storage_path('logs/withdrawals-fix.log'));
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
