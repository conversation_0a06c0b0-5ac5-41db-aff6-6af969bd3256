<?php

namespace App\Mail;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Mail\Mailables\Attachment;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;

class TicketConfirmationMail extends Mailable
{
    use Queueable, SerializesModels;

    public $user;
    public $purchases;
    public $generatedTickets;
    public $pdfUrl;
    public $eventName;
    public $ticketCount;
    public $totalAmount;
    public $currencySymbol;

    /**
     * Create a new message instance.
     */
    public function __construct(User $user, array $purchases, array $generatedTickets, ?string $pdfUrl = null)
    {
        $this->user = $user;
        $this->purchases = $purchases;
        $this->generatedTickets = $generatedTickets;
        $this->pdfUrl = $pdfUrl;
        
        // Extract event details
        $firstPurchase = $purchases[0] ?? null;
        $this->eventName = $firstPurchase->event->name ?? 'Event';
        $this->ticketCount = count($generatedTickets);
        
        // Calculate total amount
        $this->totalAmount = collect($purchases)->sum(function ($purchase) {
            return $purchase->quantity * $purchase->ticket->price;
        });
        
        // Get currency symbol from the first ticket or default to MK
        $this->currencySymbol = $firstPurchase->event->currency_symbol ?? 'MK';
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: "Your Tickets for {$this->eventName} - Purchase Confirmed",
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.ticket-confirmation',
            with: [
                'user' => $this->user,
                'eventName' => $this->eventName,
                'ticketCount' => $this->ticketCount,
                'totalAmount' => $this->totalAmount,
                'currencySymbol' => $this->currencySymbol,
                'purchases' => $this->purchases,
                'generatedTickets' => $this->generatedTickets,
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        $attachments = [];
        
        if ($this->pdfUrl) {
            // Convert URL to file path
            $pdfPath = str_replace(url('/'), '', $this->pdfUrl);
            $fullPath = public_path($pdfPath);
            
            if (file_exists($fullPath)) {
                $attachments[] = Attachment::fromPath($fullPath)
                    ->as("tickets-{$this->eventName}.pdf")
                    ->withMime('application/pdf');
            }
        }
        
        return $attachments;
    }
}
