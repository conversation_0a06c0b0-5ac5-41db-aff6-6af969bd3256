<?php

namespace App\Mail;

use App\Models\User;
use App\Models\PaymentTransaction;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class TicketPurchaseConfirmation extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public User $user;
    public Collection $purchases;
    public PaymentTransaction $transaction;
    public array $ticketDetails;

    /**
     * Create a new message instance.
     */
    public function __construct(User $user, Collection $purchases, PaymentTransaction $transaction)
    {
        $this->user = $user;
        $this->purchases = $purchases;
        $this->transaction = $transaction;

        // Prepare ticket details for the email
        $this->ticketDetails = $this->prepareTicketDetails();
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $eventNames = $this->purchases->pluck('event.title')->unique()->implode(', ');
        $ticketCount = $this->purchases->sum('quantity');

        return new Envelope(
            to: $this->user->email,
            subject: "Ticket Confirmation - {$ticketCount} ticket(s) for {$eventNames}",
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.ticket-confirmation',
            with: [
                'user' => $this->user,
                'purchases' => $this->purchases,
                'transaction' => $this->transaction,
                'ticketDetails' => $this->ticketDetails,
                'totalAmount' => $this->transaction->amount,
                'currency' => $this->transaction->currency,
                'totalTickets' => $this->purchases->sum('quantity'),
                'eventNames' => $this->purchases->pluck('event.title')->unique(),
                'purchaseDate' => $this->transaction->payment_date ?? $this->transaction->created_at,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     */
    public function attachments(): array
    {
        return [];
    }

    /**
     * Prepare ticket details for email display
     */
    private function prepareTicketDetails(): array
    {
        $details = [];

        foreach ($this->purchases as $purchase) {
            $event = $purchase->event;
            $ticket = $purchase->ticket;

            $details[] = [
                'event_title' => $event->title,
                'event_date' => $event->start_date,
                'event_time' => $event->start_time,
                'event_location' => $event->location,
                'ticket_name' => $ticket->name,
                'ticket_type' => $ticket->type,
                'quantity' => $purchase->quantity,
                'unit_price' => $ticket->price,
                'total_price' => $purchase->total_amount,
                'purchase_reference' => $purchase->purchase_reference,
                'attendee_name' => $purchase->attendee_name,
                'attendee_email' => $purchase->attendee_email,
                'attendee_phone' => $purchase->attendee_phone,
            ];
        }

        return $details;
    }
}
