<?php

namespace App\Mail;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Mail\Mailables\Attachment;
use Illuminate\Queue\SerializesModels;

class TicketGenerationMail extends Mailable
{
    use Queueable, SerializesModels;

    public User $user;
    public array $data;

    /**
     * Create a new message instance.
     */
    public function __construct(User $user, array $data)
    {
        $this->user = $user;
        $this->data = $data;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: $this->data['title'] ?? 'Event Tickets Generated',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.ticket-generation',
            with: [
                'user' => $this->user,
                'greeting' => $this->data['greeting'] ?? 'Hello!',
                'title' => $this->data['title'] ?? 'Event Tickets Generated',
                'line' => $this->data['line'] ?? 'Your tickets have been generated.',
                'action' => $this->data['action'] ?? null,
                'actionURL' => $this->data['actionURL'] ?? null,
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        $attachments = [];
        
        if (isset($this->data['pdf_attachment']) && file_exists($this->data['pdf_attachment'])) {
            $filename = $this->data['pdf_filename'] ?? 'tickets.pdf';
            $attachments[] = Attachment::fromPath($this->data['pdf_attachment'])
                ->as($filename)
                ->withMime('application/pdf');
        }
        
        return $attachments;
    }
}
