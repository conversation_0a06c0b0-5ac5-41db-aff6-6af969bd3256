<?php

namespace App\Mail;

use App\Models\User;
use App\Models\Venue;
use App\Models\VenueBooking;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class BookingConfirmation extends Mailable
{
    use Queueable, SerializesModels;

    public $booking;
    public $venue;
    public $user;

    /**
     * Create a new message instance.
     *
     * @param VenueBooking $booking
     * @param Venue $venue
     * @param User $user
     * @return void
     */
    public function __construct(VenueBooking $booking, Venue $venue, User $user)
    {
        $this->booking = $booking;
        $this->venue = $venue;
        $this->user = $user;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->subject('Your Booking Confirmation')
                    ->view('emails.booking-confirmation');
    }
}