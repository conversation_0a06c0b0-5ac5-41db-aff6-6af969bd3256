<?php

namespace App\Mail;

use App\Models\NewsletterSubscription;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class NewsletterMail extends Mailable
{
    use Queueable, SerializesModels;

    public $subscription;
    public $type;
    public $subject;
    public $content;
    public $unsubscribeUrl;

    /**
     * Create a new message instance.
     */
    public function __construct(NewsletterSubscription $subscription, string $type, string $subject, array $content)
    {
        $this->subscription = $subscription;
        $this->type = $type;
        $this->subject = $subject;
        $this->content = $content;
        $this->unsubscribeUrl = url('/api/newsletter/unsubscribe/' . $subscription->unsubscribe_token);
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: $this->subject,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.newsletter.' . $this->type,
            with: [
                'subscription' => $this->subscription,
                'type' => $this->type,
                'content' => $this->content,
                'unsubscribeUrl' => $this->unsubscribeUrl,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
