<?php

namespace App\Mail;

use App\Models\User;
use App\Models\Venue;
use App\Models\VenueBooking;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class BookingStatus extends Mailable
{
    use Queueable, SerializesModels;

    public $booking;
    public $venue;
    public $user;

    /**
     * Create a new message instance.
     *
     * @param VenueBooking $booking
     * @param Venue $venue
     * @param User $user
     */
    public function __construct(VenueBooking $booking, Venue $venue, User $user)
    {
        $this->booking = $booking;
        $this->venue = $venue;
        $this->user = $user;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $status = ucfirst($this->booking->status);
        return $this->subject("Your Booking Has Been $status")
                    ->view('emails.booking-status');
    }
}
