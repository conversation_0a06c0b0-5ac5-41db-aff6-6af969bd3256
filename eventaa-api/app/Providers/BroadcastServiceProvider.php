<?php

namespace App\Providers;

use Illuminate\Support\Facades\Broadcast;
use Illuminate\Support\ServiceProvider;

class BroadcastServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register broadcasting routes with custom middleware that handles both web and API auth
        Broadcast::routes([
            'middleware' => ['web', 'auth.broadcasting']
        ]);

        require base_path('routes/channels.php');
    }
}
