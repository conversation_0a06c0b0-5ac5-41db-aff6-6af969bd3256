<?php

namespace App\Events;

use App\Models\Subscription;
use App\Models\User;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class SubscriptionActivated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $subscription;
    public $user;

    /**
     * Create a new event instance.
     */
    public function __construct(Subscription $subscription, User $user)
    {
        $this->subscription = $subscription;
        $this->user = $user;
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('subscription.user.' . $this->user->id),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        // Ensure dates are Carbon instances before calling toISOString()
        $startDate = $this->subscription->start_date;
        $endDate = $this->subscription->end_date;

        if (is_string($startDate)) {
            $startDate = \Carbon\Carbon::parse($startDate);
        }

        if (is_string($endDate)) {
            $endDate = \Carbon\Carbon::parse($endDate);
        }

        return [
            'subscription_id' => $this->subscription->id,
            'user_id' => $this->user->id,
            'plan' => [
                'id' => $this->subscription->plan->id,
                'name' => $this->subscription->plan->name,
                'price' => $this->subscription->plan->price,
                'currency' => $this->subscription->plan->currency,
            ],
            'start_date' => $startDate?->toISOString(),
            'end_date' => $endDate?->toISOString(),
            'activated_at' => now()->toISOString(),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'subscription.activated';
    }
}
