<?php

namespace App\Events;

use App\Models\TicketPurchase;
use App\Models\User;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Log;

class TicketPurchaseCompleted implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $purchases;
    public $user;
    public $totalAmount;
    public $pdfUrl;
    public $eventName;

    /**
     * Create a new event instance.
     */
    public function __construct($purchases, User $user, float $totalAmount, ?string $pdfUrl = null, ?string $eventName = null)
    {
        $this->purchases = $purchases;
        $this->user = $user;
        $this->totalAmount = $totalAmount;
        $this->pdfUrl = $pdfUrl;
        $this->eventName = $eventName;
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('tickets.user.' . $this->user->id),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'user_id' => $this->user->id,
            'total_amount' => $this->totalAmount,
            'purchase_count' => count($this->purchases),
            'pdf_url' => $this->pdfUrl,
            'event_name' => $this->eventName,
            'purchases' => collect($this->purchases)->map(function ($purchase) {
                // Handle both Eloquent models and arrays
                if (is_array($purchase)) {
                    return [
                        'id' => $purchase['id'] ?? null,
                        'purchase_reference' => $purchase['purchase_reference'] ?? null,
                        'event_id' => $purchase['event_id'] ?? null,
                        'ticket_id' => $purchase['ticket_id'] ?? null,
                        'quantity' => $purchase['quantity'] ?? null,
                        'total_amount' => $purchase['total_amount'] ?? null,
                        'status' => $purchase['status'] ?? null,
                    ];
                } elseif (is_object($purchase)) {
                    // Handle Eloquent models
                    return [
                        'id' => $purchase->id ?? null,
                        'purchase_reference' => $purchase->purchase_reference ?? null,
                        'event_id' => $purchase->event_id ?? null,
                        'ticket_id' => $purchase->ticket_id ?? null,
                        'quantity' => $purchase->quantity ?? null,
                        'total_amount' => $purchase->total_amount ?? null,
                        'status' => $purchase->status ?? null,
                    ];
                } else {
                    // Fallback for unexpected data types
                    Log::warning('Unexpected purchase data type in TicketPurchaseCompleted event', [
                        'type' => gettype($purchase),
                        'data' => $purchase
                    ]);
                    return [
                        'id' => null,
                        'purchase_reference' => null,
                        'event_id' => null,
                        'ticket_id' => null,
                        'quantity' => null,
                        'total_amount' => null,
                        'status' => null,
                    ];
                }
            }),
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'ticket.purchase.completed';
    }
}
