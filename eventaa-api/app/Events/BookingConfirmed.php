<?php

namespace App\Events;

use App\Models\TicketPurchase;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class BookingConfirmed
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $ticketPurchase;

    /**
     * Create a new event instance.
     */
    public function __construct(TicketPurchase $ticketPurchase)
    {
        $this->ticketPurchase = $ticketPurchase;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('booking-confirmed.' . $this->ticketPurchase->user_id),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'purchase_id' => $this->ticketPurchase->id,
            'purchase_reference' => $this->ticketPurchase->purchase_reference,
            'event_title' => $this->ticketPurchase->event->title,
            'ticket_name' => $this->ticketPurchase->ticket->name,
            'quantity' => $this->ticketPurchase->quantity,
            'total_amount' => $this->ticketPurchase->total_amount,
            'attendee_name' => $this->ticketPurchase->attendee_name,
            'confirmed_at' => now(),
        ];
    }
}
