<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Lara<PERSON>\Sanctum\PersonalAccessToken;
use Symfony\Component\HttpFoundation\Response;

class AuthenticateBroadcasting
{
    /**
     * Handle an incoming request for broadcasting authentication.
     */
    public function handle(Request $request, Closure $next): Response
    {
        if ($token = $request->bearerToken()) {
            $accessToken = PersonalAccessToken::findToken($token);
            if ($accessToken && $accessToken->tokenable) {
                Auth::setUser($accessToken->tokenable);
                $request->setUserResolver(function () use ($accessToken) {
                    return $accessToken->tokenable;
                });
                return $next($request);
            }
        }

        if (Auth::check()) {
            return $next($request);
        }

        return response()->json([
            'message' => 'Unauthenticated'
        ], 403);
    }
}
