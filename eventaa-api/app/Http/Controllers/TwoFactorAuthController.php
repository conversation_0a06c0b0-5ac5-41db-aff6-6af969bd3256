<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Notifications\TwoFactorAuthNotification;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class TwoFactorAuthController extends Controller
{
    /**
     * Enable two-factor authentication for the user.
     */
    public function enable(Request $request): JsonResponse
    {
        $user = $request->user();

        $secret = Str::random(32);

        $user->two_factor_secret = $secret;
        $user->two_factor_enabled = true;
        $user->save();

        return response()->json([
            'message' => 'Two-factor authentication has been enabled',
            'status' => true
        ]);
    }

    /**
     * Disable two-factor authentication for the user.
     */
    public function disable(Request $request): JsonResponse
    {
        $user = $request->user();

        $user->two_factor_secret = null;
        $user->two_factor_enabled = false;
        $user->two_factor_verified_at = null;
        $user->save();

        return response()->json([
            'message' => 'Two-factor authentication has been disabled',
            'status' => true
        ]);
    }

    /**
     * Get the status of two-factor authentication for the user.
     */
    public function status(Request $request): JsonResponse
    {
        $user = $request->user();

        return response()->json([
            'enabled' => $user->two_factor_enabled,
            'verified' => $user->two_factor_verified_at !== null
        ]);
    }

    /**
     * Send a two-factor authentication code to the user's email.
     */
    public function sendCode(Request $request): JsonResponse
    {
        $user = User::where('email', $request->email)->first();

        if (!$user || !$user->two_factor_enabled) {
            return response()->json([
                'message' => 'User not found or two-factor authentication not enabled',
                'status' => false
            ], 404);
        }

        // Check if a code was sent recently (within 2 minutes) to prevent spam
        $recentCodeSent = cache()->has('2fa_code_sent_' . $user->id);
        if ($recentCodeSent) {
            return response()->json([
                'message' => 'A code was recently sent. Please wait before requesting a new one.',
                'status' => false
            ], 429);
        }

        $code = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
        $user->two_factor_secret = Hash::make($code);
        $user->save();

        // Set cache to prevent duplicate sends
        cache()->put('2fa_code_sent_' . $user->id, true, now()->addMinutes(2));

        $user->notify(new TwoFactorAuthNotification($code));

        return response()->json([
            'message' => 'Two-factor authentication code has been sent to your email',
            'status' => true
        ]);
    }

    /**
     * Verify a two-factor authentication code.
     */
    public function verifyCode(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'code' => 'required|string|size:6',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => $validator->errors(),
                'status' => false
            ], 422);
        }

        $user = User::where('email', $request->email)->first();

        if (!$user || !$user->two_factor_enabled) {
            return response()->json([
                'message' => 'User not found or two-factor authentication not enabled',
                'status' => false
            ], 404);
        }

        if (!Hash::check($request->code, $user->two_factor_secret)) {
            return response()->json([
                'message' => 'Invalid two-factor authentication code',
                'status' => false
            ], 401);
        }

        $user->two_factor_verified_at = now();
        $user->save();

        $token = $user->createToken(env('APP_NAME'))->plainTextToken;
        $roles = $user->getRoleNames();

        $userData = $user->toArray();
        $userData['roles'] = $roles;

        return response()->json([
            'message' => 'Two-factor authentication verified successfully',
            'status' => true,
            'user' => $userData,
            'token' => $token
        ]);
    }
}
