<?php

namespace App\Http\Controllers;

use App\Models\Rating;
use App\Models\VendorRating;
use App\Models\Event;
use App\Models\Vendor;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use App\Exports\ReviewsExport;

class ReviewController extends Controller
{
    /**
     * Get review statistics
     */
    public function stats(): JsonResponse
    {
        try {
            $currentMonth = Carbon::now()->startOfMonth();
            $previousMonth = Carbon::now()->subMonth()->startOfMonth();
            $previousMonthEnd = Carbon::now()->subMonth()->endOfMonth();

            $eventRatingsCount = Rating::count();
            $eventRatingsCurrentMonth = Rating::where('created_at', '>=', $currentMonth)->count();
            $eventRatingsPreviousMonth = Rating::whereBetween('created_at', [$previousMonth, $previousMonthEnd])->count();
            $eventAverageRating = Rating::avg('rating') ?? 0;
            $eventAverageRatingPrevious = Rating::whereBetween('created_at', [$previousMonth, $previousMonthEnd])->avg('rating') ?? 0;

            // Vendor ratings stats
            $vendorRatingsCount = VendorRating::count();
            $vendorRatingsCurrentMonth = VendorRating::where('created_at', '>=', $currentMonth)->count();
            $vendorRatingsPreviousMonth = VendorRating::whereBetween('created_at', [$previousMonth, $previousMonthEnd])->count();
            $vendorAverageRating = VendorRating::avg('rating') ?? 0;
            $vendorAverageRatingPrevious = VendorRating::whereBetween('created_at', [$previousMonth, $previousMonthEnd])->avg('rating') ?? 0;

            // Combined stats
            $totalReviews = $eventRatingsCount + $vendorRatingsCount;
            $totalCurrentMonth = $eventRatingsCurrentMonth + $vendorRatingsCurrentMonth;
            $totalPreviousMonth = $eventRatingsPreviousMonth + $vendorRatingsPreviousMonth;

            // Calculate overall average rating
            $totalRatingSum = (Rating::sum('rating') ?? 0) + (VendorRating::sum('rating') ?? 0);
            $averageRating = $totalReviews > 0 ? $totalRatingSum / $totalReviews : 0;

            $totalRatingSumPrevious = (Rating::whereBetween('created_at', [$previousMonth, $previousMonthEnd])->sum('rating') ?? 0) +
                                    (VendorRating::whereBetween('created_at', [$previousMonth, $previousMonthEnd])->sum('rating') ?? 0);
            $averageRatingPrevious = $totalPreviousMonth > 0 ? $totalRatingSumPrevious / $totalPreviousMonth : 0;

            // Calculate growth percentages
            $reviewsGrowth = $totalPreviousMonth > 0 ? (($totalCurrentMonth - $totalPreviousMonth) / $totalPreviousMonth) * 100 : 0;
            $ratingGrowth = $averageRatingPrevious > 0 ? (($averageRating - $averageRatingPrevious) / $averageRatingPrevious) * 100 : 0;

            // Positive reviews (4-5 stars)
            $positiveEventRatings = Rating::where('rating', '>=', 4)->count();
            $positiveVendorRatings = VendorRating::where('rating', '>=', 4)->count();
            $positiveReviews = $totalReviews > 0 ? (($positiveEventRatings + $positiveVendorRatings) / $totalReviews) * 100 : 0;

            $positiveEventRatingsPrevious = Rating::whereBetween('created_at', [$previousMonth, $previousMonthEnd])->where('rating', '>=', 4)->count();
            $positiveVendorRatingsPrevious = VendorRating::whereBetween('created_at', [$previousMonth, $previousMonthEnd])->where('rating', '>=', 4)->count();
            $positiveReviewsPrevious = $totalPreviousMonth > 0 ? (($positiveEventRatingsPrevious + $positiveVendorRatingsPrevious) / $totalPreviousMonth) * 100 : 0;

            $positiveGrowth = $positiveReviewsPrevious > 0 ? (($positiveReviews - $positiveReviewsPrevious) / $positiveReviewsPrevious) * 100 : 0;

            // For now, we'll simulate flagged reviews as we don't have a flagged status in the current models
            $flaggedReviews = 0;
            $flaggedGrowth = 0;

            return response()->json([
                'averageRating' => round($averageRating, 1),
                'ratingGrowth' => round($ratingGrowth, 1),
                'totalReviews' => $totalReviews,
                'reviewsGrowth' => round($reviewsGrowth, 1),
                'positiveReviews' => round($positiveReviews, 1),
                'positiveGrowth' => round($positiveGrowth, 1),
                'flaggedReviews' => $flaggedReviews,
                'flaggedGrowth' => $flaggedGrowth,
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to fetch review statistics'], 500);
        }
    }

    /**
     * Get all reviews (combined event and vendor ratings)
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $page = $request->get('page', 1);
            $perPage = $request->get('per_page', 10);
            $search = $request->get('search', '');
            $type = $request->get('type', '');
            $rating = $request->get('rating', '');
            $status = $request->get('status', '');

            $reviews = collect();

            if ($type === '' || $type === 'event') {
                $eventRatings = Rating::with(['user', 'event'])
                    ->when($search, function ($query) use ($search) {
                        $query->whereHas('user', function ($q) use ($search) {
                            $q->where('name', 'like', "%{$search}%")
                              ->orWhere('email', 'like', "%{$search}%");
                        })->orWhereHas('event', function ($q) use ($search) {
                            $q->where('title', 'like', "%{$search}%");
                        })->orWhere('review', 'like', "%{$search}%");
                    })
                    ->when($rating, function ($query) use ($rating) {
                        $query->where('rating', $rating);
                    })
                    ->get()
                    ->map(function ($rating) {
                        return [
                            'id' => $rating->id,
                            'type' => 'event',
                            'reviewer' => [
                                'name' => $rating->user->name ?? 'Unknown User',
                                'email' => $rating->user->email ?? '',
                                'avatar' => $rating->user->avatar ?? null,
                            ],
                            'rating' => $rating->rating,
                            'content' => $rating->review ?? '',
                            'for' => [
                                'id' => $rating->event->id ?? 0,
                                'name' => $rating->event->title ?? 'Unknown Event',
                            ],
                            'date' => $rating->created_at->toISOString(),
                            'status' => 'published',
                        ];
                    });

                $reviews = $reviews->merge($eventRatings);
            }

            if ($type === '' || $type === 'vendor') {
                $vendorRatings = VendorRating::with(['user', 'vendor'])
                    ->when($search, function ($query) use ($search) {
                        $query->whereHas('user', function ($q) use ($search) {
                            $q->where('name', 'like', "%{$search}%")
                              ->orWhere('email', 'like', "%{$search}%");
                        })->orWhereHas('vendor', function ($q) use ($search) {
                            $q->where('name', 'like', "%{$search}%");
                        })->orWhere('review', 'like', "%{$search}%");
                    })
                    ->when($rating, function ($query) use ($rating) {
                        $query->where('rating', $rating);
                    })
                    ->get()
                    ->map(function ($rating) {
                        return [
                            'id' => $rating->id,
                            'type' => 'vendor',
                            'reviewer' => [
                                'name' => $rating->user->name ?? 'Unknown User',
                                'email' => $rating->user->email ?? '',
                                'avatar' => $rating->user->avatar ?? null,
                            ],
                            'rating' => $rating->rating,
                            'content' => $rating->review ?? '',
                            'for' => [
                                'id' => $rating->vendor->id ?? 0,
                                'name' => $rating->vendor->name ?? 'Unknown Vendor',
                            ],
                            'date' => $rating->created_at->toISOString(),
                            'status' => 'published',
                        ];
                    });

                $reviews = $reviews->merge($vendorRatings);
            }

            if ($status && $status !== 'all' && $status !== 'published') {
                $reviews = collect();
            }

            $reviews = $reviews->sortByDesc('date')->values();

            $total = $reviews->count();
            $offset = ($page - 1) * $perPage;
            $paginatedReviews = $reviews->slice($offset, $perPage)->values();

            return response()->json([
                'data' => $paginatedReviews,
                'total' => $total,
                'current_page' => (int)$page,
                'per_page' => (int)$perPage,
                'last_page' => ceil($total / $perPage),
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to fetch reviews'], 500);
        }
    }

    /**
     * Update review status
     */
    public function update(Request $request, $id): JsonResponse
    {
        try {
            $status = $request->get('status');

            // For now, we'll just return success since we don't have status fields in the current models
            // In a real implementation, you would add status fields to the Rating and VendorRating models

            return response()->json(['message' => 'Review status updated successfully']);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to update review status'], 500);
        }
    }

    /**
     * Export reviews to Excel or CSV
     */
    public function export(Request $request): BinaryFileResponse|\Illuminate\Http\Response
    {
        try {
            $format = $request->get('format', 'csv');
            $filters = $request->only(['search', 'type', 'rating', 'status']);

            if ($format === 'excel') {
                $filename = 'reviews_export_' . now()->format('Y-m-d_H-i-s') . '.xlsx';

                // Check if we have data before exporting
                $reviewsExport = new ReviewsExport($filters);
                $reviewsCollection = $reviewsExport->collection();

                if ($reviewsCollection->isEmpty()) {
                    // Create an Excel file with just headers if no data
                    return Excel::download(new ReviewsExport($filters), $filename);
                }

                return Excel::download($reviewsExport, $filename);
            } else {
                // CSV export
                $reviews = collect();

                // Get event ratings if type filter allows
                if (empty($filters['type']) || $filters['type'] === 'event') {
                    $eventQuery = Rating::with(['user', 'event']);

                    if (!empty($filters['search'])) {
                        $search = $filters['search'];
                        $eventQuery->where(function ($query) use ($search) {
                            $query->whereHas('user', function ($q) use ($search) {
                                $q->where('name', 'like', "%{$search}%")
                                  ->orWhere('email', 'like', "%{$search}%");
                            })->orWhereHas('event', function ($q) use ($search) {
                                $q->where('title', 'like', "%{$search}%");
                            })->orWhere('review', 'like', "%{$search}%");
                        });
                    }

                    if (!empty($filters['rating']) && $filters['rating'] !== 'all') {
                        $eventQuery->where('rating', $filters['rating']);
                    }

                    $eventRatings = $eventQuery->get()->map(function ($rating) {
                        return [
                            'id' => $rating->id,
                            'reviewer_name' => $rating->user->name ?? 'Unknown User',
                            'reviewer_email' => $rating->user->email ?? 'No Email',
                            'rating' => $rating->rating ?? 0,
                            'content' => $rating->review ?? 'No Review Content',
                            'type' => 'event',
                            'for_name' => $rating->event->title ?? 'Unknown Event',
                            'date' => $rating->created_at ?? now(),
                            'status' => 'published',
                        ];
                    });

                    $reviews = $reviews->merge($eventRatings);
                }

                // Get vendor ratings if type filter allows
                if (empty($filters['type']) || $filters['type'] === 'vendor') {
                    $vendorQuery = VendorRating::with(['user', 'vendor']);

                    if (!empty($filters['search'])) {
                        $search = $filters['search'];
                        $vendorQuery->where(function ($query) use ($search) {
                            $query->whereHas('user', function ($q) use ($search) {
                                $q->where('name', 'like', "%{$search}%")
                                  ->orWhere('email', 'like', "%{$search}%");
                            })->orWhereHas('vendor', function ($q) use ($search) {
                                $q->where('name', 'like', "%{$search}%");
                            })->orWhere('comment', 'like', "%{$search}%");
                        });
                    }

                    if (!empty($filters['rating']) && $filters['rating'] !== 'all') {
                        $vendorQuery->where('rating', $filters['rating']);
                    }

                    $vendorRatings = $vendorQuery->get()->map(function ($rating) {
                        return [
                            'id' => $rating->id,
                            'reviewer_name' => $rating->user->name ?? 'Unknown User',
                            'reviewer_email' => $rating->user->email ?? 'No Email',
                            'rating' => $rating->rating ?? 0,
                            'content' => $rating->comment ?? 'No Review Content',
                            'type' => 'vendor',
                            'for_name' => $rating->vendor->name ?? 'Unknown Vendor',
                            'date' => $rating->created_at ?? now(),
                            'status' => 'published',
                        ];
                    });

                    $reviews = $reviews->merge($vendorRatings);
                }

                // Apply status filter
                if (!empty($filters['status']) && $filters['status'] !== 'all' && $filters['status'] !== 'published') {
                    $reviews = collect();
                }

                // Sort by date
                $reviews = $reviews->sortByDesc('date')->values();

                // Prepare CSV data
                $csvData = [];
                $csvData[] = [
                    'ID',
                    'Reviewer Name',
                    'Reviewer Email',
                    'Rating',
                    'Review Content',
                    'Type',
                    'For (Event/Vendor)',
                    'Date',
                    'Status'
                ];

                if ($reviews->isEmpty()) {
                    // Add a single row indicating no data
                    $csvData[] = [
                        'N/A',
                        'No reviews available',
                        'N/A',
                        'N/A',
                        'No reviews match the selected filters',
                        'N/A',
                        'N/A',
                        now()->format('Y-m-d H:i:s'),
                        'N/A'
                    ];
                } else {
                    foreach ($reviews as $review) {
                        $csvData[] = [
                            $review['id'],
                            $review['reviewer_name'],
                            $review['reviewer_email'],
                            $review['rating'] . '/5',
                            strip_tags($review['content']),
                            ucfirst($review['type']),
                            $review['for_name'],
                            $review['date']->format('Y-m-d H:i:s'),
                            ucfirst($review['status'])
                        ];
                    }
                }

                $filename = 'reviews_export_' . now()->format('Y-m-d_H-i-s') . '.csv';

                return response()->streamDownload(function () use ($csvData) {
                    $file = fopen('php://output', 'w');
                    foreach ($csvData as $row) {
                        fputcsv($file, $row);
                    }
                    fclose($file);
                }, $filename, [
                    'Content-Type' => 'text/csv',
                    'Content-Disposition' => 'attachment; filename="' . $filename . '"'
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Error exporting reviews: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to export reviews: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Delete a review
     */
    public function destroy($id): JsonResponse
    {
        try {
            // Try to find and delete from event ratings first
            $eventRating = Rating::find($id);
            if ($eventRating) {
                $eventRating->delete();
                return response()->json(['message' => 'Review deleted successfully']);
            }

            // Try to find and delete from vendor ratings
            $vendorRating = VendorRating::find($id);
            if ($vendorRating) {
                $vendorRating->delete();
                return response()->json(['message' => 'Review deleted successfully']);
            }

            return response()->json(['error' => 'Review not found'], 404);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to delete review'], 500);
        }
    }
}
