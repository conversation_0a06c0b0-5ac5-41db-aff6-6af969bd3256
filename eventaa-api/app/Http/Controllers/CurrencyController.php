<?php

namespace App\Http\Controllers;

use App\Models\Currency;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class CurrencyController extends Controller
{
    /**
     * Get all currencies with optional filtering and pagination
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $query = Currency::query();

        if ($search = $request->input('search')) {
            $query->where('name', 'like', "%{$search}%");
        }

        if ($dateFilter = $request->input('dateFilter')) {
            $now = Carbon::now();
            switch ($dateFilter) {
                case 'today':
                    $query->whereDate('created_at', $now);
                    break;
                case 'week':
                    $query->whereBetween('created_at', [$now->startOfWeek(), $now->endOfWeek()]);
                    break;
                case 'month':
                    $query->whereMonth('created_at', $now->month)
                        ->whereYear('created_at', $now->year);
                    break;
                case 'year':
                    $query->whereYear('created_at', $now->year);
                    break;
            }
        }

        $perPage = $request->input('limit', 10);
        $currencies = $query->orderBy('name')->paginate($perPage);

        return response()->json([
            'data' => [
                'currencies' => $currencies->items(),
                'total' => $currencies->total(),
                'current_page' => $currencies->currentPage(),
                'last_page' => $currencies->lastPage(),
                'per_page' => $currencies->perPage()
            ]
        ]);
    }

    /**
     * Get all currencies without pagination
     *
     * @return JsonResponse
     */
    public function all(): JsonResponse
    {
        $currencies = Currency::orderBy('name')->get();

        return response()->json([
            'currencies' => $currencies
        ]);
    }

    /**
     * Create a new currency
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:10|unique:currencies,name',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }

        $currency = Currency::create([
            'name' => strtoupper(trim($request->name))
        ]);

        return response()->json([
            'message' => 'Currency created successfully',
            'currency' => $currency
        ], 201);
    }

    /**
     * Get a specific currency
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        $currency = Currency::find($id);

        if (!$currency) {
            return response()->json([
                'message' => 'Currency not found'
            ], 404);
        }

        return response()->json([
            'currency' => $currency
        ]);
    }

    /**
     * Update a currency
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(Request $request, int $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:10|unique:currencies,name,' . $id,
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }

        $currency = Currency::find($id);

        if (!$currency) {
            return response()->json([
                'message' => 'Currency not found'
            ], 404);
        }

        $currency->update([
            'name' => strtoupper(trim($request->name))
        ]);

        return response()->json([
            'message' => 'Currency updated successfully',
            'currency' => $currency
        ]);
    }

    /**
     * Delete a currency
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        $currency = Currency::find($id);

        if (!$currency) {
            return response()->json([
                'message' => 'Currency not found'
            ], 404);
        }

        // Check if currency is being used by any users, plans, or other entities
        $usageCount = 0;

        // Check if any users have this currency as preference
        if (method_exists(\App\Models\User::class, 'currency')) {
            $usageCount += \App\Models\User::where('currency_id', $id)->count();
        }

        // Check if any plans use this currency
        $usageCount += \App\Models\Plan::where('currency', $currency->name)->count();

        if ($usageCount > 0) {
            return response()->json([
                'message' => 'Cannot delete currency as it is currently being used by ' . $usageCount . ' record(s)'
            ], 422);
        }

        $currency->delete();

        return response()->json([
            'message' => 'Currency deleted successfully'
        ]);
    }

    /**
     * Get currency statistics
     *
     * @return JsonResponse
     */
    public function stats(): JsonResponse
    {
        $totalCurrencies = Currency::count();
        $recentCurrencies = Currency::where('created_at', '>=', Carbon::now()->subDays(30))->count();

        $usageStats = [];

        // Get usage by plans
        $planUsage = \App\Models\Plan::select('currency')
            ->selectRaw('count(*) as count')
            ->groupBy('currency')
            ->get()
            ->pluck('count', 'currency')
            ->toArray();

        return response()->json([
            'total_currencies' => $totalCurrencies,
            'recent_currencies' => $recentCurrencies,
            'plan_usage' => $planUsage
        ]);
    }
}
