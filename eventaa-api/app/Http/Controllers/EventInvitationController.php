<?php

namespace App\Http\Controllers;

use App\Models\Event;
use App\Models\EventInvitation;
use App\Models\User;
use App\Notifications\EventInvitationNotification;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Auth;

class EventInvitationController extends Controller
{
    /**
     * Get all invitations for a specific event
     *
     * @param int $eventId
     * @return JsonResponse
     */
    public function index($eventId): JsonResponse
    {
        try {
            $event = Event::findOrFail($eventId);

            if ($event->user_id !== Auth::id()) {
                return response()->json([
                    'message' => 'Unauthorized to view invitations for this event'
                ], 403);
            }

            $invitations = EventInvitation::where('event_id', $eventId)
                ->orderBy('created_at', 'desc')
                ->get();

            return response()->json([
                'data' => $invitations,
                'message' => 'Event invitations retrieved successfully'
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to retrieve event invitations',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send invitation to a user
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function send(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'event_id' => 'required|exists:events,id',
                'email' => 'required|email',
                'message' => 'nullable|string|max:500'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $event = Event::findOrFail($request->event_id);

            if ($event->user_id !== Auth::id()) {
                return response()->json([
                    'message' => 'Unauthorized to send invitations for this event'
                ], 403);
            }

            $existingInvitation = EventInvitation::where('event_id', $request->event_id)
                ->where('email', $request->email)
                ->first();

            if ($existingInvitation) {
                return response()->json([
                    'message' => 'An invitation has already been sent to this email address'
                ], 409);
            }

            $invitation = EventInvitation::create([
                'event_id' => $request->event_id,
                'email' => $request->email,
                'status' => 'sent',
                'invited_by' => Auth::id(),
                'message' => $request->message
            ]);

            $user = User::where('email', $request->email)->first();
            if ($user) {
                $invitation->update([
                    'user_id' => $user->id,
                    'name' => $user->name,
                    'avatar' => $user->avatar
                ]);
            }

            try {
                if ($user) {
                    $user->notify(new EventInvitationNotification($event, $invitation));
                } else {
                    Notification::route('mail', $request->email)
                        ->notify(new EventInvitationNotification($event, $invitation));
                }

                $invitation->update(['status' => 'sent']);
            } catch (\Exception $e) {
                \Log::error('Failed to send invitation notification: ' . $e->getMessage());
                $invitation->update(['status' => 'pending']);
            }

            return response()->json([
                'data' => $invitation->fresh(),
                'message' => 'Invitation sent successfully'
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to send invitation',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Resend an invitation
     *
     * @param int $invitationId
     * @return JsonResponse
     */
    public function resend($invitationId): JsonResponse
    {
        try {
            $invitation = EventInvitation::findOrFail($invitationId);
            $event = Event::findOrFail($invitation->event_id);

            if ($event->user_id !== Auth::id()) {
                return response()->json([
                    'message' => 'Unauthorized to resend this invitation'
                ], 403);
            }

            if ($invitation->status === 'accepted') {
                return response()->json([
                    'message' => 'Cannot resend an invitation that has already been accepted'
                ], 400);
            }

            try {
                if ($invitation->user_id) {
                    $user = User::find($invitation->user_id);
                    $user->notify(new EventInvitationNotification($event, $invitation));
                } else {
                    Notification::route('mail', $invitation->email)
                        ->notify(new EventInvitationNotification($event, $invitation));
                }

                $invitation->update([
                    'status' => 'sent',
                    'resent_at' => now()
                ]);
            } catch (\Exception $e) {
                return response()->json([
                    'message' => 'Failed to resend invitation',
                    'error' => $e->getMessage()
                ], 500);
            }

            return response()->json([
                'data' => $invitation->fresh(),
                'message' => 'Invitation resent successfully'
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to resend invitation',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete an invitation
     *
     * @param int $invitationId
     * @return JsonResponse
     */
    public function delete($invitationId): JsonResponse
    {
        try {
            $invitation = EventInvitation::findOrFail($invitationId);
            $event = Event::findOrFail($invitation->event_id);

            if ($event->user_id !== Auth::id()) {
                return response()->json([
                    'message' => 'Unauthorized to delete this invitation'
                ], 403);
            }

            $invitation->delete();

            return response()->json([
                'message' => 'Invitation deleted successfully'
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to delete invitation',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Accept an invitation (for invited users)
     *
     * @param Request $request
     * @param string $token
     * @return JsonResponse
     */
    public function accept(Request $request, $token): JsonResponse
    {
        try {
            $invitation = EventInvitation::where('invitation_token', $token)->firstOrFail();

            if ($invitation->status === 'accepted') {
                return response()->json([
                    'message' => 'Invitation has already been accepted'
                ], 400);
            }

            if ($invitation->status === 'declined') {
                return response()->json([
                    'message' => 'Invitation was previously declined'
                ], 400);
            }

            $invitation->update([
                'status' => 'accepted',
                'responded_at' => now(),
                'user_id' => Auth::id()
            ]);

            $event = Event::findOrFail($invitation->event_id);
            $event->attendees()->firstOrCreate([
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'data' => $invitation->fresh(),
                'message' => 'Invitation accepted successfully'
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to accept invitation',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Decline an invitation (for invited users)
     *
     * @param Request $request
     * @param string $token
     * @return JsonResponse
     */
    public function decline(Request $request, $token): JsonResponse
    {
        try {
            $invitation = EventInvitation::where('invitation_token', $token)->firstOrFail();

            if ($invitation->status === 'accepted') {
                return response()->json([
                    'message' => 'Cannot decline an invitation that has been accepted'
                ], 400);
            }

            if ($invitation->status === 'declined') {
                return response()->json([
                    'message' => 'Invitation has already been declined'
                ], 400);
            }

            $invitation->update([
                'status' => 'declined',
                'responded_at' => now(),
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'data' => $invitation->fresh(),
                'message' => 'Invitation declined'
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to decline invitation',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get invitation details by token (for public access)
     *
     * @param string $token
     * @return JsonResponse
     */
    public function getByToken($token): JsonResponse
    {
        try {
            $invitation = EventInvitation::where('invitation_token', $token)
                ->with(['event', 'invitedBy'])
                ->firstOrFail();

            $event = $invitation->event;

            // Add invited_by_name to the invitation data
            $invitationData = $invitation->toArray();
            $invitationData['invited_by_name'] = $invitation->invitedBy ? $invitation->invitedBy->name : 'Event Organizer';

            return response()->json([
                'data' => [
                    'invitation' => $invitationData,
                    'event' => $event
                ],
                'message' => 'Invitation details retrieved successfully'
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Invitation not found or invalid',
                'error' => $e->getMessage()
            ], 404);
        }
    }
}
