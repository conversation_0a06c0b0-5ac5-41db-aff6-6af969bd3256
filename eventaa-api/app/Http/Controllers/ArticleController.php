<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Article;
use App\Models\ArticleTag;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class ArticleController extends Controller
{
    private function validationRules($articleId = null): array
    {
        return [
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'image' => $articleId ? 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048' : 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'user_id' => 'required|exists:users,id',
            'category_id' => 'required|exists:categories,id',
            'slug' => 'required|string|unique:articles,slug,' . ($articleId ?? 'NULL'),
            'is_published' => 'boolean',
            'published_at' => 'nullable|date',
        ];
    }

    public function index(Request $request): JsonResponse
    {
        $articles = Article::with('user', 'category', 'tags')->paginate($request->per_page ?? 10);
        return response()->json($articles);
    }

    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'category_id' => 'required|exists:categories,id',
            'is_published' => 'boolean',
            'published_at' => 'nullable|date',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $imagePath = $request->file('image')->store('articles', 'public');

        $article = Article::create([
            'title' => $request->title,
            'content' => $request->content,
            'image' => $imagePath,
            'user_id' => $request->user()->id,
            'category_id' => $request->category_id,
            'slug' => Str::slug($request->title, '-'),
            'is_published' => $request->is_published,
            'published_at' => $request->is_published ? now() : null,
        ]);

        if ($request->has('tags')) {
            foreach(json_decode($request->tags) as $tag) {
                ArticleTag::create([
                    'article_id' => $article->id,
                    'name' => $tag,
                ]);
            }
        }

        return response()->json($article, 201);
    }

    public function show($identifier): JsonResponse
    {
        $article = Article::with('user', 'category', 'tags')->where('id', $identifier)
            ->orWhere('slug', $identifier)
            ->firstOrFail();

        return response()->json($article);
    }

    public function update(Request $request, $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'category_id' => 'required|exists:categories,id',
            'is_published' => 'boolean',
            'published_at' => 'nullable|date',
        ]);
        
        if($validator->fails()){
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $article = Article::findOrFail($id);

        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('articles', 'public');
            if ($article->image) {
                Storage::disk('public')->delete($article->image);
            }
            $article->image = $imagePath;
        }

        $article->title = $request->title;
        $article->content = $request->content;
        $article->category_id = $request->category_id;
        $article->is_published = $request->is_published;
        $article->published_at = $request->is_published ? now() : null;
        $article->save();

        if ($request->has('tags')) {
            ArticleTag::where('article_id', $article->id)->delete();
            foreach(json_decode($request->tags) as $tag) {
                ArticleTag::create([
                    'article_id' => $article->id,
                    'name' => $tag,
                ]);
            }
        }

        return response()->json([
            'message' => 'Article updated successfully',
            'article' => $article
        ], 200);
    }

    public function destroy($id): JsonResponse
    {
        $article = Article::findOrFail($id);
        if ($article->image) {
            Storage::disk('public')->delete($article->image);
        }
        $article->delete();
        return response()->json([
            'message' => 'Article deleted successfully'
        ], 204);
    }
}
