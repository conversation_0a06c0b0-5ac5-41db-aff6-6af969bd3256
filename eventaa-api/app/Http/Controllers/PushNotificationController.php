<?php

namespace App\Http\Controllers;

use Kreait\Firebase\Factory;
use Kreait\Firebase\Messaging\CloudMessage;
use <PERSON>reait\Firebase\Messaging\Topic;

class PushNotificationController extends Controller
{
    public function sendPushNotification($title, $body, $topic)
    {
        $firebase = (new Factory)
            ->withServiceAccount(__DIR__ . '/../../config/eventaa-firebase-adminsdk-3pdh5-0999ee88cc.json');

        $messaging = $firebase->createMessaging();

        $message = CloudMessage::fromArray([
            'notification' => [
                'title' => $title,
                'body' => $body
            ],
            'topic' => $topic
        ]);

        $messaging->send($message);

        return response()->json(['message' => 'Push notification sent successfully']);
    }

    public function sendToUser($title, $body, $token)
    {
        $firebase = (new Factory)
            ->withServiceAccount(__DIR__ . '/../../config/eventaa-firebase-adminsdk-3pdh5-0999ee88cc.json');

        $messaging = $firebase->createMessaging();

        $message = CloudMessage::withTarget('token', $token)
        ->withNotification([
            'title' => $title,
            'body' => $body
        ])
        ->withData([
            'key' => 'value'
        ]);

        $messaging->send($message);
    }

    public function createTopic($topicName)
    {
        try {
            Topic::fromValue($topicName);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}
