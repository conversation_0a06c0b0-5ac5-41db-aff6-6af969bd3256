<?php

namespace App\Http\Controllers;

use App\Models\Vendor;
use App\Models\VendorBooking;
use App\Models\VendorRating;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class VendorRatingController extends Controller
{
    /**
     * Display a listing of ratings for a vendor.
     *
     * @param int $vendor_id
     * @param Request $request
     * @return JsonResponse
     */
    public function index($vendor_id, Request $request): JsonResponse
    {
        $vendor = Vendor::find($vendor_id);
        if (!$vendor) {
            return response()->json(['message' => 'Vendor not found'], 404);
        }

        $query = VendorRating::with('user')
            ->where('vendor_id', $vendor_id);

        $sortField = $request->sort_by ?? 'created_at';
        $sortDirection = $request->sort_direction ?? 'desc';
        $query->orderBy($sortField, $sortDirection);

        $perPage = $request->per_page ?? 15;
        $ratings = $query->paginate($perPage);

        return response()->json($ratings);
    }

    /**
     * Store a newly created rating.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        // Validate request data
        $validator = Validator::make($request->all(), [
            'vendor_id' => 'required|exists:vendors,id',
            'rating' => 'required|integer|min:1|max:5',
            'comment' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $vendor = Vendor::find($request->vendor_id);
        if (!$vendor) {
            return response()->json(['message' => 'Vendor not found'], 404);
        }

        $existingRating = VendorRating::where('vendor_id', $request->vendor_id)
            ->where('user_id', $request->user()->id)
            ->first();

        if ($existingRating) {
            return response()->json([
                'message' => 'You have already rated this vendor. Please update your existing rating instead.'
            ], 422);
        }

        $rating = VendorRating::create([
            'vendor_id' => $request->vendor_id,
            'user_id' => $request->user()->id,
            'rating' => $request->rating,
            'comment' => $request->comment,
        ]);

        $this->updateVendorAverageRating($request->vendor_id);

        return response()->json([
            'message' => 'Rating submitted successfully',
            'data' => $rating->load('user')
        ], 201);
    }

    /**
     * Display the specified rating.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show($id): JsonResponse
    {
        $rating = VendorRating::with('user', 'vendor')->find($id);

        if (!$rating) {
            return response()->json(['message' => 'Rating not found'], 404);
        }

        return response()->json($rating);
    }

    /**
     * Update the specified rating.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(Request $request, $id): JsonResponse
    {
        $rating = VendorRating::find($id);

        if (!$rating) {
            return response()->json(['message' => 'Rating not found'], 404);
        }

        if ($rating->user_id !== $request->user()->id) {
            return response()->json(['message' => 'Unauthorized to update this rating'], 403);
        }

        $validator = Validator::make($request->all(), [
            'rating' => 'required|integer|min:1|max:5',
            'comment' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $rating->update([
            'rating' => $request->rating,
            'comment' => $request->comment,
        ]);

        $this->updateVendorAverageRating($rating->vendor_id);

        return response()->json([
            'message' => 'Rating updated successfully',
            'data' => $rating->load('user')
        ]);
    }

    /**
     * Remove the specified rating.
     *
     * @param int $id
     * @param Request $request
     * @return JsonResponse
     */
    public function destroy($id, Request $request): JsonResponse
    {
        $rating = VendorRating::find($id);

        if (!$rating) {
            return response()->json(['message' => 'Rating not found'], 404);
        }

        if ($rating->user_id !== $request->user()->id && !$request->user()->hasRole('admin')) {
            return response()->json(['message' => 'Unauthorized to delete this rating'], 403);
        }

        $vendorId = $rating->vendor_id;

        $rating->delete();

        $this->updateVendorAverageRating($vendorId);

        return response()->json([
            'message' => 'Rating deleted successfully'
        ]);
    }

    /**
     * Get the current user's rating for a vendor.
     *
     * @param int $vendor_id
     * @param Request $request
     * @return JsonResponse
     */
    public function getUserRating($vendor_id, Request $request): JsonResponse
    {
        $vendor = Vendor::find($vendor_id);
        if (!$vendor) {
            return response()->json(['message' => 'Vendor not found'], 404);
        }

        $rating = VendorRating::where('vendor_id', $vendor_id)
            ->where('user_id', $request->user()->id)
            ->first();

        if (!$rating) {
            return response()->json(['message' => 'You have not rated this vendor yet'], 404);
        }

        return response()->json($rating);
    }

    /**
     * Update a vendor's average rating.
     *
     * @param int $vendorId
     * @return void
     */
    private function updateVendorAverageRating($vendorId): void
    {
        $vendor = Vendor::find($vendorId);
        if ($vendor) {
            $averageRating = VendorRating::where('vendor_id', $vendorId)->avg('rating');
            $vendor->average_rating = $averageRating ?? 0;
            $vendor->save();
        }
    }

    /**
     * Get the count of pending reviews for a vendor.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getPendingCount(Request $request): JsonResponse
    {
        $user = $request->user();

        if (!$user) {
            return response()->json(['message' => 'User not authenticated'], 401);
        }

        $vendor = Vendor::where('user_id', $user->id)->first();

        if (!$vendor) {
            return response()->json(['message' => 'Vendor not found for this user'], 404);
        }

        // Get completed bookings where the user hasn't left a review yet
        $completedBookingsWithoutReviews = \App\Models\VendorBooking::where('vendor_id', $vendor->id)
            ->where('status', 'completed')
            ->whereDoesntHave('user.vendorRatings', function ($query) use ($vendor) {
                $query->where('vendor_id', $vendor->id);
            })
            ->count();

        return response()->json([
            'pending_count' => $completedBookingsWithoutReviews
        ]);
    }

    public function canRateVendor($vendor_id, Request $request): JsonResponse
    {
        if (!$request->user()) {
            return response()->json(['can_rate' => false, 'message' => 'User not authenticated'], 401);
        }

        $vendor = Vendor::find($vendor_id);
        if (!$vendor) {
            return response()->json(['can_rate' => false, 'message' => 'Vendor not found'], 404);
        }

        $existingRating = VendorRating::where('vendor_id', $vendor_id)
            ->where('user_id', $request->user()->id)
            ->first();

        if ($existingRating) {
            return response()->json([
                'can_rate' => false,
                'has_rated' => true,
                'message' => 'You have already rated this vendor'
            ]);
        }

        $hasEligibleBooking = VendorBooking::where('vendor_id', $vendor_id)
            ->where('user_id', $request->user()->id)
            ->where('status', 'approved')
            ->where('booking_to', '<', now())
            ->exists();

        return response()->json([
            'can_rate' => $hasEligibleBooking,
            'has_rated' => false,
            'message' => $hasEligibleBooking
                ? 'You can rate this vendor'
                : 'You need to have a completed booking with this vendor to rate them'
        ]);
    }
}
