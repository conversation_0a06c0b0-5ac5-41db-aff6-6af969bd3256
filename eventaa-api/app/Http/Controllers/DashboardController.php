<?php

namespace App\Http\Controllers;

use App\Events\DashboardStatsUpdated;
use App\Exports\ReportsExport;
use App\Models\Event;
use App\Models\User;
use App\Models\Vendor;
use App\Models\Payment;
use App\Models\Report;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class DashboardController extends Controller
{
    public function getTopEvents(Request $request): JsonResponse
    {
        try {
            $period = $request->query('period', '30d');
            $days = match($period) {
                '7d' => 7,
                '30d' => 30,
                '90d' => 90,
                'year' => 365,
                default => 30
            };

            $events = Event::with(['user:id,name', 'category:id,name'])
                ->select([
                    'events.id',
                    'events.title',
                    'events.description',
                    'events.location',
                    'events.start',
                    'events.end',
                    'events.cover_art',
                    'events.user_id',
                    'events.category_id',
                    'events.views',
                    'events.created_at',
                    DB::raw('COALESCE(ticket_stats.tickets_sold, 0) as tickets_sold'),
                    DB::raw('COALESCE(ticket_stats.revenue, 0) as revenue'),
                    DB::raw('COALESCE(attendee_stats.attendees_count, 0) as attendees_count'),
                    DB::raw('COALESCE(rating_stats.average_rating, 0) as average_rating'),
                    DB::raw('COALESCE(rating_stats.total_ratings, 0) as total_ratings')
                ])
                ->leftJoinSub(
                    DB::table('ticket_purchases')
                        ->select([
                            'event_id',
                            DB::raw('SUM(quantity) as tickets_sold'),
                            DB::raw('SUM(total_amount) as revenue')
                        ])
                        ->where('status', 'completed')
                        ->groupBy('event_id'),
                    'ticket_stats',
                    'events.id',
                    '=',
                    'ticket_stats.event_id'
                )
                ->leftJoinSub(
                    DB::table('event_attendees')
                        ->select([
                            'event_id',
                            DB::raw('COUNT(*) as attendees_count')
                        ])
                        ->groupBy('event_id'),
                    'attendee_stats',
                    'events.id',
                    '=',
                    'attendee_stats.event_id'
                )
                ->leftJoinSub(
                    DB::table('ratings')
                        ->select([
                            'event_id',
                            DB::raw('AVG(rating) as average_rating'),
                            DB::raw('COUNT(*) as total_ratings')
                        ])
                        ->groupBy('event_id'),
                    'rating_stats',
                    'events.id',
                    '=',
                    'rating_stats.event_id'
                )
                ->where('events.created_at', '>=', now()->subDays($days))
                ->orderByDesc(DB::raw('COALESCE(ticket_stats.tickets_sold, 0) + COALESCE(attendee_stats.attendees_count, 0) + (events.views / 10)'))
                ->limit(10)
                ->get()
                ->map(function ($event) use ($period) {
                    return [
                        'id' => $event->id,
                        'title' => $event->title,
                        'description' => $event->description,
                        'location' => $event->location,
                        'start' => $event->start,
                        'end' => $event->end,
                        'cover_art' => $event->cover_art,
                        'views' => (int)($event->views ?? 0),
                        'tickets_sold' => (int)($event->tickets_sold ?? 0),
                        'revenue' => (float)($event->revenue ?? 0),
                        'attendees_count' => (int)($event->attendees_count ?? 0),
                        'average_rating' => round((float)($event->average_rating ?? 0), 1),
                        'total_ratings' => (int)($event->total_ratings ?? 0),
                        'user' => $event->user,
                        'category' => $event->category,
                        'created_at' => $event->created_at,
                        'performance_score' => (int)$event->tickets_sold + (int)$event->attendees_count + (int)($event->views / 10),
                        'chart_data' => $this->getEventChartData($event->id, $period)
                    ];
                });

            return response()->json([
                'data' => $events,
                'period' => $period,
                'total_events' => $events->count()
            ]);
        } catch (\Exception $e) {
            \Log::error('Error fetching top events: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to fetch top events', 'message' => $e->getMessage()], 500);
        }
    }

    public function getAdminStats(): JsonResponse
    {
        try {
            $stats = [
                'totalRevenue' => Payment::sum('amount'),
                'grossSales' => Payment::where('status', 'completed')->sum('amount'),
                'revenueGrowth' => $this->calculateGrowth('payments', 'amount'),
                'ticketsSold' => DB::table('ticket_purchases')->where('status', 'completed')->sum('quantity'),
                'ticketsTotal' => DB::table('tickets')->count(),
                'paidTickets' => DB::table('ticket_purchases')
                    ->join('tickets', 'ticket_purchases.ticket_id', '=', 'tickets.id')
                    ->where('ticket_purchases.status', 'completed')
                    ->where('tickets.price', '>', 0)
                    ->sum('ticket_purchases.quantity'),
                'freeTickets' => DB::table('ticket_purchases')
                    ->join('tickets', 'ticket_purchases.ticket_id', '=', 'tickets.id')
                    ->where('ticket_purchases.status', 'completed')
                    ->where('tickets.price', 0)
                    ->sum('ticket_purchases.quantity'),
                'ticketsGrowth' => $this->calculateGrowth('ticket_purchases', 'quantity', 'completed'),
                'pageViews' => DB::table('page_views')->count() ?? 0,
                'socialViews' => DB::table('page_views')->where('source', 'social')->count() ?? 0,
                'viewsGrowth' => $this->calculateGrowth('page_views') ?? 0,
                'activeUsers' => User::where('last_active_at', '>=', now()->subDays(7))->count(),
                'newUsers' => User::where('created_at', '>=', now()->subDays(7))->count(),
                'usersGrowth' => $this->calculateGrowth('users'),
                'adminStats' => [
                    'totalVendors' => Vendor::count(),
                    'pendingVendors' => Vendor::where('status', 'pending')->count(),
                    'vendorsGrowth' => $this->calculateGrowth('vendors'),
                    'totalVenues' => DB::table('venues')->count(),
                    'activeVenues' => DB::table('venues')
                        ->join('venue_bookings', 'venues.id', '=', 'venue_bookings.venue_id')
                        ->where('venue_bookings.booking_from', '>', now())
                        ->where('venue_bookings.status', 'confirmed')
                        ->distinct()
                        ->count('venues.id'),
                    'venuesGrowth' => $this->calculateGrowth('venues'),
                    'totalEvents' => Event::count(),
                    'upcomingEvents' => Event::where('start', '>', now())->count(),
                    'eventsGrowth' => $this->calculateGrowth('events'),
                    'platformRevenue' => Payment::where('type', 'platform_fee')->sum('amount'),
                    'subscriptions' => DB::table('subscriptions')
                        ->where(function ($query) {
                            $query->whereNull('end_date')
                                ->orWhere('end_date', '>', now());
                        })
                        ->where('start_date', '<=', now())
                        ->count(),
                    'revenueGrowth' => $this->calculateGrowth('payments', 'amount', null, 'platform_fee')
                ]
            ];

            try {
                if (config('broadcasting.default') !== 'null') {
                    broadcast(new DashboardStatsUpdated('admin', $stats))->toOthers();
                }
            } catch (\Exception $e) {
                \Log::error('Broadcasting error: ' . $e->getMessage());
            }

            return response()->json($stats);
        } catch (\Exception $e) {
            \Log::error('Error fetching admin stats: ' . $e->getMessage());

            return response()->json([
                'totalRevenue' => 0,
                'grossSales' => 0,
                'revenueGrowth' => 0,
                'ticketsSold' => 0,
                'ticketsTotal' => 0,
                'paidTickets' => 0,
                'freeTickets' => 0,
                'ticketsGrowth' => 0,
                'pageViews' => 0,
                'socialViews' => 0,
                'viewsGrowth' => 0,
                'activeUsers' => 0,
                'newUsers' => 0,
                'usersGrowth' => 0,
                'adminStats' => [
                    'totalVendors' => 0,
                    'pendingVendors' => 0,
                    'vendorsGrowth' => 0,
                    'totalVenues' => 0,
                    'activeVenues' => 0,
                    'venuesGrowth' => 0,
                    'totalEvents' => 0,
                    'upcomingEvents' => 0,
                    'eventsGrowth' => 0,
                    'platformRevenue' => 0,
                    'subscriptions' => 0,
                    'revenueGrowth' => 0
                ],
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getHostStats(Request $request): JsonResponse
    {
        try {
            $userId = $request->user()->id;

            $stats = [
                'totalRevenue' => Payment::where('user_id', $userId)->sum('amount'),
                'grossSales' => Payment::where('user_id', $userId)->where('status', 'completed')->sum('amount'),
                'revenueGrowth' => $this->calculateGrowth('payments', 'amount', null, null, ['user_id' => $userId]),
                'ticketsSold' => DB::table('ticket_purchases')
                    ->join('events', 'ticket_purchases.event_id', '=', 'events.id')
                    ->where('events.user_id', $userId)
                    ->where('ticket_purchases.status', 'completed')
                    ->sum('ticket_purchases.quantity'),
                'ticketsTotal' => DB::table('tickets')
                    ->join('events', 'tickets.event_id', '=', 'events.id')
                    ->where('events.user_id', $userId)
                    ->count(),
                'paidTickets' => DB::table('ticket_purchases')
                    ->join('events', 'ticket_purchases.event_id', '=', 'events.id')
                    ->join('tickets', 'ticket_purchases.ticket_id', '=', 'tickets.id')
                    ->where('events.user_id', $userId)
                    ->where('ticket_purchases.status', 'completed')
                    ->where('tickets.price', '>', 0)
                    ->sum('ticket_purchases.quantity'),
                'freeTickets' => DB::table('ticket_purchases')
                    ->join('events', 'ticket_purchases.event_id', '=', 'events.id')
                    ->join('tickets', 'ticket_purchases.ticket_id', '=', 'tickets.id')
                    ->where('events.user_id', $userId)
                    ->where('ticket_purchases.status', 'completed')
                    ->where('tickets.price', 0)
                    ->sum('ticket_purchases.quantity'),
                'ticketsGrowth' => $this->calculateGrowth('ticket_purchases', 'quantity', 'completed', null, ['user_id' => $userId]),
                'pageViews' => DB::table('page_views')
                    ->join('events', 'page_views.event_id', '=', 'events.id')
                    ->where('events.user_id', $userId)
                    ->count() ?? 0,
                'socialViews' => DB::table('page_views')
                    ->join('events', 'page_views.event_id', '=', 'events.id')
                    ->where('events.user_id', $userId)
                    ->where('page_views.source', 'social')
                    ->count() ?? 0,
                'viewsGrowth' => $this->calculateGrowth('page_views', null, null, null, ['user_id' => $userId]) ?? 0,
                'activeUsers' => DB::table('event_attendees')
                    ->join('events', 'event_attendees.event_id', '=', 'events.id')
                    ->where('events.user_id', $userId)
                    ->where('event_attendees.last_active_at', '>=', now()->subDays(7))
                    ->count(),
                'newUsers' => DB::table('event_attendees')
                    ->join('events', 'event_attendees.event_id', '=', 'events.id')
                    ->where('events.user_id', $userId)
                    ->where('event_attendees.created_at', '>=', now()->subDays(7))
                    ->count(),
                'usersGrowth' => $this->calculateGrowth('event_attendees', null, null, null, ['user_id' => $userId])
            ];

            try {
                if (config('broadcasting.default') !== 'null') {
                    broadcast(new DashboardStatsUpdated('host', $stats, $userId))->toOthers();
                }
            } catch (\Exception $e) {
                \Log::error('Broadcasting error: ' . $e->getMessage());
            }

            return response()->json($stats);
        } catch (\Exception $e) {
            \Log::error('Error fetching host stats: ' . $e->getMessage());
            return response()->json([
                'totalRevenue' => 0,
                'grossSales' => 0,
                'revenueGrowth' => 0,
                'ticketsSold' => 0,
                'ticketsTotal' => 0,
                'paidTickets' => 0,
                'freeTickets' => 0,
                'ticketsGrowth' => 0,
                'pageViews' => 0,
                'socialViews' => 0,
                'viewsGrowth' => 0,
                'activeUsers' => 0,
                'newUsers' => 0,
                'usersGrowth' => 0,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getAdminActivities(): JsonResponse
    {
        $activities = DB::table('activities')
            ->select('users.name as user', 'activities.action', 'activities.resource', 'activities.created_at as date', 'activities.status')
            ->join('users', 'activities.user_id', '=', 'users.id')
            ->orderBy('activities.created_at', 'desc')
            ->limit(10)
            ->get();

        return response()->json($activities);
    }

    public function getHostActivities(Request $request): JsonResponse
    {
        $userId = $request->user()->id;

        $activities = DB::table('ticket_purchases')
            ->select(
                'users.name as user',
                'tiers.name as action',
                'ticket_purchases.total_amount as price',
                'ticket_purchases.created_at as date',
                'ticket_purchases.status'
            )
            ->join('users', 'ticket_purchases.user_id', '=', 'users.id')
            ->join('tickets', 'ticket_purchases.ticket_id', '=', 'tickets.id')
            ->join('tiers', 'tickets.tier_id', '=', 'tiers.id')
            ->join('events', 'tiers.event_id', '=', 'events.id')
            ->where('events.user_id', $userId)
            ->orderBy('ticket_purchases.created_at', 'desc')
            ->limit(10)
            ->get();

        return response()->json($activities);
    }

    public function getAdminRevenueChart(Request $request): JsonResponse
    {
        $period = $request->input('period', '30days');
        $data = $this->getChartData('payments', 'amount', $period, null, 'platform_fee');
        return response()->json($data);
    }

    public function getAdminUserGrowthChart(Request $request): JsonResponse
    {
        $period = $request->input('period', '30days');
        $data = $this->getChartData('users', null, $period);
        return response()->json($data);
    }

    public function getHostRevenueChart(Request $request): JsonResponse
    {
        $period = $request->input('period', '30days');
        $userId = $request->user()->id;
        $data = $this->getChartData('payments', 'amount', $period, null, null, ['user_id' => $userId]);
        return response()->json($data);
    }

    public function getHostTicketSalesChart(Request $request): JsonResponse
    {
        $period = $request->input('period', '30days');
        $userId = $request->user()->id;
        $data = $this->getChartData('ticket_purchases', 'quantity', $period, 'completed', null, ['user_id' => $userId]);
        return response()->json($data);
    }

    /**
     * Get public landing page statistics
     */
    public function getLandingPageStats(): JsonResponse
    {
        try {
            // Get total events count
            $totalEvents = Event::count();

            // Get total tickets sold from ticket_purchases table
            $totalTickets = DB::table('ticket_purchases')
                ->where('status', 'completed')
                ->sum('quantity');

            // Calculate average rating from all event ratings with proper fallback
            $averageRating = DB::table('ratings')->avg('rating');
            if ($averageRating === null) {
                // If no ratings exist, use a reasonable default (4.5 out of 5)
                $averageRating = 4.5;
            }
            $averageRating = round($averageRating, 1);

            // Calculate current month ticket sales
            $currentMonthTickets = DB::table('ticket_purchases')
                ->where('status', 'completed')
                ->where('created_at', '>=', now()->startOfMonth())
                ->sum('quantity');

            // Calculate last month ticket sales
            $lastMonthTickets = DB::table('ticket_purchases')
                ->where('status', 'completed')
                ->whereBetween('created_at', [
                    now()->subMonth()->startOfMonth(),
                    now()->subMonth()->endOfMonth()
                ])
                ->sum('quantity');

            // Calculate sales growth percentage
            if ($lastMonthTickets > 0) {
                $salesIncrease = round((($currentMonthTickets - $lastMonthTickets) / $lastMonthTickets) * 100);
            } else {
                // If no sales last month, show positive growth if there are current sales
                $salesIncrease = $currentMonthTickets > 0 ? 100 : 0;
            }

            // Ensure minimum positive growth for display purposes (optional)
            $salesIncrease = max($salesIncrease, 0);

            return response()->json([
                'totalEvents' => (int)$totalEvents,
                'totalTickets' => (int)$totalTickets,
                'customerRating' => (float)$averageRating,
                'averageIncrease' => (int)$salesIncrease
            ]);
        } catch (\Exception $e) {
            \Log::error('Error fetching landing page stats: ' . $e->getMessage());
            \Log::error('Stack trace: ' . $e->getTraceAsString());

            return response()->json([
                'totalEvents' => 0,
                'totalTickets' => 0,
                'customerRating' => 4.5,
                'averageIncrease' => 0
            ]);
        }
    }

    public function getAnalyticsStats(Request $request): JsonResponse
    {
        try {
            $userId = $request->user()->id;
            $period = $request->query('period', '30d');
            $days = match($period) {
                '7d' => 7,
                '30d' => 30,
                '90d' => 90,
                'year' => 365,
                default => 30
            };

            // Get basic stats with error handling
            $ticketsSold = 0;
            $revenue = 0;
            $attendees = 0;
            $pageViews = 0;

            try {
                $ticketsSold = DB::table('ticket_purchases')
                    ->join('events', 'ticket_purchases.event_id', '=', 'events.id')
                    ->where('events.user_id', $userId)
                    ->where('ticket_purchases.status', 'completed')
                    ->sum('ticket_purchases.quantity');
            } catch (\Exception $e) {
                \Log::warning('Error fetching tickets sold: ' . $e->getMessage());
            }

            try {
                $revenue = Payment::where('user_id', $userId)->where('status', 'completed')->sum('amount') ?? 0;
            } catch (\Exception $e) {
                \Log::warning('Error fetching revenue: ' . $e->getMessage());
            }

            try {
                $attendees = DB::table('ticket_purchases')
                    ->join('events', 'ticket_purchases.event_id', '=', 'events.id')
                    ->where('events.user_id', $userId)
                    ->where('ticket_purchases.status', 'completed')
                    ->sum('ticket_purchases.quantity');
            } catch (\Exception $e) {
                \Log::warning('Error fetching attendees: ' . $e->getMessage());
            }

            // Skip page views for now as table doesn't exist
            $pageViews = 0;

            // Get growth stats with fallbacks
            $ticketsGrowth = 0;
            $revenueGrowth = 0;
            $attendeesGrowth = 0;
            $pageViewsGrowth = 0;

            try {
                $ticketsGrowth = $this->calculateGrowth('ticket_purchases', 'quantity', 'completed', null, ['user_id' => $userId]);
            } catch (\Exception $e) {
                \Log::warning('Error calculating tickets growth: ' . $e->getMessage());
            }

            try {
                $revenueGrowth = $this->calculateGrowth('payments', 'amount', 'completed', null, ['user_id' => $userId]);
            } catch (\Exception $e) {
                \Log::warning('Error calculating revenue growth: ' . $e->getMessage());
            }

            try {
                $attendeesGrowth = $this->calculateGrowth('ticket_purchases', 'quantity', 'completed', null, ['user_id' => $userId]);
            } catch (\Exception $e) {
                \Log::warning('Error calculating attendees growth: ' . $e->getMessage());
            }

            // Skip page views growth calculation
            $pageViewsGrowth = 0;

            $stats = [
                'ticketsSold' => $ticketsSold,
                'ticketsGrowth' => $ticketsGrowth,
                'revenue' => $revenue,
                'revenueGrowth' => $revenueGrowth,
                'attendees' => $attendees,
                'attendeesGrowth' => $attendeesGrowth,
                'pageViews' => $pageViews,
                'pageViewsGrowth' => $pageViewsGrowth,
            ];

            return response()->json($stats);
        } catch (\Exception $e) {
            \Log::error('Error fetching analytics stats: ' . $e->getMessage());
            \Log::error('Stack trace: ' . $e->getTraceAsString());

            // Return default stats instead of error
            return response()->json([
                'ticketsSold' => 0,
                'ticketsGrowth' => 0,
                'revenue' => 0,
                'revenueGrowth' => 0,
                'attendees' => 0,
                'attendeesGrowth' => 0,
                'pageViews' => 0,
                'pageViewsGrowth' => 0,
            ]);
        }
    }



    public function getRevenueAnalytics(Request $request): JsonResponse
    {
        try {
            $userId = $request->user()->id;
            $period = $request->query('period', '30d');
            $chartData = $this->getChartData('payments', 'amount', $period, 'completed', null, ['user_id' => $userId]);

            return response()->json([
                'chart_data' => $chartData,
                'total_revenue' => Payment::where('user_id', $userId)->where('status', 'completed')->sum('amount'),
                'period' => $period
            ]);
        } catch (\Exception $e) {
            \Log::error('Error fetching revenue analytics: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to fetch revenue analytics'], 500);
        }
    }

    public function getAttendeesAnalytics(Request $request): JsonResponse
    {
        try {
            $userId = $request->user()->id;
            $period = $request->query('period', '30d');
            $chartData = $this->getChartData('ticket_purchases', 'quantity', $period, 'completed', null, ['user_id' => $userId]);

            return response()->json([
                'chart_data' => $chartData,
                'total_attendees' => DB::table('ticket_purchases')
                    ->join('events', 'ticket_purchases.event_id', '=', 'events.id')
                    ->where('events.user_id', $userId)
                    ->where('ticket_purchases.status', 'completed')
                    ->sum('ticket_purchases.quantity'),
                'period' => $period
            ]);
        } catch (\Exception $e) {
            \Log::error('Error fetching attendees analytics: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to fetch attendees analytics'], 500);
        }
    }

    public function getReports(Request $request): JsonResponse
    {
        try {
            $query = Report::with('user:id,name,email');

            // Apply search filter
            if ($request->filled('search')) {
                $query->search($request->search);
            }

            // Apply type filter
            if ($request->filled('type') && $request->type !== 'all') {
                $query->ofType($request->type);
            }

            // Apply period filter
            if ($request->filled('period') && $request->period !== 'all') {
                $query->forPeriod($request->period);
            }

            // Apply date range filter
            if ($request->filled('start_date') && $request->filled('end_date')) {
                $query->whereBetween('start_date', [$request->start_date, $request->end_date]);
            }

            // Apply pagination
            $page = $request->get('page', 1);
            $limit = $request->get('limit', 10);

            $reports = $query->orderBy('created_at', 'desc')
                           ->paginate($limit, ['*'], 'page', $page);

            return response()->json([
                'data' => $reports->items(),
                'total' => $reports->total(),
                'current_page' => $reports->currentPage(),
                'per_page' => $reports->perPage(),
                'last_page' => $reports->lastPage()
            ]);
        } catch (\Exception $e) {
            \Log::error('Error fetching reports: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to fetch reports'], 500);
        }
    }

    public function getReportsStats(Request $request): JsonResponse
    {
        try {
            $period = $request->query('period', '30d');
            $days = match($period) {
                '7d' => 7,
                '30d' => 30,
                '90d' => 90,
                'year' => 365,
                default => 30
            };

            $stats = [
                'totalRevenue' => Payment::where('status', 'completed')->sum('amount'),
                'revenueGrowth' => $this->calculateGrowth('payments', 'amount', 'completed'),
                'ticketsSold' => DB::table('ticket_purchases')->where('status', 'completed')->sum('quantity'),
                'ticketsGrowth' => $this->calculateGrowth('ticket_purchases', 'quantity', 'completed'),
                'totalAttendees' => DB::table('ticket_purchases')->where('status', 'completed')->sum('quantity'),
                'attendeesGrowth' => $this->calculateGrowth('ticket_purchases', 'quantity', 'completed'),
                'vendorRevenue' => Payment::where('type', 'vendor_payment')->where('status', 'completed')->sum('amount'),
                'vendorGrowth' => $this->calculateGrowth('payments', 'amount', 'completed', 'vendor_payment'),
                'totalEvents' => Event::count(),
                'eventsGrowth' => $this->calculateGrowth('events'),
            ];

            return response()->json($stats);
        } catch (\Exception $e) {
            \Log::error('Error fetching reports stats: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to fetch reports stats'], 500);
        }
    }

    public function generateReport(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'type' => 'required|in:financial,attendance,sales,vendor',
                'period' => 'required|string',
                'start_date' => 'nullable|date',
                'end_date' => 'nullable|date|after_or_equal:start_date',
                'format' => 'nullable|in:pdf,csv,json'
            ]);

            $type = $request->input('type');
            $period = $request->input('period');
            $format = $request->input('format', 'pdf');
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');

            // Calculate date range based on period if dates not provided
            if (!$startDate || !$endDate) {
                $days = match($period) {
                    'week' => 7,
                    'month' => 30,
                    'quarter' => 90,
                    'year' => 365,
                    default => 30
                };
                $endDate = now()->toDateString();
                $startDate = now()->subDays($days)->toDateString();
            }

            // Generate report name and description
            $reportName = ucfirst($type) . ' Report - ' . ucfirst($period);
            $reportDescription = "Generated {$type} report for period: {$period}";

            // Create report record
            $report = Report::create([
                'name' => $reportName,
                'description' => $reportDescription,
                'type' => $type,
                'period' => $period,
                'status' => 'processing',
                'user_id' => $request->user()->id,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'data' => null
            ]);

            // Generate report data
            $data = $this->generateReportData($type, $startDate, $endDate);

            // Update report with data and mark as completed
            $report->update([
                'data' => $data,
                'status' => 'completed',
                'generated_at' => now()
            ]);

            return response()->json([
                'id' => $report->id,
                'message' => 'Report generated successfully',
                'report' => $report->fresh()
            ]);
        } catch (\Exception $e) {
            \Log::error('Error generating report: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to generate report'], 500);
        }
    }

    public function exportReports(Request $request)
    {
        try {
            $format = $request->get('format', 'csv');
            $query = Report::with('user:id,name,email');

            // Apply filters
            if ($request->filled('search')) {
                $query->search($request->search);
            }
            if ($request->filled('type') && $request->type !== 'all') {
                $query->ofType($request->type);
            }
            if ($request->filled('period') && $request->period !== 'all') {
                $query->forPeriod($request->period);
            }
            if ($request->filled('start_date') && $request->filled('end_date')) {
                $query->whereBetween('start_date', [$request->start_date, $request->end_date]);
            }

            $query->orderBy('created_at', 'desc');

            if ($format === 'excel') {
                $filename = 'reports-export-' . now()->format('Y-m-d-H-i-s') . '.xlsx';
                return Excel::download(new ReportsExport($query), $filename);
            } else {
                // CSV export
                $reports = $query->get();

                $csvData = [];
                $csvData[] = ['ID', 'Name', 'Type', 'Period', 'Status', 'Generated By', 'Created At', 'Generated At'];

                foreach ($reports as $report) {
                    $csvData[] = [
                        $report->id,
                        $report->name,
                        ucfirst($report->type),
                        $report->period,
                        ucfirst($report->status),
                        $report->user->name ?? 'Unknown',
                        $report->created_at->format('Y-m-d H:i:s'),
                        $report->generated_at ? $report->generated_at->format('Y-m-d H:i:s') : 'N/A'
                    ];
                }

                $filename = 'reports-export-' . now()->format('Y-m-d-H-i-s') . '.csv';

                return response()->streamDownload(function () use ($csvData) {
                    $file = fopen('php://output', 'w');
                    foreach ($csvData as $row) {
                        fputcsv($file, $row);
                    }
                    fclose($file);
                }, $filename, [
                    'Content-Type' => 'text/csv',
                    'Content-Disposition' => 'attachment; filename="' . $filename . '"'
                ]);
            }
        } catch (\Exception $e) {
            \Log::error('Error exporting reports: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to export reports'], 500);
        }
    }

    public function getReport(Request $request, $id): JsonResponse
    {
        try {
            $report = Report::with('user:id,name,email')->findOrFail($id);
            return response()->json($report);
        } catch (\Exception $e) {
            \Log::error('Error fetching report: ' . $e->getMessage());
            return response()->json(['error' => 'Report not found'], 404);
        }
    }

    public function downloadReport(Request $request, $id)
    {
        try {
            $report = Report::findOrFail($id);

            if ($report->status !== 'completed') {
                return response()->json(['error' => 'Report is not ready for download'], 400);
            }

            $filename = str_replace(' ', '-', strtolower($report->name)) . '.pdf';

            // For now, return a mock PDF content
            $pdfContent = "Mock PDF content for report: {$report->name}\n\nGenerated at: {$report->generated_at}\n\nData: " . json_encode($report->data, JSON_PRETTY_PRINT);

            return response($pdfContent, 200, [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"'
            ]);
        } catch (\Exception $e) {
            \Log::error('Error downloading report: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to download report'], 500);
        }
    }

    public function deleteReport(Request $request, $id): JsonResponse
    {
        try {
            $report = Report::findOrFail($id);
            $report->delete();

            return response()->json(['message' => 'Report deleted successfully']);
        } catch (\Exception $e) {
            \Log::error('Error deleting report: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to delete report'], 500);
        }
    }

    public function regenerateReport(Request $request, $id): JsonResponse
    {
        try {
            $report = Report::findOrFail($id);

            // Update status to processing
            $report->update(['status' => 'processing']);

            // Regenerate report data
            $data = $this->generateReportData($report->type, $report->start_date, $report->end_date);

            // Update report with new data
            $report->update([
                'data' => $data,
                'status' => 'completed',
                'generated_at' => now()
            ]);

            return response()->json([
                'message' => 'Report regenerated successfully',
                'report' => $report->fresh()
            ]);
        } catch (\Exception $e) {
            \Log::error('Error regenerating report: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to regenerate report'], 500);
        }
    }

    private function generateReportData($type, $startDate, $endDate): array
    {
        $data = [];

        switch ($type) {
            case 'financial':
                $data = [
                    'revenue' => Payment::where('status', 'completed')
                        ->whereBetween('created_at', [$startDate, $endDate])
                        ->sum('amount'),
                    'transactions' => Payment::whereBetween('created_at', [$startDate, $endDate])->count(),
                    'refunds' => Payment::where('status', 'refunded')
                        ->whereBetween('created_at', [$startDate, $endDate])
                        ->sum('amount'),
                    'average_transaction' => Payment::where('status', 'completed')
                        ->whereBetween('created_at', [$startDate, $endDate])
                        ->avg('amount'),
                ];
                break;
            case 'attendance':
                $data = [
                    'tickets_sold' => DB::table('ticket_purchases')
                        ->where('status', 'completed')
                        ->whereBetween('created_at', [$startDate, $endDate])
                        ->sum('quantity'),
                    'events_count' => Event::whereBetween('created_at', [$startDate, $endDate])->count(),
                    'average_attendance' => DB::table('ticket_purchases')
                        ->where('status', 'completed')
                        ->whereBetween('created_at', [$startDate, $endDate])
                        ->sum('quantity') / max(1, Event::whereBetween('created_at', [$startDate, $endDate])->count()),
                ];
                break;
            case 'sales':
                $data = [
                    'total_sales' => Payment::where('status', 'completed')
                        ->whereBetween('created_at', [$startDate, $endDate])
                        ->sum('amount'),
                    'tickets_sold' => DB::table('ticket_purchases')
                        ->where('status', 'completed')
                        ->whereBetween('created_at', [$startDate, $endDate])
                        ->sum('quantity'),
                    'conversion_rate' => 0.15, // Mock conversion rate
                ];
                break;
            case 'vendor':
                $data = [
                    'total_vendors' => Vendor::whereBetween('created_at', [$startDate, $endDate])->count(),
                    'active_vendors' => Vendor::where('status', 'approved')
                        ->whereBetween('created_at', [$startDate, $endDate])
                        ->count(),
                    'pending_vendors' => Vendor::where('status', 'pending')
                        ->whereBetween('created_at', [$startDate, $endDate])
                        ->count(),
                    'vendor_revenue' => Payment::where('type', 'vendor_payment')
                        ->where('status', 'completed')
                        ->whereBetween('created_at', [$startDate, $endDate])
                        ->sum('amount'),
                ];
                break;
        }

        return $data;
    }

    private function calculateGrowth(
        string $table,
        ?string $sumColumn = null,
        ?string $whereValue = null,
        ?string $whereType = null,
        array $additionalWhere = []
    ): float {
        $query = DB::table($table);

        // Handle user filtering for tickets and ticket_purchases tables
        if (($table === 'tickets' || $table === 'ticket_purchases') && isset($additionalWhere['user_id'])) {
            $query->join('events', "$table.event_id", '=', 'events.id')
                  ->where('events.user_id', $additionalWhere['user_id']);

            $dateColumn = "$table.created_at";
            $statusColumn = "$table.status";
        } else {
            $dateColumn = 'created_at';
            $statusColumn = 'status';

            if (!empty($additionalWhere)) {
                foreach ($additionalWhere as $column => $value) {
                    $query->where($column, $value);
                }
            }
        }

        if ($whereValue) {
            $query->where($statusColumn, $whereValue);
        }

        if ($whereType) {
            $query->where('type', $whereType);
        }

        $currentPeriod = clone $query;
        $previousPeriod = clone $query;

        if ($sumColumn) {
            $sumColumnWithTable = $table === 'tickets' && isset($additionalWhere['user_id'])
                ? "$table.$sumColumn"
                : $sumColumn;
            $current = $currentPeriod->whereBetween($dateColumn, [now()->subDays(30), now()])->sum($sumColumnWithTable);
            $previous = $previousPeriod->whereBetween($dateColumn, [now()->subDays(60), now()->subDays(30)])->sum($sumColumnWithTable);
        } else {
            $current = $currentPeriod->whereBetween($dateColumn, [now()->subDays(30), now()])->count();
            $previous = $previousPeriod->whereBetween($dateColumn, [now()->subDays(60), now()->subDays(30)])->count();
        }

        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }

        return round((($current - $previous) / $previous) * 100, 1);
    }

    private function getChartData(
        string $table,
        ?string $sumColumn = null,
        string $period = '30days',
        ?string $whereValue = null,
        ?string $whereType = null,
        array $additionalWhere = []
    ): array {
        $days = match($period) {
            '7days' => 7,
            '30days' => 30,
            '90days' => 90,
            'year' => 365,
            default => 30
        };

        $query = DB::table($table);

        // Handle user filtering for tickets and ticket_purchases tables
        if (($table === 'tickets' || $table === 'ticket_purchases') && isset($additionalWhere['user_id'])) {
            $query->join('events', "$table.event_id", '=', 'events.id')
                  ->where('events.user_id', $additionalWhere['user_id'])
                  ->select(
                      DB::raw("DATE($table.created_at) as date"),
                      DB::raw($sumColumn ? "SUM($table.$sumColumn) as value" : 'COUNT(*) as value')
                  )
                  ->whereBetween("$table.created_at", [now()->subDays($days), now()]);

            if ($whereValue) {
                $query->where("$table.status", $whereValue);
            }
        } else {
            $query->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw($sumColumn ? "SUM($sumColumn) as value" : 'COUNT(*) as value')
            )
            ->whereBetween('created_at', [now()->subDays($days), now()]);

            if (!empty($additionalWhere)) {
                foreach ($additionalWhere as $column => $value) {
                    $query->where($column, $value);
                }
            }

            if ($whereValue) {
                $query->where('status', $whereValue);
            }

            if ($whereType) {
                $query->where('type', $whereType);
            }
        }

        $data = $query->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'labels' => $data->pluck('date'),
            'values' => $data->pluck('value')
        ];
    }

    private function getEventChartData($eventId, $period): array
    {
        $days = match($period) {
            '7d' => 7,
            '30d' => 30,
            '90d' => 90,
            'year' => 365,
            default => 30
        };

        $startDate = now()->subDays($days);
        $endDate = now();

        $ticketSales = DB::table('ticket_purchases')
            ->where('event_id', $eventId)
            ->where('status', 'completed')
            ->where('created_at', '>=', $startDate)
            ->selectRaw('DATE(created_at) as date, SUM(quantity) as tickets, SUM(total_amount) as revenue')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $attendees = DB::table('event_attendees')
            ->where('event_id', $eventId)
            ->where('created_at', '>=', $startDate)
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $chartData = [];
        $currentDate = $startDate->copy();

        while ($currentDate <= $endDate) {
            $dateStr = $currentDate->format('Y-m-d');

            $ticketData = $ticketSales->firstWhere('date', $dateStr);
            $attendeeData = $attendees->firstWhere('date', $dateStr);

            $chartData[] = [
                'date' => $dateStr,
                'tickets_sold' => $ticketData ? (int)$ticketData->tickets : 0,
                'revenue' => $ticketData ? (float)$ticketData->revenue : 0,
                'attendees' => $attendeeData ? (int)$attendeeData->count : 0,
            ];

            $currentDate->addDay();
        }

        return [
            'labels' => collect($chartData)->pluck('date'),
            'datasets' => [
                [
                    'label' => 'Tickets Sold',
                    'data' => collect($chartData)->pluck('tickets_sold'),
                    'borderColor' => '#3B82F6',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                ],
                [
                    'label' => 'Revenue',
                    'data' => collect($chartData)->pluck('revenue'),
                    'borderColor' => '#10B981',
                    'backgroundColor' => 'rgba(16, 185, 129, 0.1)',
                ],
                [
                    'label' => 'Attendees',
                    'data' => collect($chartData)->pluck('attendees'),
                    'borderColor' => '#F59E0B',
                    'backgroundColor' => 'rgba(245, 158, 11, 0.1)',
                ]
            ]
        ];
    }
}
