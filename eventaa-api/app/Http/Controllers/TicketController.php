<?php

namespace App\Http\Controllers;

use App\Models\Event;
use App\Models\Ticket;
use App\Models\TicketPurchase;
use App\Models\RefundRequest;
use App\Models\User;
use App\Models\UserTicket;
use App\Models\Payment;
use App\Notifications\UserNotification;
use App\Notifications\RefundRequestNotification;
use App\Notifications\RefundStatusNotification;
use App\Notifications\EventOwnerRefundNotification;
use App\Services\TicketService;
use App\Services\PayChanguService;
use App\Jobs\ProcessRefundRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class TicketController extends Controller
{
    /**
     * Get available tickets for an event
     */
    public function getEventTickets(Request $request, $eventId): JsonResponse
    {
        $event = Event::with([
            'tiers' => function ($query) {
                $query->orderBy('price', 'asc');
            }
        ])->find($eventId);

        if (!$event) {
            return response()->json(['message' => 'Event not found'], 404);
        }

        $tickets = Ticket::with('tier')
            ->where('event_id', $eventId)
            ->available()
            ->get()
            ->map(function ($ticket) {
                return [
                    'id' => $ticket->id,
                    'name' => $ticket->name,
                    'price' => $ticket->price,
                    'description' => $ticket->description,
                    'banner' => $ticket->banner,
                    'quantity_available' => $ticket->quantity_available,
                    'quantity_sold' => $ticket->quantity_sold,
                    'remaining_quantity' => $ticket->remaining_quantity,
                    'sale_start_date' => $ticket->sale_start_date,
                    'sale_end_date' => $ticket->sale_end_date,
                    'is_refundable' => $ticket->is_refundable,
                    'refund_fee_percentage' => $ticket->refund_fee_percentage,
                    'tier' => $ticket->tier,
                    'is_available' => $ticket->isAvailable(),
                ];
            });

        return response()->json([
            'message' => 'Tickets retrieved successfully',
            'data' => [
                'event' => $event,
                'tickets' => $tickets
            ]
        ]);
    }

    /**
     * Purchase tickets
     */
    public function purchaseTickets(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'event_id' => 'required|integer|exists:events,id',
            'tickets' => 'required|array|min:1',
            'tickets.*.ticket_id' => 'required|integer|exists:tickets,id',
            'tickets.*.quantity' => 'required|integer|min:1|max:10',
            'attendee_details' => 'sometimes|array',
            'attendee_details.*.name' => 'required_with:attendee_details|string|max:255',
            'attendee_details.*.email' => 'required_with:attendee_details|email|max:255',
            'attendee_details.*.phone' => 'sometimes|string|max:20',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = $request->user();
        $event = Event::findOrFail($request->event_id);

        if ($event->status !== 'published') {
            return response()->json(['message' => 'Event is not available for ticket purchase'], 400);
        }

        return DB::transaction(function () use ($request, $user, $event) {
            $totalAmount = 0;
            $purchaseData = [];
            $ticketIds = [];

            foreach ($request->tickets as $index => $ticketData) {
                $ticket = Ticket::with('tier')->findOrFail($ticketData['ticket_id']);

                if ($ticket->event_id !== $event->id) {
                    throw new \Exception("Ticket {$ticket->id} does not belong to event {$event->id}");
                }

                if (!$ticket->isAvailable()) {
                    throw new \Exception("Ticket '{$ticket->name}' is not available for purchase");
                }

                if ($ticket->remaining_quantity < $ticketData['quantity']) {
                    throw new \Exception("Only {$ticket->remaining_quantity} tickets remaining for '{$ticket->name}'");
                }

                $subtotal = $ticket->price * $ticketData['quantity'];
                $totalAmount += $subtotal;

                $attendeeDetails = $request->attendee_details[$index] ?? [
                    'name' => $user->name,
                    'email' => $user->email,
                    'phone' => $user->phone ?? null
                ];

                $purchaseData[] = [
                    'ticket' => $ticket,
                    'quantity' => $ticketData['quantity'],
                    'unit_price' => $ticket->price,
                    'subtotal' => $subtotal,
                    'attendee_details' => $attendeeDetails
                ];

                $ticketIds[] = $ticket->id;
            }

            $fees = $totalAmount * 0.03;
            $taxes = $totalAmount * 0.05;
            $finalAmount = $totalAmount + $fees + $taxes;

            $attempts = 0;
            $maxAttempts = 20;

            do {
                $attempts++;

                $microtime = microtime(true);
                $microseconds = (int) (($microtime - floor($microtime)) * 1000000);
                $randomString = strtoupper(Str::random(6));

                $purchaseReference = sprintf(
                    'PUR-%d-%d-%d-%s',
                    $user->id,
                    time(),
                    $microseconds,
                    $randomString
                );

                $exists = TicketPurchase::where('purchase_reference', $purchaseReference)->exists();

                if (!$exists) {
                    break;
                }

                usleep($attempts * 1000);

            } while ($attempts < $maxAttempts);

            if ($attempts >= $maxAttempts) {
                throw new \Exception('Unable to generate unique purchase reference after ' . $maxAttempts . ' attempts');
            }

            $payment = Payment::create([
                'user_id' => $user->id,
                'amount' => $finalAmount,
                'status' => 'pending',
                'type' => 'ticket_purchase',
                'payment_method' => 'paychangu',
                'transaction_id' => $purchaseReference,
                'metadata' => [
                    'event_id' => $event->id,
                    'ticket_ids' => $ticketIds,
                    'breakdown' => [
                        'subtotal' => $totalAmount,
                        'fees' => $fees,
                        'taxes' => $taxes,
                        'total' => $finalAmount
                    ]
                ]
            ]);

            $purchases = [];
            foreach ($purchaseData as $data) {
                $purchase = TicketPurchase::create([
                    'user_id' => $user->id,
                    'event_id' => $event->id,
                    'ticket_id' => $data['ticket']->id,
                    'payment_id' => $payment->id,
                    'purchase_reference' => $purchaseReference,
                    'quantity' => $data['quantity'],
                    'unit_price' => $data['unit_price'],
                    'total_amount' => $data['subtotal'],
                    'fees' => ($fees * $data['subtotal']) / $totalAmount,
                    'taxes' => ($taxes * $data['subtotal']) / $totalAmount,
                    'status' => 'pending',
                    'attendee_name' => $data['attendee_details']['name'],
                    'attendee_email' => $data['attendee_details']['email'],
                    'attendee_phone' => $data['attendee_details']['phone'] ?? null,
                    'attendee_details' => $data['attendee_details']
                ]);

                $purchases[] = $purchase;

                $data['ticket']->increment('quantity_sold', $data['quantity']);
            }

            try {
                $payChanguService = new PayChanguService();
                $webhookUrl = route('payments.webhook');

                $ticketsForPayment = [];
                foreach ($purchaseData as $data) {
                    $ticketsForPayment[] = [
                        'ticket_id' => $data['ticket']->id,
                        'quantity' => $data['quantity']
                    ];
                }

                $phoneNumber = $user->phone ?? '265888000000'; // Default phone number
                $operatorId = 'airtel'; // Default operator

                $paymentResult = $payChanguService->processTicketPurchase(
                    $ticketsForPayment,
                    $user,
                    $phoneNumber,
                    $operatorId,
                    null // No payment method
                    // Let PayChangu service generate its own unique transaction reference
                );

                if (!$paymentResult['status']) {
                    // Rollback ticket reservations
                    foreach ($purchaseData as $data) {
                        $data['ticket']->decrement('quantity_sold', $data['quantity']);
                    }

                    throw new \Exception($paymentResult['message']);
                }

                // Find the created transaction and broadcast payment initiated event
                $transaction = \App\Models\PaymentTransaction::where('transaction_id', $paymentResult['data']['reference'])->first();
                if ($transaction) {
                    event(new \App\Events\PaymentInitiated($transaction));
                }
            } catch (\Exception $e) {
                // Rollback ticket reservations
                foreach ($purchaseData as $data) {
                    $data['ticket']->decrement('quantity_sold', $data['quantity']);
                }

                throw new \Exception('Payment processing failed: ' . $e->getMessage());
            }

            return response()->json([
                'message' => 'Ticket purchase initiated successfully',
                'data' => [
                    'purchase_reference' => $purchaseReference,
                    'payment' => $payment,
                    'purchases' => $purchases,
                    'payment_url' => $paymentResult['data']['payment_url'] ?? null,
                    'paychangu_reference' => $paymentResult['data']['reference'] ?? null,
                    'total_amount' => $finalAmount,
                    'breakdown' => [
                        'subtotal' => $totalAmount,
                        'fees' => $fees,
                        'taxes' => $taxes,
                        'total' => $finalAmount
                    ]
                ]
            ]);
        });
    }

    /**
     * Confirm ticket purchase after payment
     */
    public function confirmPurchase(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'purchase_reference' => 'required|string|exists:ticket_purchases,purchase_reference',
            'payment_confirmation' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        return DB::transaction(function () use ($request) {
            $purchases = TicketPurchase::with(['ticket', 'event', 'payment', 'user'])
                ->where('purchase_reference', $request->purchase_reference)
                ->where('user_id', $request->user()->id)
                ->get();

            if ($purchases->isEmpty()) {
                return response()->json(['message' => 'Purchase not found'], 404);
            }

            $firstPurchase = $purchases->first();
            $payment = Payment::find($firstPurchase->payment_id);
            $user = User::find($firstPurchase->user_id);

            // Verify payment with payment gateway
            $verified = $this->verifyPayment($payment, $request->payment_confirmation);

            if (!$verified) {
                return response()->json(['message' => 'Payment verification failed'], 400);
            }

            // Update payment status
            $payment->update(['status' => 'completed']);

            // Generate tickets and update purchase status
            $generatedTickets = [];
            foreach ($purchases as $purchase) {
                $purchase->update([
                    'status' => 'completed',
                    'purchased_at' => now()
                ]);

                // Generate individual ticket instances for each quantity
                for ($i = 0; $i < $purchase->quantity; $i++) {
                    $ticketUuid = Str::uuid()->toString();

                    $userTicket = UserTicket::create([
                        'user_id' => $purchase->user_id,
                        'ticket_id' => $purchase->ticket_id
                    ]);

                    // Generate QR code and ticket
                    $ticketService = new TicketService();
                    $qrCode = $ticketService->generateTicketImage($userTicket->id, $purchase->ticket->tier);

                    $generatedTickets[] = [
                        'user_ticket_id' => $userTicket->id,
                        'ticket_uuid' => $ticketUuid,
                        'qr_code' => $qrCode,
                        'attendee_name' => $purchase->attendee_name,
                        'ticket_name' => $purchase->ticket->name
                    ];
                }
            }

            // Send confirmation email
            $this->sendTicketConfirmationEmail($user, $purchases, $generatedTickets);

            return response()->json([
                'message' => 'Ticket purchase confirmed successfully',
                'data' => [
                    'purchase_reference' => $request->purchase_reference,
                    'tickets' => $generatedTickets,
                    'event' => $purchases->first()->event
                ]
            ]);
        });
    }

    /**
     * Get user's ticket purchases
     */
    public function getUserPurchases(Request $request): JsonResponse
    {
        $user = $request->user();

        $purchases = TicketPurchase::with(['event', 'ticket.tier.currency'])
            ->where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->paginate($request->per_page ?? 3);

        return response()->json([
            'message' => 'User purchases retrieved successfully',
            'data' => $purchases
        ]);
    }

    /**
     * Download ticket PDF for a specific purchase
     */
    public function downloadTicket(Request $request, $purchaseId)
    {
        $user = $request->user();

        $purchase = TicketPurchase::with(['event', 'ticket.tier', 'payment'])
            ->where('user_id', $user->id)
            ->where('id', $purchaseId)
            ->first();

        if (!$purchase) {
            return response()->json(['message' => 'Ticket purchase not found'], 404);
        }

        try {
            $ticketService = new TicketService();

            $eventDate = $purchase->event->start ?
                date('M d, Y \a\t g:i A', strtotime($purchase->event->start)) :
                'Date TBA';

            // Calculate unit price for individual tickets
            $unitPrice = ($purchase->total_amount ?? $purchase->unit_price ?? 0) / ($purchase->quantity ?? 1);
            $priceFormatted = '$' . number_format($unitPrice, 2);
            if ($purchase->payment && $purchase->payment->currency) {
                $priceFormatted = $purchase->payment->currency->symbol . number_format($unitPrice, 2) . ' ' . $purchase->payment->currency->code;
            }

            // Generate individual ticket data for each quantity
            $ticketData = [];
            $quantity = $purchase->quantity ?? 1;

            for ($i = 1; $i <= $quantity; $i++) {
                $ticketData[] = [
                    'user_ticket_id' => $purchase->id . '-' . $i,
                    'ticket_uuid' => ($purchase->purchase_reference ?? $purchase->id) . '-' . $i,
                    'attendee_name' => $purchase->attendee_name ?? $user->name,
                    'ticket_name' => $purchase->ticket->name ?? 'Event Ticket',
                    'event_name' => $purchase->event->title,
                    'event_date' => $eventDate,
                    'event_location' => $purchase->event->location ?? 'Location TBA',
                    'price' => $priceFormatted,
                    'quantity' => 1, // Each individual ticket has quantity 1
                    'banner' => $purchase->ticket->tier->banner ?? $purchase->event->cover_art ?? null,
                    'ticket_number' => $i . ' of ' . $quantity
                ];
            }

            $pdfUrl = $ticketService->generatePurchaseTicketsPDF($purchase->purchase_reference ?? $purchase->id, $ticketData);

            $pdfPath = str_replace(url('/'), '', $pdfUrl);
            $fullPath = public_path($pdfPath);

            if (file_exists($fullPath)) {
                return response()->download($fullPath, "ticket-{$purchase->id}.pdf");
            } else {
                return response()->json(['message' => 'Ticket PDF not found'], 404);
            }

        } catch (\Exception $e) {
            return response()->json(['message' => 'Error generating ticket PDF'], 500);
        }
    }

    /**
     * Check refund eligibility for a ticket purchase
     */
    public function checkRefundEligibility(Request $request, $purchaseId): JsonResponse
    {
        $purchase = TicketPurchase::with(['ticket.tier', 'event', 'payment'])
            ->where('id', $purchaseId)
            ->where('user_id', $request->user()->id)
            ->firstOrFail();

        $eligible = $purchase->canBeRefunded();
        $refundAmount = $eligible ? $purchase->calculateRefundAmount() : 0;
        $processingFee = $eligible ? ($purchase->total_amount - $refundAmount) : 0;

        $response = [
            'eligible' => $eligible,
            'refund_amount' => $refundAmount,
            'processing_fee' => $processingFee,
            'original_amount' => $purchase->total_amount,
        ];

        if (!$eligible) {
            if ($purchase->status !== 'completed') {
                $response['reason'] = 'Purchase is not completed';
            } elseif (!$purchase->ticket->tier || !$purchase->ticket->tier->is_refundable) {
                $response['reason'] = 'This ticket type is non-refundable';
            } elseif ($purchase->event->start < now()) {
                $response['reason'] = 'Event has already started';
            } else {
                $refundCutoff = $purchase->event->start->subHours(24);
                if (now() > $refundCutoff) {
                    $response['reason'] = 'Refund deadline has passed (24 hours before event)';
                }
            }
        } else {
            $refundDeadline = $purchase->event->start->subHours(24);
            $response['refund_deadline'] = $refundDeadline->toISOString();
        }

        return response()->json([
            'message' => 'Refund eligibility checked',
            'data' => $response
        ]);
    }

    /**
     * Request ticket refund
     */
    public function requestRefund(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'purchase_id' => 'required|integer|exists:ticket_purchases,id',
            'reason' => 'required|string|max:500'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $purchase = TicketPurchase::with(['ticket.tier', 'event', 'payment', 'user.currency'])
            ->where('id', $request->purchase_id)
            ->where('user_id', $request->user()->id)
            ->firstOrFail();

        // Check if payment exists
        if (!$purchase->payment_id || !$purchase->payment) {
            return response()->json([
                'message' => 'Unable to process refund. Payment information is missing.',
                'data' => ['purchase_id' => $purchase->id]
            ], 400);
        }

        $existingRefund = RefundRequest::where('ticket_purchase_id', $purchase->id)
            ->whereIn('status', ['pending', 'approved', 'processing'])
            ->first();

        if ($existingRefund) {
            return response()->json([
                'message' => 'A refund request already exists for this purchase',
                'data' => ['refund_reference' => $existingRefund->refund_reference]
            ], 400);
        }

        $refundAmount = $purchase->calculateRefundAmount();
        $processingFee = $purchase->total_amount - $refundAmount;
        $feePercentage = $purchase->ticket->tier->refund_fee_percentage ??
            $purchase->ticket->refund_fee_percentage ?? 5;

        return DB::transaction(function () use ($purchase, $request, $refundAmount, $processingFee, $feePercentage) {
            $paymentMethod = 'unknown';
            $paymentId = null;

            if ($purchase->payment_id && $purchase->payment) {
                $paymentMethod = $purchase->payment->payment_method ?? 'unknown';
                $paymentId = $purchase->payment_id;
            } else {
                $payment = Payment::where('reference', $purchase->purchase_reference)->first();
                if ($payment) {
                    $paymentMethod = $payment->payment_method ?? 'unknown';
                    $paymentId = $payment->id;
                }
            }

            $refundRequest = RefundRequest::create([
                'user_id' => $purchase->user_id,
                'ticket_purchase_id' => $purchase->id,
                'event_id' => $purchase->event_id,
                'payment_id' => $paymentId,
                'reason' => $request->reason,
                'original_amount' => $purchase->total_amount,
                'refund_amount' => $refundAmount,
                'processing_fee' => $processingFee,
                'fee_percentage' => $feePercentage,
                'payment_method' => $paymentMethod,
                'status' => 'pending',
                'requested_at' => now(),
            ]);

            $purchase->user->notify(new RefundRequestNotification($refundRequest));

            $eventOwner = $purchase->event->user;
            if ($eventOwner && $eventOwner->id !== $purchase->user_id) {
                $eventOwner->notify(new EventOwnerRefundNotification($refundRequest, 'request'));
            }

            ProcessRefundRequest::dispatch($refundRequest);

            return response()->json([
                'message' => 'Refund request submitted successfully',
                'data' => [
                    'refund_reference' => $refundRequest->refund_reference,
                    'refund_amount' => $refundAmount,
                    'original_amount' => $purchase->total_amount,
                    'processing_fee' => $processingFee,
                    'status' => 'pending',
                    'estimated_processing_time' => '3-5 business days'
                ]
            ]);
        });
    }

    /**
     * Get refund requests (Admin only)
     */
    public function getRefundRequests(Request $request): JsonResponse
    {
        $query = RefundRequest::with(['user', 'ticketPurchase.event', 'ticketPurchase.ticket', 'payment', 'processedBy'])
            ->orderBy('created_at', 'desc');

        if ($request->has('status') && $request->status !== 'all') {
            $query->where('status', $request->status);
        }

        if ($request->has('event_id')) {
            $query->where('event_id', $request->event_id);
        }

        if ($request->has('from_date')) {
            $query->whereDate('created_at', '>=', $request->from_date);
        }
        if ($request->has('to_date')) {
            $query->whereDate('created_at', '<=', $request->to_date);
        }

        $refunds = $query->paginate($request->per_page ?? 15);

        return response()->json([
            'message' => 'Refund requests retrieved successfully',
            'data' => $refunds
        ]);
    }

    /**
     * Approve refund request (Admin only)
     */
    public function approveRefund(Request $request, $refundId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'admin_notes' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $refundRequest = RefundRequest::with(['user', 'ticketPurchase', 'event', 'payment'])
            ->findOrFail($refundId);

        if (!$refundRequest->canBeApproved()) {
            return response()->json(['message' => 'Refund request cannot be approved'], 400);
        }

        return DB::transaction(function () use ($refundRequest, $request) {
            $admin = $request->user();

            $refundRequest->approve($admin, $request->admin_notes);
            $refundRequest->user->notify(new RefundStatusNotification($refundRequest));

            ProcessRefundRequest::dispatch($refundRequest);

            return response()->json([
                'message' => 'Refund request approved successfully',
                'data' => $refundRequest->fresh()
            ]);
        });
    }

    /**
     * Reject refund request (Admin only)
     */
    public function rejectRefund(Request $request, $refundId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'rejection_reason' => 'required|string|max:500',
            'admin_notes' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $refundRequest = RefundRequest::with(['user', 'ticketPurchase', 'event'])
            ->findOrFail($refundId);

        if (!$refundRequest->canBeRejected()) {
            return response()->json(['message' => 'Refund request cannot be rejected'], 400);
        }

        return DB::transaction(function () use ($refundRequest, $request) {
            $admin = $request->user();

            $refundRequest->reject($admin, $request->rejection_reason, $request->admin_notes);

            $refundRequest->user->notify(new RefundStatusNotification($refundRequest));

            return response()->json([
                'message' => 'Refund request rejected successfully',
                'data' => $refundRequest->fresh()
            ]);
        });
    }

    public function generate(Request $request): JsonResponse
    {
        set_time_limit(120); // 2 minutes
        ini_set('memory_limit', '512M');

        $user = $request->user();

        if (!$user->hasRole(['host', 'admin'])) {
            return response()->json([
                'message' => 'Unauthorized. Only hosts and admins can generate tickets.'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'event_id' => 'required|integer',
            'tickets' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $packages = json_decode($request->tickets);
            $ticketIds = [];

            foreach ($packages as $package) {
                for ($limit = 0; $limit < $package->quantity; $limit++) {
                    $ticket_uuid = Str::uuid()->toString();
                    $ticket = Ticket::create([
                        "event_id" => $request->event_id,
                        "tier_id" => $package->tier->id,
                        "uuid" => $ticket_uuid,
                        "name" => $package->tier->name,
                        "price" => $package->tier->price,
                        "banner" => $package->tier->banner,
                    ]);
                    $id = $this->generateQRCodes($ticket->id, $package->tier);
                    array_push($ticketIds, $id);
                }
            }

            $hasEmailNotifications = $this->userHasEmailNotificationsEnabled($user);

            if ($hasEmailNotifications) {
                $this->sendTicketGenerationEmailNotification($user, $request->event_id, count($ticketIds));
            }

            return response()->json([
                'message' => 'Tickets generated successfully',
                'ticket_count' => count($ticketIds),
                'email_sent' => $hasEmailNotifications
            ]);

        } catch (\Exception $e) {
            Log::error('Ticket generation failed', [
                'user_id' => $user->id,
                'event_id' => $request->event_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Failed to generate tickets. Please try again.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function scan(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'ticketUUID' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $ticketService = new TicketService();
            $verificationResult = $ticketService->verifyAndUpdate($request->ticketUUID);

            if ($verificationResult) {
                return response()->json([
                    'message' => 'Ticket scanned successfully',
                    'status' => 'success'
                ], 200);
            } else {
                // Check if ticket exists but is already scanned
                $ticket = Ticket::where('uuid', $request->ticketUUID)->first();

                if ($ticket) {
                    // Check if any purchase for this ticket is already scanned
                    $scannedPurchase = $ticket->purchases()
                        ->where('status', 'completed')
                        ->where('scanned', true)
                        ->exists();

                    if ($scannedPurchase) {
                        return response()->json([
                            'message' => 'Ticket has already been scanned',
                            'status' => 'already_scanned'
                        ], 400);
                    } else {
                        return response()->json([
                            'message' => 'No valid purchase found for this ticket',
                            'status' => 'invalid_purchase'
                        ], 400);
                    }
                } else {
                    return response()->json([
                        'message' => 'Invalid ticket UUID',
                        'status' => 'invalid_ticket'
                    ], 404);
                }
            }
        } catch (\Exception $e) {
            Log::error('Ticket scan failed', [
                'ticket_uuid' => $request->ticketUUID,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'An error occurred while scanning the ticket',
                'status' => 'error'
            ], 500);
        }
    }

    public function generateQRCodes($ticketId, $tier)
    {
        $ticketService = new TicketService();
        $id = $ticketService->generateTicketImage($ticketId, $tier);
        return $id;
    }

    public function assignUserTickets(Request $request): JsonResponse
    {
        // This method is now deprecated in favor of the new purchase flow
        return response()->json([
            'message' => 'This method is deprecated. Please use the new purchase flow.',
            'redirect' => '/api/tickets/purchase'
        ], 410);
    }

    /**
     * Private helper methods
     */

    private function verifyPayment(Payment $payment, string $confirmation): bool
    {
        // Use PayChangu service for verification
        try {
            $payChanguService = new PayChanguService();
            $result = $payChanguService->verifyPayment($payment->transaction_id);

            return $result['status'] &&
                isset($result['data']['status']) &&
                $result['data']['status'] === 'success';
        } catch (\Exception $e) {
            Log::error('Payment verification failed', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    private function processRefund(Payment $payment, float $amount): void
    {
        // Process refund using PayChangu
        try {
            // For now, we'll handle refunds manually through PayChangu dashboard
            // In the future, we can implement PayChangu's refund API when available
            Log::info('Refund request processed', [
                'payment_id' => $payment->id,
                'transaction_id' => $payment->transaction_id,
                'refund_amount' => $amount,
                'original_amount' => $payment->amount,
            ]);

            // TODO: Implement PayChangu refund API when available
            // $payChanguService = new PayChanguService();
            // $result = $payChanguService->processRefund($payment->transaction_id, $amount);

        } catch (\Exception $e) {
            Log::error('Refund processing failed', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    private function sendTicketConfirmationEmail(User $user, $purchases, array $tickets): void
    {
        // Queue email job
        // SendTicketConfirmationEmail::dispatch($user, $purchases, $tickets);

        // For now, send notification
        $data = [
            'greeting' => 'Hello ' . $user->name . '!',
            'title' => 'Ticket Purchase Confirmed',
            'line' => 'Your ticket purchase has been confirmed. You can download your tickets from your account.',
            'action' => 'View Tickets',
            'actionURL' => '/tickets/my-tickets'
        ];

        $user->notify(new UserNotification($data));
    }

    /**
     * Check if user has email notifications enabled
     */
    private function userHasEmailNotificationsEnabled(User $user): bool
    {
        // Check if user has any email notification settings enabled
        $emailSettings = $user->settings()
            ->where('type', 'email')
            ->where('enabled', true)
            ->exists();

        // If no specific email settings found, default to true for backwards compatibility
        return $emailSettings ?: true;
    }

    /**
     * Send ticket generation email notification (without PDF)
     */
    private function sendTicketGenerationEmailNotification(User $user, int $eventId, int $ticketCount): void
    {
        try {
            $event = Event::find($eventId);
            $eventName = $event ? $event->title : 'Event';

            // Send simple email notification without PDF attachment
            $data = [
                'greeting' => 'Hello ' . $user->name . '!',
                'title' => 'Event Tickets Generated Successfully',
                'line' => "Great news! {$ticketCount} ticket(s) for '{$eventName}' have been successfully generated. You can now manage and distribute these tickets through your dashboard.",
                'action' => 'View Event Dashboard',
                'actionURL' => '/events/' . $eventId
            ];

            // Send notification through the existing notification system
            $user->notify(new UserNotification($data));

            Log::info('Ticket generation notification sent', [
                'user_id' => $user->id,
                'event_id' => $eventId,
                'ticket_count' => $ticketCount
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send ticket generation notification', [
                'user_id' => $user->id,
                'event_id' => $eventId,
                'ticket_count' => $ticketCount,
                'error' => $e->getMessage()
            ]);
        }
    }
}
