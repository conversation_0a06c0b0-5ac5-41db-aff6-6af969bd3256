<?php

namespace App\Http\Controllers;

use App\Models\Vendor;
use App\Models\VendorCustomPricing;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class VendorCustomPricingController extends Controller
{
    /**
     * Get custom pricing for a vendor
     *
     * @param int $vendorId
     * @return JsonResponse
     */
    public function show($vendorId): JsonResponse
    {
        $vendor = Vendor::find($vendorId);

        if (!$vendor) {
            return response()->json(['message' => 'Vendor not found'], 404);
        }

        $customPricing = VendorCustomPricing::with('currency')
            ->where('vendor_id', $vendorId)
            ->first();

        if (!$customPricing) {
            return response()->json([
                'data' => [
                    'hourly_rate' => 0,
                    'day_rate' => 0,
                    'currency_id' => null,
                    'notes' => ''
                ]
            ]);
        }

        return response()->json(['data' => $customPricing]);
    }

    /**
     * Store or update custom pricing for a vendor
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'vendor_id' => 'required|exists:vendors,id',
            'hourly_rate' => 'required|numeric|min:0',
            'day_rate' => 'required|numeric|min:0',
            'currency_id' => 'required|exists:currencies,id',
            'notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Check if the vendor belongs to the authenticated user
        $vendor = Vendor::find($request->vendor_id);
        if (!$vendor || $vendor->user_id !== $request->user()->id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $customPricing = VendorCustomPricing::updateOrCreate(
            ['vendor_id' => $request->vendor_id],
            [
                'hourly_rate' => $request->hourly_rate,
                'day_rate' => $request->day_rate,
                'currency_id' => $request->currency_id,
                'notes' => $request->notes,
            ]
        );

        return response()->json([
            'message' => 'Custom pricing saved successfully',
            'data' => $customPricing->fresh('currency')
        ]);
    }

    /**
     * Update custom pricing for a vendor
     *
     * @param Request $request
     * @param int $vendorId
     * @return JsonResponse
     */
    public function update(Request $request, $vendorId): JsonResponse
    {
        $vendor = Vendor::find($vendorId);

        if (!$vendor) {
            return response()->json(['message' => 'Vendor not found'], 404);
        }

        // Check if the vendor belongs to the authenticated user
        if ($vendor->user_id !== $request->user()->id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'hourly_rate' => 'sometimes|required|numeric|min:0',
            'day_rate' => 'sometimes|required|numeric|min:0',
            'currency_id' => 'sometimes|required|exists:currencies,id',
            'notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $customPricing = VendorCustomPricing::updateOrCreate(
            ['vendor_id' => $vendorId],
            $request->all()
        );

        return response()->json([
            'message' => 'Custom pricing updated successfully',
            'data' => $customPricing->fresh('currency')
        ]);
    }
}
