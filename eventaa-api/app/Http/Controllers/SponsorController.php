<?php

namespace App\Http\Controllers;

use App\Models\Sponsor;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SponsorController extends Controller
{
    public function create(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|unique:sponsors',
            'logo' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'latitude' => 'nullable|string',
            'longitude' => 'nullable|string',
            'address' => 'required|string',
            'city' => 'required|string',
            'country' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $logo = 'logo.png';

        if ($request->file('logo')) {
            $image = $request->file('logo');
            $logo = time() . '.' . $image->getClientOriginalExtension();
            $image->move(public_path('storage/sponsors'), $logo);
        }

        Sponsor::create([
            'name' => $request->name,
            'logo' => $logo,
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
            'address' => $request->address,
            'city' => $request->city,
            'country' => $request->country,
        ]);

        return response()->json(['message' => 'Sponsor created successfully'], 201);
    }

    public function update(Request $request, $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|unique:sponsors,name,' . $id,
            'logo' => 'required|string',
            'latitude' => 'nullable|string',
            'longitude' => 'nullable|string',
            'address' => 'required|string',
            'city' => 'required|string',
            'country' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $sponsor = Sponsor::find($id);
        if (!$sponsor) {
            return response()->json(['error' => 'Sponsor not found'], 422);
        }

        $logo = $sponsor->logo;
        if ($request->file('logo')) {
            $image = $request->file('logo');
            $logo = time() . '.' . $image->getClientOriginalExtension();
            $image->move(public_path('storage/sponsors'), $logo);
        }

        $sponsor->name = $request->name;
        $sponsor->logo = $logo;
        $sponsor->latitude = $request->latitude;
        $sponsor->longitude = $request->longitude;
        $sponsor->address = $request->address;
        $sponsor->city = $request->city;
        $sponsor->country = $request->country;
        $sponsor->save();

        return response()->json(['message' => 'Sponsor updated successfully'], 200);
    }

    public function delete(Request $request, $id): JsonResponse
    {
        $sponsor = Sponsor::find($id);
        if (!$sponsor) {
            return response()->json(['error' => 'Sponsor not found'], 422);
        }

        $sponsor->delete();
        return response()->json(['message' => 'Sponsor deleted successfully'], 200);
    }

    public function read(Request $request): JsonResponse
    {
        $sponsors = Sponsor::all();
        return response()->json(['sponsors' => $sponsors], 200);
    }
}
