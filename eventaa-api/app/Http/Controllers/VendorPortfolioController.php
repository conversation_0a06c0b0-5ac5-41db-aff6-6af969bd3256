<?php

namespace App\Http\Controllers;

use App\Models\Vendor;
use App\Models\VendorPortfolio;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class VendorPortfolioController extends Controller
{
    /**
     * Display a listing of the portfolio items for a vendor.
     *
     * @param int $vendorId
     * @param Request $request
     * @return JsonResponse
     */
    public function index($vendorId, Request $request): JsonResponse
    {
        $vendor = Vendor::find($vendorId);
        if (!$vendor) {
            return response()->json(['message' => 'Vendor not found'], 404);
        }

        $query = VendorPortfolio::where('vendor_id', $vendorId);

        // Filter by category if provided
        if ($request->has('category') && $request->category !== 'all') {
            $query->where('category', $request->category);
        }

        // Sort by created_at by default, or by the provided sort field
        $sortField = $request->sort_by ?? 'created_at';
        $sortDirection = $request->sort_direction ?? 'desc';
        $query->orderBy($sortField, $sortDirection);

        $perPage = $request->per_page ?? 12;
        $portfolioItems = $query->paginate($perPage);

        // Get unique categories for filtering
        $categories = VendorPortfolio::where('vendor_id', $vendorId)
            ->whereNotNull('category')
            ->distinct()
            ->pluck('category');

        return response()->json([
            'portfolio_items' => $portfolioItems,
            'categories' => $categories
        ]);
    }

    /**
     * Store a newly created portfolio item.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'vendor_id' => 'required|exists:vendors,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'image' => 'required|image|max:5120', // 5MB max
            'media_type' => 'required|in:image,video',
            'category' => 'nullable|string|max:100',
            'is_featured' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Check if the authenticated user is the owner of the vendor
        $user = $request->user();
        $vendor = Vendor::find($request->vendor_id);

        if (!$vendor || $vendor->user_id !== $user->id) {
            return response()->json(['message' => 'Unauthorized to add portfolio items for this vendor'], 403);
        }

        // Handle file upload
        if ($request->hasFile('image')) {
            $file = $request->file('image');
            $path = $file->store('vendor_portfolio', 'public');
        } else {
            return response()->json(['message' => 'Image file is required'], 422);
        }

        $portfolioItem = VendorPortfolio::create([
            'vendor_id' => $request->vendor_id,
            'title' => $request->title,
            'description' => $request->description,
            'image_path' => $path,
            'media_type' => $request->media_type,
            'category' => $request->category,
            'is_featured' => $request->is_featured ?? false,
        ]);

        return response()->json([
            'message' => 'Portfolio item created successfully',
            'data' => $portfolioItem
        ], 201);
    }

    /**
     * Display the specified portfolio item.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show($id): JsonResponse
    {
        $portfolioItem = VendorPortfolio::with('vendor')->find($id);

        if (!$portfolioItem) {
            return response()->json(['message' => 'Portfolio item not found'], 404);
        }

        return response()->json($portfolioItem);
    }

    /**
     * Update the specified portfolio item.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(Request $request, $id): JsonResponse
    {
        $portfolioItem = VendorPortfolio::find($id);

        if (!$portfolioItem) {
            return response()->json(['message' => 'Portfolio item not found'], 404);
        }

        $user = $request->user();
        $vendor = Vendor::find($portfolioItem->vendor_id);

        if (!$vendor || $vendor->user_id !== $user->id) {
            return response()->json(['message' => 'Unauthorized to update this portfolio item'], 403);
        }

        $validator = Validator::make($request->all(), [
            'title' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|image|max:5120',
            'media_type' => 'sometimes|required|in:image,video',
            'category' => 'nullable|string|max:100',
            'is_featured' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        if ($request->hasFile('image')) {
            if ($portfolioItem->image_path) {
                Storage::disk('public')->delete($portfolioItem->image_path);
            }

            $file = $request->file('image');
            $path = $file->store('vendor_portfolio', 'public');
            $portfolioItem->image_path = $path;
        }

        if ($request->has('title')) {
            $portfolioItem->title = $request->title;
        }
        if ($request->has('description')) {
            $portfolioItem->description = $request->description;
        }
        if ($request->has('media_type')) {
            $portfolioItem->media_type = $request->media_type;
        }
        if ($request->has('category')) {
            $portfolioItem->category = $request->category;
        }
        if ($request->has('is_featured')) {
            $portfolioItem->is_featured = $request->is_featured;
        }

        $portfolioItem->save();

        return response()->json([
            'message' => 'Portfolio item updated successfully',
            'data' => $portfolioItem
        ]);
    }

    /**
     * Remove the specified portfolio item.
     *
     * @param int $id
     * @param Request $request
     * @return JsonResponse
     */
    public function destroy($id, Request $request): JsonResponse
    {
        $portfolioItem = VendorPortfolio::find($id);

        if (!$portfolioItem) {
            return response()->json(['message' => 'Portfolio item not found'], 404);
        }

        $user = $request->user();
        $vendor = Vendor::find($portfolioItem->vendor_id);

        if (!$vendor || $vendor->user_id !== $user->id) {
            return response()->json(['message' => 'Unauthorized to delete this portfolio item'], 403);
        }

        if ($portfolioItem->image_path) {
            Storage::disk('public')->delete($portfolioItem->image_path);
        }

        $portfolioItem->delete();

        return response()->json([
            'message' => 'Portfolio item deleted successfully'
        ]);
    }
}
