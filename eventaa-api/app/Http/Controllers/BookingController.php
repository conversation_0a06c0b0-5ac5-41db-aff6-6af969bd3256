<?php

namespace App\Http\Controllers;

use App\Mail\BookingConfirmation;
use App\Mail\BookingStatus;
use App\Models\User;
use App\Models\Venue;
use App\Models\VenueBooking;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class BookingController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();

        if (!$user) {
            return response()->json(['message' => 'Unauthenticated. Please log in.'], 401);
        }

        $venueBookings = VenueBooking::with(['venue', 'category', 'venuePrice.currency'])
            ->where('user_id', $user->id)
            ->latest()
            ->paginate($request->per_page ?? 10);

        return response()->json($venueBookings);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'venue_id' => 'required|exists:venues,id',
            'category_id' => 'required|exists:categories,id',
            'booking_from' => 'required|date|after_or_equal:today',
            'booking_to' => 'required|date|after_or_equal:booking_from',
            'number_of_guests' => 'required|integer|min:1',
            'message' => 'required|string',
            'venue_price_id' => 'required|exists:venue_prices,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $validatedData = $validator->validated();

        $existingBooking = VenueBooking::where('venue_id', $validatedData['venue_id'])
            ->where(function ($query) use ($validatedData) {
                $query->whereBetween('booking_from', [$validatedData['booking_from'], $validatedData['booking_to']])
                    ->orWhereBetween('booking_to', [$validatedData['booking_from'], $validatedData['booking_to']])
                    ->orWhere(function ($q) use ($validatedData) {
                        $q->where('booking_from', '<=', $validatedData['booking_from'])
                            ->where('booking_to', '>=', $validatedData['booking_to']);
                    });
            })
            ->where('status', '!=', 'rejected')
            ->first();

        if ($existingBooking) {
            return response()->json([
                'status' => 'error',
                'message' => 'The venue is already booked for the selected dates.',
            ], 422);
        }

        $venueBooking = new VenueBooking();
        $venueBooking->venue_id = $validatedData['venue_id'];
        $venueBooking->user_id = $request->user()->id;
        $venueBooking->category_id = $validatedData['category_id'];
        $venueBooking->booking_from = date('Y-m-d H:i:s', strtotime($validatedData['booking_from']));
        $venueBooking->booking_to = date('Y-m-d H:i:s', strtotime($validatedData['booking_to']));
        $venueBooking->number_of_guests = $validatedData['number_of_guests'];
        $venueBooking->message = $validatedData['message'];
        $venueBooking->venue_price_id = $validatedData['venue_price_id'];
        $venueBooking->status = 'pending';
        $venueBooking->save();

        $user = $request->user();
        $venue = Venue::find($venueBooking->venue_id);
        Mail::to($user->email)->send(new BookingConfirmation($venueBooking, $venue, $user));

        return response()->json([
            'status' => 'success',
            'message' => 'Venue booking created successfully',
            'data' => $venueBooking->load(['venue', 'user', 'category', 'venuePrice']),
        ], 201);
    }

    public function show($id)
    {
        $venueBooking = VenueBooking::with(['venue', 'user', 'category', 'venuePrice.currency'])->find($id);

        if (!$venueBooking) {
            return response()->json([
                'status' => 'error',
                'message' => 'Venue booking not found',
            ], 404);
        }

        return response()->json([
            'status' => 'success',
            'data' => $venueBooking,
        ]);
    }

    public function createOrUpdate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'sometimes|exists:venue_bookings,id',
            'venue_id' => 'required|exists:venues,id',
            'category_id' => 'required|exists:categories,id',
            'booking_from' => 'required|date',
            'booking_to' => 'required|date|after_or_equal:booking_from',
            'number_of_guests' => 'required|integer|min:1',
            'message' => 'required|string',
            'venue_price_id' => 'required|exists:venue_prices,id',
            'status' => ['sometimes', Rule::in(['pending', 'approved', 'rejected'])],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $validatedData = $validator->validated();

        if (isset($validatedData['id'])) {
            $venueBooking = VenueBooking::find($validatedData['id']);
            if (!$venueBooking) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Venue booking not found',
                ], 404);
            }

            $existingBooking = VenueBooking::where('venue_id', $validatedData['venue_id'])
                ->where('id', '!=', $validatedData['id'])
                ->where(function ($query) use ($validatedData) {
                    $query->whereBetween('booking_from', [$validatedData['booking_from'], $validatedData['booking_to']])
                        ->orWhereBetween('booking_to', [$validatedData['booking_from'], $validatedData['booking_to']])
                        ->orWhere(function ($q) use ($validatedData) {
                            $q->where('booking_from', '<=', $validatedData['booking_from'])
                                ->where('booking_to', '>=', $validatedData['booking_to']);
                        });
                })
                ->where('status', '!=', 'rejected')
                ->first();
        } else {
            $venueBooking = new VenueBooking();
            $venueBooking->user_id = $request->user()->id;
            $venueBooking->status = 'pending';

            $existingBooking = VenueBooking::where('venue_id', $validatedData['venue_id'])
                ->where(function ($query) use ($validatedData) {
                    $query->whereBetween('booking_from', [$validatedData['booking_from'], $validatedData['booking_to']])
                        ->orWhereBetween('booking_to', [$validatedData['booking_from'], $validatedData['booking_to']])
                        ->orWhere(function ($q) use ($validatedData) {
                            $q->where('booking_from', '<=', $validatedData['booking_from'])
                                ->where('booking_to', '>=', $validatedData['booking_to']);
                        });
                })
                ->where('status', '!=', 'rejected')
                ->first();
        }

        if ($existingBooking) {
            return response()->json([
                'status' => 'error',
                'message' => 'The venue is already booked for the selected dates.',
            ], 422);
        }

        $venueBooking->venue_id = $validatedData['venue_id'];
        $venueBooking->category_id = $validatedData['category_id'];
        $venueBooking->booking_to = date('Y-m-d H:i:s', strtotime($validatedData['booking_to']));
        $venueBooking->number_of_guests = $validatedData['number_of_guests'];
        $venueBooking->message = $validatedData['message'];
        $venueBooking->venue_price_id = $validatedData['venue_price_id'];

        if (isset($validatedData['status'])) {
            $venueBooking->status = $validatedData['status'];
        }

        $venueBooking->save();

        return response()->json([
            'status' => 'success',
            'message' => isset($validatedData['id']) ? 'Venue booking updated successfully' : 'Venue booking created successfully',
            'data' => $venueBooking->load(['venue', 'user', 'category', 'venuePrice']),
        ], isset($validatedData['id']) ? 200 : 201);
    }

    public function destroy($id)
    {
        $venueBooking = VenueBooking::find($id);

        if (!$venueBooking) {
            return response()->json([
                'status' => 'error',
                'message' => 'Venue booking not found',
            ], 404);
        }

        $venueBooking->delete();

        return response()->json([
            'status' => 'success',
            'message' => 'Venue booking deleted successfully',
        ]);
    }

    public function updateStatus(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'status' => ['required', Rule::in(['pending', 'approved', 'rejected'])],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $venueBooking = VenueBooking::find($id);

        if (!$venueBooking) {
            return response()->json([
                'status' => 'error',
                'message' => 'Venue booking not found',
            ], 404);
        }

        $venueBooking->status = $request->status;
        $venueBooking->save();

        $venue = Venue::find($venueBooking->venue_id);
        $user = User::find($venueBooking->user_id);
        Mail::to($user->email)->send(new BookingStatus($venueBooking, $venue, $user));

        return response()->json([
            'status' => 'success',
            'message' => 'Booking status updated successfully',
            'data' => $venueBooking,
        ]);
    }
}
