<?php

namespace App\Http\Controllers;

use App\Events\NotificationCreated;
use App\Models\Notification;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class NotificationController extends Controller
{
    public function index(Request $request) : JsonResponse
    {
        $user = $request->user();
        $query = $user->notifications();

        // Check if only unread notifications are requested
        if ($request->has('unread') && $request->unread == '1') {
            $query = $user->unreadNotifications();
        }

        $notifications = $query->paginate($request->per_page ?? 10);
        return response()->json($notifications);
    }

    public function create(Request $request) : JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'type' =>'required|string',
            'title' =>'required|string',
            'data' =>'required|string',
        ]);

        if ($validator->fails()){
            return response()->json(['error' => $validator->errors()], 400);
        }

        Notification::create([
            'type' => $request->type,
            'notifiable_type' => $request->notifiable_type,
            'notifiable_id' => $request->notifiable_id,
            'title' => $request->title,
            'data' => $request->data,
        ]);

        return response()->json(['success'=> 'Notification created successfully'],201);
    }
    public function markAsRead($id) : JsonResponse
    {
        $notification = Notification::findOrFail($id);
        $notification->read_at = \Carbon\Carbon::now();
        $notification->save();
        return response()->json(['success'=> 'Notification marked as read'],201);
    }

    public function markAllAsRead(Request $request) : JsonResponse
    {
        $user = $request->user();
        $user->unreadNotifications->markAsRead();
        return response()->json(['message'=> 'All notifications marked as read'],201);
    }

    public function deleteNotification($id) : JsonResponse
    {
        $notification = Notification::findOrFail($id);
        $notification->delete();
        return response()->json(['success'=> 'Notification deleted successfully!'],201);
    }

    public function getUnreadCount(Request $request) : JsonResponse
    {
        $user = $request->user();
        $unreadCount = $user->unreadNotifications()->count();
        return response()->json(['unread_count' => $unreadCount]);
    }
}
