<?php

namespace App\Http\Controllers;

use App\Mail\ContactFormMail;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class ContactController extends Controller
{
    /**
     * Validate if email domain exists (MX record check)
     */
    private function isValidEmailDomain(string $email): bool
    {
        $domain = substr(strrchr($email, "@"), 1);

        if (empty($domain)) {
            return false;
        }

        return checkdnsrr($domain, 'MX') || checkdnsrr($domain, 'A');
    }

    /**
     * Check if email domain is from a known disposable email provider
     */
    private function isDisposableEmail(string $email): bool
    {
        $domain = strtolower(substr(strrchr($email, "@"), 1));

        $disposableDomains = [
            '10minutemail.com',
            'tempmail.org',
            'guerrillamail.com',
            'mailinator.com',
            'yopmail.com',
            'temp-mail.org',
            'throwaway.email',
            'getnada.com',
            'maildrop.cc',
            'sharklasers.com',
            'guerrillamailblock.com',
            'pokemail.net',
            'spam4.me',
            'bccto.me',
            'chacuo.net',
            'dispostable.com',
            'fakeinbox.com',
            'hide.biz.st',
            'mytrashmail.com',
            'nobulk.com',
            'sogetthis.com',
            'spamherelots.com',
            'spamhereplease.com',
            'spamthisplease.com',
            'takethemail.com',
            'trashmail.net',
            'wegwerfmail.de',
            'wegwerfmail.net',
            'wegwerfmail.org',
            'wh4f.org',
            'whatpaas.com',
            'zoemail.org'
        ];

        return in_array($domain, $disposableDomains);
    }

    /**
     * Check for spam patterns in the message content
     */
    private function containsSpamPatterns(string $message): bool
    {
        $spamPatterns = [
            '/\b(viagra|cialis|pharmacy|casino|lottery|winner|congratulations)\b/i',
            '/\b(click here|act now|limited time|urgent|free money)\b/i',
            '/\b(make money|work from home|earn \$|guaranteed income)\b/i',
            '/\b(bitcoin|cryptocurrency|investment opportunity)\b/i',
            '/http[s]?:\/\/[^\s]+/i',
            '/\b[A-Z]{5,}\b/',
        ];

        foreach ($spamPatterns as $pattern) {
            if (preg_match($pattern, $message)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Handle contact form submission
     */
    public function submit(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'firstName' => 'required|string|max:255',
                'lastName' => 'required|string|max:255',
                'email' => 'required|email|max:255',
                'message' => 'required|string|max:2000',
                'terms_agreement' => 'required|accepted'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $email = $request->input('email');
            $message = $request->input('message');

            if ($this->isDisposableEmail($email)) {
                Log::warning('Contact form submission blocked - disposable email', [
                    'email' => $email,
                    'ip' => $request->ip()
                ]);

                return response()->json([
                    'message' => 'Please use a valid business or personal email address.',
                    'success' => false
                ], 422);
            }

            if (!$this->isValidEmailDomain($email)) {
                Log::warning('Contact form submission blocked - invalid domain', [
                    'email' => $email,
                    'domain' => substr(strrchr($email, "@"), 1),
                    'ip' => $request->ip()
                ]);

                return response()->json([
                    'message' => 'Please provide a valid email address with an existing domain.',
                    'success' => false
                ], 422);
            }


            if ($this->containsSpamPatterns($message)) {
                Log::warning('Contact form submission blocked - spam patterns detected', [
                    'email' => $email,
                    'ip' => $request->ip(),
                    'message_length' => strlen($message)
                ]);

                return response()->json([
                    'message' => 'Your message contains content that cannot be processed. Please revise and try again.',
                    'success' => false
                ], 422);
            }

            $contactData = [
                'firstName' => $request->input('firstName'),
                'lastName' => $request->input('lastName'),
                'email' => $request->input('email'),
                'message' => $request->input('message'),
                'submitted_at' => now(),
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent()
            ];

            $adminEmails = config('mail.admin_emails', ['<EMAIL>']);

            foreach ($adminEmails as $adminEmail) {
                Mail::to($adminEmail)->send(new ContactFormMail($contactData));
            }

            Log::info('Contact form submitted successfully', [
                'email' => $request->input('email'),
                'name' => $request->input('firstName') . ' ' . $request->input('lastName'),
                'ip' => $request->ip()
            ]);

            return response()->json([
                'message' => 'Thank you for your message! We will get back to you soon.',
                'success' => true
            ], 200);

        } catch (\Exception $e) {
            Log::error('Contact form submission failed', [
                'error' => $e->getMessage(),
                'email' => $request->input('email', 'unknown'),
                'ip' => $request->ip()
            ]);

            return response()->json([
                'message' => 'Failed to send your message. Please try again later.',
                'success' => false
            ], 500);
        }
    }
}
