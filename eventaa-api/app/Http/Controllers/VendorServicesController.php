<?php

namespace App\Http\Controllers;

use App\Models\Vendor;
use App\Models\VendorService;
use App\Models\Service;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class VendorServicesController extends Controller
{
    /**
     * Display a listing of vendor services.
     *
     * @param int $vendor_id
     * @param Request $request
     * @return JsonResponse
     */
    public function index($vendor_id, Request $request): JsonResponse
    {
        $vendor = Vendor::find($vendor_id);
        if (!$vendor) {
            return response()->json(['message' => 'Vendor not found'], 404);
        }

        $query = VendorService::with('service')
            ->where('vendor_id', $vendor_id);

        if ($request->has('search')) {
            $searchTerm = $request->search;
            $query->whereHas('service', function ($q) use ($searchTerm) {
                $q->where('name', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('description', 'LIKE', "%{$searchTerm}%");
            });
        }

        $sortField = $request->sort_by ?? 'created_at';
        $sortDirection = $request->sort_direction ?? 'desc';
        $query->orderBy($sortField, $sortDirection);

        $perPage = $request->per_page ?? 15;
        $vendorServices = $query->paginate($perPage);

        return response()->json($vendorServices);
    }

    /**
     * Store a newly created vendor service.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'vendor_id' => 'required|exists:vendors,id',
            'service_id' => 'required|exists:services,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $vendor = Vendor::find($request->vendor_id);
        if (!$vendor) {
            return response()->json(['message' => 'Vendor not found'], 404);
        }

        $service = Service::find($request->service_id);
        if (!$service) {
            return response()->json(['message' => 'Service not found'], 404);
        }

        $existingService = VendorService::where('vendor_id', $request->vendor_id)
            ->where('service_id', $request->service_id)
            ->first();

        if ($existingService) {
            return response()->json([
                'message' => 'This service is already associated with this vendor'
            ], 422);
        }

        $vendorService = VendorService::create([
            'vendor_id' => $request->vendor_id,
            'service_id' => $request->service_id,
        ]);

        return response()->json([
            'message' => 'Vendor service created successfully',
            'data' => $vendorService->load('service')
        ], 201);
    }

    /**
     * Display the specified vendor service.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show($id): JsonResponse
    {
        $vendorService = VendorService::with(['service', 'vendor'])->find($id);

        if (!$vendorService) {
            return response()->json(['message' => 'Vendor service not found'], 404);
        }

        return response()->json($vendorService);
    }

    /**
     * Update the specified vendor service.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(Request $request, $id): JsonResponse
    {
        $vendorService = VendorService::find($id);

        if (!$vendorService) {
            return response()->json(['message' => 'Vendor service not found'], 404);
        }

        $validator = Validator::make($request->all(), [
            'service_id' => 'required|exists:services,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $service = Service::find($request->service_id);
        if (!$service) {
            return response()->json(['message' => 'Service not found'], 404);
        }

        if ($request->service_id != $vendorService->service_id) {
            $existingService = VendorService::where('vendor_id', $vendorService->vendor_id)
                ->where('service_id', $request->service_id)
                ->first();

            if ($existingService) {
                return response()->json([
                    'message' => 'This service is already associated with this vendor'
                ], 422);
            }
        }

        $vendorService->update([
            'service_id' => $request->service_id,
        ]);

        return response()->json([
            'message' => 'Vendor service updated successfully',
            'data' => $vendorService->load('service')
        ]);
    }

    /**
     * Remove the specified vendor service.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy($id): JsonResponse
    {
        $vendorService = VendorService::find($id);

        if (!$vendorService) {
            return response()->json(['message' => 'Vendor service not found'], 404);
        }

        $vendorService->delete();

        return response()->json([
            'message' => 'Vendor service deleted successfully'
        ]);
    }
}
