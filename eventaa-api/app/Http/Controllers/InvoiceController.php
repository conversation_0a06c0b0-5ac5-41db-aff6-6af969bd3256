<?php

namespace App\Http\Controllers;

use App\Models\PaymentTransaction;
use App\Models\Subscription;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class InvoiceController extends Controller
{
    /**
     * Get user's invoices
     */
    public function index(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'per_page' => 'nullable|integer|min:1|max:100',
            'page' => 'nullable|integer|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $perPage = $request->per_page ?? 10;

        // Get all payment transactions for the user
        $invoices = PaymentTransaction::where('user_id', $request->user()->id)
            ->with(['paymentMethod.paymentGateway'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);

        // Transform the data to match the expected format
        $transformedInvoices = $invoices->map(function ($transaction) {
            $description = 'Payment';

            // Determine description based on bookable type
            if ($transaction->bookable_type === Subscription::class) {
                $planName = $transaction->metadata['plan_name'] ?? 'Subscription';
                $description = "{$planName} Plan";
            } elseif ($transaction->bookable_type) {
                $bookableType = class_basename($transaction->bookable_type);
                $description = "{$bookableType} Payment";
            }

            return [
                'id' => $transaction->id,
                'transaction_id' => $transaction->transaction_id,
                'user_id' => $transaction->user_id,
                'amount' => $transaction->amount,
                'currency' => $transaction->currency,
                'description' => $description,
                'status' => ucfirst($transaction->status),
                'payment_date' => $transaction->payment_date,
                'created_at' => $transaction->created_at,
                'updated_at' => $transaction->updated_at,
                'payment_method' => $transaction->paymentMethod && $transaction->paymentMethod->paymentGateway ? [
                    'id' => $transaction->paymentMethod->id,
                    'name' => $transaction->paymentMethod->paymentGateway->name ?? 'Unknown',
                    'type' => $transaction->paymentMethod->paymentGateway->slug ?? 'unknown'
                ] : null
            ];
        });

        return response()->json([
            'message' => 'Invoices retrieved successfully',
            'data' => $transformedInvoices,
            'meta' => [
                'current_page' => $invoices->currentPage(),
                'from' => $invoices->firstItem(),
                'last_page' => $invoices->lastPage(),
                'per_page' => $invoices->perPage(),
                'to' => $invoices->lastItem(),
                'total' => $invoices->total(),
            ]
        ]);
    }

    /**
     * Get a specific invoice
     */
    public function show(Request $request, $id): JsonResponse
    {
        $transaction = PaymentTransaction::where('id', $id)
            ->where('user_id', $request->user()->id)
            ->with(['paymentMethod.paymentGateway', 'bookable'])
            ->first();

        if (!$transaction) {
            return response()->json([
                'message' => 'Invoice not found',
                'data' => null
            ], 404);
        }

        // Determine description based on bookable type
        $description = 'Payment';
        if ($transaction->bookable_type === Subscription::class) {
            $planName = $transaction->metadata['plan_name'] ?? 'Subscription';
            $description = "{$planName} Plan";
        } elseif ($transaction->bookable_type) {
            $bookableType = class_basename($transaction->bookable_type);
            $description = "{$bookableType} Payment";
        }

        $invoice = [
            'id' => $transaction->id,
            'transaction_id' => $transaction->transaction_id,
            'user_id' => $transaction->user_id,
            'amount' => $transaction->amount,
            'currency' => $transaction->currency,
            'description' => $description,
            'status' => ucfirst($transaction->status),
            'payment_date' => $transaction->payment_date,
            'created_at' => $transaction->created_at,
            'updated_at' => $transaction->updated_at,
            'payment_method' => $transaction->paymentMethod && $transaction->paymentMethod->paymentGateway ? [
                'id' => $transaction->paymentMethod->id,
                'name' => $transaction->paymentMethod->paymentGateway->name ?? 'Unknown',
                'type' => $transaction->paymentMethod->paymentGateway->slug ?? 'unknown'
            ] : null,
            'bookable' => $transaction->bookable
        ];

        return response()->json([
            'message' => 'Invoice retrieved successfully',
            'data' => $invoice
        ]);
    }
}
