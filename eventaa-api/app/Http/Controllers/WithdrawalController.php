<?php

namespace App\Http\Controllers;

use App\Models\Withdrawal;
use App\Models\HostBalance;
use App\Services\PayChanguService;
use App\Services\RecaptchaService;
use App\Exports\WithdrawalsExport;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Http\Response;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class WithdrawalController extends Controller
{
    /**
     * Get host balance and withdrawal history
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();

        // Get or create host balance
        $balance = HostBalance::getOrCreateForUser($user->id);

        // Get withdrawal history
        $withdrawals = Withdrawal::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return response()->json([
            'message' => 'Host balance and withdrawals retrieved successfully',
            'data' => [
                'balance' => $balance,
                'withdrawals' => $withdrawals,
                'minimum_withdrawal' => 1000, // MWK 1,000 minimum
                'withdrawal_fee_percentage' => 1.5, // 1.5% withdrawal fee
            ]
        ]);
    }

    /**
     * Get host balance
     */
    public function getBalance(Request $request): JsonResponse
    {
        $user = $request->user();

        // Get or create host balance
        $balance = HostBalance::getOrCreateForUser($user->id);

        return response()->json([
            'message' => 'Host balance retrieved successfully',
            'data' => [
                'balance' => $balance,
                'minimum_withdrawal' => 1000, // MWK 1,000 minimum
                'withdrawal_fee_percentage' => 1.5, // 1.5% withdrawal fee
            ]
        ]);
    }

    /**
     * Request a withdrawal
     */
    public function requestWithdrawal(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:1000',
            'payment_method' => 'required|in:mobile_money,bank_transfer',
            'payment_details' => 'required|array',
            'recaptcha_token' => 'required|string',
            'payment_details.phone_number' => 'required_if:payment_method,mobile_money|string',
            'payment_details.operator' => 'required_if:payment_method,mobile_money|in:tnm,airtel',
            'payment_details.account_name' => 'required_if:payment_method,bank_transfer|string',
            'payment_details.account_number' => 'required_if:payment_method,bank_transfer|string',
            'payment_details.bank_name' => 'required_if:payment_method,bank_transfer|string',
            'payment_details.branch' => 'required_if:payment_method,bank_transfer|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();
        $amount = $request->amount;

        $recaptchaService = new RecaptchaService();
        $isRecaptchaValid = $recaptchaService->verify(
            $request->recaptcha_token,
            $request->ip()
        );

        if (!$isRecaptchaValid) {
            return response()->json([
                'message' => 'reCAPTCHA verification failed. Please try again.',
                'errors' => ['recaptcha_token' => ['Invalid reCAPTCHA token']]
            ], 422);
        }

        $hostBalance = HostBalance::getOrCreateForUser($user->id);

        if ($hostBalance->available_balance < $amount) {
            return response()->json([
                'message' => 'Insufficient balance',
                'data' => [
                    'available_balance' => $hostBalance->available_balance,
                    'requested_amount' => $amount
                ]
            ], 400);
        }

        $feePercentage = 1.5;
        $fee = ($amount * $feePercentage) / 100;
        $netAmount = $amount - $fee;

        return DB::transaction(function () use ($request, $user, $amount, $fee, $netAmount, $hostBalance) {
            try {
                $withdrawal = Withdrawal::create([
                    'user_id' => $user->id,
                    'reference' => Withdrawal::generateReference(),
                    'amount' => $amount,
                    'fee' => $fee,
                    'net_amount' => $netAmount,
                    'payment_method' => $request->payment_method,
                    'payment_details' => $request->payment_details,
                    'status' => 'pending',
                    'gateway' => 'paychangu',
                ]);

                $hostBalance->decrement('available_balance', $amount);
                $hostBalance->increment('pending_balance', $amount);

                try {
                    $this->processWithdrawalWithPayChangu($withdrawal);
                } catch (\Exception $e) {
                    // PayChangu processing failed, revert balance changes but keep withdrawal record
                    $hostBalance->increment('available_balance', $amount);
                    $hostBalance->decrement('pending_balance', $amount);

                    // The withdrawal status should already be set to 'failed' by processWithdrawalWithPayChangu
                    Log::error('Withdrawal processing failed, balance reverted', [
                        'withdrawal_id' => $withdrawal->id,
                        'user_id' => $user->id,
                        'error' => $e->getMessage()
                    ]);

                    // Return error response but don't throw to avoid transaction rollback
                    return response()->json([
                        'message' => 'Withdrawal request failed to process',
                        'error' => $e->getMessage()
                    ], 422);
                }

                Log::info('Withdrawal request created', [
                    'withdrawal_id' => $withdrawal->id,
                    'user_id' => $user->id,
                    'amount' => $amount,
                    'net_amount' => $netAmount
                ]);

                return response()->json([
                    'message' => 'Withdrawal request submitted successfully',
                    'data' => [
                        'withdrawal' => $withdrawal,
                        'estimated_processing_time' => '1-3 business days'
                    ]
                ], 201);

            } catch (\Exception $e) {
                Log::error('Withdrawal request failed', [
                    'user_id' => $user->id,
                    'amount' => $amount,
                    'error' => $e->getMessage()
                ]);

                return response()->json([
                    'message' => 'Failed to process withdrawal request',
                    'error' => $e->getMessage()
                ], 500);
            }
        });
    }

    /**
     * Cancel a pending withdrawal
     */
    public function cancelWithdrawal(Request $request, $id): JsonResponse
    {
        $user = $request->user();

        $withdrawal = Withdrawal::where('id', $id)
            ->where('user_id', $user->id)
            ->firstOrFail();

        if (!$withdrawal->canBeCancelled()) {
            return response()->json([
                'message' => 'This withdrawal cannot be cancelled',
                'status' => $withdrawal->status
            ], 400);
        }

        return DB::transaction(function () use ($withdrawal, $user) {
            $withdrawal->update([
                'status' => 'cancelled',
                'notes' => 'Cancelled by user'
            ]);

            $hostBalance = HostBalance::getOrCreateForUser($user->id);
            $hostBalance->increment('available_balance', (float) $withdrawal->amount);
            $hostBalance->decrement('pending_balance', (float) $withdrawal->amount);

            return response()->json([
                'message' => 'Withdrawal cancelled successfully',
                'data' => $withdrawal
            ]);
        });
    }

    /**
     * Get withdrawal details
     */
    public function show(Request $request, $id): JsonResponse
    {
        $user = $request->user();

        $withdrawal = Withdrawal::where('id', $id)
            ->where('user_id', $user->id)
            ->firstOrFail();

        return response()->json([
            'message' => 'Withdrawal details retrieved successfully',
            'data' => $withdrawal
        ]);
    }

    /**
     * Process withdrawal with PayChangu
     */
    private function processWithdrawalWithPayChangu(Withdrawal $withdrawal): void
    {
        try {
            $payChanguService = new PayChanguService();
            $user = $withdrawal->user()->first();

            if ($withdrawal->payment_method === 'bank_transfer') {
                $bankDetails = [
                    'account_name' => $withdrawal->payment_details['account_name'],
                    'account_number' => $withdrawal->payment_details['account_number'],
                    'bank_name' => $withdrawal->payment_details['bank_name'],
                    'branch' => $withdrawal->payment_details['branch'],
                    'bank_uuid' => $this->getBankUuidFromName($withdrawal->payment_details['bank_name']),
                ];

                $response = $payChanguService->initializeBankTransferWithdrawal(
                    (float) $withdrawal->net_amount,
                    'MWK',
                    $withdrawal->reference,
                    $bankDetails,
                    [
                        'withdrawal_id' => $withdrawal->id,
                        'user_id' => $withdrawal->user_id,
                    ],
                    $user
                );
            } else {
                $operatorMap = [
                    'tnm' => 'tnm_mpamba',
                    'airtel' => 'airtel_money',
                ];

                $response = $payChanguService->initializeMobileMoneyWithdrawal(
                    (float) $withdrawal->net_amount,
                    'MWK',
                    $withdrawal->reference,
                    $withdrawal->payment_details['phone_number'],
                    $operatorMap[$withdrawal->payment_details['operator']],
                    [
                        'withdrawal_id' => $withdrawal->id,
                        'user_id' => $withdrawal->user_id,
                    ],
                    $user
                );
            }

            // Check if PayChangu API call was successful
            if (!$response || !isset($response['status']) || $response['status'] !== true) {
                // API call failed, mark withdrawal as failed
                $errorMessage = $response['message'] ?? 'Unknown PayChangu API error';

                $withdrawal->update([
                    'status' => 'failed',
                    'gateway_response' => $response,
                    'notes' => 'PayChangu API error: ' . $errorMessage
                ]);

                Log::error('PayChangu withdrawal API call failed', [
                    'withdrawal_id' => $withdrawal->id,
                    'error_message' => $errorMessage,
                    'response' => $response
                ]);

                throw new \Exception('PayChangu API call failed: ' . $errorMessage);
            }

            $withdrawal->update([
                'status' => 'processing',
                'gateway_reference' => $response['reference'] ?? $withdrawal->reference,
                'gateway_response' => $response,
                'notes' => 'Processing with PayChangu'
            ]);

            Log::info('Withdrawal processing started', [
                'withdrawal_id' => $withdrawal->id,
                'gateway_reference' => $withdrawal->gateway_reference,
                'response' => $response
            ]);

        } catch (\Exception $e) {
            Log::error('PayChangu withdrawal processing failed', [
                'withdrawal_id' => $withdrawal->id,
                'error' => $e->getMessage()
            ]);

            $withdrawal->update([
                'status' => 'failed',
                'notes' => 'Failed to process with PayChangu: ' . $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Handle PayChangu webhook for withdrawal status updates
     */
    public function handlePayChanguWebhook(Request $request): JsonResponse
    {
        try {
            $payChanguService = new PayChanguService();
            $result = $payChanguService->handleWithdrawalWebhook($request->all());

            return response()->json([
                'message' => 'Webhook processed successfully',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            Log::error('PayChangu withdrawal webhook failed', [
                'payload' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'message' => 'Webhook processing failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export withdrawals to Excel or CSV
     */
    public function export(Request $request): BinaryFileResponse|JsonResponse|Response
    {
        try {
            $format = $request->get('format', 'excel');
            $filters = $request->only([
                'status',
                'payment_method',
                'gateway',
                'user_id',
                'date_from',
                'date_to',
                'search'
            ]);

            // For admin users, allow all data. For regular users, filter by their user_id
            $user = $request->user();
            if (!$user->hasRole('admin') && !$user->hasRole('super_admin')) {
                $filters['user_id'] = $user->id;
            }

            if ($format === 'excel') {
                $filename = 'withdrawals_export_' . now()->format('Y-m-d_H-i-s') . '.xlsx';
                return Excel::download(new WithdrawalsExport($filters), $filename);
            } else {
                // Simple CSV export without Excel package for better compatibility
                $withdrawals = collect();
                $query = Withdrawal::with(['user']);

                // Apply the same filters as in WithdrawalsExport
                if (!empty($filters['status'])) {
                    $query->where('status', $filters['status']);
                }

                if (!empty($filters['payment_method'])) {
                    $query->where('payment_method', $filters['payment_method']);
                }

                if (!empty($filters['gateway'])) {
                    $query->where('gateway', $filters['gateway']);
                }

                if (!empty($filters['user_id'])) {
                    $query->where('user_id', $filters['user_id']);
                }

                if (!empty($filters['date_from'])) {
                    $query->whereDate('created_at', '>=', $filters['date_from']);
                }

                if (!empty($filters['date_to'])) {
                    $query->whereDate('created_at', '<=', $filters['date_to']);
                }

                if (!empty($filters['search'])) {
                    $search = $filters['search'];
                    $query->where(function ($q) use ($search) {
                        $q->where('reference', 'like', "%{$search}%")
                          ->orWhere('gateway_reference', 'like', "%{$search}%")
                          ->orWhere('notes', 'like', "%{$search}%")
                          ->orWhereHas('user', function ($userQuery) use ($search) {
                              $userQuery->where('name', 'like', "%{$search}%")
                                       ->orWhere('email', 'like', "%{$search}%");
                          });
                    });
                }

                $withdrawals = $query->orderBy('created_at', 'desc')->get();

                // Build CSV content as string
                $csvContent = '';

                // Add headers
                $headers = [
                    'ID', 'Reference', 'User Name', 'User Email', 'Amount (MWK)',
                    'Fee (MWK)', 'Net Amount (MWK)', 'Payment Method', 'Payment Details',
                    'Status', 'Gateway', 'Gateway Reference', 'Notes', 'Created At', 'Processed At'
                ];
                $csvContent .= '"' . implode('","', $headers) . '"' . "\n";

                foreach ($withdrawals as $withdrawal) {
                    $paymentDetails = '';
                    if (is_array($withdrawal->payment_details)) {
                        if ($withdrawal->payment_method === 'mobile_money') {
                            $paymentDetails = ($withdrawal->payment_details['phone_number'] ?? '') .
                                            ' (' . ($withdrawal->payment_details['network'] ?? '') . ')';
                        } elseif ($withdrawal->payment_method === 'bank_transfer') {
                            $paymentDetails = ($withdrawal->payment_details['account_name'] ?? '') . ' - ' .
                                            ($withdrawal->payment_details['account_number'] ?? '') . ' (' .
                                            ($withdrawal->payment_details['bank_name'] ?? '') . ')';
                        }
                    }

                    $row = [
                        $withdrawal->id,
                        $withdrawal->reference,
                        $withdrawal->user->name ?? 'N/A',
                        $withdrawal->user->email ?? 'N/A',
                        number_format((float) $withdrawal->amount, 2),
                        number_format((float) $withdrawal->fee, 2),
                        number_format((float) $withdrawal->net_amount, 2),
                        ucfirst(str_replace('_', ' ', $withdrawal->payment_method)),
                        $paymentDetails,
                        ucfirst($withdrawal->status),
                        $withdrawal->gateway ?? 'N/A',
                        $withdrawal->gateway_reference ?? 'N/A',
                        $withdrawal->notes ?? 'N/A',
                        $withdrawal->created_at->format('Y-m-d H:i:s'),
                        $withdrawal->processed_at ? $withdrawal->processed_at->format('Y-m-d H:i:s') : 'N/A',
                    ];

                    // Escape quotes and wrap in quotes
                    $escapedRow = array_map(function($field) {
                        return '"' . str_replace('"', '""', $field) . '"';
                    }, $row);

                    $csvContent .= implode(',', $escapedRow) . "\n";
                }

                $filename = 'withdrawals_export_' . now()->format('Y-m-d_H-i-s') . '.csv';

                return response($csvContent, 200, [
                    'Content-Type' => 'text/csv; charset=UTF-8',
                    'Content-Disposition' => 'attachment; filename="' . $filename . '"',
                    'Pragma' => 'no-cache',
                    'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
                    'Expires' => '0'
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Error exporting withdrawals: ' . $e->getMessage());
            return response()->json([
                'error' => 'Failed to export withdrawals: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get bank UUID from bank name for PayChangu API
     */
    private function getBankUuidFromName(string $bankName): string
    {
        // Map common Malawian bank names to their PayChangu UUIDs
        // These should ideally be fetched from PayChangu's get-banks endpoint and cached
        $bankUuids = [
            // Standard Bank variants
            'standard bank' => '82310dd1-ec9b-4fe7-a32c-2f262ef08681',
            'standard bank malawi' => '82310dd1-ec9b-4fe7-a32c-2f262ef08681',
            'standard' => '82310dd1-ec9b-4fe7-a32c-2f262ef08681',

            // National Bank variants
            'national bank' => 'f4d4d4d4-ec9b-4fe7-a32c-2f262ef08681',
            'national bank of malawi' => 'f4d4d4d4-ec9b-4fe7-a32c-2f262ef08681',
            'nbm' => 'f4d4d4d4-ec9b-4fe7-a32c-2f262ef08681',
            'nbs bank' => 'f4d4d4d4-ec9b-4fe7-a32c-2f262ef08681',

            // First Capital Bank variants
            'first capital bank' => 'a1b2c3d4-ec9b-4fe7-a32c-2f262ef08681',
            'fcb' => 'a1b2c3d4-ec9b-4fe7-a32c-2f262ef08681',
            'first capital' => 'a1b2c3d4-ec9b-4fe7-a32c-2f262ef08681',

            // MyBucks Bank variants
            'mybucks' => 'b2c3d4e5-ec9b-4fe7-a32c-2f262ef08681',
            'mybucks bank' => 'b2c3d4e5-ec9b-4fe7-a32c-2f262ef08681',

            // FDH Bank variants
            'fdh' => 'c3d4e5f6-ec9b-4fe7-a32c-2f262ef08681',
            'fdh bank' => 'c3d4e5f6-ec9b-4fe7-a32c-2f262ef08681',
            'finance development holdings' => 'c3d4e5f6-ec9b-4fe7-a32c-2f262ef08681',

            // Nedbank variants
            'nedbank' => 'd4e5f6g7-ec9b-4fe7-a32c-2f262ef08681',
            'nedbank malawi' => 'd4e5f6g7-ec9b-4fe7-a32c-2f262ef08681',

            // Opportunity Bank variants
            'opportunity bank' => 'e5f6g7h8-ec9b-4fe7-a32c-2f262ef08681',
            'opportunity' => 'e5f6g7h8-ec9b-4fe7-a32c-2f262ef08681',
        ];

        $normalizedBankName = strtolower(trim($bankName));

        // Try exact match first
        if (isset($bankUuids[$normalizedBankName])) {
            return $bankUuids[$normalizedBankName];
        }

        // Try partial match
        foreach ($bankUuids as $name => $uuid) {
            if (str_contains($normalizedBankName, $name) || str_contains($name, $normalizedBankName)) {
                return $uuid;
            }
        }

        // Default to Standard Bank if no match found
        Log::warning('Bank UUID not found for bank name, defaulting to Standard Bank', [
            'bank_name' => $bankName,
            'normalized_name' => $normalizedBankName
        ]);

        return '82310dd1-ec9b-4fe7-a32c-2f262ef08681'; // Standard Bank UUID
    }
}
