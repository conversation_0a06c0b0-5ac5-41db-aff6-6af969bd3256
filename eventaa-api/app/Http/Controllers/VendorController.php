<?php

namespace App\Http\Controllers;

use App\Models\Plan;
use App\Models\Subscription;
use App\Models\User;
use App\Models\Vendor;
use App\Models\VendorLike;
use App\Models\VendorMedia;
use App\Models\VendorPrice;
use App\Models\VendorService;
use App\Notifications\NewVendorRequestNotification;
use App\Notifications\VendorRequestApprovedNotification;
use App\Notifications\VendorRequestRejectedNotification;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class VendorController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        $query = Vendor::withAvg('ratings', 'rating')
            ->withCount('ratings')
            ->with('user', 'services.service', 'media', 'prices.currency');

        // For admin dashboard, show all vendors. For public, show only approved
        if (!$request->has('admin') || $request->admin !== 'true') {
            $query->where('status', 'approved');
        }

        // Admin can filter by status
        if ($request->has('status') && !empty($request->status)) {
            $query->where('status', $request->status);
        }

        // Admin can filter by category
        if ($request->has('category') && !empty($request->category)) {
            $query->whereHas('services', function ($q) use ($request) {
                $q->where('service_id', $request->category);
            });
        }

        if ($request->has('services') && !empty($request->services)) {
            $serviceIds = explode(',', $request->services);
            $query->whereHas('services', function ($q) use ($serviceIds) {
                $q->whereIn('service_id', $serviceIds);
            });
        }

        if ($request->has('min_price') && is_numeric($request->min_price)) {
            $query->whereHas('prices', function ($q) use ($request) {
                $q->where('price', '>=', $request->min_price);
            });
        }

        if ($request->has('max_price') && is_numeric($request->max_price)) {
            $query->whereHas('prices', function ($q) use ($request) {
                $q->where('price', '<=', $request->max_price);
            });
        }

        if ($request->has('location') && !empty($request->location)) {
            $query->where('location', 'LIKE', '%' . $request->location . '%');
        }

        if ($request->has('min_rating') && is_numeric($request->min_rating)) {
            $query->having('ratings_avg_rating', '>=', $request->min_rating);
        }

        if ($request->has('is_available')) {
            $isAvailable = $request->is_available === 'true' || $request->is_available === '1';
            $query->where('is_available', $isAvailable);
        }

        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm, $request) {
                $q->where('name', 'LIKE', '%' . $searchTerm . '%')
                  ->orWhere('bio', 'LIKE', '%' . $searchTerm . '%');

                // For admin searches, also search business_email and phone
                if ($request->has('admin') && $request->admin === 'true') {
                    $q->orWhere('business_email', 'LIKE', '%' . $searchTerm . '%')
                      ->orWhere('phone', 'LIKE', '%' . $searchTerm . '%');
                }
            });
        }

        $sortField = 'created_at';
        $sortDirection = 'desc';

        if ($request->has('sort')) {
            switch ($request->sort) {
                case 'rating':
                    $sortField = 'ratings_avg_rating';
                    $sortDirection = 'desc';
                    break;
                case 'price-low':
                    $query->orderByRaw('(SELECT MIN(price) FROM vendor_prices WHERE vendor_prices.vendor_id = vendors.id) ASC');
                    break;
                case 'price-high':
                    $query->orderByRaw('(SELECT MAX(price) FROM vendor_prices WHERE vendor_prices.vendor_id = vendors.id) DESC');
                    break;
                case 'reviews':
                    $sortField = 'ratings_count';
                    $sortDirection = 'desc';
                    break;
                case 'newest':
                    $sortField = 'created_at';
                    $sortDirection = 'desc';
                    break;
                case 'oldest':
                    $sortField = 'created_at';
                    $sortDirection = 'asc';
                    break;
            }
        }

        if (!in_array($request->sort, ['price-low', 'price-high'])) {
            $query->orderBy($sortField, $sortDirection);
        }

        $perPage = $request->input('per_page', 10);
        $vendors = $query->paginate($perPage);

        return response()->json($vendors);
    }

    public function profile(Request $request): JsonResponse
    {
        $vendor = Vendor::withAvg('ratings', 'rating')
            ->withCount('ratings')
            ->with('user', 'services.service', 'media', 'prices.currency')
            ->where('user_id', $request->user()->id)
            ->first();

        if (!$vendor) {
            return response()->json(['message' => 'Vendor not found'], 404);
        }
        return response()->json($vendor);
    }

    public function read($slug, Request $request): JsonResponse
    {
        $vendor = Vendor::withAvg('ratings', 'rating')
            ->withCount('ratings')
            ->with('user', 'services.service', 'media', 'prices.currency', 'ratings')
            ->where('slug', $slug)
            ->first();

        if (!$vendor) {
            return response()->json(['message' => 'Vendor not found'], 404);
        }

        // Check if the current user has liked this vendor (if authenticated)
        $userHasLiked = false;
        if ($request->user()) {
            $userHasLiked = VendorLike::where('vendor_id', $vendor->id)
                ->where('user_id', $request->user()->id)
                ->exists();
        }

        // Add user_has_liked to the vendor data
        $vendorData = $vendor->toArray();
        $vendorData['user_has_liked'] = $userHasLiked;

        $vendor->incrementViews($request);

        return response()->json($vendorData);
    }

    public function create(Request $request): JsonResponse
    {
        $validator = Validator::make(
            $request->all(),
            [
                'name' => 'required|string|max:255',
                'logo' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
                'location' => 'required|string|max:255',
                'bio' => 'required|string|max:1000',
                'languages' => 'required|string|max:255',
                'phone' => 'required|string|max:20',
                'business_email' => 'required|email|max:255',
                'is_available' => 'required',
                'services' => 'required',
                'prices' => 'required',
                'mediaMetadata' => 'required',
            ]
        );

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $logoPath = "";
        if ($request->hasFile('logo')) {
            $logo = $request->file('logo');
            $logoPath = $logo->store('vendors', 'public');
        }

        $vendor = Vendor::create([
            'user_id' => $request->user()->id,
            'name' => $request->input('name'),
            'slug' => Str::slug($request->input('name'), '-'),
            'logo' => $logoPath,
            'location' => $request->input('location'),
            'bio' => $request->input('bio'),
            'languages' => $request->input('languages'),
            'phone' => $request->input('phone'),
            'business_email' => $request->input('business_email'),
            'website' => $request->input('website'),
            'facebook' => $request->input('facebook'),
            'instagram' => $request->input('instagram'),
            'twitter' => $request->input('twitter'),
            'is_available' => $request->input('is_available') == 'true' ? true : false,
            'status' => 'pending'
        ]);

        if ($request->has('services')) {
            foreach (json_decode($request->input('services'), true) as $service) {
                VendorService::create([
                    'vendor_id' => $vendor->id,
                    'service_id' => $service,
                ]);
            }
        }

        if ($request->has('prices')) {
            foreach (json_decode($request->input('prices'), true) as $price) {
                VendorPrice::create([
                    'vendor_id' => $vendor->id,
                    'currency_id' => $price['currency_id']['id'],
                    'price' => $price['price'],
                    'description' => $price['description'],
                ]);
            }
        }

        if ($request->has('mediaMetadata')) {
            $this->processMediaUpload($request, $vendor);
        }

        $admins = User::role('admin')->get();
        Notification::send($admins, new NewVendorRequestNotification($vendor));

        return response()->json([
            'message' => 'Vendor request submitted successfully. Please wait for approval.',
            'vendor' => $vendor
        ], 201);
    }

    protected function processMediaUpload($request, $vendor)
    {
        $mediaItems = json_decode($request->input('mediaMetadata'), true) ?? [];
        foreach ($mediaItems as $media) {
            $fileKey = 'mediaFile_' . $media['index'];

            if ($request->hasFile($fileKey)) {
                $file = $request->file($fileKey);
                $filePath = $file->store('vendors/media', 'public');

                VendorMedia::create([
                    'vendor_id' => $vendor->id,
                    'title' => $media['title'],
                    'type' => $media['type'],
                    'path' => $filePath,
                ]);
            }
        }
    }

    public function update(Request $request, $id): JsonResponse
    {
        $validator = Validator::make(
            $request->all(),
            [
                'name' => 'sometimes|required|string|max:255',
                'logo' => 'sometimes|image|mimes:jpeg,png,jpg,gif|max:2048',
                'location' => 'sometimes|required|string|max:255',
                'bio' => 'sometimes|required|string|max:1000',
                'languages' => 'sometimes|required|string|max:255',
                'phone' => 'sometimes|required|string|max:20',
                'business_email' => 'sometimes|required|email|max:255',
                'website' => 'sometimes|required|url|max:255',
                'facebook' => 'sometimes|required|string|max:255',
                'instagram' => 'sometimes|required|string|max:255',
                'twitter' => 'sometimes|required|string|max:255',
                'is_available' => 'sometimes|required|boolean',
                'services' => 'sometimes|required|array',
                'services.*' => 'required|integer|exists:vendor_services,id',
                'prices' => 'sometimes|required|array',
                'prices.*.service_id' => 'required|integer|exists:vendor_services,id',
                'prices.*.price' => 'required|numeric|min:0',
                'prices.*.currency_id' => 'required|integer|exists:currencies,id',
                'prices.*.description' => 'nullable|string|max:1000',
                'media' => 'sometimes|required|array',
                'media.*.title' => 'required|string|max:255',
                'media.*.type' => 'required|string|in:image,video',
                'media.*.path' => 'required|string|url',
            ]
        );

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $vendor = Vendor::findOrFail($id);

        if ($request->hasFile('logo')) {
            $logoPath = $request->file('logo')->store('vendors');
            $vendor->logo = $logoPath;
        }

        if ($request->has('name')) {
            $vendor->name = $request->input('name');
            $vendor->slug = Str::slug($request->input('name'), '-');
        }
        if ($request->has('location')) $vendor->location = $request->input('location');
        if ($request->has('bio')) $vendor->bio = $request->input('bio');
        if ($request->has('languages')) $vendor->languages = $request->input('languages');
        if ($request->has('phone')) $vendor->phone = $request->input('phone');
        if ($request->has('business_email')) $vendor->business_email = $request->input('business_email');
        if ($request->has('website')) $vendor->website = $request->input('website');
        if ($request->has('facebook')) $vendor->facebook = $request->input('facebook');
        if ($request->has('instagram')) $vendor->instagram = $request->input('instagram');
        if ($request->has('twitter')) $vendor->twitter = $request->input('twitter');
        if ($request->has('is_available')) $vendor->is_available = $request->input('is_available');

        $vendor->save();

        if ($request->has('services')) {
            $vendor->services()->sync($request->input('services'));
        }

        if ($request->has('prices')) {
            VendorPrice::where('vendor_id', $vendor->id)->delete();
            foreach ($request->input('prices') as $price) {
                VendorPrice::create([
                    'vendor_id' => $vendor->id,
                    'currency_id' => $price['currency_id'],
                    'price' => $price['price'],
                    'description' => $price['description'] ?? null,
                ]);
            }
        }

        if ($request->has('media')) {
            VendorMedia::where('vendor_id', $vendor->id)->delete();
            foreach ($request->input('media') as $media) {
                VendorMedia::create([
                    'vendor_id' => $vendor->id,
                    'title' => $media['title'],
                    'type' => $media['type'],
                    'path' => $media['path'],
                ]);
            }
        }

        return response()->json([
            'message' => 'Vendor updated successfully',
        ], 200);
    }


    public function destroy($id): JsonResponse
    {
        $vendor = Vendor::findOrFail($id);
        $vendor->delete();
        return response()->json([
            'message' => "Vendor deleted successfully",
        ]);
    }

    /**
     * Approve a vendor request.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function approve(int $id): JsonResponse
    {
        if (!request()->user()->hasRole('admin')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $vendor = Vendor::findOrFail($id);

        if ($vendor->status !== 'pending') {
            return response()->json([
                'message' => 'This vendor request has already been ' . $vendor->status . '.'
            ], 422);
        }

        $vendor->status = 'approved';
        $vendor->approved_at = Carbon::now();
        $vendor->save();

        $user = User::find($vendor->user_id);
        $user->assignRole('vendor');

        $trialPlan = Plan::where('name', 'Trial')->first();
        if ($trialPlan) {
            Subscription::create([
                'user_id' => $vendor->user_id,
                'plan_id' => $trialPlan->id,
                'start_date' => Carbon::now(),
                'end_date' => Carbon::now()->addDays(14),
            ]);
        }

        $user->notify(new VendorRequestApprovedNotification($vendor));

        return response()->json([
            'message' => 'Vendor request approved successfully. The user has been assigned the vendor role and given a 14-day trial subscription.',
            'vendor' => $vendor
        ]);
    }

    /**
     * Reject a vendor request.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function reject(Request $request, int $id): JsonResponse
    {
        if (!request()->user()->hasRole('admin')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'rejection_reason' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $vendor = Vendor::findOrFail($id);

        if ($vendor->status !== 'pending') {
            return response()->json([
                'message' => 'This vendor request has already been ' . $vendor->status . '.'
            ], 422);
        }

        $vendor->status = 'rejected';
        $vendor->rejection_reason = $request->input('rejection_reason');
        $vendor->rejected_at = Carbon::now();
        $vendor->save();

        $user = User::find($vendor->user_id);
        $user->notify(new VendorRequestRejectedNotification($vendor));

        return response()->json([
            'message' => 'Vendor request rejected successfully.',
            'vendor' => $vendor
        ]);
    }

    /**
     * Get pending vendor requests.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function pendingRequests(Request $request): JsonResponse
    {
        $user = $request->user();

        if (!$user->hasRole('admin')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $vendors = Vendor::with('user')
            ->where('status', 'pending')
            ->orderBy('created_at', 'desc')
            ->paginate($request->input('per_page', 10));

        return response()->json($vendors);
    }

    /**
     * Like or unlike a vendor.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function likeVendor(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'vendor_id' => 'required|integer|exists:vendors,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 400);
        }

        $userId = $request->user()->id;
        $vendorId = $request->vendor_id;

        $vendor = Vendor::find($vendorId);
        if (!$vendor) {
            return response()->json(['error' => 'Vendor not found'], 404);
        }

        $existingLike = VendorLike::where('vendor_id', $vendorId)
            ->where('user_id', $userId)
            ->first();

        if ($existingLike) {
            // Unlike the vendor
            $existingLike->delete();
            $vendor->decrement('likes_count');
            return response()->json([
                'message' => 'You have unliked this vendor',
                'liked' => false,
                'likes_count' => $vendor->likes_count
            ], 200);
        }

        // Like the vendor
        VendorLike::create([
            'user_id' => $userId,
            'vendor_id' => $vendorId,
        ]);

        $vendor->increment('likes_count');

        return response()->json([
            'message' => 'You have liked this vendor',
            'liked' => true,
            'likes_count' => $vendor->likes_count
        ], 200);
    }

    /**
     * Check if the current user has liked a vendor.
     *
     * @param Request $request
     * @param int $vendorId
     * @return JsonResponse
     */
    public function checkLikeStatus(Request $request, int $vendorId): JsonResponse
    {
        $userId = $request->user()->id;

        $vendor = Vendor::find($vendorId);
        if (!$vendor) {
            return response()->json(['error' => 'Vendor not found'], 404);
        }

        $liked = VendorLike::where('vendor_id', $vendorId)
            ->where('user_id', $userId)
            ->exists();

        return response()->json([
            'liked' => $liked,
            'likes_count' => $vendor->likes_count
        ], 200);
    }

    /**
     * Get filter options for vendors page.
     *
     * @return JsonResponse
     */
    public function getFilterOptions(): JsonResponse
    {
        // Get all services
        $services = \App\Models\Service::select('id', 'name', 'description')->get();

        // Get min and max prices
        $minPrice = \App\Models\VendorPrice::min('price') ?: 0;
        $maxPrice = \App\Models\VendorPrice::max('price') ?: 10000;

        // Get all locations (unique)
        $locations = Vendor::where('status', 'approved')
            ->whereNotNull('location')
            ->distinct()
            ->pluck('location');

        // Get rating range
        $minRating = 0;
        $maxRating = 5;

        return response()->json([
            'services' => $services,
            'price_range' => [
                'min' => (int)$minPrice,
                'max' => (int)$maxPrice
            ],
            'locations' => $locations,
            'rating_range' => [
                'min' => $minRating,
                'max' => $maxRating
            ]
        ]);
    }

    /**
     * Get vendor statistics
     */
    public function stats(): JsonResponse
    {
        $totalVendors = Vendor::count();
        $pendingApproval = Vendor::where('status', 'pending')->count();
        $approvedVendors = Vendor::where('status', 'approved')->count();
        $rejectedVendors = Vendor::where('status', 'rejected')->count();
        $suspendedVendors = Vendor::where('status', 'suspended')->count();

        $totalRevenue = \DB::table('vendor_bookings')
            ->where('status', 'completed')
            ->sum('total_price');

        $totalBookings = \DB::table('vendor_bookings')->count();

        return response()->json([
            'total_vendors' => $totalVendors,
            'pending_approval' => $pendingApproval,
            'approved_vendors' => $approvedVendors,
            'rejected_vendors' => $rejectedVendors,
            'suspended_vendors' => $suspendedVendors,
            'total_revenue' => $totalRevenue ?: 0,
            'total_bookings' => $totalBookings,
        ]);
    }

    /**
     * Get vendor categories
     */
    public function categories(): JsonResponse
    {
        $categories = \DB::table('services')
            ->select('id', 'name', 'description')
            ->orderBy('name')
            ->get();

        return response()->json($categories);
    }

    /**
     * Get vendor locations
     */
    public function locations(): JsonResponse
    {
        $locations = Vendor::select('location')
            ->whereNotNull('location')
            ->where('location', '!=', '')
            ->distinct()
            ->orderBy('location')
            ->pluck('location');

        return response()->json($locations);
    }

    /**
     * Export vendors to Excel
     */
    public function export(Request $request): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        $query = Vendor::with('user', 'services.service')
            ->withAvg('ratings', 'rating')
            ->withCount('ratings');

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('location')) {
            $query->where('location', 'like', '%' . $request->location . '%');
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', '%' . $search . '%')
                  ->orWhere('business_email', 'like', '%' . $search . '%')
                  ->orWhere('phone', 'like', '%' . $search . '%');
            });
        }

        $vendors = $query->get();

        $csvData = [];
        $csvData[] = ['Name', 'Email', 'Phone', 'Location', 'Status', 'Rating', 'Reviews', 'Services', 'Created At'];

        foreach ($vendors as $vendor) {
            $services = $vendor->services->pluck('service.name')->join(', ');
            $csvData[] = [
                $vendor->name,
                $vendor->business_email,
                $vendor->phone,
                $vendor->location,
                ucfirst($vendor->status),
                $vendor->ratings_avg_rating ? round($vendor->ratings_avg_rating, 1) : 'N/A',
                $vendor->ratings_count ?: 0,
                $services,
                $vendor->created_at->format('Y-m-d H:i:s'),
            ];
        }

        $filename = 'vendors-' . now()->format('Y-m-d-H-i-s') . '.csv';
        $filePath = storage_path('app/temp/' . $filename);

        if (!file_exists(dirname($filePath))) {
            mkdir(dirname($filePath), 0755, true);
        }

        $file = fopen($filePath, 'w');
        foreach ($csvData as $row) {
            fputcsv($file, $row);
        }
        fclose($file);

        return response()->download($filePath, $filename, [
            'Content-Type' => 'text/csv',
        ])->deleteFileAfterSend();
    }

    /**
     * Suspend a vendor
     */
    public function suspend($id): JsonResponse
    {
        if (!request()->user()->hasRole('admin')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $vendor = Vendor::findOrFail($id);
        $vendor->update(['status' => 'suspended']);

        return response()->json([
            'message' => 'Vendor suspended successfully',
            'data' => $vendor
        ]);
    }

    /**
     * Reactivate a suspended vendor
     */
    public function reactivate($id): JsonResponse
    {
        if (!request()->user()->hasRole('admin')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $vendor = Vendor::findOrFail($id);

        if ($vendor->status !== 'suspended') {
            return response()->json([
                'message' => 'Only suspended vendors can be reactivated.'
            ], 422);
        }

        $vendor->update(['status' => 'approved']);

        return response()->json([
            'message' => 'Vendor reactivated successfully',
            'data' => $vendor
        ]);
    }
}
