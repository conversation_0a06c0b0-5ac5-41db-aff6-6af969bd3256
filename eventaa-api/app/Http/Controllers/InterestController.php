<?php

namespace App\Http\Controllers;

use App\Models\Interest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class InterestController extends Controller
{
    public function create(Request $request){
        $validator = Validator::make($request->all(), [
            "category_id" => "required"
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422 );
        };

        $userId = $request->user()->id;

        foreach (json_decode($request->category_id) as $id) {
            Interest::create([
                'user_id' => $userId,
                'category_id'=> $id
            ]);
        }

        return response()->json(['message'=> 'Interest created successfully'], 201);
    }


public function update(Request $request){

    $validator = Validator::make($request->all(), [
        "category_id" => "required|array"
    ]);

    if ($validator->fails()) {
        return response()->json(['errors' => $validator->errors()] );
    };

    $id = $request->user()->id;
    $interests = Interest::where('user_id', $id)->get();

    foreach ($interests as $interest) {
        $interest->delete();
    }

    foreach ($request->category_id as $category_id) {
        Interest::create([
            'user_id' => $id,
            'category_id'=> $category_id
        ]);
    }

    return response()->json(['message'=> 'Interests updated successfully'], 200);
}
}
