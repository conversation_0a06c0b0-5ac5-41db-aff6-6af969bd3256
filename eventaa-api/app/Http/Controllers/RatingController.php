<?php

namespace App\Http\Controllers;

use App\Models\EventAttendee;
use App\Models\Rating;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class RatingController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        $ratings = Rating::all();
        return response()->json($ratings);
    }

    public function create(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'event_id' => 'required|integer',
            'rating' => 'required|integer',
            'review' => 'required',
            'organization' => 'required|integer',
            'content' => 'required|integer',
            'technical' => 'required|integer',
            'engagement' => 'required|integer',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }

        if ($request->user()) {
            $rating = Rating::where('user_id', $request->user()->id)->where('event_id', $request->event_id)->first();
            if($rating){
                return response()->json([
                    'message' => 'You have already rated this event'
                ], 201);
            }
            $attended = EventAttendee::where('user_id', $request->user()->id)->get();
            if ($attended) {
                Rating::create([
                    'event_id' => $request->event_id,
                    'user_id' => $request->user()->id,
                    'rating' => $request->rating,
                    'review' => $request->review,
                    'organization' => $request->organization,
                    'content' => $request->content,
                    'technical' => $request->technical,
                    'engagement' => $request->engagement
                ]);

                return response()->json([
                    'message' => 'Your rating has been added successfully'
                ], 201);
            }
        }

        return response()->json([
            'message' => 'You did not attend this event'
        ], 201);
    }

    public function read($id): JsonResponse
    {
        $rating = Rating::find($id);
        return response()->json([
            'rating' => $rating
        ]);
    }

    public function update(Request $request, int $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'event_id' => 'required|integer',
            'rating' => 'required|integer',
            'review' => 'required|text'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }

        $rating = Rating::find($id);
        $rating->event_id = $request->event_id;
        $rating->user_id = $request->user()->id;
        $rating->rating = $request->rating;
        $rating->review = $request->review;
        $rating->save();

        return response()->json([
            'message' => 'Rating updated successfully'
        ]);
    }

    public function destroy(int $id): JsonResponse
    {
        $rating = Rating::find($id);
        $rating->delete();

        return response()->json([
            'message' => 'Rating removed successfully'
        ], 201);
    }
}
