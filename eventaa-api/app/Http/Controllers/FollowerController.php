<?php

namespace App\Http\Controllers;

use App\Models\Follower;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class FollowerController extends Controller
{
    public function create(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            "followee_id" => "required|integer|exists:users,id",
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => $validator->errors(),
            ], 400);
        }

        $user = $request->user();
        if (!$user) {
            return response()->json([
                'error' => 'Unauthenticated user',
            ], 401);
        }

        // Prevent users from following themselves
        if ($user->id == $request->followee_id) {
            return response()->json([
                'error' => 'You cannot follow yourself',
            ], 400);
        }

        // Check if already following
        $existingFollow = Follower::where('follower_id', $user->id)
            ->where('followee_id', $request->followee_id)
            ->first();

        if ($existingFollow) {
            return response()->json([
                'error' => 'You are already following this user',
            ], 400);
        }

        $follow = Follower::create([
            'follower_id' => $user->id,
            'followee_id' => $request->followee_id,
        ]);

        // Log for debugging
        \Log::info('Follow relationship created', [
            'follow_id' => $follow->id,
            'follower_id' => $user->id,
            'followee_id' => $request->followee_id,
            'created_at' => $follow->created_at
        ]);

        // Verify the creation
        $verification = Follower::where('follower_id', $user->id)
            ->where('followee_id', $request->followee_id)
            ->exists();

        \Log::info('Follow verification', [
            'verification_exists' => $verification
        ]);

        return response()->json([
            'message' => 'Following successful',
            'follow_id' => $follow->id
        ]);
    }

    public function read($followerId): JsonResponse
    {
        $follower = Follower::find($followerId);

        if (!$follower) {
            return response()->json([
                'error' => 'Follower not found',
            ], 404);
        }

        return response()->json($follower);
    }

    public function update(Request $request, $followerId): JsonResponse
    {
        $follower = Follower::find($followerId);

        if (!$follower) {
            return response()->json([
                'error' => 'Follower not found',
            ], 404);
        }

        $follower->update($request->all());

        return response()->json([
            'message' => 'Follower updated successfully',
        ]);
    }

    public function delete(Request $request, $followerId): JsonResponse
    {
        $user = $request->user();
        if (!$user) {
            return response()->json([
                'error' => 'Unauthenticated user',
            ], 401);
        }

        $follower = Follower::where('id', $followerId)
            ->where('follower_id', $user->id)
            ->first();

        if (!$follower) {
            return response()->json([
                'error' => 'Follow relationship not found or you do not have permission to delete it',
            ], 404);
        }

        $follower->delete();

        return response()->json([
            'message' => 'Unfollowed successfully',
        ]);
    }

    public function unfollow(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            "followee_id" => "required|integer|exists:users,id",
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => $validator->errors(),
            ], 400);
        }

        $user = $request->user();
        if (!$user) {
            return response()->json([
                'error' => 'Unauthenticated user',
            ], 401);
        }

        $follower = Follower::where('follower_id', $user->id)
            ->where('followee_id', $request->followee_id)
            ->first();

        if (!$follower) {
            return response()->json([
                'error' => 'You are not following this user',
            ], 404);
        }

        $follower->delete();

        return response()->json([
            'message' => 'Unfollowed successfully',
        ]);
    }

    public function checkFollowStatus(Request $request, $userId)
    {
        $currentUser = $request->user();

        if (!$currentUser) {
            return response()->json([
                'is_following' => false,
                'message' => 'Unauthenticated'
            ]);
        }

        $isFollowing = Follower::where('follower_id', $currentUser->id)
            ->where('followee_id', $userId)
            ->exists();

        return response()->json([
            'is_following' => $isFollowing
        ]);
    }

    public function debugFollowers(Request $request)
    {
        $followers = Follower::all();
        return response()->json([
            'total_followers' => $followers->count(),
            'followers' => $followers
        ]);
    }
}
