<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Auth\Events\Verified;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;

class VerifyEmailController extends Controller
{

    public function __invoke(Request $request): RedirectResponse
    {
        $user = User::where('id', $request->route('id'))->firstOrFail();

        if ($user->hasVerifiedEmail()) {
            return redirect()->away(env('FRONTEND_URL', 'http://localhost:3000') . '/email/verify/already-success');
        }

        if ($user->markEmailAsVerified()) {
            event(new Verified($user));
        }
        return redirect()->away(env('FRONTEND_URL', 'http://localhost:3000') . '/email/verify/success');
    }
}
