<?php

namespace App\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Models\Category;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;

class CategoryController extends Controller
{
    public function index(Request $request)
    {
        $categories = Category::withCount('events')->get();
        return response()->json($categories, 201);
    }

    public function create(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "name" => "required|string",
            "description" => "required|string",
            "icon" => "image|mimes:png,jpg,jpeg,gif"
        ]);
        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ]);
        }

        $category = new Category;

        if ($request->file('icon')) {
            $image = $request->file('image');
            $iconName = time() . '.' . $image->getClientOriginalExtension();
            $image->move(public_path('images/category'), $iconName);
            $category->icon = $iconName;
        }

        $category->name = $request->name;
        $category->description = $request->description;
        $category->save();

        return response()->json($category);
    }

    public function read($id)
    {
        $category = Category::find($id);
        return response()->json($category);
    }

    public function update(Request $request, $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string',
            'description' => 'required|string',
            'icon' => 'image|mimes:png,jpg,jpeg,gif'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $category = Category::find($id);

        if (!$category) {
            return response()->json(['message' => 'Category not found'], 404);
        }

        $category->name = $request->input('name', $category->name);
        $category->description = $request->input('description', $category->description);

        if ($request->hasFile('icon')) {
            $image = $request->file('icon');
            $iconName = time() . '.' . $image->getClientOriginalExtension();
            $imagePath = 'categories/' . Str::lower($iconName);

            $saved = Storage::disk('public')->put($imagePath, file_get_contents($image));

            if ($saved) {
                $category->icon = $iconName;
            } else {
                return response()->json(['message' => 'Failed to save icon'], 500);
            }
        }

        $category->save();

        return response()->json($category);
    }

    public function delete($id)
    {
        $category = Category::find($id);
        $category->delete();

        return response()->json('Category deleted successfully');
    }
}
