<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Vendor;
use App\Models\VendorBooking;
use App\Models\VendorService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class VendorBookingController extends Controller
{
    /**
     * Display a listing of bookings.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $query = VendorBooking::with(['user', 'vendor', 'vendorService.service', 'category']);

        if ($request->has('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->has('vendor_id')) {
            $query->where('vendor_id', $request->vendor_id);
        }

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('from_date') && $request->has('to_date')) {
            $query->whereBetween('booking_from', [$request->from_date, $request->to_date]);
        }

        $sortField = $request->sort_by ?? 'created_at';
        $sortDirection = $request->sort_direction ?? 'desc';
        $query->orderBy($sortField, $sortDirection);

        $perPage = $request->per_page ?? 15;
        $bookings = $query->paginate($perPage);

        return response()->json($bookings);
    }

    /**
     * Display bookings for the authenticated user.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function userBookings(Request $request): JsonResponse
    {
        $user = $request->user();

        if (!$user) {
            return response()->json(['message' => 'Unauthenticated. Please log in.'], 401);
        }

        $query = VendorBooking::with(['vendor', 'vendorService.service', 'category'])
            ->where('user_id', $user->id);

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        $sortField = $request->sort_by ?? 'created_at';
        $sortDirection = $request->sort_direction ?? 'desc';
        $query->orderBy($sortField, $sortDirection);

        $perPage = $request->per_page ?? 15;
        $bookings = $query->paginate($perPage);

        return response()->json($bookings);
    }

    /**
     * Display bookings for a vendor.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function vendorBookings(Request $request): JsonResponse
    {
        $user = $request->user();

        if (!$user) {
            return response()->json(['message' => 'Unauthenticated. Please log in.'], 401);
        }

        $vendor = Vendor::where('user_id', $user->id)->first();

        if (!$vendor) {
            return response()->json(['message' => 'Vendor not found for this user'], 404);
        }

        $query = VendorBooking::with(['user', 'vendorService.service', 'category'])
            ->where('vendor_id', $vendor->id);


        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        $sortField = $request->sort_by ?? 'created_at';
        $sortDirection = $request->sort_direction ?? 'desc';
        $query->orderBy($sortField, $sortDirection);

        $perPage = $request->per_page ?? 15;
        $bookings = $query->paginate($perPage);

        return response()->json($bookings);
    }

    /**
     * Store a newly created booking.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'vendor_id' => 'required|exists:vendors,id',
            'vendor_service_id' => 'required|exists:vendor_services,id',
            'category_id' => 'required|exists:categories,id',
            'booking_from' => 'required|date|after_or_equal:today',
            'booking_to' => 'required|date|after_or_equal:booking_from',
            'number_of_guests' => 'required|integer|min:1',
            'message' => 'nullable|string',
            'total_price' => 'required|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $vendor = Vendor::find($request->vendor_id);
        if (!$vendor) {
            return response()->json(['message' => 'Vendor not found'], 404);
        }

        $vendorService = VendorService::where('id', $request->vendor_service_id)
            ->where('vendor_id', $request->vendor_id)
            ->first();

        if (!$vendorService) {
            return response()->json(['message' => 'Vendor service not found or does not belong to this vendor'], 404);
        }

        $category = Category::find($request->category_id);
        if (!$category) {
            return response()->json(['message' => 'Category not found'], 404);
        }

        $bookingFrom = Carbon::parse($request->booking_from);
        $bookingTo = Carbon::parse($request->booking_to);

        $query = VendorBooking::where('vendor_id', $request->vendor_id)
            ->where('status', '!=', 'rejected')
            ->where('status', '!=', 'cancelled');

        // If we're updating an existing booking, exclude the current booking from conflict check
        if (isset($request->booking_id)) {
            $query->where('id', '!=', $request->booking_id);
        }

        $query->where(function ($query) use ($bookingFrom, $bookingTo) {

            $query->orWhere(function ($q) use ($bookingFrom, $bookingTo) {
                $q->where('booking_from', '<=', $bookingFrom)
                    ->where('booking_to', '>', $bookingFrom);
            })
                ->orWhere(function ($q) use ($bookingFrom, $bookingTo) {
                    $q->where('booking_from', '<', $bookingTo)
                        ->where('booking_to', '>=', $bookingTo);
                })
                ->orWhere(function ($q) use ($bookingFrom, $bookingTo) {
                    $q->where('booking_from', '>=', $bookingFrom)
                        ->where('booking_to', '<=', $bookingTo);
                })
                ->orWhere(function ($q) use ($bookingFrom, $bookingTo) {
                    $q->where('booking_from', '<=', $bookingFrom)
                        ->where('booking_to', '>=', $bookingTo);
                });
        });

        $conflictingBookingsData = $query->get();
        if ($conflictingBookingsData->count() > 0) {
            return response()->json([
                'message' => 'The vendor is already booked during this time period'
            ], 422);
        }

        $user = $request->user();

        if (!$user) {
            return response()->json(['message' => 'Unauthenticated. Please log in.'], 401);
        }

        $booking = VendorBooking::create([
            'user_id' => $user->id,
            'vendor_id' => $request->vendor_id,
            'vendor_service_id' => $request->vendor_service_id,
            'category_id' => $request->category_id,
            'booking_from' => $request->booking_from,
            'booking_to' => $request->booking_to,
            'number_of_guests' => $request->number_of_guests,
            'message' => $request->message,
            'total_price' => $request->total_price,
            'status' => 'pending',
        ]);

        return response()->json([
            'message' => 'Booking created successfully',
            'data' => $booking->load(['vendor', 'vendorService.service', 'category'])
        ], 201);
    }

    /**
     * Display the specified booking.
     *
     * @param int $id
     * @param Request $request
     * @return JsonResponse
     */
    public function show($id, Request $request): JsonResponse
    {
        $booking = VendorBooking::with(['user', 'vendor', 'vendorService.service', 'category'])->find($id);

        if (!$booking) {
            return response()->json(['message' => 'Booking not found'], 404);
        }


        $user = $request->user();
        $isVendorOwner = $booking->vendor->user_id === $user->id;
        $isBookingOwner = $booking->user_id === $user->id;

        if (!$isVendorOwner && !$isBookingOwner && !$user->hasRole('admin')) {
            return response()->json(['message' => 'Unauthorized to view this booking'], 403);
        }

        return response()->json($booking);
    }

    /**
     * Update the specified booking.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(Request $request, $id): JsonResponse
    {
        $booking = VendorBooking::find($id);

        if (!$booking) {
            return response()->json(['message' => 'Booking not found'], 404);
        }

        $user = $request->user();
        $isBookingOwner = $booking->user_id === $user->id;

        if (!$isBookingOwner && !$user->hasRole('admin')) {
            return response()->json(['message' => 'Unauthorized to update this booking'], 403);
        }

        if ($booking->status !== 'pending') {
            return response()->json([
                'message' => 'Cannot update booking that is not in pending status'
            ], 422);
        }

        $validator = Validator::make($request->all(), [
            'booking_from' => 'sometimes|required|date|after_or_equal:today',
            'booking_to' => 'sometimes|required|date|after_or_equal:booking_from',
            'number_of_guests' => 'sometimes|required|integer|min:1',
            'message' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        if ($request->has('booking_from') || $request->has('booking_to')) {
            $bookingFrom = Carbon::parse($request->booking_from ?? $booking->booking_from);
            $bookingTo = Carbon::parse($request->booking_to ?? $booking->booking_to);

            $conflictingBookings = VendorBooking::where('vendor_id', $booking->vendor_id)
                ->where('id', '!=', $booking->id)
                ->where('status', '!=', 'rejected')
                ->where('status', '!=', 'cancelled')
                ->where(function ($query) use ($bookingFrom, $bookingTo) {
                    // Check if booking starts during an existing booking
                    $query->where(function ($q) use ($bookingFrom) {
                        $q->where('booking_from', '<=', $bookingFrom)
                            ->where('booking_to', '>', $bookingFrom);
                    })
                        // Check if booking ends during an existing booking
                        ->orWhere(function ($q) use ($bookingTo) {
                            $q->where('booking_from', '<', $bookingTo)
                                ->where('booking_to', '>=', $bookingTo);
                        })
                        // Check if booking completely contains an existing booking
                        ->orWhere(function ($q) use ($bookingFrom, $bookingTo) {
                            $q->where('booking_from', '>=', $bookingFrom)
                                ->where('booking_to', '<=', $bookingTo);
                        });
                })
                ->exists();

            if ($conflictingBookings) {
                return response()->json([
                    'message' => 'The vendor is already booked during this time period'
                ], 422);
            }
        }

        $booking->update($request->only([
            'booking_from',
            'booking_to',
            'number_of_guests',
            'message',
        ]));

        return response()->json([
            'message' => 'Booking updated successfully',
            'data' => $booking->load(['vendor', 'vendorService.service', 'category'])
        ]);
    }

    /**
     * Update the status of a booking.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function updateStatus(Request $request, $id): JsonResponse
    {
        $booking = VendorBooking::find($id);

        if (!$booking) {
            return response()->json(['message' => 'Booking not found'], 404);
        }

        $user = $request->user();
        $isVendorOwner = $booking->vendor->user_id === $user->id;
        $isBookingOwner = $booking->user_id === $user->id;

        $validator = Validator::make($request->all(), [
            'status' => [
                'required',
                Rule::in(['pending', 'approved', 'rejected', 'completed', 'cancelled']),
            ],
            'rejection_reason' => 'required_if:status,rejected|nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $newStatus = $request->status;

        if (in_array($newStatus, ['approved', 'rejected'])) {
            if (!$isVendorOwner && !$user->hasRole('admin')) {
                return response()->json(['message' => 'Unauthorized to approve or reject this booking'], 403);
            }
        } elseif ($newStatus === 'cancelled') {
            if (!$isBookingOwner && !$user->hasRole('admin')) {
                return response()->json(['message' => 'Unauthorized to cancel this booking'], 403);
            }

            if (!in_array($booking->status, ['pending', 'approved'])) {
                return response()->json([
                    'message' => 'Cannot cancel booking that is not pending or approved'
                ], 422);
            }
        } elseif ($newStatus === 'completed') {
            if (!$isVendorOwner && !$user->hasRole('admin')) {
                return response()->json(['message' => 'Unauthorized to complete this booking'], 403);
            }

            if ($booking->status !== 'approved') {
                return response()->json([
                    'message' => 'Cannot complete booking that is not approved'
                ], 422);
            }
        } elseif ($newStatus === 'pending') {
            return response()->json([
                'message' => 'Cannot change booking status back to pending'
            ], 422);
        }

        $updateData = ['status' => $newStatus];

        if ($newStatus === 'rejected' && $request->has('rejection_reason')) {
            $updateData['rejection_reason'] = $request->rejection_reason;
        }

        $booking->update($updateData);

        return response()->json([
            'message' => "Booking {$newStatus} successfully",
            'data' => $booking->load(['vendor', 'vendorService.service', 'category'])
        ]);
    }

    /**
     * Remove the specified booking.
     *
     * @param int $id
     * @param Request $request
     * @return JsonResponse
     */
    public function destroy($id, Request $request): JsonResponse
    {
        $booking = VendorBooking::find($id);

        if (!$booking) {
            return response()->json(['message' => 'Booking not found'], 404);
        }

        $user = $request->user();
        $isBookingOwner = $booking->user_id === $user->id;

        if (!$isBookingOwner && !$user->hasRole('vendor')) {
            return response()->json(['message' => 'Unauthorized to delete this booking'], 403);
        }

        if (!in_array($booking->status, ['pending', 'cancelled', 'rejected'])) {
            return response()->json([
                'message' => 'Cannot delete booking that is not pending, cancelled, or rejected'
            ], 422);
        }

        $booking->delete();

        return response()->json([
            'message' => 'Booking deleted successfully'
        ]);
    }

    /**
     * Get available time slots for a vendor.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getAvailableTimeSlots(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'vendor_id' => 'required|exists:vendors,id',
            'date' => 'required|date|after_or_equal:today',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $vendor = Vendor::find($request->vendor_id);
        if (!$vendor) {
            return response()->json(['message' => 'Vendor not found'], 404);
        }

        $date = Carbon::parse($request->date)->startOfDay();
        $nextDay = (clone $date)->addDay();

        $bookings = VendorBooking::where('vendor_id', $request->vendor_id)
            ->where('status', '!=', 'rejected')
            ->where('status', '!=', 'cancelled')
            ->where(function ($query) use ($date, $nextDay) {
                $query->whereBetween('booking_from', [$date, $nextDay])
                    ->orWhereBetween('booking_to', [$date, $nextDay])
                    ->orWhere(function ($q) use ($date, $nextDay) {
                        $q->where('booking_from', '<=', $date)
                            ->where('booking_to', '>=', $nextDay);
                    });
            })
            ->orderBy('booking_from')
            ->get(['booking_from', 'booking_to']);

        $businessStart = (clone $date)->setHour(9)->setMinute(0)->setSecond(0);
        $businessEnd = (clone $date)->setHour(17)->setMinute(0)->setSecond(0);

        $availableSlots = [];
        $currentSlot = clone $businessStart;

        if ($bookings->isEmpty()) {
            $availableSlots[] = [
                'start' => $businessStart->toDateTimeString(),
                'end' => $businessEnd->toDateTimeString(),
            ];
        } else {
            foreach ($bookings as $booking) {
                $bookingStart = Carbon::parse($booking->booking_from);
                $bookingEnd = Carbon::parse($booking->booking_to);

                if ($currentSlot < $bookingStart) {
                    $availableSlots[] = [
                        'start' => $currentSlot->toDateTimeString(),
                        'end' => $bookingStart->toDateTimeString(),
                    ];
                }

                $currentSlot = clone $bookingEnd;
            }

            if ($currentSlot < $businessEnd) {
                $availableSlots[] = [
                    'start' => $currentSlot->toDateTimeString(),
                    'end' => $businessEnd->toDateTimeString(),
                ];
            }
        }

        return response()->json([
            'date' => $date->toDateString(),
            'available_slots' => $availableSlots
        ]);
    }

    /**
     * Get the count of pending bookings for a vendor.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getPendingCount(Request $request): JsonResponse
    {
        $user = $request->user();

        if (!$user) {
            return response()->json(['message' => 'Unauthenticated. Please log in.'], 401);
        }

        $vendor = Vendor::where('user_id', $user->id)->first();

        if (!$vendor) {
            return response()->json(['message' => 'Vendor not found for this user'], 404);
        }

        $pendingCount = VendorBooking::where('vendor_id', $vendor->id)
            ->where('status', 'pending')
            ->count();

        return response()->json([
            'pending_count' => $pendingCount
        ]);
    }
}
