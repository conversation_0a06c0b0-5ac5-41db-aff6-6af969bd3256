<?php

namespace App\Http\Controllers;

use App\Models\Vendor;
use App\Models\VendorShare;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class VendorShareController extends Controller
{
    /**
     * Record a vendor share.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function share(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'vendor_id' => 'required|integer|exists:vendors,id',
            'platform' => 'required|string|in:whatsapp,facebook,twitter,instagram,email,copy',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 400);
        }

        $userId = $request->user() ? $request->user()->id : null;
        $vendor = Vendor::find($request->vendor_id);

        if (!$vendor) {
            return response()->json(['error' => 'Vendor not found'], 404);
        }

        VendorShare::create([
            'user_id' => $userId,
            'vendor_id' => $vendor->id,
            'platform' => $request->platform,
            'ip_address' => $request->ip(),
        ]);

        return response()->json(['message' => 'Vendor shared successfully'], 201);
    }

    /**
     * Get share statistics for a vendor.
     *
     * @param Request $request
     * @param int $vendorId
     * @return JsonResponse
     */
    public function getShareStats(Request $request, int $vendorId): JsonResponse
    {
        $vendor = Vendor::find($vendorId);

        if (!$vendor) {
            return response()->json(['error' => 'Vendor not found'], 404);
        }

        $stats = [
            'total_shares' => $vendor->shares()->count(),
            'platform_breakdown' => [
                'whatsapp' => $vendor->shares()->where('platform', 'whatsapp')->count(),
                'facebook' => $vendor->shares()->where('platform', 'facebook')->count(),
                'twitter' => $vendor->shares()->where('platform', 'twitter')->count(),
                'instagram' => $vendor->shares()->where('platform', 'instagram')->count(),
                'email' => $vendor->shares()->where('platform', 'email')->count(),
                'copy' => $vendor->shares()->where('platform', 'copy')->count(),
            ]
        ];

        return response()->json($stats);
    }
}
