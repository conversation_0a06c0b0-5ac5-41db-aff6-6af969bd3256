<?php

namespace App\Http\Controllers;

use App\Events\NotificationCreated;
use App\Models\Event;
use App\Models\Profile;
use App\Models\User;
use App\Notifications\UserNotification;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Notification;

class ProfileController extends Controller
{
    public function getProfile(Request $request): JsonResponse
    {
        $user = $request->user()->load('currency');

        $likedEventIds = $user->likedEvents->pluck('id')->toArray();
        $attendedEventIds = $user->attendedEvents->pluck('id')->toArray();

        $events = Event::whereIn('id', array_merge($likedEventIds, $attendedEventIds))
            ->latest()
            ->take(3)
            ->get()
            ->map(function ($event) use ($likedEventIds, $attendedEventIds) {
                return [
                    ...$event->toArray(),
                    'is_liked' => in_array($event->id, $likedEventIds),
                    'is_attendee' => in_array($event->id, $attendedEventIds)
                ];
            });

        $roles = $user->getRoleNames();

        $profileData = [
            ...$user->toArray(),
            'followers_count' => $user->followers()->count(),
            'following_count' => $user->following()->count(),
            'profile' => $user->profile ?? [],
            'interests' => $user->interests ?? [],
            'events' => $events,
            'roles' => $roles,
            'currency' => $user->currency
        ];

        return response()->json($profileData);
    }

    public function changeProfilePhoto(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'photo' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:10048',
        ]);
        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }
        $user = $request->user();
        $imageName = time() . '.' . $request->photo->extension();
        $request->photo->move(public_path('storage/avatars'), $imageName);
        $user->avatar = $imageName;
        $user->save();

        $notificationData = [
            'user_id' => $user->id,
            'greeting' => 'Hello',
            'title' => 'Profile photo changed',
            'line' => 'Your profile photo was changed successfully, if this was not you please contact us',
            'action' => 'View App',
            'actionURL' => '#'
        ];

        if ($request->mobile) {
            $this->sendToUser($notificationData['title'], $notificationData['line'], $user->android_app_token);
        }

        $user->notify(new UserNotification($notificationData));
        $latestNotification = $user->notifications()->latest()->first();
        broadcast(new NotificationCreated($latestNotification));

        return response()->json([
            'message' => 'Profile photo updated successfully',
            'user' => $user
        ]);
    }

    public function updateProfile(Request $request): JsonResponse
    {

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'email' => 'sometimes|required|string|email|max:255|unique:users,email,' . $request->user()->id,
            'phone' => 'sometimes|nullable|string|max:255',
            'twitter_url' => 'sometimes|nullable|string|max:255',
            'facebook_url' => 'sometimes|nullable|string|max:255',
            'currency_id' => 'sometimes|nullable|exists:currencies,id',
            'show_currency_symbol' => 'sometimes|boolean',
            'auto_convert_prices' => 'sometimes|boolean',
        ]);
        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }
        $user = $request->user();

        if ($request->has('name')) {
            $user->name = $request->name;
        }
        if ($request->has('email')) {
            $user->email = $request->email;
        }
        if ($request->has('phone')) {
            $user->phone = $request->phone;
        }
        if ($request->has('twitter_url')) {
            $user->twitter_url = $request->twitter_url;
        }
        if ($request->has('facebook_url')) {
            $user->facebook_url = $request->facebook_url;
        }

        if ($request->has('currency_id')) {
            $user->currency_id = $request->currency_id;
        }

        $user->save();

        if ($request->interests) {
            $interestIds = is_array($request->interests)
                ? $request->interests
                : json_decode($request->interests, true);

            $user->interests()->syncWithoutDetaching($interestIds);
        }

        $user->load('currency');

        return response()->json([
            'message' => 'Profile updated successfully',
            'user' => $user
        ], 201);
    }

    public function saveFirebaseToken(Request $request): JsonResponse
    {
        $user = User::find($request->user()->id);
        $user->android_app_token = $request->token;
        $user->save();
        return response()->json([
            'message' => 'Firebase token saved successfully'
        ], 201);
    }

    public function updateBio(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'bio' => 'required|string',
        ]);

        $profile = Profile::firstOrCreate(
            ['user_id' => $request->user()->id],
            ['about' => $validated['bio']]
        );

        $profile->about = $validated['bio'];
        $profile->save();

        return response()->json([
            'message' => 'Profile bio updated successfully!',
        ]);
    }
}
