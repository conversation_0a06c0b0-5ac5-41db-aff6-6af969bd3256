<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class UserController extends Controller
{
    public function index(Request $request)
    {
        $query = User::query();

        if ($search = $request->input('search')) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('id', 'like', "%{$search}%");
            });
        }

        if ($role = $request->input('role')) {
            if ($role !== 'all') {
                $query->where(function ($q) use ($role) {
                    $q->where('role', $role)
                      ->orWhereNull('role');
                });
            }
        }

        if ($status = $request->input('status')) {
            if ($status !== 'all') {
                $query->where(function ($q) use ($status) {
                    $q->where('status', $status)
                      ->orWhereNull('status');
                });
            }
        }

        if ($dateFilter = $request->input('dateFilter')) {
            $now = Carbon::now();
            switch ($dateFilter) {
                case 'today':
                    $query->whereDate('created_at', $now);
                    break;
                case 'week':
                    $query->whereBetween('created_at', [$now->startOfWeek(), $now->endOfWeek()]);
                    break;
                case 'month':
                    $query->whereMonth('created_at', $now->month)
                          ->whereYear('created_at', $now->year);
                    break;
                case 'year':
                    $query->whereYear('created_at', $now->year);
                    break;
            }
        }

        $perPage = $request->input('limit', 10);
        $users = $query->paginate($perPage);

        $users->through(function ($user) {
            // Get the user's roles using Spatie
            if (!$user->roles || $user->roles->isEmpty()) {
                $user->assignRole('user'); // Default role is 'user'
            }
            $user->status = $user->status ?? 'pending';
            return $user;
        });

        return response()->json([
            'data' => [
                'users' => $users->items(),
                'total' => $users->total()
            ]
        ]);
    }

    public function stats()
    {
        $now = Carbon::now();
        $lastMonth = Carbon::now()->subMonth();
        $startOfMonth = $now->startOfMonth()->toDateTimeString();
        $lastMonthStart = $lastMonth->startOfMonth()->toDateTimeString();
        $lastMonthEnd = $lastMonth->endOfMonth()->toDateTimeString();

        $currentStats = DB::table('users')
            ->select([
                DB::raw('COUNT(*) as total'),
                DB::raw("COUNT(CASE WHEN created_at >= '{$startOfMonth}' THEN 1 END) as new_users"),
                DB::raw('COUNT(CASE WHEN COALESCE(role, "") = "vendor" THEN 1 END) as vendors'),
                DB::raw('COUNT(CASE WHEN COALESCE(status, "") = "inactive" THEN 1 END) as inactive_users')
            ])
            ->first();

        $lastMonthStats = DB::table('users')
            ->select([
                DB::raw('COUNT(*) as total'),
                DB::raw("COUNT(CASE WHEN created_at >= '{$lastMonthStart}' AND created_at <= '{$lastMonthEnd}' THEN 1 END) as new_users"),
                DB::raw('COUNT(CASE WHEN COALESCE(role, "") = "vendor" THEN 1 END) as vendors'),
                DB::raw('COUNT(CASE WHEN COALESCE(status, "") = "inactive" THEN 1 END) as inactive_users')
            ])
            ->where('created_at', '<', $startOfMonth)
            ->first();

        $calculateGrowth = function($current, $previous) {
            if ($previous == 0) return 0;
            return round((($current - $previous) / $previous) * 100, 1);
        };

        return response()->json([
            'data' => [
                'totalUsers' => $currentStats->total,
                'usersGrowth' => $calculateGrowth($currentStats->total, $lastMonthStats->total),
                'newUsers' => $currentStats->new_users,
                'newUsersGrowth' => $calculateGrowth($currentStats->new_users, $lastMonthStats->new_users),
                'vendors' => $currentStats->vendors,
                'vendorsGrowth' => $calculateGrowth($currentStats->vendors, $lastMonthStats->vendors),
                'inactiveUsers' => $currentStats->inactive_users,
                'inactiveGrowth' => $calculateGrowth($currentStats->inactive_users, $lastMonthStats->inactive_users)
            ]
        ]);
    }

    public function updateStatus(Request $request, $id)
    {
        $user = User::find($id);

        if (!$user) {
            return response()->json(['message' => 'User not found'], 404);
        }

        $request->validate([
            'status' => 'required|in:active,inactive,suspended,pending'
        ]);

        $user->update([
            'status' => $request->status
        ]);

        return response()->json([
            'message' => 'User status updated successfully',
            'data' => $user
        ]);
    }

    public function show(Request $request, $id)
    {
        $user = User::with(['profile', 'events', 'interests', 'vendors', 'vendor', 'venues'])
            ->withCount(['followers', 'following'])
            ->find($id);

        if (!$user) {
            return response()->json(['message' => 'User not found'], 404);
        }

        $events = $user->events()->select([
            'id', 'title', 'description', 'slug', 'cover_art',
            'start', 'end', 'location', 'user_id'
        ])->get();

        $vendors = $user->vendors()->with(['ratings'])
            ->withAvg('ratings', 'rating')
            ->select([
                'id', 'name', 'slug', 'logo', 'bio', 'location',
                'is_available', 'average_rating', 'user_id'
            ])->get()->map(function ($vendor) {
                $vendor->calculated_avg_rating = $vendor->ratings_avg_rating ?? 0;
                return $vendor;
            });

        $venues = $user->venues()->with(['ratings', 'images'])
            ->withAvg('ratings', 'rating')
            ->select([
                'id', 'name', 'slug', 'description', 'capacity',
                'user_id'
            ])->get()->map(function ($venue) {
                $venue->calculated_avg_rating = $venue->ratings_avg_rating ?? 0;
                return $venue;
            });

        $isFollowing = false;
        $currentUser = $request->user();
        if ($currentUser) {
            // Use a direct database query to be absolutely sure
            $followRecord = \DB::table('followers')
                ->where('follower_id', $currentUser->id)
                ->where('followee_id', $user->id)
                ->first();

            $isFollowing = $followRecord !== null;

            // Also check using the model
            $modelCheck = \App\Models\Follower::where('follower_id', $currentUser->id)
                ->where('followee_id', $user->id)
                ->exists();
        }

        $userData = [
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'avatar' => $user->avatar,
            'is_verified' => $user->is_verified,
            'created_at' => $user->created_at,
            'email_verified_at' => $user->email_verified_at,
            'facebook_url' => $user->facebook_url,
            'twitter_url' => $user->twitter_url,
            'followers_count' => $user->followers_count,
            'following_count' => $user->following_count,
            'is_following' => $isFollowing,
            'profile' => $user->profile,
            'interests' => $user->interests,
            'events' => $events,
            'vendors' => $vendors,
            'vendor' => $user->vendor,
            'venues' => $venues
        ];

        return response()->json($userData);
    }

    public function destroy($id)
    {
        $user = User::find($id);

        if (!$user) {
            return response()->json(['message' => 'User not found'], 404);
        }

        $user->delete();

        return response()->json([
            'message' => 'User deleted successfully'
        ]);
    }
}
