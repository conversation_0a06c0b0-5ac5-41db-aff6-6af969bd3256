<?php

namespace App\Http\Controllers;

use App\Models\UserSetting;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class SettingController extends Controller
{
    public function getUserSettings(Request $request): JsonResponse
    {
        $user = $request->user();

        // Get all available settings
        $allSettings = \App\Models\Setting::all();

        // Get user's enabled settings
        $userSettingIds = $user->settings()->pluck('settings.id')->toArray();

        // Mark settings as enabled based on user preferences
        $settings = $allSettings->map(function($setting) use ($userSettingIds) {
            $setting->user_enabled = in_array($setting->id, $userSettingIds);
            return $setting;
        });

        return response()->json([
            'message' => 'User settings retrieved successfully',
            'settings' => $settings
        ]);
    }

    public function addUserSetting(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'settings' => 'required|array',
            'settings.*.id' => 'required|integer',
            'settings.*.enabled' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();

        if (!$user) {
            return response()->json([
                'message' => 'Unauthenticated'
            ], 401);
        }

        // Clear existing user settings
        UserSetting::where('user_id', $user->id)->delete();

        // Add enabled settings
        foreach ($request->settings as $setting) {
            if ($setting['enabled']) {
                UserSetting::create([
                    'user_id' => $user->id,
                    'setting_id' => $setting['id'],
                ]);
            }
        }

        return response()->json([
            'message' => 'User settings updated successfully',
        ]);
    }

    public function updateUserPreferences(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'currency_id' => 'nullable|integer',
            'show_currency_symbol' => 'required|boolean',
            'auto_convert_prices' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();

        if (!$user) {
            return response()->json([
                'message' => 'Unauthenticated'
            ], 401);
        }

        if ($request->has('currency_id')) {
            $user->currency_id = $request->currency_id;
        }

        $user->show_currency_symbol = $request->show_currency_symbol;

        $user->auto_convert_prices = $request->auto_convert_prices;
        $user->save();

        return response()->json([
            'message' => 'User preferences updated successfully',
        ]);
    }

    public function deleteUserSettings(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'setting_id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();

        if (!$user) {
            return response()->json([
                'message' => 'Unauthenticated'
            ], 401);
        }

        UserSetting::where('user_id', $user->id)
            ->where('setting_id', $request->setting_id)
            ->delete();

        return response()->json([
            'message' => 'User setting deleted successfully',
        ]);
    }

    public function toggleUserSetting(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'setting_id' => 'required|integer',
            'enabled' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();

        if (!$user) {
            return response()->json([
                'message' => 'Unauthenticated'
            ], 401);
        }

        if ($request->enabled) {
            // Enable the setting - create if not exists
            UserSetting::firstOrCreate([
                'user_id' => $user->id,
                'setting_id' => $request->setting_id,
            ]);
            $message = 'Setting enabled successfully';
        } else {
            // Disable the setting - delete if exists
            UserSetting::where('user_id', $user->id)
                ->where('setting_id', $request->setting_id)
                ->delete();
            $message = 'Setting disabled successfully';
        }

        return response()->json([
            'message' => $message,
        ]);
    }

    public function getSessions(Request $request): JsonResponse
    {
        $user = $request->user();

        if (!$user) {
            return response()->json([
                'message' => 'Unauthenticated'
            ], 401);
        }

        // Get all sessions for the user
        $allSessions = DB::table('sessions')
            ->where('user_id', $user->id)
            ->orderBy('last_activity', 'desc')
            ->get();

        // Group sessions by IP and user agent and keep only the most recent one
        $groupedSessions = collect();
        $sessionKeys = [];

        foreach ($allSessions as $session) {
            $key = "{$session->ip_address}|{$session->user_agent}";

            // If we haven't seen this combination before, or if this session is more recent
            if (!isset($sessionKeys[$key]) || $session->last_activity > $sessionKeys[$key]->last_activity) {
                $sessionKeys[$key] = $session;
            }
        }

        $sessions = collect(array_values($sessionKeys))
            ->map(function ($session) use ($request) {
                $userAgent = $session->user_agent ?? '';

                // Simple device detection
                $device = 'Unknown';
                if (strpos($userAgent, 'Mobile') !== false) {
                    $device = 'Mobile';
                } elseif (strpos($userAgent, 'Tablet') !== false) {
                    $device = 'Tablet';
                } else {
                    $device = 'Desktop';
                }

                // Simple browser detection
                $browser = 'Unknown';
                if (strpos($userAgent, 'Chrome') !== false) {
                    $browser = 'Chrome';
                } elseif (strpos($userAgent, 'Firefox') !== false) {
                    $browser = 'Firefox';
                } elseif (strpos($userAgent, 'Safari') !== false) {
                    $browser = 'Safari';
                } elseif (strpos($userAgent, 'Edge') !== false) {
                    $browser = 'Edge';
                } elseif (strpos($userAgent, 'MSIE') !== false || strpos($userAgent, 'Trident') !== false) {
                    $browser = 'Internet Explorer';
                }

                // Simple platform detection
                $platform = 'Unknown';
                if (strpos($userAgent, 'Windows') !== false) {
                    $platform = 'Windows';
                } elseif (strpos($userAgent, 'Mac') !== false) {
                    $platform = 'Mac OS';
                } elseif (strpos($userAgent, 'Linux') !== false) {
                    $platform = 'Linux';
                } elseif (strpos($userAgent, 'Android') !== false) {
                    $platform = 'Android';
                } elseif (strpos($userAgent, 'iOS') !== false || strpos($userAgent, 'iPhone') !== false || strpos($userAgent, 'iPad') !== false) {
                    $platform = 'iOS';
                }

                return [
                    'id' => $session->id,
                    'ip_address' => $session->ip_address,
                    'user_agent' => $userAgent,
                    'last_active' => Carbon::createFromTimestamp($session->last_activity)->diffForHumans(),
                    'is_current' => $session->id === $request->session()->getId(),
                    'device' => $device,
                    'platform' => $platform,
                    'browser' => $browser
                ];
            });

        return response()->json([
            'sessions' => $sessions->values() // Convert to indexed array to ensure proper JSON encoding
        ]);
    }

    public function revokeSession(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'session_id' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();

        if (!$user) {
            return response()->json([
                'message' => 'Unauthenticated'
            ], 401);
        }

        if ($request->session()->getId() === $request->session_id) {
            return response()->json([
                'message' => 'Cannot revoke current session'
            ], 422);
        }

        $sessionToRevoke = DB::table('sessions')
            ->where('id', $request->session_id)
            ->where('user_id', $user->id)
            ->first();

        if ($sessionToRevoke) {
            DB::table('sessions')
                ->where('id', $request->session_id)
                ->where('user_id', $user->id)
                ->delete();

            DB::table('sessions')
                ->where('user_id', $user->id)
                ->where('ip_address', $sessionToRevoke->ip_address)
                ->where('user_agent', $sessionToRevoke->user_agent)
                ->where('id', '!=', $request->session()->getId())
                ->delete();
        }

        return response()->json([
            'message' => 'Session revoked successfully'
        ]);
    }
}
