<?php

namespace App\Http\Controllers;

use App\Models\PaymentGateway;
use App\Models\PaymentTransaction;
use App\Models\User;
use App\Models\VendorBooking;
use App\Models\VendorPaymentMethod;
use App\Services\PayChanguService;
use App\Traits\CurrencyHelper;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class PaymentController extends Controller
{
    use CurrencyHelper;

    public function initializeBookingPayment(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'booking_id' => 'required|integer|exists:vendor_bookings,id',
            'payment_method_id' => 'nullable|integer|exists:vendor_payment_methods,id',
            'phone_number' => 'required|string',
            'operator_id' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $booking = VendorBooking::findOrFail($request->booking_id);
        $paymentMethod = null;

        if ($request->payment_method_id) {
            $paymentMethod = VendorPaymentMethod::with('paymentGateway')->findOrFail($request->payment_method_id);

            if ($booking->vendor_id !== $paymentMethod->vendor_id) {
                return response()->json([
                    'message' => 'The payment method does not belong to the vendor of this booking'
                ], 403);
            }
        }

        if ($booking->is_paid) {
            return response()->json([
                'message' => 'This booking is already paid'
            ], 400);
        }

        try {
            $payChanguService = new PayChanguService();

            $result = $payChanguService->processBookingPayment(
                $booking,
                $paymentMethod,
                $request->user()->id,
                $request->phone_number,
                $request->operator_id
            );

            if ($result['status']) {
                $transaction = PaymentTransaction::where('transaction_id', $result['data']['reference'])->first();
                if ($transaction) {
                    event(new \App\Events\PaymentInitiated($transaction));
                }

                return response()->json([
                    'message' => 'Payment initialized successfully',
                    'data' => $result['data']
                ]);
            }

            return response()->json([
                'message' => $result['message'],
                'data' => $result['data'] ?? null
            ], 400);
        } catch (\Exception $e) {
            Log::error('Payment initialization failed', [
                'error' => $e->getMessage(),
                'booking_id' => $request->booking_id,
                'user_id' => $request->user()->id,
            ]);

            return response()->json([
                'message' => 'Payment initialization failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function verifyPayment(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'reference' => 'required|string|exists:payment_transactions,transaction_id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $transaction = PaymentTransaction::where('transaction_id', $request->reference)
            ->with(['paymentMethod.paymentGateway', 'bookable'])
            ->first();

        if (!$transaction) {
            return response()->json([
                'message' => 'Transaction not found'
            ], 404);
        }

        try {
            $payChanguService = new PayChanguService();

            $chargeId = $transaction->metadata['charge_id'] ?? null;
            if (!$chargeId) {
                return response()->json([
                    'message' => 'Charge ID not found in transaction metadata'
                ], 400);
            }

            $result = $payChanguService->verifyDirectCharge($chargeId);

            if ($result['status'] && isset($result['data']['status']) && $result['data']['status'] === 'success') {
                $oldStatus = $transaction->status;
                $transaction->status = 'completed';
                $transaction->payment_date = now();
                $transaction->gateway_response = array_merge(
                    $transaction->gateway_response ?? [],
                    ['verification' => $result]
                );
                $transaction->save();

                $this->handleSuccessfulPayment($transaction);

                event(new \App\Events\PaymentStatusUpdated($transaction, $oldStatus, 'completed'));

                return response()->json([
                    'message' => 'Payment verified successfully',
                    'data' => [
                        'transaction' => $transaction,
                        'bookable' => $transaction->bookable
                    ]
                ]);
            }

            $oldStatus = $transaction->status;
            $transaction->status = 'failed';
            $transaction->gateway_response = array_merge(
                $transaction->gateway_response ?? [],
                ['verification' => $result]
            );
            $transaction->save();

            event(new \App\Events\PaymentStatusUpdated($transaction, $oldStatus, 'failed'));

            return response()->json([
                'message' => 'Payment verification failed',
                'data' => $result['data'] ?? null
            ], 400);
        } catch (\Exception $e) {
            Log::error('Payment verification failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $request->reference,
            ]);

            return response()->json([
                'message' => 'Payment verification error',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    private function handleSuccessfulPayment(PaymentTransaction $transaction): void
    {
        switch ($transaction->payment_type) {
            case 'booking':
                if ($transaction->bookable_type === VendorBooking::class) {
                    $booking = $transaction->bookable;
                    $booking->is_paid = true;
                    $booking->save();
                }
                break;

            case 'ticket_purchase':
                $purchases = \App\Models\TicketPurchase::where('purchase_reference', $transaction->transaction_id)
                    ->get();

                // If no purchases found, create them from transaction metadata
                if ($purchases->isEmpty() && isset($transaction->metadata['tickets'])) {
                    Log::info('Creating TicketPurchase records from transaction metadata', [
                        'transaction_id' => $transaction->transaction_id,
                        'tickets_data' => $transaction->metadata['tickets']
                    ]);

                    $user = User::find($transaction->user_id);
                    if (!$user) {
                        Log::error('User not found for ticket purchase', [
                            'transaction_id' => $transaction->transaction_id,
                            'user_id' => $transaction->user_id
                        ]);
                        break;
                    }

                    // Create Payment record if it doesn't exist
                    $payment = \App\Models\Payment::where('transaction_id', $transaction->transaction_id)->first();
                    if (!$payment) {
                        $payment = \App\Models\Payment::create([
                            'user_id' => $transaction->user_id,
                            'amount' => $transaction->amount,
                            'status' => 'completed',
                            'type' => 'ticket_purchase',
                            'payment_method' => 'paychangu',
                            'transaction_id' => $transaction->transaction_id,
                            'metadata' => $transaction->metadata
                        ]);
                    }

                    // Create TicketPurchase records from metadata
                    foreach ($transaction->metadata['tickets'] as $ticketData) {
                        $ticket = \App\Models\Ticket::find($ticketData['ticket_id']);
                        if ($ticket) {
                            $purchase = \App\Models\TicketPurchase::create([
                                'user_id' => $transaction->user_id,
                                'event_id' => $ticketData['event_id'],
                                'ticket_id' => $ticketData['ticket_id'],
                                'payment_id' => $payment->id,
                                'purchase_reference' => $transaction->transaction_id,
                                'quantity' => $ticketData['quantity'],
                                'unit_price' => $ticketData['unit_price'],
                                'total_amount' => $ticketData['total_price'],
                                'status' => 'completed',
                                'purchased_at' => now(),
                                'attendee_name' => $user->name,
                                'attendee_email' => $user->email,
                                'attendee_phone' => $user->phone,
                            ]);
                            $purchases->push($purchase);
                        }
                    }

                    Log::info('Created TicketPurchase records from metadata', [
                        'transaction_id' => $transaction->transaction_id,
                        'purchases_created' => $purchases->count()
                    ]);
                } else {
                    // Update existing purchases to completed
                    $purchases->each(function ($purchase) {
                        $purchase->update(['status' => 'completed']);
                    });
                }

                if ($purchases->isNotEmpty()) {
                    $user = User::find($transaction->user_id);
                    event(new \App\Events\TicketPurchaseCompleted(
                        $purchases,
                        $user,
                        (float) $transaction->amount
                    ));

                    Log::info('Ticket purchase completed successfully', [
                        'transaction_id' => $transaction->transaction_id,
                        'user_id' => $transaction->user_id,
                        'purchases_count' => $purchases->count(),
                        'total_amount' => $transaction->amount
                    ]);
                } else {
                    Log::error('Cannot complete ticket purchase - no tickets found', [
                        'transaction_id' => $transaction->transaction_id,
                        'metadata' => $transaction->metadata
                    ]);
                }
                break;
        }
    }

    public function initializeTicketPurchase(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'tickets' => 'required|array|min:1',
            'tickets.*.ticket_id' => 'required|integer',
            'tickets.*.quantity' => 'required|integer|min:1',
            'tickets.*.event_id' => 'required|integer|exists:events,id',
            'phone_number' => 'required|string',
            'operator_id' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $processedTickets = [];
            foreach ($request->tickets as $ticketData) {
                $tierId = $ticketData['ticket_id']; 
                $eventId = $ticketData['event_id'];
                $quantity = $ticketData['quantity'];

                $ticket = \App\Models\Ticket::where('tier_id', $tierId)
                    ->where('event_id', $eventId)
                    ->where('status', 'available')
                    ->first();

                if (!$ticket) {
                    $tier = \App\Models\Tier::find($tierId);
                    $tierName = $tier ? $tier->name : "ID {$tierId}";
                    return response()->json([
                        'message' => "No available tickets found for tier: {$tierName} in the specified event"
                    ], 400);
                }

                $processedTickets[] = [
                    'ticket_id' => $ticket->id,
                    'quantity' => $quantity
                ];

                Log::info('Selected ticket for purchase', [
                    'tier_id' => $tierId,
                    'event_id' => $eventId,
                    'ticket_id' => $ticket->id,
                    'user_id' => $request->user()->id
                ]);
            }

            $payChanguService = new PayChanguService();

            $result = $payChanguService->processTicketPurchase(
                $processedTickets,
                $request->user(),
                $request->phone_number,
                $request->operator_id
            );

            if ($result['status']) {
                $transaction = PaymentTransaction::where('transaction_id', $result['data']['reference'])->first();
                if ($transaction) {
                    event(new \App\Events\PaymentInitiated($transaction));
                }

                return response()->json([
                    'message' => 'Ticket purchase payment initialized successfully',
                    'data' => $result['data']
                ], 201);
            }

            return response()->json([
                'message' => $result['message'],
                'data' => $result['data'] ?? null
            ], 400);
        } catch (\Exception $e) {
            Log::error('Ticket purchase payment initialization failed', [
                'error' => $e->getMessage(),
                'tickets' => $request->tickets,
                'user_id' => $request->user()->id,
            ]);

            return response()->json([
                'message' => 'Ticket purchase payment initialization failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getMobileMoneyOperators(Request $request): JsonResponse
    {
        try {
            $payChanguService = new PayChanguService();
            $result = $payChanguService->getMobileMoneyOperators();

            if ($result['status']) {
                return response()->json([
                    'data' => $result['data']
                ]);
            }

            return response()->json([
                'message' => 'Failed to fetch mobile money operators',
                'data' => []
            ], 400);
        } catch (\Exception $e) {
            Log::error('Failed to fetch mobile money operators', [
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'message' => 'Failed to fetch mobile money operators',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getVendorPaymentMethods(Request $request, $vendor_id): JsonResponse
    {
        $validator = Validator::make(['vendor_id' => $vendor_id], [
            'vendor_id' => 'required|integer|exists:vendors,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $paymentMethods = VendorPaymentMethod::where('vendor_id', $vendor_id)
            ->with('paymentGateway')
            ->where('status', 'active')
            ->get();

        return response()->json([
            'data' => $paymentMethods
        ]);
    }

    public function getUserTransactions(Request $request): JsonResponse
    {
        $transactions = PaymentTransaction::where('user_id', $request->user()->id)
            ->with(['paymentMethod.paymentGateway', 'bookable'])
            ->orderBy('created_at', 'desc')
            ->paginate($request->per_page ?? 10);

        return response()->json($transactions);
    }

    public function getUserReceivedTransactions(Request $request): JsonResponse
    {
        $user = $request->user();

        // Validate request parameters
        $validator = Validator::make($request->all(), [
            'page' => 'nullable|integer|min:1',
            'limit' => 'nullable|integer|min:1|max:100',
            'search' => 'nullable|string|max:255',
            'status' => 'nullable|string|in:completed,pending,failed,refunded',
            'event' => 'nullable|integer|exists:events,id',
            'date_filter' => 'nullable|string|in:today,week,month,year',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $query = PaymentTransaction::where('payment_type', 'ticket_purchase')
            ->where('user_id', '!=', $user->id)
            ->whereExists(function ($query) use ($user) {
                $query->select(\DB::raw(1))
                    ->from('ticket_purchases')
                    ->whereColumn('ticket_purchases.purchase_reference', 'payment_transactions.transaction_id')
                    ->whereExists(function ($eventQuery) use ($user) {
                        $eventQuery->select(\DB::raw(1))
                            ->from('events')
                            ->whereColumn('events.id', 'ticket_purchases.event_id')
                            ->where('events.user_id', $user->id);
                    });
            });

        // Apply status filter
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Apply search filter
        if ($request->filled('search')) {
            $searchTerm = '%' . $request->search . '%';
            $query->where(function ($q) use ($searchTerm) {
                $q->where('transaction_id', 'like', $searchTerm)
                    ->orWhereHas('user', function ($userQuery) use ($searchTerm) {
                        $userQuery->where('name', 'like', $searchTerm)
                            ->orWhere('email', 'like', $searchTerm);
                    })
                    ->orWhereExists(function ($eventQuery) use ($searchTerm) {
                        $eventQuery->select(\DB::raw(1))
                            ->from('ticket_purchases')
                            ->whereColumn('ticket_purchases.purchase_reference', 'payment_transactions.transaction_id')
                            ->whereExists(function ($eventSubQuery) use ($searchTerm) {
                                $eventSubQuery->select(\DB::raw(1))
                                    ->from('events')
                                    ->whereColumn('events.id', 'ticket_purchases.event_id')
                                    ->where('events.title', 'like', $searchTerm);
                            });
                    });
            });
        }

        // Apply event filter
        if ($request->filled('event')) {
            $query->whereExists(function ($eventQuery) use ($request) {
                $eventQuery->select(\DB::raw(1))
                    ->from('ticket_purchases')
                    ->whereColumn('ticket_purchases.purchase_reference', 'payment_transactions.transaction_id')
                    ->where('ticket_purchases.event_id', $request->event);
            });
        }

        // Apply date filter
        if ($request->filled('date_filter')) {
            $now = now();
            switch ($request->date_filter) {
                case 'today':
                    $query->whereDate('created_at', $now->toDateString());
                    break;
                case 'week':
                    $query->whereBetween('created_at', [
                        $now->startOfWeek()->toDateTimeString(),
                        $now->endOfWeek()->toDateTimeString()
                    ]);
                    break;
                case 'month':
                    $query->whereBetween('created_at', [
                        $now->startOfMonth()->toDateTimeString(),
                        $now->endOfMonth()->toDateTimeString()
                    ]);
                    break;
                case 'year':
                    $query->whereBetween('created_at', [
                        $now->startOfYear()->toDateTimeString(),
                        $now->endOfYear()->toDateTimeString()
                    ]);
                    break;
            }
        }

        $transactions = $query->with([
            'paymentMethod.paymentGateway',
            'user'
        ])
            ->orderBy('created_at', 'desc')
            ->paginate($request->limit ?? 10);

        // Add event information to each transaction
        $transactions->getCollection()->transform(function ($transaction) {
            $ticketPurchase = \App\Models\TicketPurchase::where('purchase_reference', $transaction->transaction_id)
                ->with('event')
                ->first();

            if ($ticketPurchase && $ticketPurchase->event) {
                $transaction->event = [
                    'id' => $ticketPurchase->event->id,
                    'title' => $ticketPurchase->event->title,
                    'date' => $ticketPurchase->event->start
                ];
            }

            return $transaction;
        });

        return response()->json($transactions);
    }

    public function exportUserReceivedTransactions(Request $request)
    {
        $user = $request->user();

        // Validate request parameters
        $validator = Validator::make($request->all(), [
            'search' => 'nullable|string|max:255',
            'status' => 'nullable|string|in:completed,pending,failed,refunded',
            'event' => 'nullable|integer|exists:events,id',
            'date_filter' => 'nullable|string|in:today,week,month,year',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $query = PaymentTransaction::where('payment_type', 'ticket_purchase')
            ->where('user_id', '!=', $user->id)
            ->whereExists(function ($query) use ($user) {
                $query->select(\DB::raw(1))
                    ->from('ticket_purchases')
                    ->whereColumn('ticket_purchases.purchase_reference', 'payment_transactions.transaction_id')
                    ->whereExists(function ($eventQuery) use ($user) {
                        $eventQuery->select(\DB::raw(1))
                            ->from('events')
                            ->whereColumn('events.id', 'ticket_purchases.event_id')
                            ->where('events.user_id', $user->id);
                    });
            });

        // Apply same filters as the main endpoint
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('search')) {
            $searchTerm = '%' . $request->search . '%';
            $query->where(function ($q) use ($searchTerm) {
                $q->where('transaction_id', 'like', $searchTerm)
                    ->orWhereHas('user', function ($userQuery) use ($searchTerm) {
                        $userQuery->where('name', 'like', $searchTerm)
                            ->orWhere('email', 'like', $searchTerm);
                    });
            });
        }

        if ($request->filled('event')) {
            $query->whereExists(function ($eventQuery) use ($request) {
                $eventQuery->select(\DB::raw(1))
                    ->from('ticket_purchases')
                    ->whereColumn('ticket_purchases.purchase_reference', 'payment_transactions.transaction_id')
                    ->where('ticket_purchases.event_id', $request->event);
            });
        }

        if ($request->filled('date_filter')) {
            $now = now();
            switch ($request->date_filter) {
                case 'today':
                    $query->whereDate('created_at', $now->toDateString());
                    break;
                case 'week':
                    $query->whereBetween('created_at', [
                        $now->startOfWeek()->toDateTimeString(),
                        $now->endOfWeek()->toDateTimeString()
                    ]);
                    break;
                case 'month':
                    $query->whereBetween('created_at', [
                        $now->startOfMonth()->toDateTimeString(),
                        $now->endOfMonth()->toDateTimeString()
                    ]);
                    break;
                case 'year':
                    $query->whereBetween('created_at', [
                        $now->startOfYear()->toDateTimeString(),
                        $now->endOfYear()->toDateTimeString()
                    ]);
                    break;
            }
        }

        $transactions = $query->with(['user'])
            ->orderBy('created_at', 'desc')
            ->get();

        // Generate CSV
        $csvData = [];
        $csvData[] = ['Transaction ID', 'Customer Name', 'Customer Email', 'Amount', 'Currency', 'Status', 'Payment Method', 'Date'];

        foreach ($transactions as $transaction) {
            $paymentMethod = 'Unknown';
            if (
                $transaction->gateway_response &&
                isset($transaction->gateway_response['data']['data']['mobile_money']['name'])
            ) {
                $paymentMethod = $transaction->gateway_response['data']['data']['mobile_money']['name'];
            }

            $csvData[] = [
                $transaction->transaction_id,
                $transaction->user->name ?? 'N/A',
                $transaction->user->email ?? 'N/A',
                $transaction->amount,
                $transaction->currency,
                ucfirst($transaction->status),
                $paymentMethod,
                $transaction->created_at->format('Y-m-d H:i:s')
            ];
        }

        $filename = 'transactions_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function () use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    public function getVendorTransactions(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'vendor_id' => 'required|integer|exists:vendors,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $paymentMethodIds = VendorPaymentMethod::where('vendor_id', $request->vendor_id)
            ->pluck('id');

        $transactions = PaymentTransaction::whereIn('vendor_payment_method_id', $paymentMethodIds)
            ->with(['paymentMethod.paymentGateway', 'bookable', 'user'])
            ->orderBy('created_at', 'desc')
            ->paginate($request->per_page ?? 10);

        return response()->json($transactions);
    }

    public function handlePaymentReturn(Request $request)
    {
        try {
            $txRef = $request->query('tx_ref');
            $status = $request->query('status');

            Log::info('Payment return received', [
                'tx_ref' => $txRef,
                'status' => $status,
                'all_params' => $request->all()
            ]);

            if (!$txRef) {
                return redirect(env('FRONTEND_URL') . '/payment/error?message=Missing transaction reference');
            }

            $transaction = PaymentTransaction::where('transaction_id', $txRef)->first();
            if (!$transaction) {
                Log::warning('Transaction not found for payment return', ['tx_ref' => $txRef]);
                return redirect(env('FRONTEND_URL') . '/payment/error?message=Transaction not found');
            }

            $payChanguService = new PayChanguService();
            $verificationResult = $payChanguService->verifyPayment($txRef);

            if ($verificationResult['status'] && isset($verificationResult['data']['status'])) {
                $paymentStatus = $verificationResult['data']['status'];

                if ($transaction->status !== $paymentStatus) {
                    $oldStatus = $transaction->status;
                    $newStatus = $paymentStatus === 'success' ? 'completed' : ($paymentStatus === 'failed' ? 'failed' : 'pending');

                    $transaction->status = $newStatus;
                    if ($newStatus === 'completed') {
                        $transaction->payment_date = now();
                    }
                    $transaction->gateway_response = array_merge(
                        $transaction->gateway_response ?? [],
                        ['return_verification' => $verificationResult['data']]
                    );
                    $transaction->save();

                    if ($newStatus === 'completed') {
                        $this->handleSuccessfulPayment($transaction);
                    }

                    event(new \App\Events\PaymentStatusUpdated($transaction, $oldStatus, $newStatus));
                }

                if ($paymentStatus === 'success') {
                    return redirect(env('FRONTEND_URL') . '/payment/success?tx_ref=' . $txRef);
                } else {
                    return redirect(env('FRONTEND_URL') . '/payment/failed?tx_ref=' . $txRef);
                }
            } else {
                Log::error('Payment verification failed on return', [
                    'tx_ref' => $txRef,
                    'verification_result' => $verificationResult
                ]);
                return redirect(env('FRONTEND_URL') . '/payment/error?message=Payment verification failed');
            }

        } catch (\Exception $e) {
            Log::error('Payment return processing failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return redirect(env('FRONTEND_URL') . '/payment/error?message=Payment processing failed');
        }
    }

    public function handleWebhook(Request $request): JsonResponse
    {
        try {
            $signature = $request->header('Signature');
            $payload = $request->getContent();

            Log::info('PayChangu webhook received', [
                'headers' => $request->headers->all(),
                'payload' => $payload,
                'signature' => $signature
            ]);

            $payChanguService = new PayChanguService();
            if (!empty(env('PAYCHANGU_WEBHOOK_SECRET'))) {
                if (!$signature) {
                    Log::warning('PayChangu webhook received without signature');
                    return response()->json(['error' => 'Missing signature'], 400);
                }

                if (!$payChanguService->verifyWebhookSignature($payload, $signature)) {
                    Log::warning('PayChangu webhook signature verification failed', [
                        'signature' => $signature,
                        'payload' => $payload
                    ]);
                    return response()->json(['error' => 'Invalid signature'], 401);
                }
            }

            $data = $request->all();
            Log::info('PayChangu webhook data', ['data' => $data]);

            if (isset($data['event_type'])) {
                switch ($data['event_type']) {
                    case 'api.charge.payment':
                    case 'checkout.payment':
                        $this->processPaymentWebhook($data);
                        break;
                    case 'api.payout':
                        $this->processPayoutWebhook($data);
                        break;
                    default:
                        Log::info('Unhandled webhook event type', ['event_type' => $data['event_type']]);
                }
            } else {
                $this->processPaymentWebhook($data);
            }

            return response()->json(['status' => 'success'], 200);
        } catch (\Exception $e) {
            Log::error('Webhook processing failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json(['error' => 'Webhook processing failed'], 500);
        }
    }

    private function processPaymentWebhook(array $data): void
    {
        $reference = $data['tx_ref'] ?? $data['ref_id'] ?? $data['reference'] ?? null;

        if (!$reference) {
            Log::warning('Payment webhook missing reference/tx_ref', ['data' => $data]);
            return;
        }

        Log::info('Processing payment webhook', [
            'reference' => $reference,
            'webhook_status' => $data['status'] ?? 'unknown',
            'event_type' => $data['event_type'] ?? 'unknown'
        ]);

        $transaction = PaymentTransaction::where('transaction_id', $reference)->first();
        if (!$transaction) {
            Log::warning('Transaction not found for webhook', ['reference' => $reference]);
            return;
        }

        $verifiedData = $data;
        $verifiedStatus = $data['status'] ?? 'unknown';

        $newStatus = match ($verifiedStatus) {
            'success' => 'completed',
            'failed' => 'failed',
            'pending' => 'pending',
            default => 'pending'
        };

        $oldStatus = $transaction->status;

        if ($oldStatus !== $newStatus) {
            $transaction->status = $newStatus;

            if ($newStatus === 'completed') {
                $transaction->payment_date = now();
            }

            $transaction->gateway_response = array_merge(
                $transaction->gateway_response ?? [],
                [
                    'webhook' => $data,
                    'verification' => $verifiedData,
                    'webhook_processed_at' => now()->toISOString()
                ]
            );
            $transaction->save();

            if ($newStatus === 'completed') {
                $this->handleSuccessfulPayment($transaction);
            }

            event(new \App\Events\PaymentStatusUpdated($transaction, $oldStatus, $newStatus));

            Log::info('Payment webhook processed successfully', [
                'reference' => $reference,
                'old_status' => $oldStatus,
                'new_status' => $newStatus,
                'verified_status' => $verifiedStatus
            ]);
        } else {
            Log::info('Payment webhook received but status unchanged', [
                'reference' => $reference,
                'current_status' => $oldStatus,
                'webhook_status' => $data['status'] ?? 'unknown',
                'verified_status' => $verifiedStatus
            ]);
        }
    }

    private function processPayoutWebhook(array $data): void
    {
        Log::info('Payout webhook received', [
            'reference' => $data['reference'] ?? 'unknown',
            'status' => $data['status'] ?? 'unknown',
            'data' => $data
        ]);
    }

    public function getPaymentStats(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            $period = $request->query('period', '30d');
            $days = match ($period) {
                '7d' => 7,
                '30d' => 30,
                '90d' => 90,
                'year' => 365,
                default => 30
            };

            // Base query for user's received transactions (from their events)
            $baseQuery = PaymentTransaction::where('payment_type', 'ticket_purchase')
                ->where('user_id', '!=', $user->id)
                ->whereExists(function ($query) use ($user) {
                    $query->select(\DB::raw(1))
                        ->from('ticket_purchases')
                        ->whereColumn('ticket_purchases.purchase_reference', 'payment_transactions.transaction_id')
                        ->whereExists(function ($eventQuery) use ($user) {
                            $eventQuery->select(\DB::raw(1))
                                ->from('events')
                                ->whereColumn('events.id', 'ticket_purchases.event_id')
                                ->where('events.user_id', $user->id);
                        });
                });

            $stats = [
                'totalRevenue' => (clone $baseQuery)->where('status', 'completed')->sum('amount'),
                'revenueGrowth' => $this->calculateUserPaymentGrowth($user, 'amount', $days),
                'totalTransactions' => (clone $baseQuery)->count(),
                'transactionsGrowth' => $this->calculateUserPaymentGrowth($user, null, $days),
                'ticketsSold' => (clone $baseQuery)->where('status', 'completed')->count(),
                'ticketsGrowth' => $this->calculateUserTicketGrowth($user, $days),
                'totalRefunds' => (clone $baseQuery)->where('status', 'refunded')->sum('amount'),
                'refundsGrowth' => $this->calculateUserPaymentGrowth($user, 'amount', $days, 'refunded'),
            ];

            return response()->json($stats);
        } catch (\Exception $e) {
            Log::error('Error fetching payment stats: ' . $e->getMessage(), [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['error' => 'Failed to fetch payment stats'], 500);
        }
    }

    public function verifyDirectChargeStatus(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'charge_id' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $payChanguService = new PayChanguService();
            $result = $payChanguService->verifyDirectCharge($request->charge_id);

            $transaction = PaymentTransaction::where('metadata->charge_id', $request->charge_id)->first();

            if (!$transaction) {
                return response()->json([
                    'status' => false,
                    'message' => 'Transaction not found for this charge ID',
                    'data' => $result['data'] ?? null
                ], 404);
            }

            if ($result['status'] && isset($result['data']['status']) && $result['data']['status'] === 'success') {
                $oldStatus = $transaction->status;

                if ($oldStatus !== 'completed') {
                    $transaction->status = 'completed';
                    $transaction->payment_date = now();
                    $transaction->gateway_response = array_merge(
                        $transaction->gateway_response ?? [],
                        ['direct_verification' => $result]
                    );
                    $transaction->save();

                    $this->handleSuccessfulPayment($transaction);

                    event(new \App\Events\PaymentStatusUpdated($transaction, $oldStatus, 'completed'));
                }

                return response()->json([
                    'status' => true,
                    'message' => 'Payment verified successfully',
                    'data' => [
                        'transaction_id' => $transaction->transaction_id,
                        'status' => 'completed',
                        'amount' => $transaction->amount,
                        'currency' => $transaction->currency,
                        'payment_type' => $transaction->payment_type
                    ]
                ]);
            }

            return response()->json([
                'status' => false,
                'message' => 'Payment verification failed or payment is still pending',
                'data' => [
                    'transaction_id' => $transaction->transaction_id,
                    'status' => $transaction->status,
                    'payment_status' => $result['data']['status'] ?? 'unknown',
                    'amount' => $transaction->amount,
                    'currency' => $transaction->currency
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Direct charge verification failed', [
                'error' => $e->getMessage(),
                'charge_id' => $request->charge_id,
            ]);

            return response()->json([
                'status' => false,
                'message' => 'Payment verification error',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function processRefund(Request $request, $transactionId): JsonResponse
    {
        try {
            $transaction = PaymentTransaction::findOrFail($transactionId);

            if ($transaction->status !== 'completed') {
                return response()->json(['error' => 'Transaction cannot be refunded'], 400);
            }

            $transaction->status = 'refunded';
            $transaction->refunded_at = now();
            $transaction->save();

            if ($transaction->bookable_type === VendorBooking::class) {
                $booking = $transaction->bookable;
                $booking->is_paid = false;
                $booking->status = 'cancelled';
                $booking->save();
            }

            return response()->json([
                'message' => 'Refund processed successfully',
                'transaction' => $transaction
            ]);
        } catch (\Exception $e) {
            Log::error('Error processing refund: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to process refund'], 500);
        }
    }

    public function downloadReceipt(Request $request, $transactionId)
    {
        try {
            $transaction = PaymentTransaction::with(['paymentMethod.paymentGateway', 'bookable', 'user.currency'])->findOrFail($transactionId);

            $paymentMethodName = 'N/A';
            if ($transaction->paymentMethod && $transaction->paymentMethod->paymentGateway) {
                $paymentMethodName = $transaction->paymentMethod->paymentGateway->name;
            }

            $paymentDate = $transaction->payment_date ? $transaction->payment_date->format('F d, Y h:i A') : 'N/A';

            $user = $transaction->user;
            $currencyCode = $this->getUserCurrencyCode($user);
            $currencySymbol = $this->getCurrencySymbol($currencyCode);

            $formattedAmount = $this->formatCurrency($transaction->amount, $user);

            $eventName = 'N/A';
            $ticketDetails = [];

            if ($transaction->payment_type === 'ticket_purchase' && $transaction->bookable) {
                if (method_exists($transaction->bookable, 'event') && $transaction->bookable->event) {
                    $eventName = $transaction->bookable->event->title ?? 'N/A';
                }

                if (method_exists($transaction->bookable, 'ticket') && $transaction->bookable->ticket) {
                    $ticketDetails = [
                        'name' => $transaction->bookable->ticket->name ?? 'N/A',
                        'price' => $transaction->bookable->ticket->price ?? 0,
                        'quantity' => $transaction->bookable->quantity ?? 1,
                        'formatted_price' => $this->formatAmount($transaction->bookable->ticket->price ?? 0, $user)
                    ];
                }
            }

            $pdf = PDF::loadView('pdf.receipt', [
                'transaction' => $transaction,
                'transaction_id' => $transaction->transaction_id,
                'amount' => $formattedAmount,
                'currency_code' => $currencyCode,
                'currency_symbol' => $currencySymbol,
                'status' => ucfirst($transaction->status),
                'payment_date' => $paymentDate,
                'payment_method' => $paymentMethodName,
                'payment_type' => ucfirst(str_replace('_', ' ', $transaction->payment_type)),
                'user_name' => $transaction->user->name ?? 'N/A',
                'user_email' => $transaction->user->email ?? 'N/A',
                'event_name' => $eventName,
                'ticket_details' => $ticketDetails,
                'company_name' => env('APP_NAME', 'EventaHub'),
                'company_address' => env('COMPANY_ADDRESS', 'Lilongwe, Malawi'),
                'company_email' => env('COMPANY_EMAIL', '<EMAIL>'),
                'company_phone' => env('COMPANY_PHONE', '+*********** 999'),
            ]);

            $pdf->setPaper('a4', 'portrait');

            return $pdf->download("receipt-{$transaction->transaction_id}.pdf");

        } catch (\Exception $e) {
            Log::error('Error downloading receipt: ' . $e->getMessage(), [
                'transaction_id' => $transactionId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['error' => 'Failed to download receipt'], 500);
        }
    }

    private function calculatePaymentGrowth(?string $sumColumn = null, int $days = 30, ?string $status = null): float
    {
        $query = PaymentTransaction::query();

        if ($status) {
            $query->where('status', $status);
        }

        $currentPeriod = clone $query;
        $previousPeriod = clone $query;

        if ($sumColumn) {
            $current = $currentPeriod->whereBetween('created_at', [now()->subDays($days), now()])->sum($sumColumn);
            $previous = $previousPeriod->whereBetween('created_at', [now()->subDays($days * 2), now()->subDays($days)])->sum($sumColumn);
        } else {
            $current = $currentPeriod->whereBetween('created_at', [now()->subDays($days), now()])->count();
            $previous = $previousPeriod->whereBetween('created_at', [now()->subDays($days * 2), now()->subDays($days)])->count();
        }

        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }

        return round((($current - $previous) / $previous) * 100, 1);
    }

    private function calculateTicketGrowth(int $days = 30): float
    {
        $query = PaymentTransaction::where('status', 'completed')
            ->where('payment_type', 'ticket_purchase');

        $currentPeriod = clone $query;
        $previousPeriod = clone $query;

        $current = $currentPeriod->whereBetween('created_at', [now()->subDays($days), now()])->count();
        $previous = $previousPeriod->whereBetween('created_at', [now()->subDays($days * 2), now()->subDays($days)])->count();

        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }

        return round((($current - $previous) / $previous) * 100, 1);
    }

    private function calculateUserPaymentGrowth($user, ?string $sumColumn = null, int $days = 30, ?string $status = null): float
    {
        $baseQuery = PaymentTransaction::where('payment_type', 'ticket_purchase')
            ->where('user_id', '!=', $user->id)
            ->whereExists(function ($query) use ($user) {
                $query->select(\DB::raw(1))
                    ->from('ticket_purchases')
                    ->whereColumn('ticket_purchases.purchase_reference', 'payment_transactions.transaction_id')
                    ->whereExists(function ($eventQuery) use ($user) {
                        $eventQuery->select(\DB::raw(1))
                            ->from('events')
                            ->whereColumn('events.id', 'ticket_purchases.event_id')
                            ->where('events.user_id', $user->id);
                    });
            });

        if ($status) {
            $baseQuery->where('status', $status);
        }

        $currentPeriod = clone $baseQuery;
        $previousPeriod = clone $baseQuery;

        if ($sumColumn) {
            $current = $currentPeriod->whereBetween('created_at', [now()->subDays($days), now()])->sum($sumColumn);
            $previous = $previousPeriod->whereBetween('created_at', [now()->subDays($days * 2), now()->subDays($days)])->sum($sumColumn);
        } else {
            $current = $currentPeriod->whereBetween('created_at', [now()->subDays($days), now()])->count();
            $previous = $previousPeriod->whereBetween('created_at', [now()->subDays($days * 2), now()->subDays($days)])->count();
        }

        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }

        return round((($current - $previous) / $previous) * 100, 1);
    }

    private function calculateUserTicketGrowth($user, int $days = 30): float
    {
        $baseQuery = PaymentTransaction::where('status', 'completed')
            ->where('payment_type', 'ticket_purchase')
            ->where('user_id', '!=', $user->id)
            ->whereExists(function ($query) use ($user) {
                $query->select(\DB::raw(1))
                    ->from('ticket_purchases')
                    ->whereColumn('ticket_purchases.purchase_reference', 'payment_transactions.transaction_id')
                    ->whereExists(function ($eventQuery) use ($user) {
                        $eventQuery->select(\DB::raw(1))
                            ->from('events')
                            ->whereColumn('events.id', 'ticket_purchases.event_id')
                            ->where('events.user_id', $user->id);
                    });
            });

        $currentPeriod = clone $baseQuery;
        $previousPeriod = clone $baseQuery;

        $current = $currentPeriod->whereBetween('created_at', [now()->subDays($days), now()])->count();
        $previous = $previousPeriod->whereBetween('created_at', [now()->subDays($days * 2), now()->subDays($days)])->count();

        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }

        return round((($current - $previous) / $previous) * 100, 1);
    }
}
