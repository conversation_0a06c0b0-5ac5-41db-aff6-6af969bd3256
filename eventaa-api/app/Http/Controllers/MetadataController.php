<?php

namespace App\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use <PERSON>bauman\Location\Facades\Location;

class MetadataController extends Controller
{
    public function getLocationDetails(Request $request): JsonResponse
    {
        $location = Location::get($request->ip);

        if ($location) {
            $response = Http::get("https://restcountries.com/v3.1/alpha/{$location->countryCode}");
            $data = $response->json();

            return response()->json([
                'location' => $location,
                'currency' => $data[0]['currencies'],
                'flag' => $data[0]['flag'],
            ]);
        }

        return response()->json([
            'location' => null,
            'currency' => 'MWK',
            'flag' => 'mw',
        ]);
    }
}
