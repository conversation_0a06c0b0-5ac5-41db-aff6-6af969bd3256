<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\PaymentGateway;
use App\Services\PayChanguService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class PaymentGatewayController extends Controller
{
    /**
     * Get all payment gateways
     */
    public function index(): JsonResponse
    {
        $gateways = PaymentGateway::all();
        
        return response()->json([
            'message' => 'Payment gateways retrieved successfully',
            'data' => $gateways
        ]);
    }

    /**
     * Get PayChangu gateway configuration
     */
    public function getPayChanguConfig(): JsonResponse
    {
        $gateway = PaymentGateway::where('slug', 'paychangu')->first();
        
        if (!$gateway) {
            return response()->json([
                'message' => 'PayChangu gateway not found'
            ], 404);
        }

        // Hide sensitive information
        $config = $gateway->config;
        if (isset($config['api_key'])) {
            $config['api_key'] = $this->maskSensitiveData($config['api_key']);
        }
        if (isset($config['secret_key'])) {
            $config['secret_key'] = $this->maskSensitiveData($config['secret_key']);
        }
        if (isset($config['webhook_secret'])) {
            $config['webhook_secret'] = $this->maskSensitiveData($config['webhook_secret']);
        }

        $gateway->config = $config;

        return response()->json([
            'message' => 'PayChangu configuration retrieved successfully',
            'data' => $gateway
        ]);
    }

    /**
     * Update PayChangu gateway configuration
     */
    public function updatePayChanguConfig(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'api_key' => 'sometimes|string|min:10',
            'secret_key' => 'sometimes|string|min:10',
            'merchant_id' => 'sometimes|string|min:5',
            'webhook_secret' => 'sometimes|string|min:10',
            'test_mode' => 'sometimes|boolean',
            'is_active' => 'sometimes|boolean',
            'payment_methods' => 'sometimes|array',
            'payment_methods.*.is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $gateway = PaymentGateway::where('slug', 'paychangu')->first();
        
        if (!$gateway) {
            return response()->json([
                'message' => 'PayChangu gateway not found'
            ], 404);
        }

        try {
            $config = $gateway->config;

            // Update configuration fields
            if ($request->has('api_key') && !$this->isMaskedData($request->api_key)) {
                $config['api_key'] = $request->api_key;
            }
            if ($request->has('secret_key') && !$this->isMaskedData($request->secret_key)) {
                $config['secret_key'] = $request->secret_key;
            }
            if ($request->has('merchant_id')) {
                $config['merchant_id'] = $request->merchant_id;
            }
            if ($request->has('webhook_secret') && !$this->isMaskedData($request->webhook_secret)) {
                $config['webhook_secret'] = $request->webhook_secret;
            }
            if ($request->has('payment_methods')) {
                $config['payment_methods'] = array_merge(
                    $config['payment_methods'] ?? [],
                    $request->payment_methods
                );
            }

            $gateway->config = $config;
            
            if ($request->has('test_mode')) {
                $gateway->test_mode = $request->test_mode;
            }
            if ($request->has('is_active')) {
                $gateway->is_active = $request->is_active;
            }

            $gateway->save();

            return response()->json([
                'message' => 'PayChangu configuration updated successfully',
                'data' => $gateway
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update PayChangu configuration', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'message' => 'Failed to update configuration',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test PayChangu connection
     */
    public function testPayChanguConnection(): JsonResponse
    {
        try {
            $gateway = PaymentGateway::where('slug', 'paychangu')->first();
            
            if (!$gateway) {
                return response()->json([
                    'message' => 'PayChangu gateway not found'
                ], 404);
            }

            $payChanguService = new PayChanguService($gateway);
            
            // Test by getting mobile money operators
            $result = $payChanguService->getMobileMoneyOperators();

            if ($result['status']) {
                return response()->json([
                    'message' => 'PayChangu connection successful',
                    'data' => [
                        'status' => 'connected',
                        'test_mode' => $gateway->test_mode,
                        'operators' => $result['data'] ?? []
                    ]
                ]);
            }

            return response()->json([
                'message' => 'PayChangu connection failed',
                'data' => [
                    'status' => 'failed',
                    'error' => $result['message'] ?? 'Unknown error'
                ]
            ], 400);
        } catch (\Exception $e) {
            Log::error('PayChangu connection test failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'message' => 'Connection test failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get payment gateway statistics
     */
    public function getStatistics(): JsonResponse
    {
        try {
            $stats = [
                'total_transactions' => \App\Models\PaymentTransaction::count(),
                'successful_transactions' => \App\Models\PaymentTransaction::where('status', 'completed')->count(),
                'failed_transactions' => \App\Models\PaymentTransaction::where('status', 'failed')->count(),
                'pending_transactions' => \App\Models\PaymentTransaction::where('status', 'pending')->count(),
                'total_amount' => \App\Models\PaymentTransaction::where('status', 'completed')->sum('amount'),
                'transactions_by_type' => \App\Models\PaymentTransaction::selectRaw('payment_type, COUNT(*) as count, SUM(amount) as total_amount')
                    ->where('status', 'completed')
                    ->groupBy('payment_type')
                    ->get(),
                'recent_transactions' => \App\Models\PaymentTransaction::with(['user:id,name,email'])
                    ->latest()
                    ->limit(10)
                    ->get(),
            ];

            return response()->json([
                'message' => 'Payment statistics retrieved successfully',
                'data' => $stats
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get payment statistics', [
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'message' => 'Failed to retrieve statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mask sensitive data for display
     */
    private function maskSensitiveData(string $data): string
    {
        if (strlen($data) <= 8) {
            return str_repeat('*', strlen($data));
        }
        
        return substr($data, 0, 4) . str_repeat('*', strlen($data) - 8) . substr($data, -4);
    }

    /**
     * Check if data is masked
     */
    private function isMaskedData(string $data): bool
    {
        return strpos($data, '*') !== false;
    }
}
