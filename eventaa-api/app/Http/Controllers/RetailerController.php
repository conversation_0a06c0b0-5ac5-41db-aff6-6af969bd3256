<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Models\Retailer;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class RetailerController extends Controller
{
    public function index(Request $request) : JsonResource
    {
        return JsonResource::collection(Retailer::all());
    }

    public function create(Request $request) : JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string',
            'email' => 'required|email',
            'phone' => 'required|string',
            'logo' => 'required',
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'address' => 'required|string',
            'city' => 'required|string',
            'country' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $logo = 'retailer.png';

        if ($request->file('logo')) {
            $image = $request->file('logo');
            $logo = time() . '.' . $image->getClientOriginalExtension();
            $image->move(public_path('storage/logos'), $logo);
        }

        Retailer::create([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'logo' => $logo,
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
            'address' => $request->address,
            'city' => $request->city,
            'country' => $request->country,
        ]);

        return response()->json([
            "message" => 'Retailer created successfully',
        ], 200);
    }

    public function update(Request $request, $id) : JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string',
            'email' => 'required|email',
            'phone' => 'required|string',
            'logo' => 'required',
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'address' => 'required|string',
            'city' => 'required|string',
            'country' => 'required|string',
        ]);
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $retailer = Retailer::findOrFail($id);
        $logo = $retailer->logo;
        if ($request->file('logo')) {
            $image = $request->file('logo');
            $logo = time() . '.' . $image->getClientOriginalExtension();
            $image->move(public_path('storage/logos'), $logo);
        }
        $retailer->update([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'logo' => $logo,
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
            'address' => $request->address,
            'city' => $request->city,
            'country' => $request->country,
        ]);
        return response()->json([
            "message" => 'Retailer updated successfully',
        ], 201);
    }

    public function delete($id) : JsonResponse{
        $retailer = Retailer::findOrFail($id);
        $retailer->delete();
        return response()->json([
            "message" => 'Retailer deleted successfully',
        ], 201);
    }
}
