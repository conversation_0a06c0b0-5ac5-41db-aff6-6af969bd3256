<?php

namespace App\Http\Controllers;

use App\Models\Subscription;
use App\Models\PaymentTransaction;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;

class SubscriptionPaymentController extends Controller
{
    /**
     * Get all subscription payments
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function get(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $perPage = $request->query('per_page', 10);

            $payments = PaymentTransaction::where('type', 'subscription')
                ->when($user->hasRole('admin'), function ($query) {
                    return $query;
                }, function ($query) use ($user) {
                    return $query->where('user_id', $user->id);
                })
                ->with('user')
                ->orderBy('created_at', 'desc')
                ->paginate($perPage);

            return response()->json([
                'status' => 'success',
                'data' => $payments
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting subscription payments: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve subscription payments: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a new subscription payment
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function create(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'subscription_id' => 'required|exists:subscriptions,id',
                'payment_method' => 'required|string',
                'amount' => 'required|numeric',
                'currency' => 'required|string|size:3',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();
            $subscription = Subscription::findOrFail($request->subscription_id);

            // Create payment transaction
            $payment = new PaymentTransaction();
            $payment->user_id = $user->id;
            $payment->reference = 'SUB-' . strtoupper(substr(md5(uniqid()), 0, 8));
            $payment->type = 'subscription';
            $payment->amount = $request->amount;
            $payment->currency = $request->currency;
            $payment->payment_method = $request->payment_method;
            $payment->status = 'pending';
            $payment->metadata = json_encode([
                'subscription_id' => $subscription->id,
                'subscription_name' => $subscription->name,
                'period' => $subscription->billing_cycle
            ]);
            $payment->save();

            // Logic to process payment would go here
            // This would typically involve calling a payment gateway service

            return response()->json([
                'status' => 'success',
                'message' => 'Subscription payment initiated',
                'data' => $payment
            ]);
        } catch (\Exception $e) {
            Log::error('Error creating subscription payment: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to create subscription payment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Retry a failed subscription payment
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function retry(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'payment_id' => 'required|exists:payment_transactions,id',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();
            $payment = PaymentTransaction::findOrFail($request->payment_id);

            // Check if payment belongs to user or user is admin
            if ($payment->user_id != $user->id && !$user->hasRole('admin')) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Unauthorized to retry this payment'
                ], 403);
            }

            // Check if payment is in a state that can be retried
            if (!in_array($payment->status, ['failed', 'declined', 'expired'])) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'This payment cannot be retried'
                ], 400);
            }

            // Update payment status
            $payment->status = 'pending';
            $payment->updated_at = now();
            $payment->save();

            // Logic to process payment would go here
            // This would typically involve calling a payment gateway service

            return response()->json([
                'status' => 'success',
                'message' => 'Subscription payment retry initiated',
                'data' => $payment
            ]);
        } catch (\Exception $e) {
            Log::error('Error retrying subscription payment: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retry subscription payment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Cancel a subscription payment
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function cancel(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'payment_id' => 'required|exists:payment_transactions,id',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();
            $payment = PaymentTransaction::findOrFail($request->payment_id);

            // Check if payment belongs to user or user is admin
            if ($payment->user_id != $user->id && !$user->hasRole('admin')) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Unauthorized to cancel this payment'
                ], 403);
            }

            // Check if payment is in a state that can be cancelled
            if (!in_array($payment->status, ['pending', 'processing'])) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'This payment cannot be cancelled'
                ], 400);
            }

            // Update payment status
            $payment->status = 'cancelled';
            $payment->updated_at = now();
            $payment->save();

            return response()->json([
                'status' => 'success',
                'message' => 'Subscription payment cancelled successfully',
                'data' => $payment
            ]);
        } catch (\Exception $e) {
            Log::error('Error cancelling subscription payment: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to cancel subscription payment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get subscription payment statistics
     *
     * @return JsonResponse
     */
    public function stats(): JsonResponse
    {
        try {
            $user = Auth::user();

            // Base query
            $query = PaymentTransaction::where('type', 'subscription');

            // Filter by user unless admin
            if (!$user->hasRole('admin')) {
                $query->where('user_id', $user->id);
            }

            // Get stats
            $totalPayments = $query->count();
            $totalAmount = $query->sum('amount');
            $successfulPayments = $query->clone()->where('status', 'completed')->count();
            $pendingPayments = $query->clone()->whereIn('status', ['pending', 'processing'])->count();
            $failedPayments = $query->clone()->whereIn('status', ['failed', 'declined', 'expired'])->count();

            // Get monthly breakdown
            $monthlyData = $query->selectRaw('YEAR(created_at) as year, MONTH(created_at) as month, COUNT(*) as count, SUM(amount) as total')
                ->groupBy('year', 'month')
                ->orderBy('year', 'desc')
                ->orderBy('month', 'desc')
                ->limit(12)
                ->get();

            return response()->json([
                'status' => 'success',
                'data' => [
                    'total_payments' => $totalPayments,
                    'total_amount' => $totalAmount,
                    'successful_payments' => $successfulPayments,
                    'pending_payments' => $pendingPayments,
                    'failed_payments' => $failedPayments,
                    'monthly_data' => $monthlyData
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting subscription payment stats: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve subscription payment statistics: ' . $e->getMessage()
            ], 500);
        }
    }
}
