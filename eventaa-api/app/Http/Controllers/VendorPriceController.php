<?php

namespace App\Http\Controllers;

use App\Models\Vendor;
use App\Models\VendorPrice;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class VendorPriceController extends Controller
{
    /**
     * Get all prices for a vendor
     *
     * @param int $vendorId
     * @return JsonResponse
     */
    public function index($vendorId): JsonResponse
    {
        $vendor = Vendor::find($vendorId);
        
        if (!$vendor) {
            return response()->json(['message' => 'Vendor not found'], 404);
        }
        
        $prices = VendorPrice::with(['service', 'currency'])
            ->where('vendor_id', $vendorId)
            ->get();
            
        return response()->json(['data' => $prices]);
    }

    /**
     * Store a new price package
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'vendor_id' => 'required|exists:vendors,id',
            'service_id' => 'required|exists:vendor_services,id',
            'price' => 'required|numeric|min:0',
            'currency_id' => 'required|exists:currencies,id',
            'name' => 'required|string|max:255',
            'duration' => 'required|string|max:50',
            'description' => 'nullable|string',
            'active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }


        $vendor = Vendor::find($request->vendor_id);
        if (!$vendor || $vendor->user_id !== $request->user()->id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $price = VendorPrice::create([
            'vendor_id' => $request->vendor_id,
            'service_id' => $request->service_id,
            'price' => $request->price,
            'currency_id' => $request->currency_id,
            'name' => $request->name,
            'duration' => $request->duration,
            'description' => $request->description,
            'active' => $request->active ?? true,
        ]);

        return response()->json([
            'message' => 'Price package created successfully',
            'data' => $price->load(['service', 'currency'])
        ], 201);
    }

    /**
     * Display the specified price package
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show($id): JsonResponse
    {
        $price = VendorPrice::with(['service', 'currency'])->find($id);
        
        if (!$price) {
            return response()->json(['message' => 'Price package not found'], 404);
        }
        
        return response()->json(['data' => $price]);
    }

    /**
     * Update the specified price package
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(Request $request, $id): JsonResponse
    {
        $price = VendorPrice::find($id);
        
        if (!$price) {
            return response()->json(['message' => 'Price package not found'], 404);
        }
        
        $vendor = Vendor::find($price->vendor_id);
        if (!$vendor || $vendor->user_id !== $request->user()->id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }
        
        $validator = Validator::make($request->all(), [
            'service_id' => 'sometimes|required|exists:vendor_services,id',
            'price' => 'sometimes|required|numeric|min:0',
            'currency_id' => 'sometimes|required|exists:currencies,id',
            'name' => 'sometimes|required|string|max:255',
            'duration' => 'sometimes|required|string|max:50',
            'description' => 'nullable|string',
            'active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }
        
        $price->update($request->all());
        
        return response()->json([
            'message' => 'Price package updated successfully',
            'data' => $price->fresh(['service', 'currency'])
        ]);
    }

    /**
     * Remove the specified price package
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(Request $request, $id): JsonResponse
    {
        $price = VendorPrice::find($id);
        
        if (!$price) {
            return response()->json(['message' => 'Price package not found'], 404);
        }
        
        $vendor = Vendor::find($price->vendor_id);
        if (!$vendor || $vendor->user_id !== $request->user()->id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }
        
        $price->delete();
        
        return response()->json(['message' => 'Price package deleted successfully']);
    }
}
