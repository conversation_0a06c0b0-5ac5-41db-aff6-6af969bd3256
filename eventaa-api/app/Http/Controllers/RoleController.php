<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RoleController extends Controller
{
    /**
     * Get all roles
     *
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $roles = Role::with('permissions')->get();

        return response()->json([
            'roles' => $roles
        ], 200);
    }

    /**
     * Get a specific role by ID
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        $role = Role::with('permissions')->findOrFail($id);

        return response()->json([
            'role' => $role
        ], 200);
    }

    /**
     * Create a new role
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|unique:roles,name',
            'permissions' => 'nullable|array',
            'permissions.*' => 'exists:permissions,name'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }

        $role = Role::create(['name' => $request->name, 'guard_name' => 'web']);

        if ($request->has('permissions')) {
            // Get permissions with the correct guard
            $permissions = Permission::whereIn('name', $request->permissions)
                ->where('guard_name', 'web')
                ->get();
            $role->syncPermissions($permissions);
        }

        return response()->json([
            'message' => 'Role created successfully',
            'role' => $role->load('permissions')
        ], 201);
    }

    /**
     * Update a role
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(Request $request, int $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|unique:roles,name,' . $id,
            'permissions' => 'nullable|array',
            'permissions.*' => 'exists:permissions,name'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }

        $role = Role::findOrFail($id);

        if ($request->has('name')) {
            $role->name = $request->name;
            $role->save();
        }

        if ($request->has('permissions')) {
            // Get permissions with the correct guard
            $permissions = Permission::whereIn('name', $request->permissions)
                ->where('guard_name', 'web')
                ->get();
            $role->syncPermissions($permissions);
        }

        return response()->json([
            'message' => 'Role updated successfully',
            'role' => $role->load('permissions')
        ], 200);
    }

    /**
     * Delete a role
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        $role = Role::findOrFail($id);

        // Don't allow deletion of core roles
        if (in_array($role->name, ['admin', 'user', 'host', 'vendor'])) {
            return response()->json([
                'error' => 'Cannot delete core system roles'
            ], 403);
        }

        $role->delete();

        return response()->json([
            'message' => 'Role deleted successfully'
        ], 200);
    }

    /**
     * Assign permissions to a role
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function assignPermissions(Request $request, int $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'permissions' => 'required|array',
            'permissions.*' => 'exists:permissions,name'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }

        $role = Role::findOrFail($id);

        // Get permissions with the correct guard
        $permissions = Permission::whereIn('name', $request->permissions)
            ->where('guard_name', 'web')
            ->get();

        $role->syncPermissions($permissions);

        return response()->json([
            'message' => 'Permissions assigned successfully',
            'role' => $role->load('permissions')
        ], 200);
    }

    /**
     * Get users with a specific role
     *
     * @param int $id
     * @return JsonResponse
     */
    public function users(int $id): JsonResponse
    {
        $role = Role::findOrFail($id);
        $users = $role->users()->paginate(15);

        return response()->json([
            'users' => $users
        ], 200);
    }

    public function getRolePermissions(int $id): JsonResponse
    {
        $role = Role::findOrFail($id);
        $permissions = $role->permissions;

        return response()->json([
            'permissions' => $permissions
        ], 200);
    }
}
