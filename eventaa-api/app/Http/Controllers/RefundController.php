<?php

namespace App\Http\Controllers;

use App\Models\RefundRequest;
use App\Models\TicketPurchase;
use App\Services\RefundService;
use App\Notifications\RefundStatusNotification;
use App\Notifications\EventOwnerRefundNotification;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class RefundController extends Controller
{
    protected RefundService $refundService;

    public function __construct(RefundService $refundService)
    {
        $this->refundService = $refundService;
    }

    /**
     * Get all refund requests (Admin only)
     */
    public function index(Request $request): JsonResponse
    {
        $query = RefundRequest::with([
            'user:id,name,email,currency_id',
            'user.currency:id,name',
            'ticketPurchase.event:id,title,start',
            'ticketPurchase.ticket:id,name,price',
            'payment:id,payment_method,transaction_id',
            'processedBy:id,name'
        ])->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->filled('status') && $request->status !== 'all') {
            $query->where('status', $request->status);
        }

        if ($request->filled('event_id')) {
            $query->where('event_id', $request->event_id);
        }

        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->filled('from_date')) {
            $query->whereDate('created_at', '>=', $request->from_date);
        }

        if ($request->filled('to_date')) {
            $query->whereDate('created_at', '<=', $request->to_date);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('refund_reference', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  })
                  ->orWhereHas('ticketPurchase.event', function ($eventQuery) use ($search) {
                      $eventQuery->where('title', 'like', "%{$search}%");
                  });
            });
        }

        $refunds = $query->paginate($request->per_page ?? 15);

        return response()->json([
            'message' => 'Refund requests retrieved successfully',
            'data' => $refunds
        ]);
    }

    /**
     * Show a specific refund request
     */
    public function show(Request $request, $id): JsonResponse
    {
        $refund = RefundRequest::with([
            'user.currency',
            'ticketPurchase.event',
            'ticketPurchase.ticket',
            'payment',
            'processedBy'
        ])->findOrFail($id);

        return response()->json([
            'message' => 'Refund request retrieved successfully',
            'data' => $refund
        ]);
    }

    /**
     * Approve a refund request (Admin only)
     */
    public function approve(Request $request, $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'admin_notes' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $refundRequest = RefundRequest::with(['user.currency', 'ticketPurchase', 'event', 'payment'])
            ->findOrFail($id);

        if (!$refundRequest->canBeApproved()) {
            return response()->json([
                'message' => 'Refund request cannot be approved',
                'current_status' => $refundRequest->status
            ], 400);
        }

        return DB::transaction(function () use ($refundRequest, $request) {
            $admin = $request->user();

            $refundRequest->approve($admin, $request->admin_notes);

            // Send notification to user
            $refundRequest->user->notify(new RefundStatusNotification($refundRequest));

            // Process the refund
            $result = $this->refundService->processRefund($refundRequest);

            return response()->json([
                'message' => 'Refund request approved and processed successfully',
                'data' => $refundRequest->fresh(),
                'processing_result' => $result
            ]);
        });
    }

    /**
     * Reject a refund request (Admin only)
     */
    public function reject(Request $request, $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'rejection_reason' => 'required|string|max:500',
            'admin_notes' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $refundRequest = RefundRequest::with(['user.currency', 'ticketPurchase', 'event'])
            ->findOrFail($id);

        if (!$refundRequest->canBeRejected()) {
            return response()->json([
                'message' => 'Refund request cannot be rejected',
                'current_status' => $refundRequest->status
            ], 400);
        }

        return DB::transaction(function () use ($refundRequest, $request) {
            $admin = $request->user();

            $refundRequest->reject($admin, $request->rejection_reason, $request->admin_notes);

            // Send notification to user
            $refundRequest->user->notify(new RefundStatusNotification($refundRequest));

            // Notify event owner
            $eventOwner = $refundRequest->ticketPurchase->event->user;
            if ($eventOwner && $eventOwner->id !== $refundRequest->user_id) {
                $eventOwner->notify(new EventOwnerRefundNotification($refundRequest, 'rejected'));
            }

            return response()->json([
                'message' => 'Refund request rejected successfully',
                'data' => $refundRequest->fresh()
            ]);
        });
    }

    /**
     * Get refund statistics (Admin only)
     */
    public function statistics(Request $request): JsonResponse
    {
        $filters = $request->only(['event_id', 'user_id', 'status', 'date_from', 'date_to']);
        $stats = $this->refundService->getRefundStatistics($filters);

        return response()->json([
            'message' => 'Refund statistics retrieved successfully',
            'data' => $stats
        ]);
    }

    /**
     * Bulk approve refund requests (Admin only)
     */
    public function bulkApprove(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'refund_ids' => 'required|array|min:1',
            'refund_ids.*' => 'integer|exists:refund_requests,id',
            'admin_notes' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $refundRequests = RefundRequest::with(['user.currency', 'ticketPurchase', 'event', 'payment'])
            ->whereIn('id', $request->refund_ids)
            ->where('status', 'pending')
            ->get();

        if ($refundRequests->isEmpty()) {
            return response()->json(['message' => 'No eligible refund requests found'], 400);
        }

        $results = [];
        $admin = $request->user();

        foreach ($refundRequests as $refundRequest) {
            try {
                DB::transaction(function () use ($refundRequest, $admin, $request) {
                    $refundRequest->approve($admin, $request->admin_notes);
                    $refundRequest->user->notify(new RefundStatusNotification($refundRequest));
                    $this->refundService->processRefund($refundRequest);
                });

                $results[] = [
                    'refund_id' => $refundRequest->id,
                    'refund_reference' => $refundRequest->refund_reference,
                    'status' => 'approved',
                    'message' => 'Approved and processed successfully'
                ];

            } catch (\Exception $e) {
                $results[] = [
                    'refund_id' => $refundRequest->id,
                    'refund_reference' => $refundRequest->refund_reference,
                    'status' => 'error',
                    'message' => 'Failed to process: ' . $e->getMessage()
                ];
            }
        }

        return response()->json([
            'message' => 'Bulk approval completed',
            'data' => $results
        ]);
    }

    /**
     * Export refund requests (Admin only)
     */
    public function export(Request $request): JsonResponse
    {
        $query = RefundRequest::with([
            'user:id,name,email,currency_id',
            'user.currency:id,name',
            'ticketPurchase.event:id,title,start',
            'ticketPurchase.ticket:id,name,price',
            'payment:id,payment_method,transaction_id',
            'processedBy:id,name'
        ]);

        // Apply same filters as index
        if ($request->filled('status') && $request->status !== 'all') {
            $query->where('status', $request->status);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $refunds = $query->orderBy('created_at', 'desc')->get();

        $exportData = $refunds->map(function ($refund) {
            return [
                'Refund Reference' => $refund->refund_reference,
                'Customer Name' => $refund->user->name,
                'Customer Email' => $refund->user->email,
                'Event Title' => $refund->ticketPurchase->event->title,
                'Ticket Type' => $refund->ticketPurchase->ticket->name,
                'Original Amount' => $refund->original_amount,
                'Refund Amount' => $refund->refund_amount,
                'Processing Fee' => $refund->processing_fee,
                'Status' => $refund->status_text,
                'Reason' => $refund->reason,
                'Payment Method' => $refund->payment_method,
                'Requested Date' => $refund->requested_at->format('Y-m-d H:i:s'),
                'Processed Date' => $refund->completed_at?->format('Y-m-d H:i:s') ?? 'N/A',
                'Processed By' => $refund->processedBy?->name ?? 'System',
            ];
        });

        return response()->json([
            'message' => 'Refund data exported successfully',
            'data' => $exportData,
            'total_records' => $exportData->count()
        ]);
    }

    /**
     * Get refund requests for host's events
     */
    public function hostIndex(Request $request): JsonResponse
    {
        $user = $request->user();

        $query = RefundRequest::with([
            'user:id,name,email,currency_id',
            'user.currency:id,name',
            'ticketPurchase.event:id,title,start,user_id',
            'ticketPurchase.ticket:id,name,price',
            'payment:id,payment_method,transaction_id',
            'processedBy:id,name'
        ])
        ->whereHas('ticketPurchase.event', function ($query) use ($user) {
            $query->where('user_id', $user->id);
        })
        ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->filled('status') && $request->status !== 'all') {
            $query->where('status', $request->status);
        }

        if ($request->filled('event_id')) {
            $query->whereHas('ticketPurchase.event', function ($eventQuery) use ($request) {
                $eventQuery->where('id', $request->event_id);
            });
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('refund_reference', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  })
                  ->orWhereHas('ticketPurchase.event', function ($eventQuery) use ($search) {
                      $eventQuery->where('title', 'like', "%{$search}%");
                  });
            });
        }

        $perPage = $request->get('per_page', 10);
        $refunds = $query->paginate($perPage);

        return response()->json([
            'message' => 'Host refund requests retrieved successfully',
            'data' => $refunds
        ]);
    }

    /**
     * Show specific refund request for host
     */
    public function hostShow(Request $request, $id): JsonResponse
    {
        $user = $request->user();

        $refundRequest = RefundRequest::with([
            'user:id,name,email,currency_id',
            'user.currency:id,name',
            'ticketPurchase.event:id,title,start,user_id',
            'ticketPurchase.ticket:id,name,price',
            'payment:id,payment_method,transaction_id',
            'processedBy:id,name'
        ])
        ->whereHas('ticketPurchase.event', function ($query) use ($user) {
            $query->where('user_id', $user->id);
        })
        ->findOrFail($id);

        return response()->json([
            'message' => 'Refund request retrieved successfully',
            'data' => $refundRequest
        ]);
    }

    /**
     * Approve refund request (Host)
     */
    public function hostApprove(Request $request, $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'admin_notes' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = $request->user();

        $refundRequest = RefundRequest::with(['user.currency', 'ticketPurchase', 'payment'])
            ->whereHas('ticketPurchase.event', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->findOrFail($id);

        if ($refundRequest->status !== 'pending') {
            return response()->json([
                'message' => 'Refund request cannot be approved',
                'current_status' => $refundRequest->status
            ], 400);
        }

        return DB::transaction(function () use ($refundRequest, $request, $user) {
            $refundRequest->update([
                'status' => 'approved',
                'processed_by' => $user->id,
                'admin_notes' => $request->admin_notes,
                'processed_at' => now()
            ]);

            // Send notification to user
            $refundRequest->user->notify(new \App\Notifications\RefundStatusNotification($refundRequest));

            return response()->json([
                'message' => 'Refund request approved successfully',
                'data' => $refundRequest->fresh()
            ]);
        });
    }

    /**
     * Reject refund request (Host)
     */
    public function hostReject(Request $request, $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'rejection_reason' => 'required|string|max:500',
            'admin_notes' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = $request->user();

        $refundRequest = RefundRequest::with(['user.currency', 'ticketPurchase'])
            ->whereHas('ticketPurchase.event', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->findOrFail($id);

        if ($refundRequest->status !== 'pending') {
            return response()->json([
                'message' => 'Refund request cannot be rejected',
                'current_status' => $refundRequest->status
            ], 400);
        }

        return DB::transaction(function () use ($refundRequest, $request, $user) {
            $refundRequest->update([
                'status' => 'rejected',
                'processed_by' => $user->id,
                'rejection_reason' => $request->rejection_reason,
                'admin_notes' => $request->admin_notes,
                'processed_at' => now()
            ]);

            // Send notification to user
            $refundRequest->user->notify(new \App\Notifications\RefundStatusNotification($refundRequest));

            return response()->json([
                'message' => 'Refund request rejected successfully',
                'data' => $refundRequest->fresh()
            ]);
        });
    }

    /**
     * Get refund statistics for host's events
     */
    public function hostStatistics(Request $request): JsonResponse
    {
        $user = $request->user();

        $baseQuery = RefundRequest::whereHas('ticketPurchase.event', function ($query) use ($user) {
            $query->where('user_id', $user->id);
        });

        if ($request->filled('date_from')) {
            $baseQuery->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $baseQuery->whereDate('created_at', '<=', $request->date_to);
        }

        if ($request->filled('event_id')) {
            $baseQuery->whereHas('ticketPurchase.event', function ($eventQuery) use ($request) {
                $eventQuery->where('id', $request->event_id);
            });
        }

        $stats = [
            'total_requests' => (clone $baseQuery)->count(),
            'pending_requests' => (clone $baseQuery)->where('status', 'pending')->count(),
            'approved_requests' => (clone $baseQuery)->where('status', 'approved')->count(),
            'rejected_requests' => (clone $baseQuery)->where('status', 'rejected')->count(),
            'completed_requests' => (clone $baseQuery)->where('status', 'completed')->count(),
            'total_refund_amount' => (clone $baseQuery)->where('status', 'completed')->sum('refund_amount'),
            'total_processing_fees' => (clone $baseQuery)->where('status', 'completed')->sum('processing_fee'),
        ];

        return response()->json([
            'message' => 'Host refund statistics retrieved successfully',
            'data' => $stats
        ]);
    }

    /**
     * Export refund requests for host's events
     */
    public function hostExport(Request $request)
    {
        $user = $request->user();

        $query = RefundRequest::with([
            'user:id,name,email',
            'ticketPurchase.event:id,title',
            'ticketPurchase.ticket:id,name,price',
            'processedBy:id,name'
        ])
        ->whereHas('ticketPurchase.event', function ($query) use ($user) {
            $query->where('user_id', $user->id);
        });

        if ($request->filled('status') && $request->status !== 'all') {
            $query->where('status', $request->status);
        }

        if ($request->filled('event_id')) {
            $query->whereHas('ticketPurchase.event', function ($eventQuery) use ($request) {
                $eventQuery->where('id', $request->event_id);
            });
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $refunds = $query->orderBy('created_at', 'desc')->get();

        $csvData = [];
        $csvData[] = [
            'Refund Reference',
            'Customer Name',
            'Customer Email',
            'Event Title',
            'Ticket Type',
            'Original Amount',
            'Refund Amount',
            'Processing Fee',
            'Status',
            'Reason',
            'Requested Date',
            'Processed Date',
            'Processed By'
        ];

        foreach ($refunds as $refund) {
            $csvData[] = [
                $refund->refund_reference,
                $refund->user->name,
                $refund->user->email,
                $refund->ticketPurchase->event->title,
                $refund->ticketPurchase->ticket->name,
                $refund->original_amount,
                $refund->refund_amount,
                $refund->processing_fee,
                $refund->status,
                $refund->reason,
                $refund->requested_at->format('Y-m-d H:i:s'),
                $refund->processed_at?->format('Y-m-d H:i:s') ?? 'N/A',
                $refund->processedBy?->name ?? 'System',
            ];
        }

        $filename = 'refunds-' . now()->format('Y-m-d-H-i-s') . '.csv';

        return response()->streamDownload(function () use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        }, $filename, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"'
        ]);
    }
}
