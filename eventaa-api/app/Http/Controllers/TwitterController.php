<?php

namespace App\Http\Controllers;

use <PERSON>\TwitterOAuth\TwitterOAuth;
use App\Models\CachedTweet;
use Illuminate\Http\Request;

class TwitterController extends Controller
{
    public function fetchTweetsByHashtag(Request $request)
    {
        $hashtag = $request->input('hashtag');
        $limit = $request->input('limit', 20);

        if (empty($hashtag)) {
            return response()->json(['error' => 'Hashtag is required'], 400);
        }

        $tweets = CachedTweet::orderBy('tweet_created_at', 'desc')->take($limit)->get();

        if ($tweets->count() == 0 || $tweets->first()->created_at->diffInHours() > 1) {
            dispatch(function () use ($hashtag) {
                \Illuminate\Support\Facades\Artisan::call('tweets:fetch', [
                    'hashtag' => $hashtag
                ]);
            })->onQueue('low');
        }

        return response()->json([
            'data' => $tweets,
            'cached_at' => $tweets->first()?->created_at,
            'count' => $tweets->count()
        ]);
    }
}
