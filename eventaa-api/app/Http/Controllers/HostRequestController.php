<?php

namespace App\Http\Controllers;

use App\Models\HostRequest;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class HostRequestController extends Controller
{
    /**
     * Submit a host request
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'reason' => 'required|string|min:10|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = $request->user();

        // Check if user already has host role
        if ($user->hasRole('host')) {
            return response()->json([
                'message' => 'You already have host privileges'
            ], 400);
        }

        // Check if user already has a pending request
        $existingRequest = HostRequest::where('user_id', $user->id)
            ->where('status', 'pending')
            ->first();

        if ($existingRequest) {
            return response()->json([
                'message' => 'You already have a pending host request'
            ], 400);
        }

        $hostRequest = HostRequest::create([
            'user_id' => $user->id,
            'reason' => $request->reason,
            'status' => 'pending',
            'requested_at' => now(),
        ]);

        return response()->json([
            'message' => 'Host request submitted successfully',
            'data' => $hostRequest->load('user')
        ], 201);
    }

    /**
     * Get user's host request status
     */
    public function getUserRequest(Request $request): JsonResponse
    {
        $user = $request->user();

        $hostRequest = HostRequest::where('user_id', $user->id)
            ->latest()
            ->first();

        return response()->json([
            'data' => $hostRequest ? $hostRequest->load(['processedBy:id,name']) : null
        ]);
    }

    /**
     * Get all host requests (Admin only)
     */
    public function index(Request $request): JsonResponse
    {
        $query = HostRequest::with(['user:id,name,email', 'processedBy:id,name'])
            ->orderBy('requested_at', 'desc');

        // Filter by status
        if ($request->filled('status') && $request->status !== 'all') {
            $query->where('status', $request->status);
        }

        // Search by user name or email
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $perPage = $request->get('per_page', 10);
        $requests = $query->paginate($perPage);

        return response()->json([
            'message' => 'Host requests retrieved successfully',
            'data' => $requests
        ]);
    }

    /**
     * Show specific host request (Admin only)
     */
    public function show($id): JsonResponse
    {
        $hostRequest = HostRequest::with(['user:id,name,email', 'processedBy:id,name'])
            ->findOrFail($id);

        return response()->json([
            'message' => 'Host request retrieved successfully',
            'data' => $hostRequest
        ]);
    }

    /**
     * Approve host request (Admin only)
     */
    public function approve(Request $request, $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'admin_notes' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $hostRequest = HostRequest::findOrFail($id);

        if ($hostRequest->status !== 'pending') {
            return response()->json([
                'message' => 'Host request has already been processed',
                'current_status' => $hostRequest->status
            ], 400);
        }

        DB::transaction(function () use ($hostRequest, $request) {
            // Update the request
            $hostRequest->update([
                'status' => 'approved',
                'admin_notes' => $request->admin_notes,
                'processed_at' => now(),
                'processed_by' => $request->user()->id,
            ]);

            // Assign host role to the user
            $user = $hostRequest->user;
            if (!$user->hasRole('host')) {
                $user->assignRole('host');
            }
        });

        return response()->json([
            'message' => 'Host request approved successfully',
            'data' => $hostRequest->load(['user:id,name,email', 'processedBy:id,name'])
        ]);
    }

    /**
     * Reject host request (Admin only)
     */
    public function reject(Request $request, $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'admin_notes' => 'required|string|max:1000'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $hostRequest = HostRequest::findOrFail($id);

        if ($hostRequest->status !== 'pending') {
            return response()->json([
                'message' => 'Host request has already been processed',
                'current_status' => $hostRequest->status
            ], 400);
        }

        $hostRequest->update([
            'status' => 'rejected',
            'admin_notes' => $request->admin_notes,
            'processed_at' => now(),
            'processed_by' => $request->user()->id,
        ]);

        return response()->json([
            'message' => 'Host request rejected successfully',
            'data' => $hostRequest->load(['user:id,name,email', 'processedBy:id,name'])
        ]);
    }

    /**
     * Get host request statistics (Admin only)
     */
    public function statistics(): JsonResponse
    {
        $stats = [
            'total' => HostRequest::count(),
            'pending' => HostRequest::pending()->count(),
            'approved' => HostRequest::approved()->count(),
            'rejected' => HostRequest::rejected()->count(),
        ];

        return response()->json([
            'message' => 'Host request statistics retrieved successfully',
            'data' => $stats
        ]);
    }

    /**
     * Export host requests to CSV (Admin only)
     */
    public function export(Request $request)
    {
        $query = HostRequest::with(['user:id,name,email', 'processedBy:id,name'])
            ->orderBy('requested_at', 'desc');

        // Filter by status
        if ($request->filled('status') && $request->status !== 'all') {
            $query->where('status', $request->status);
        }

        // Search by user name or email
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $hostRequests = $query->get();

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="host-requests-' . date('Y-m-d') . '.csv"',
        ];

        $callback = function() use ($hostRequests) {
            $file = fopen('php://output', 'w');

            // Add CSV headers
            fputcsv($file, [
                'ID',
                'User Name',
                'User Email',
                'Status',
                'Requested Date',
                'Processed Date',
                'Processed By',
                'Admin Notes'
            ]);

            // Add data rows
            foreach ($hostRequests as $request) {
                fputcsv($file, [
                    $request->id,
                    $request->user->name,
                    $request->user->email,
                    $request->status,
                    $request->requested_at,
                    $request->processed_at,
                    $request->processedBy ? $request->processedBy->name : 'N/A',
                    $request->admin_notes ?? 'N/A'
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
