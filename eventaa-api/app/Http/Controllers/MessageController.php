<?php

namespace App\Http\Controllers;

use App\Models\Conversation;
use App\Models\Message;
use App\Models\User;
use App\Models\UserStatus;
use App\Models\Vendor;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class MessageController extends Controller
{
    /**
     * Get all conversations for the authenticated user.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getConversations(Request $request): JsonResponse
    {
        $user = $request->user();
        $isVendor = false;
        $vendorId = null;

        $vendor = Vendor::where('user_id', $user->id)->first();
        if ($vendor) {
            $isVendor = true;
            $vendorId = $vendor->id;
        }

        if ($isVendor) {
            $conversations = Conversation::with(['user', 'vendor', 'vendor.user', 'latestMessage'])
                ->where(function ($query) use ($user, $vendorId) {
                    $query->where('user_id', $user->id)
                          ->orWhere('vendor_id', $vendorId);
                })
                ->orderBy('last_message_at', 'desc')
                ->paginate(15);

        } else {
            $conversations = Conversation::with(['vendor', 'vendor.user', 'latestMessage'])
                ->where('user_id', $user->id)
                ->orderBy('last_message_at', 'desc')
                ->paginate(15);
        }

        foreach ($conversations as $conversation) {
            $unreadCount = Message::where('conversation_id', $conversation->id)
                ->where('user_id', '!=', $user->id)
                ->where('is_read', false)
                ->count();

            $conversation->unread_count = $unreadCount;

            // Make sure vendor relationship is loaded
            if ($conversation->vendor_id && !$conversation->relationLoaded('vendor')) {
                $conversation->load('vendor');
            }

            // Make sure vendor.user relationship is loaded
            if ($conversation->vendor && !$conversation->vendor->relationLoaded('user')) {
                $conversation->vendor->load('user');
            }
        }

        // Log the conversations for debugging
        Log::debug("Conversations for user {$user->id}", [
            'count' => $conversations->count(),
            'conversations' => $conversations->toArray()
        ]);

        return response()->json($conversations);
    }

    /**
     * Get or create a conversation between a user and a vendor.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getOrCreateConversation(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'vendor_id' => 'required|exists:vendors,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = $request->user();
        $vendorId = $request->vendor_id;

        $vendor = Vendor::find($vendorId);
        if ($vendor && $vendor->user_id === $user->id) {
            return response()->json(['message' => 'You cannot message your own vendor account'], 422);
        }

        $conversation = Conversation::firstOrCreate(
            [
                'user_id' => $user->id,
                'vendor_id' => $vendorId,
            ],
            [
                'last_message_at' => now(),
            ]
        );

        return response()->json([
            'conversation' => $conversation->load(['user', 'vendor', 'messages' => function ($query) {
                $query->orderBy('created_at', 'desc')->limit(15);
            }])
        ]);
    }

    /**
     * Get messages for a conversation.
     *
     * @param int $conversationId
     * @param Request $request
     * @return JsonResponse
     */
    public function getMessages($conversationId, Request $request): JsonResponse
    {
        $user = $request->user();
        $conversation = Conversation::find($conversationId);

        if (!$conversation) {
            return response()->json(['message' => 'Conversation not found'], 404);
        }

        $vendor = Vendor::where('user_id', $user->id)->first();
        $isAuthorized = $conversation->user_id === $user->id ||
                        ($vendor && $conversation->vendor_id === $vendor->id);

        if (!$isAuthorized) {
            return response()->json(['message' => 'Unauthorized to view this conversation'], 403);
        }

        // Add unread_count to the conversation response
        $unreadCount = Message::where('conversation_id', $conversationId)
            ->where('user_id', '!=', $user->id)
            ->where('is_read', false)
            ->count();

        $conversation->unread_count = $unreadCount;

        $page = $request->page ?? 1;
        $perPage = $request->per_page ?? 15;
        $messages = Message::where('conversation_id', $conversationId)
            ->with('sender')
            ->orderBy('created_at', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);

        $this->markMessagesAsRead($conversationId, $user->id);

        return response()->json($messages);
    }

    /**
     * Send a message.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function sendMessage(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'conversation_id' => 'required|exists:conversations,id',
            'content' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = $request->user();
        $conversationId = $request->conversation_id;
        $content = $request->content;

        $conversation = Conversation::find($conversationId);
        if (!$conversation) {
            return response()->json(['message' => 'Conversation not found'], 404);
        }

        $vendor = Vendor::where('user_id', $user->id)->first();
        $isAuthorized = $conversation->user_id === $user->id ||
                        ($vendor && $conversation->vendor_id === $vendor->id);

        if (!$isAuthorized) {
            return response()->json(['message' => 'Unauthorized to send messages in this conversation'], 403);
        }

        $message = Message::create([
            'conversation_id' => $conversationId,
            'user_id' => $user->id,
            'content' => $content,
            'is_read' => false,
        ]);

        $conversation->update(['last_message_at' => now()]);

        event(new \App\Events\MessageSent($message, $user, $conversationId));

        return response()->json([
            'message' => 'Message sent successfully',
            'data' => $message->load('sender')
        ]);
    }

    /**
     * Mark messages as read.
     *
     * @param int $conversationId
     * @param int $userId
     * @return void
     */
    private function markMessagesAsRead($conversationId, $userId): void
    {
        Message::where('conversation_id', $conversationId)
            ->where('user_id', '!=', $userId)
            ->where('is_read', false)
            ->update([
                'is_read' => true,
                'read_at' => now(),
            ]);
    }

    /**
     * Mark a conversation as read.
     *
     * @param int $conversationId
     * @param Request $request
     * @return JsonResponse
     */
    public function markConversationAsRead($conversationId, Request $request): JsonResponse
    {
        $user = $request->user();
        $conversation = Conversation::find($conversationId);

        if (!$conversation) {
            return response()->json(['message' => 'Conversation not found'], 404);
        }

        $vendor = Vendor::where('user_id', $user->id)->first();
        $isAuthorized = $conversation->user_id === $user->id ||
                        ($vendor && $conversation->vendor_id === $vendor->id);

        if (!$isAuthorized) {
            return response()->json(['message' => 'Unauthorized to mark this conversation as read'], 403);
        }

        $this->markMessagesAsRead($conversationId, $user->id);

        return response()->json(['message' => 'Conversation marked as read']);
    }

    /**
     * Delete a message.
     *
     * @param int $messageId
     * @param Request $request
     * @return JsonResponse
     */
    public function deleteMessage($messageId, Request $request): JsonResponse
    {
        $user = $request->user();
        $message = Message::find($messageId);

        if (!$message) {
            return response()->json(['message' => 'Message not found'], 404);
        }

        if ($message->user_id !== $user->id) {
            return response()->json(['message' => 'Unauthorized to delete this message'], 403);
        }

        $message->delete();

        return response()->json(['message' => 'Message deleted successfully']);
    }

    /**
     * Update user online status.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function updateOnlineStatus(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'is_online' => 'required|boolean',
            'socket_id' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = $request->user();
        $isOnline = $request->is_online;
        $socketId = $request->socket_id;

        $userStatus = UserStatus::firstOrCreate(
            ['user_id' => $user->id],
            [
                'is_online' => false,
                'last_seen_at' => now(),
            ]
        );

        $userStatus->updateOnlineStatus($isOnline, $socketId);

        event(new \App\Events\UserStatusUpdated($userStatus));

        return response()->json([
            'message' => 'Online status updated successfully',
            'data' => $userStatus
        ]);
    }

    /**
     * Get user online status.
     *
     * @param int $userId
     * @return JsonResponse
     */
    public function getUserStatus($userId): JsonResponse
    {
        $user = User::find($userId);

        if (!$user) {
            return response()->json(['message' => 'User not found'], 404);
        }

        $userStatus = UserStatus::where('user_id', $userId)->first();

        if (!$userStatus) {
            return response()->json([
                'is_online' => false,
                'last_seen_at' => null,
            ]);
        }

        return response()->json([
            'is_online' => $userStatus->is_online,
            'last_seen_at' => $userStatus->last_seen_at,
        ]);
    }

    /**
     * Get unread message count for vendor.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getUnreadCount(Request $request): JsonResponse
    {
        $user = $request->user();
        $vendor = Vendor::where('user_id', $user->id)->first();

        if (!$vendor) {
            return response()->json(['message' => 'User is not a vendor'], 403);
        }

        $vendorId = $vendor->id;

        // Get all conversations for this vendor
        $conversations = Conversation::where(function ($query) use ($user, $vendorId) {
            $query->where('user_id', $user->id)
                  ->orWhere('vendor_id', $vendorId);
        })->pluck('id');

        // Count unread messages across all conversations
        $unreadCount = Message::whereIn('conversation_id', $conversations)
            ->where('user_id', '!=', $user->id)
            ->where('is_read', false)
            ->count();

        return response()->json([
            'unread_count' => $unreadCount
        ]);
    }
}
