<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class PermissionController extends Controller
{
    /**
     * Get all permissions
     *
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $permissions = Permission::all();

        return response()->json([
            'permissions' => $permissions
        ], 200);
    }

    /**
     * Get permissions by category
     *
     * @return JsonResponse
     */
    public function getByCategory(): JsonResponse
    {
        $permissions = Permission::all();

        $categorized = [];
        foreach ($permissions as $permission) {
            $parts = explode('.', $permission->name);
            $category = $parts[0] ?? 'other';

            if (!isset($categorized[$category])) {
                $categorized[$category] = [];
            }

            $categorized[$category][] = $permission;
        }

        return response()->json([
            'permissions' => $categorized
        ], 200);
    }

    /**
     * Get a specific permission by ID
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        $permission = Permission::with('roles')->findOrFail($id);

        return response()->json([
            'permission' => $permission
        ], 200);
    }

    /**
     * Create a new permission
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|unique:permissions,name',
            'description' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }

        $permission = Permission::create([
            'name' => $request->name,
            'description' => $request->description,
            'guard_name' => 'web'
        ]);

        return response()->json([
            'message' => 'Permission created successfully',
            'permission' => $permission
        ], 201);
    }

    /**
     * Update a permission
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(Request $request, int $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|unique:permissions,name,' . $id,
            'description' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }

        $permission = Permission::findOrFail($id);

        if ($request->has('name')) {
            $permission->name = $request->name;
        }

        if ($request->has('description')) {
            $permission->description = $request->description;
        }

        $permission->save();

        return response()->json([
            'message' => 'Permission updated successfully',
            'permission' => $permission
        ], 200);
    }

    /**
     * Delete a permission
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        $permission = Permission::findOrFail($id);

        $corePermissions = [
            'event.create', 'event.update', 'event.delete', 'event.view',
            'user.manage', 'profile.view', 'profile.update'
        ];

        if (in_array($permission->name, $corePermissions)) {
            return response()->json([
                'error' => 'Cannot delete core system permissions'
            ], 403);
        }

        $permission->delete();

        return response()->json([
            'message' => 'Permission deleted successfully'
        ], 200);
    }

    /**
     * Get roles that have a specific permission
     *
     * @param int $id
     * @return JsonResponse
     */
    public function roles(int $id): JsonResponse
    {
        $permission = Permission::findOrFail($id);
        $roles = $permission->roles;

        return response()->json([
            'roles' => $roles
        ], 200);
    }

    /**
     * Assign a permission to roles
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function assignToRoles(Request $request, int $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'roles' => 'required|array',
            'roles.*' => 'exists:roles,name'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }

        $permission = Permission::findOrFail($id);

        foreach ($request->roles as $roleName) {
            $role = Role::where('name', $roleName)->first();
            $role->givePermissionTo($permission);
        }

        return response()->json([
            'message' => 'Permission assigned to roles successfully',
            'permission' => $permission->load('roles')
        ], 200);
    }
}
