<?php

namespace App\Http\Controllers;

use App\Models\NewsletterSubscription;
use App\Models\Event;
use App\Models\Venue;
use App\Jobs\SendNewsletterEmail;
use App\Mail\NewsletterWelcomeMail;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Carbon\Carbon;

class NewsletterController extends Controller
{
    /**
     * Subscribe to newsletter
     */
    public function subscribe(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|max:255',
            'name' => 'nullable|string|max:255',
            'preferences' => 'nullable|array',
            'preferences.*' => 'string|in:latest_events,recommended_events,new_venues,event_updates'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $preferences = $request->preferences ?? NewsletterSubscription::getDefaultPreferences();

            $subscription = NewsletterSubscription::updateOrCreate(
                ['email' => $request->email],
                [
                    'name' => $request->name,
                    'preferences' => $preferences,
                    'is_active' => true,
                    'subscribed_at' => now(),
                    'unsubscribed_at' => null
                ]
            );

            // Send welcome email to new subscribers
            if ($subscription->wasRecentlyCreated) {
                try {
                    Mail::to($subscription->email)->send(new NewsletterWelcomeMail($subscription));
                    Log::info('Welcome email sent to new newsletter subscriber', [
                        'email' => $subscription->email
                    ]);
                } catch (\Exception $e) {
                    Log::error('Failed to send welcome email to newsletter subscriber', [
                        'email' => $subscription->email,
                        'error' => $e->getMessage()
                    ]);
                    // Don't fail the subscription if email sending fails
                }
            }

            return response()->json([
                'message' => 'Successfully subscribed to newsletter!',
                'data' => [
                    'email' => $subscription->email,
                    'preferences' => $subscription->preferences
                ]
            ], 201);

        } catch (\Exception $e) {
            Log::error('Newsletter subscription failed', [
                'email' => $request->email,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'message' => 'Failed to subscribe to newsletter. Please try again.',
                'error' => 'Subscription failed'
            ], 500);
        }
    }

    /**
     * Unsubscribe from newsletter
     */
    public function unsubscribe(Request $request, $token)
    {
        $subscription = NewsletterSubscription::where('unsubscribe_token', $token)->first();

        if (!$subscription) {
            // Check if request expects JSON
            if ($request->expectsJson() || $request->ajax()) {
                return response()->json([
                    'message' => 'Invalid unsubscribe token'
                ], 404);
            }

            // For browser requests, redirect to frontend with error
            $frontendUrl = config('app.frontend_url', 'https://eventahub.com');
            return redirect()->to("{$frontendUrl}/newsletter/unsubscribe/{$token}?error=invalid_token");
        }

        $subscription->unsubscribe();

        // Check if request expects JSON
        if ($request->expectsJson() || $request->ajax()) {
            return response()->json([
                'message' => 'Successfully unsubscribed from newsletter'
            ]);
        }

        // For browser requests, show a success page
        return view('emails.newsletter.unsubscribe-success', [
            'frontend_url' => config('app.frontend_url', 'https://eventahub.com'),
            'current_year' => date('Y'),
            'subscription' => $subscription
        ]);
    }

    /**
     * Get subscription preferences
     */
    public function getPreferences(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Invalid email',
                'errors' => $validator->errors()
            ], 422);
        }

        $subscription = NewsletterSubscription::where('email', $request->email)->first();

        if (!$subscription) {
            return response()->json([
                'message' => 'Email not found in newsletter subscriptions'
            ], 404);
        }

        return response()->json([
            'data' => [
                'email' => $subscription->email,
                'name' => $subscription->name,
                'preferences' => $subscription->preferences,
                'is_active' => $subscription->is_active,
                'subscribed_at' => $subscription->subscribed_at
            ]
        ]);
    }

    /**
     * Update subscription preferences
     */
    public function updatePreferences(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'preferences' => 'required|array',
            'preferences.*' => 'string|in:latest_events,recommended_events,new_venues,event_updates'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $subscription = NewsletterSubscription::where('email', $request->email)->first();

        if (!$subscription) {
            return response()->json([
                'message' => 'Email not found in newsletter subscriptions'
            ], 404);
        }

        $subscription->update([
            'preferences' => $request->preferences
        ]);

        return response()->json([
            'message' => 'Preferences updated successfully',
            'data' => [
                'email' => $subscription->email,
                'preferences' => $subscription->preferences
            ]
        ]);
    }

    /**
     * Send newsletter to all active subscribers
     */
    public function sendNewsletter(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'type' => 'required|string|in:latest_events,recommended_events,new_venues,weekly_digest',
            'subject' => 'nullable|string|max:255'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $type = $request->type;
            $subject = $request->subject;

            $content = $this->getNewsletterContent($type);

            if (empty($content)) {
                return response()->json([
                    'message' => 'No content available for newsletter type: ' . $type
                ], 400);
            }

            $subscribers = NewsletterSubscription::active()
                ->withPreference($type)
                ->get();

            if ($subscribers->isEmpty()) {
                return response()->json([
                    'message' => 'No active subscribers found for this newsletter type'
                ], 400);
            }

            foreach ($subscribers as $subscriber) {
                SendNewsletterEmail::dispatch($subscriber, $type, $subject, $content);
            }

            return response()->json([
                'message' => 'Newsletter queued successfully',
                'data' => [
                    'type' => $type,
                    'subscribers_count' => $subscribers->count(),
                    'content_items' => count($content)
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Newsletter sending failed', [
                'type' => $request->type,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'message' => 'Failed to send newsletter',
                'error' => 'Newsletter sending failed'
            ], 500);
        }
    }

    /**
     * Get newsletter content based on type
     */
    private function getNewsletterContent($type): array
    {
        switch ($type) {
            case 'latest_events':
                return Event::where('created_at', '>=', Carbon::now()->subWeek())
                    ->where('status', 'published')
                    ->orderBy('created_at', 'desc')
                    ->limit(10)
                    ->get()
                    ->toArray();

            case 'recommended_events':
                return Event::where('status', 'published')
                    ->where('start_date', '>=', Carbon::now())
                    ->orderBy('views', 'desc')
                    ->limit(8)
                    ->get()
                    ->toArray();

            case 'new_venues':
                return Venue::where('created_at', '>=', Carbon::now()->subWeek())
                    ->where('status', 'approved')
                    ->orderBy('created_at', 'desc')
                    ->limit(6)
                    ->get()
                    ->toArray();

            case 'weekly_digest':
                return [
                    'latest_events' => Event::where('created_at', '>=', Carbon::now()->subWeek())
                        ->where('status', 'published')
                        ->orderBy('created_at', 'desc')
                        ->limit(5)
                        ->get()
                        ->toArray(),
                    'recommended_events' => Event::where('status', 'published')
                        ->where('start_date', '>=', Carbon::now())
                        ->orderBy('views', 'desc')
                        ->limit(3)
                        ->get()
                        ->toArray(),
                    'new_venues' => Venue::where('created_at', '>=', Carbon::now()->subWeek())
                        ->where('status', 'approved')
                        ->orderBy('created_at', 'desc')
                        ->limit(3)
                        ->get()
                        ->toArray()
                ];

            default:
                return [];
        }
    }

    /**
     * Get newsletter statistics
     */
    public function getStats(): JsonResponse
    {
        $stats = [
            'total_subscribers' => NewsletterSubscription::count(),
            'active_subscribers' => NewsletterSubscription::active()->count(),
            'inactive_subscribers' => NewsletterSubscription::where('is_active', false)->count(),
            'recent_subscriptions' => NewsletterSubscription::where('created_at', '>=', Carbon::now()->subWeek())->count(),
            'preferences_breakdown' => [
                'latest_events' => NewsletterSubscription::active()->withPreference('latest_events')->count(),
                'recommended_events' => NewsletterSubscription::active()->withPreference('recommended_events')->count(),
                'new_venues' => NewsletterSubscription::active()->withPreference('new_venues')->count(),
                'event_updates' => NewsletterSubscription::active()->withPreference('event_updates')->count()
            ]
        ];

        return response()->json([
            'message' => 'Newsletter statistics retrieved successfully',
            'data' => $stats
        ]);
    }

    /**
     * Reactivate a newsletter subscription
     */
    public function reactivate(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'preferences' => 'nullable|array',
            'preferences.*' => 'string|in:latest_events,recommended_events,new_venues,event_updates'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $subscription = NewsletterSubscription::where('email', $request->email)->first();

        if (!$subscription) {
            return response()->json([
                'message' => 'Email not found in newsletter subscriptions'
            ], 404);
        }

        $preferences = $request->preferences ?? $subscription->preferences ?? NewsletterSubscription::getDefaultPreferences();

        $subscription->update([
            'preferences' => $preferences,
            'is_active' => true,
            'unsubscribed_at' => null
        ]);

        // Send reactivation confirmation email
        try {
            Mail::to($subscription->email)->send(new NewsletterWelcomeMail($subscription));
            Log::info('Reactivation email sent to newsletter subscriber', [
                'email' => $subscription->email
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send reactivation email to newsletter subscriber', [
                'email' => $subscription->email,
                'error' => $e->getMessage()
            ]);
        }

        return response()->json([
            'message' => 'Subscription reactivated successfully',
            'data' => [
                'email' => $subscription->email,
                'preferences' => $subscription->preferences,
                'is_active' => $subscription->is_active
            ]
        ]);
    }
}
