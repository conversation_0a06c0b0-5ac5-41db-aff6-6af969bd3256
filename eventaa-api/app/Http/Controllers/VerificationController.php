<?php
namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class VerificationController extends Controller
{
    public function request(Request $request) : JsonResponse
    {
        $request->validate([
            'verification_document' => 'required|file|mimes:pdf,jpg,jpeg,png|max:2048'
        ]);

        $path = $request->file('verification_document')->store('verification-documents');
        
        $user = User::find($request->user->id);
        $user->verification_document = $path;
        $user->verification_status = 'pending';
        $user->save();

        return response()->json([
            'message' => 'Verification request submitted successfully'
        ], 201);
    }

    public function adminVerify(Request $request, User $user) : JsonResponse
    {
        $request->validate([
            'status' => 'required|in:approved,rejected',
            'notes' => 'nullable|string'
        ]);

        $user->verification_status = $request->status;
        $user->verification_notes = $request->notes;
        
        if ($request->status === 'approved') {
            $user->is_verified = true;
            $user->verified_at = now();
        }
        
        $user->save();

        return response()->json([
            'message' => 'Verification status updated successfully'
        ], 201);
    }

    public function checkStatus(Request $request) : JsonResponse
    {
        $user = User::find($request->user->id);
        
        return response()->json([
            'is_verified' => $user->is_verified,
            'status' => $user->verification_status,
            'notes' => $user->verification_notes,
            'verified_at' => $user->verified_at
        ], 200);
    }
}