<?php

namespace App\Http\Controllers;

use App\Models\Venue;
use App\Models\VenueActivity;
use App\Models\VenueBooking;
use App\Models\VenueImage;
use App\Models\VenueRating;
use App\Models\VenuePrice;
use App\Models\VenueVideo;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class VenuesController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        $query = Venue::withAvg('ratings', 'rating')
            ->with(['user', 'activities.category', 'images', 'prices.currency']);

        if ($request->has('category_id')) {
            $query->whereHas('activities', function ($q) use ($request) {
                $q->where('category_id', $request->category_id);
            });
        }

        if ($request->has('city')) {
            $query->where('city', 'like', '%' . $request->city . '%');
        }

        if ($request->has(['latitude', 'longitude', 'radius'])) {
            $latitude = $request->latitude;
            $longitude = $request->longitude;
            $radius = $request->radius;

            $query->whereRaw("
            ST_Distance_Sphere(
                point(longitude, latitude),
                point(?, ?)
            ) <= ?
        ", [$longitude, $latitude, $radius * 1000]);
        }

        $venues = $query->paginate($request->per_page ?? 6)
            ->appends($request->query());

        return response()->json($venues);
    }

    public function user(Request $request): JsonResponse
    {
        $venues = Venue::withAvg('ratings', 'rating')
            ->with(['user', 'activities.category', 'images', 'prices.currency'])
            ->where('user_id', $request->user()->id)
            ->paginate($request->per_page ?? 6)
            ->appends($request->query());

        return response()->json($venues);
    }

    public function create(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string',
            'activities' => 'required',
            'address' => 'required|string',
            'city' => 'required|string',
            'state' => 'required|string',
            'zip' => 'required|string',
            'country' => 'required|string',
            'phone' => 'required|string',
            'email' => 'required|email',
            'website' => 'nullable|url',
            'description' => 'required|string',
            'logo' => 'required|image',
            'capacity' => 'required|integer',
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'images.*' => 'nullable|image',
            'videos.*' => 'nullable|mimetypes:video/mp4,video/avi,video/mpeg,video/quicktime'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $logoPath = $request->file('logo')->store('logos', 'public');

        $venue = Venue::create([
            'user_id' => $request->user()->id,
            'name' => $request->name,
            'address' => $request->address,
            'city' => $request->city,
            'state' => $request->state,
            'zip' => $request->zip,
            'country' => $request->country,
            'phone' => $request->phone,
            'email' => $request->email,
            'website' => $request->website,
            'description' => $request->description,
            'logo' => $logoPath,
            'capacity' => $request->capacity,
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
            'slug' => Str::slug($request->name, '-'),
        ]);

        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $image) {
                $imagePath = $image->store('venue_images', 'public');
                VenueImage::create([
                    'venue_id' => $venue->id,
                    'image_path' => $imagePath,
                ]);
            }
        }

        if ($request->hasFile('videos')) {
            foreach ($request->file('videos') as $video) {
                $videoPath = $video->store('venue_videos', 'public');
                VenueVideo::create([
                    'venue_id' => $venue->id,
                    'video_path' => $videoPath,
                ]);
            }
        }

        if ($request->has('activities')) {
            $activities = json_decode($request->activities, true);
            foreach ($activities as $activityId) {
                VenueActivity::create([
                    'venue_id' => $venue->id,
                    'category_id' => $activityId,
                ]);
            }
        }

        $pricesJson = $request->prices;
        $prices = json_decode($pricesJson, true);

        foreach ($prices as $price) {
            VenuePrice::create([
                'venue_id' => $venue->id,
                'price' => $price['amount'],
                'currency_id' => $price['currency'],
                'ammenities' => json_encode($price['attributes'])
            ]);
        }

        return response()->json(['message' => 'Venue created successfully!', 'venue' => $venue->load(['images', 'videos'])], 201);
    }

    public function show($slug): JsonResponse
    {
        $venue = Venue::where('slug', $slug)->first();

        if (!$venue) {
            return response()->json(['message' => 'Venue not found'], 404);
        }

        $venue = Venue::withAvg('ratings', 'rating')->with([
            'user',
            'activities.category',
            'images',
            'prices.currency',
            'videos',
            'bookings' => function ($query) {
                $query->where('status', '!=', 'rejected');
            },
            'ratings' => function ($query) {
                $query->orderBy('created_at', 'desc');
            },
            'ratings.user'
        ])->find($venue->id);

        return response()->json(['venue' => $venue], 200);
    }


    public function update(Request $request, $slug): JsonResponse
    {
        $id = Venue::where('slug', $slug)->first()->id;
        $venue = Venue::find($id);

        if (!$venue) {
            return response()->json(['message' => 'Venue not found'], 404);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string',
            'address' => 'sometimes|required|string',
            'city' => 'sometimes|required|string',
            'state' => 'sometimes|required|string',
            'zip' => 'sometimes|required|string',
            'country' => 'sometimes|required|string',
            'phone' => 'sometimes|required|string',
            'email' => 'sometimes|required|email',
            'website' => 'nullable|url',
            'description' => 'sometimes|required|string',
            'logo' => 'sometimes|required|image',
            'capacity' => 'sometimes|required|integer',
            'latitude' => 'sometimes|required|numeric',
            'longitude' => 'sometimes|required|numeric',
            'images.*' => 'nullable|image',
            'videos.*' => 'nullable|mimetypes:video/mp4,video/avi,video/mpeg,video/quicktime'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        if ($request->hasFile('logo')) {
            if ($venue->image) {
                Storage::disk('public')->delete($venue->image);
            }
            $venue->logo = $request->file('logo')->store('logos', 'public');
        }

        $venue->user_id = $request->user()->id;
        $venue->update($request->except('logo', 'images', 'videos'));

        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $image) {
                $imagePath = $image->store('venue_images', 'public');
                VenueImage::create([
                    'venue_id' => $venue->id,
                    'image_path' => $imagePath,
                ]);
            }
        }

        if ($request->hasFile('videos')) {
            foreach ($request->file('videos') as $video) {
                $videoPath = $video->store('venue_videos', 'public');
                VenueVideo::create([
                    'venue_id' => $venue->id,
                    'video_path' => $videoPath,
                ]);
            }
        }

        if ($request->has('activities')) {
            foreach (json_decode($request->activities, true) as $activityId) {
                VenueActivity::updateOrCreate(
                    [
                        'venue_id' => $venue->id,
                        'category_id' => $activityId
                    ],
                    []
                );
            }
        }

        if ($request->has('prices')) {
            $pricesJson = $request->prices;
            $prices = json_decode($pricesJson, true);

            foreach ($prices as $price) {
                $existing = VenuePrice::where('venue_id', $venue->id)
                    ->where('currency_id', $price['currency'])
                    ->where('price', $price['amount'])
                    ->first();

                if (!$existing) {
                    VenuePrice::updateOrCreate(
                        [
                            'venue_id' => $venue->id,
                            'currency_id' => $price['currency']
                        ],
                        [
                            'price' => $price['amount'],
                            'ammenities' => json_encode($price['attributes'])
                        ]
                    );
                }
            }
        }


        return response()->json(['message' => 'Venue updated successfully!', 'venue' => $venue->load(['images', 'videos'])], 200);
    }

    public function destroy($id): JsonResponse
    {
        $venue = Venue::find($id);

        if (!$venue) {
            return response()->json(['message' => 'Venue not found'], 404);
        }

        if ($venue->image) {
            Storage::disk('public')->delete($venue->image);
        }

        $venue->delete();

        return response()->json(['message' => 'Venue deleted successfully'], 200);
    }

    public function rate(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'venue_id' => 'required|exists:venues,id',
            'rating' => 'required|integer|min:1|max:5',
            'review' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $venue = Venue::find($request->venue_id);
        if (!$venue) {
            return response()->json(['message' => 'Venue not found'], 404);
        }

        $existingRating = VenueRating::where('venue_id', $request->venue_id)
            ->where('user_id', $request->user()->id)
            ->first();

        if ($existingRating) {
            $existingRating->update([
                'rating' => $request->rating,
                'comment' => $request->review,
            ]);

            return response()->json(['message' => 'Rating updated successfully'], 200);
        }

        $venue->ratings()->create([
            'user_id' => $request->user()->id,
            'rating' => $request->rating,
            'comment' => $request->review,
        ]);

        return response()->json(['message' => 'Rating submitted successfully'], 201);
    }

    public function book(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'booking_from' => 'required|date',
            'booking_to' => 'required|date',
            'message' => 'required|integer',
            'category_id' => 'required|integer',
            'venue_id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $venue = Venue::find($request->venue_id);

        if (!$venue) {
            return response()->json(['message' => 'Venue not found'], 404);
        }

        $booking = $venue->bookings()->create([
            'booking_from' => $request->booking_from,
            'booking_to' => $request->booking_to,
            'message' => $request->message,
            'category_id' => $request->category_id,
            'user_id' => $request->user()->id,
        ]);

        return response()->json(['booking' => $booking], 201);
    }

    public function bookings(Request $request, $id)
    {
        $venueBooking = VenueBooking::with(['venue', 'user', 'category', 'venuePrice.currency'])->where('venue_id', $id)->paginate(
            $request->per_page ?? 30
        );

        if (!$venueBooking) {
            return response()->json([
                'status' => 'error',
                'message' => 'Venue booking not found',
            ], 404);
        }

        return response()->json([
            'status' => 'success',
            'bookings' => $venueBooking,
        ]);
    }

    public function locations(Request $request): JsonResponse
    {
        $locations = Venue::select('city', 'state', 'country')
            ->groupBy('city', 'state', 'country')
            ->get();

        return response()->json($locations);
    }
}
