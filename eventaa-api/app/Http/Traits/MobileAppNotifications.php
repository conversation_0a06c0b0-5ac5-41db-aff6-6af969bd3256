<?php

namespace App\Http\Traits;
use Kreait\Firebase\Factory;
use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\Topic;

trait MobileAppNotifications{
    public function sendPushNotification($title, $body, $topic)
    {
        $serviceAccountPath = base_path('config/eventa.json');

        if (!$serviceAccountPath || !is_readable($serviceAccountPath)) {
            throw new \Exception("Invalid service account: The file at '{$serviceAccountPath}' is not readable");
        }
        $firebase = (new Factory)
            ->withServiceAccount($serviceAccountPath);

        $messaging = $firebase->createMessaging();

        $message = CloudMessage::fromArray([
            'notification' => [
                'title' => $title,
                'body' => $body
            ],
            'topic' => $topic
        ]);

        $messaging->send($message);

        return response()->json(['message' => 'Push notification sent successfully']);
    }

    public function sendToUser($title, $body, $token)
    {
        $serviceAccountPath = base_path('config/eventa.json');

        if (!$serviceAccountPath || !is_readable($serviceAccountPath)) {
            throw new \Exception("Invalid service account: The file at '{$serviceAccountPath}' is not readable");
        }
        $firebase = (new Factory)
            ->withServiceAccount($serviceAccountPath);

        $messaging = $firebase->createMessaging();

        $message = CloudMessage::withTarget('token', $token)
        ->withNotification([
            'title' => $title,
            'body' => $body
        ])
        ->withData([
            'key' => 'value'
        ]);

        $messaging->send($message);
    }

    public function createTopic($topicName)
    {
        try {
            Topic::fromValue($topicName);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}