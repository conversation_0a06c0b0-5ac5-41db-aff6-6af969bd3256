<?php

if (! function_exists('get_app_logo_url')) {
    /**
     * Get the application logo URL for email templates
     *
     * @return string
     */
    function get_app_logo_url(): string
    {
        // Check if logo exists in public storage
        $logoPath = public_path('assets/images/logo.png');
        if (file_exists($logoPath)) {
            return asset('assets/images/logo.png');
        }

        // Check for icon.png as fallback
        $iconPath = public_path('icon.png');
        if (file_exists($iconPath)) {
            return asset('icon.png');
        }

        // Return a placeholder or null if no logo found
        return '';
    }
}

if (! function_exists('get_app_brand_colors')) {
    /**
     * Get the application brand colors
     *
     * @return array
     */
    function get_app_brand_colors(): array
    {
        return [
            'primary' => '#ef4444',
            'primary_hover' => '#dc2626',
            'primary_light' => '#fecaca',
            'text_primary' => '#111827',
            'text_secondary' => '#4b5563',
            'text_muted' => '#6b7280',
            'bg_main' => '#f9fafb',
            'bg_card' => '#ffffff',
            'border' => '#e5e7eb',
        ];
    }
}
