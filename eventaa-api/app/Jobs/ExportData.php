<?php

namespace App\Jobs;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ExportData implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $exportType;
    protected $filters;
    protected $format;
    protected $user;
    protected $fileName;

    public $tries = 3;
    public $timeout = 900;

    /**
     * Create a new job instance.
     */
    public function __construct(string $exportType, array $filters, string $format, User $user, string $fileName = null)
    {
        $this->exportType = $exportType;
        $this->filters = $filters;
        $this->format = strtolower($format);
        $this->user = $user;
        $this->fileName = $fileName ?: Str::slug($exportType) . '_export_' . now()->format('Y_m_d_H_i_s') . '.' . $this->format;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('Starting data export', [
                'export_type' => $this->exportType,
                'format' => $this->format,
                'user_id' => $this->user->id,
                'file_name' => $this->fileName
            ]);

            $data = $this->fetchExportData();
            $filePath = $this->generateExportFile($data);

            $this->storeExportMetadata($filePath, count($data));

            dispatch(new SendEmailNotification(
                $this->user,
                'Data Export Completed',
                "Your {$this->exportType} data export has been completed and is ready for download.",
                null,
                [
                    'download_link' => Storage::url($filePath),
                    'record_count' => count($data),
                    'file_size' => $this->formatFileSize(Storage::size($filePath))
                ]
            ));

            Log::info('Data export completed successfully', [
                'export_type' => $this->exportType,
                'user_id' => $this->user->id,
                'file_path' => $filePath,
                'record_count' => count($data)
            ]);

        } catch (\Exception $e) {
            Log::error('Data export failed', [
                'export_type' => $this->exportType,
                'user_id' => $this->user->id,
                'error' => $e->getMessage()
            ]);

            dispatch(new SendEmailNotification(
                $this->user,
                'Data Export Failed',
                "Sorry, there was an error processing your {$this->exportType} data export. Please try again later."
            ));

            throw $e;
        }
    }

    /**
     * Fetch data for export based on type and filters.
     */
    private function fetchExportData(): array
    {
        switch ($this->exportType) {
            case 'users':
                return $this->exportUsers();
            case 'events':
                return $this->exportEvents();
            case 'tickets':
                return $this->exportTickets();
            case 'purchases':
                return $this->exportPurchases();
            case 'payments':
                return $this->exportPayments();
            case 'attendees':
                return $this->exportAttendees();
            default:
                throw new \Exception("Unknown export type: {$this->exportType}");
        }
    }

    /**
     * Export users data.
     */
    private function exportUsers(): array
    {
        $query = \DB::table('users')
            ->select([
                'id',
                'name',
                'email',
                'email_verified_at',
                'phone',
                'date_of_birth',
                'gender',
                'location',
                'created_at',
                'updated_at'
            ]);

        $this->applyDateFilters($query, 'created_at');
        $this->applyStatusFilters($query);

        return $query->get()->toArray();
    }

    /**
     * Export events data.
     */
    private function exportEvents(): array
    {
        $query = \DB::table('events')
            ->leftJoin('users', 'events.organizer_id', '=', 'users.id')
            ->select([
                'events.id',
                'events.title',
                'events.description',
                'events.start_date',
                'events.end_date',
                'events.location',
                'events.address',
                'events.category',
                'events.status',
                'events.max_attendees',
                'events.is_free',
                'events.created_at',
                'users.name as organizer_name',
                'users.email as organizer_email'
            ]);

        $this->applyDateFilters($query, 'events.start_date');

        if (isset($this->filters['category'])) {
            $query->where('events.category', $this->filters['category']);
        }

        if (isset($this->filters['status'])) {
            $query->where('events.status', $this->filters['status']);
        }

        return $query->get()->toArray();
    }

    /**
     * Export tickets data.
     */
    private function exportTickets(): array
    {
        $query = \DB::table('tickets')
            ->join('events', 'tickets.event_id', '=', 'events.id')
            ->select([
                'tickets.id',
                'tickets.name',
                'tickets.description',
                'tickets.price',
                'tickets.quantity_available',
                'tickets.quantity_sold',
                'tickets.sale_start_date',
                'tickets.sale_end_date',
                'tickets.is_refundable',
                'tickets.refund_fee_percentage',
                'events.title as event_title',
                'events.start_date as event_date'
            ]);

        if (isset($this->filters['event_id'])) {
            $query->where('tickets.event_id', $this->filters['event_id']);
        }

        $this->applyDateFilters($query, 'tickets.sale_start_date');

        return $query->get()->toArray();
    }

    /**
     * Export purchases data.
     */
    private function exportPurchases(): array
    {
        $query = \DB::table('ticket_purchases')
            ->join('users', 'ticket_purchases.user_id', '=', 'users.id')
            ->join('events', 'ticket_purchases.event_id', '=', 'events.id')
            ->join('tickets', 'ticket_purchases.ticket_id', '=', 'tickets.id')
            ->select([
                'ticket_purchases.id',
                'ticket_purchases.purchase_reference',
                'ticket_purchases.quantity',
                'ticket_purchases.unit_price',
                'ticket_purchases.total_amount',
                'ticket_purchases.fees',
                'ticket_purchases.taxes',
                'ticket_purchases.status',
                'ticket_purchases.purchased_at',
                'ticket_purchases.attendee_name',
                'ticket_purchases.attendee_email',
                'users.name as buyer_name',
                'users.email as buyer_email',
                'events.title as event_title',
                'tickets.name as ticket_name'
            ]);

        $this->applyDateFilters($query, 'ticket_purchases.purchased_at');

        if (isset($this->filters['event_id'])) {
            $query->where('ticket_purchases.event_id', $this->filters['event_id']);
        }

        if (isset($this->filters['status'])) {
            $query->where('ticket_purchases.status', $this->filters['status']);
        }

        return $query->get()->toArray();
    }

    /**
     * Export payments data.
     */
    private function exportPayments(): array
    {
        $query = \DB::table('payments')
            ->join('users', 'payments.user_id', '=', 'users.id')
            ->select([
                'payments.id',
                'payments.amount',
                'payments.currency',
                'payments.payment_method',
                'payments.type',
                'payments.status',
                'payments.transaction_id',
                'payments.processed_at',
                'payments.created_at',
                'users.name as user_name',
                'users.email as user_email'
            ]);

        $this->applyDateFilters($query, 'payments.created_at');

        if (isset($this->filters['payment_method'])) {
            $query->where('payments.payment_method', $this->filters['payment_method']);
        }

        if (isset($this->filters['status'])) {
            $query->where('payments.status', $this->filters['status']);
        }

        return $query->get()->toArray();
    }

    /**
     * Export attendees data.
     */
    private function exportAttendees(): array
    {
        $query = \DB::table('ticket_purchases')
            ->join('events', 'ticket_purchases.event_id', '=', 'events.id')
            ->join('tickets', 'ticket_purchases.ticket_id', '=', 'tickets.id')
            ->select([
                'ticket_purchases.attendee_name',
                'ticket_purchases.attendee_email',
                'ticket_purchases.attendee_phone',
                'ticket_purchases.quantity',
                'ticket_purchases.purchased_at',
                'events.title as event_title',
                'events.start_date as event_date',
                'tickets.name as ticket_type'
            ])
            ->where('ticket_purchases.status', 'completed');

        if (isset($this->filters['event_id'])) {
            $query->where('ticket_purchases.event_id', $this->filters['event_id']);
        }

        $this->applyDateFilters($query, 'ticket_purchases.purchased_at');

        return $query->get()->toArray();
    }

    /**
     * Apply date filters to query.
     */
    private function applyDateFilters($query, string $dateColumn): void
    {
        if (isset($this->filters['start_date'])) {
            $query->where($dateColumn, '>=', $this->filters['start_date']);
        }

        if (isset($this->filters['end_date'])) {
            $query->where($dateColumn, '<=', $this->filters['end_date']);
        }
    }

    /**
     * Apply status filters to query.
     */
    private function applyStatusFilters($query): void
    {
        if (isset($this->filters['status'])) {
            $query->where('status', $this->filters['status']);
        }
    }

    /**
     * Generate export file from data.
     */
    private function generateExportFile(array $data): string
    {
        $filePath = "exports/{$this->fileName}";

        switch ($this->format) {
            case 'csv':
                $content = $this->generateCsv($data);
                break;
            case 'json':
                $content = $this->generateJson($data);
                break;
            case 'xlsx':
                $content = $this->generateExcel($data);
                break;
            default:
                throw new \Exception("Unsupported export format: {$this->format}");
        }

        Storage::put($filePath, $content);

        return $filePath;
    }

    /**
     * Generate CSV content.
     */
    private function generateCsv(array $data): string
    {
        if (empty($data)) {
            return "No data available for export.\n";
        }

        $output = fopen('php://temp', 'r+');

        // Add headers
        $headers = array_keys((array) $data[0]);
        fputcsv($output, $headers);

        // Add data rows
        foreach ($data as $row) {
            fputcsv($output, (array) $row);
        }

        rewind($output);
        $csv = stream_get_contents($output);
        fclose($output);

        return $csv;
    }

    /**
     * Generate JSON content.
     */
    private function generateJson(array $data): string
    {
        return json_encode([
            'export_type' => $this->exportType,
            'generated_at' => now()->toISOString(),
            'record_count' => count($data),
            'data' => $data
        ], JSON_PRETTY_PRINT);
    }

    /**
     * Generate Excel content (basic implementation).
     */
    private function generateExcel(array $data): string
    {
        // This is a simplified implementation
        // For a production app, you'd want to use a library like PhpSpreadsheet
        return $this->generateCsv($data);
    }

    /**
     * Store export metadata.
     */
    private function storeExportMetadata(string $filePath, int $recordCount): void
    {
        Log::info('Export metadata', [
            'user_id' => $this->user->id,
            'export_type' => $this->exportType,
            'format' => $this->format,
            'file_path' => $filePath,
            'record_count' => $recordCount,
            'filters' => $this->filters,
            'generated_at' => now()
        ]);
    }

    /**
     * Format file size for human readability.
     */
    private function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= (1 << (10 * $pow));

        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Data export job failed', [
            'export_type' => $this->exportType,
            'user_id' => $this->user->id,
            'error' => $exception->getMessage()
        ]);
    }
}
