<?php

namespace App\Jobs;

use App\Mail\EventNotification;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendEmailNotification implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $user;
    protected $subject;
    protected $message;
    protected $mailableClass;
    protected $data;

    public $tries = 3;
    public $timeout = 60;

    /**
     * Create a new job instance.
     */
    public function __construct(User $user, string $subject, string $message, string $mailableClass = null, array $data = [])
    {
        $this->user = $user;
        $this->subject = $subject;
        $this->message = $message;
        $this->mailableClass = $mailableClass;
        $this->data = $data;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('Sending email notification', [
                'user_id' => $this->user->id,
                'user_email' => $this->user->email,
                'subject' => $this->subject
            ]);

            if ($this->mailableClass && class_exists($this->mailableClass)) {
                // Use custom mailable class
                $mailable = new $this->mailableClass($this->user, $this->data);
                Mail::to($this->user->email)->send($mailable);
            } else {
                // Use generic event notification mailable
                Mail::to($this->user->email)->send(
                    new EventNotification($this->user, $this->subject, $this->message, $this->data)
                );
            }

            Log::info('Email notification sent successfully', [
                'user_id' => $this->user->id,
                'user_email' => $this->user->email
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send email notification', [
                'user_id' => $this->user->id,
                'user_email' => $this->user->email,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Email notification job failed', [
            'user_id' => $this->user->id,
            'user_email' => $this->user->email,
            'subject' => $this->subject,
            'error' => $exception->getMessage()
        ]);
    }
}
