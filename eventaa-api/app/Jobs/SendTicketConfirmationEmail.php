<?php

namespace App\Jobs;

use App\Models\User;
use App\Notifications\UserNotification;
use App\Mail\TicketConfirmationMail;
use App\Services\TicketService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendTicketConfirmationEmail implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $user;
    protected $purchases;
    protected $generatedTickets;

    /**
     * Create a new job instance.
     */
    public function __construct(User $user, array $purchases, array $generatedTickets)
    {
        $this->user = $user;
        $this->purchases = $purchases;
        $this->generatedTickets = $generatedTickets;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('Sending ticket confirmation email', [
                'user_id' => $this->user->id,
                'user_email' => $this->user->email,
                'ticket_count' => count($this->generatedTickets)
            ]);

            $firstPurchase = $this->purchases[0];
            $eventName = $firstPurchase->event->name ?? 'Event';
            $ticketCount = count($this->generatedTickets);

            // Generate PDF for email attachment
            $ticketService = new TicketService();
            $pdfUrl = null;

            if (!empty($this->generatedTickets)) {
                $purchaseReference = $firstPurchase->purchase_reference ?? $firstPurchase->id;

                // Prepare ticket data for PDF generation
                $ticketData = [];
                foreach ($this->generatedTickets as $ticket) {
                    $ticketData[] = [
                        'attendee_name' => $this->user->name,
                        'ticket_name' => $ticket['tier_name'] ?? 'General Admission',
                        'event_name' => $eventName,
                        'event_date' => $firstPurchase->event->start ?? now(),
                        'event_location' => $firstPurchase->event->location ?? 'TBA',
                        'ticket_uuid' => $ticket['uuid'] ?? '',
                        'qr_code_data' => $ticket['uuid'] ?? '',
                        'price' => $ticket['price'] ?? 0,
                        'currency_symbol' => $firstPurchase->event->currency_symbol ?? 'MK'
                    ];
                }

                $pdfUrl = $ticketService->generatePurchaseTicketsPDF($purchaseReference, $ticketData);
            }

            // Send email with PDF attachment
            Mail::to($this->user->email)->send(new TicketConfirmationMail(
                $this->user,
                $this->purchases,
                $this->generatedTickets,
                $pdfUrl
            ));

            // Send notification through the existing notification system
            $notificationData = [
                'greeting' => 'Hello ' . $this->user->name . '!',
                'title' => 'Ticket Purchase Confirmed',
                'line' => "Your purchase of {$ticketCount} ticket(s) for '{$eventName}' has been confirmed. Check your email for your tickets.",
                'action' => 'View My Tickets',
                'actionURL' => '/tickets/my-purchases'
            ];

            $this->user->notify(new UserNotification($notificationData));

            Log::info('Ticket confirmation email sent successfully', [
                'user_id' => $this->user->id,
                'user_email' => $this->user->email,
                'pdf_generated' => !empty($pdfUrl)
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send ticket confirmation email', [
                'user_id' => $this->user->id,
                'user_email' => $this->user->email,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Ticket confirmation email job failed permanently', [
            'user_id' => $this->user->id,
            'user_email' => $this->user->email,
            'error' => $exception->getMessage()
        ]);
    }
}
