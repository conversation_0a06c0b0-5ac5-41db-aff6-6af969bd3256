<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessQueueHealth implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $checkType;
    protected $metadata;

    public $tries = 1;
    public $timeout = 30;

    /**
     * Create a new job instance.
     */
    public function __construct(string $checkType = 'health', array $metadata = [])
    {
        $this->checkType = $checkType;
        $this->metadata = $metadata;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('Queue health check started', [
                'check_type' => $this->checkType,
                'queue' => $this->queue,
                'connection' => $this->connection,
                'metadata' => $this->metadata,
                'executed_at' => now()
            ]);

            switch ($this->checkType) {
                case 'health':
                    $this->performHealthCheck();
                    break;
                case 'stress':
                    $this->performStressTest();
                    break;
                case 'cleanup':
                    $this->performCleanup();
                    break;
                default:
                    Log::info('Unknown check type, performing basic health check');
                    $this->performHealthCheck();
            }

            Log::info('Queue health check completed successfully', [
                'check_type' => $this->checkType,
                'executed_at' => now()
            ]);

        } catch (\Exception $e) {
            Log::error('Queue health check failed', [
                'check_type' => $this->checkType,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Perform basic health check.
     */
    private function performHealthCheck(): void
    {
        $metrics = [
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true),
            'execution_time' => microtime(true),
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version()
        ];

        Log::info('Queue health metrics', $metrics);
    }

    /**
     * Perform stress test by processing multiple dummy operations.
     */
    private function performStressTest(): void
    {
        $operations = $this->metadata['operations'] ?? 100;
        $results = [];

        for ($i = 0; $i < $operations; $i++) {
            $start = microtime(true);

            // Simulate some work
            usleep(1000); // 1ms delay

            $results[] = [
                'operation' => $i + 1,
                'duration' => microtime(true) - $start
            ];
        }

        $averageDuration = array_sum(array_column($results, 'duration')) / count($results);

        Log::info('Queue stress test completed', [
            'operations' => $operations,
            'average_duration' => $averageDuration,
            'total_time' => array_sum(array_column($results, 'duration'))
        ]);
    }

    /**
     * Perform cleanup operations.
     */
    private function performCleanup(): void
    {
        // Clean up old log entries (this is just an example)
        $cutoffDate = now()->subDays(30);

        Log::info('Queue cleanup started', [
            'cutoff_date' => $cutoffDate
        ]);

        // Here you could clean up old job records, temporary files, etc.
        // Example: cleanup old failed jobs, expired tokens, etc.

        Log::info('Queue cleanup completed');
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Queue health check job failed', [
            'check_type' => $this->checkType,
            'error' => $exception->getMessage()
        ]);
    }
}
