<?php

namespace App\Jobs;

use App\Events\PaymentCompleted;
use App\Models\Payment;
use App\Models\TicketPurchase;
use App\Services\PaymentService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProcessPayment implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $payment;
    protected $paymentData;

    public $tries = 3;
    public $timeout = 120;

    /**
     * Create a new job instance.
     */
    public function __construct(Payment $payment, array $paymentData = [])
    {
        $this->payment = $payment;
        $this->paymentData = $paymentData;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('Processing payment', [
                'payment_id' => $this->payment->id,
                'amount' => $this->payment->amount,
                'method' => $this->payment->payment_method
            ]);

            DB::beginTransaction();

            $paymentService = new PaymentService();

            switch ($this->payment->payment_method) {
                case 'stripe':
                    $result = $paymentService->processStripePayment($this->payment, $this->paymentData);
                    break;
                case 'paypal':
                    $result = $paymentService->processPayPalPayment($this->payment, $this->paymentData);
                    break;
                case 'bank_transfer':
                    $result = $paymentService->processBankTransfer($this->payment, $this->paymentData);
                    break;
                default:
                    throw new \Exception("Unsupported payment method: {$this->payment->payment_method}");
            }

            if ($result['success']) {
                $this->payment->update([
                    'status' => 'completed',
                    'processed_at' => now(),
                    'transaction_id' => $result['transaction_id'] ?? null,
                    'gateway_response' => $result['response'] ?? null
                ]);

                if ($this->payment->type === 'ticket_purchase') {
                    TicketPurchase::where('payment_id', $this->payment->id)
                        ->update(['status' => 'completed']);
                }

                event(new PaymentCompleted($this->payment));

                Log::info('Payment processed successfully', [
                    'payment_id' => $this->payment->id,
                    'transaction_id' => $result['transaction_id'] ?? null
                ]);

            } else {
                $this->payment->update([
                    'status' => 'failed',
                    'processed_at' => now(),
                    'gateway_response' => $result['response'] ?? null,
                    'failure_reason' => $result['error'] ?? 'Payment processing failed'
                ]);

                if ($this->payment->type === 'ticket_purchase') {
                    TicketPurchase::where('payment_id', $this->payment->id)
                        ->update(['status' => 'failed']);
                }

                throw new \Exception($result['error'] ?? 'Payment processing failed');
            }

            DB::commit();

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Payment processing failed', [
                'payment_id' => $this->payment->id,
                'error' => $e->getMessage()
            ]);

            $this->payment->update([
                'status' => 'failed',
                'processed_at' => now(),
                'failure_reason' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Payment processing job failed', [
            'payment_id' => $this->payment->id,
            'error' => $exception->getMessage()
        ]);

        $this->payment->update([
            'status' => 'failed',
            'processed_at' => now(),
            'failure_reason' => $exception->getMessage()
        ]);
    }
}
