<?php

namespace App\Jobs;

use App\Models\Payment;
use App\Models\RefundRequest;
use App\Models\TicketPurchase;
use App\Notifications\RefundStatusNotification;
use App\Notifications\EventOwnerRefundNotification;
use App\Services\PaymentService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProcessRefundRequest implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected RefundRequest $refundRequest;

    /**
     * Create a new job instance.
     */
    public function __construct(RefundRequest $refundRequest)
    {
        $this->refundRequest = $refundRequest;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $refund = $this->refundRequest->fresh();

            // Skip if already processed
            if (!in_array($refund->status, ['pending', 'approved'])) {
                Log::info('Refund request already processed', ['refund_id' => $refund->id, 'status' => $refund->status]);
                return;
            }

            // Auto-approve if conditions are met (optional business logic)
            if ($refund->status === 'pending' && $this->shouldAutoApprove($refund)) {
                $refund->update([
                    'status' => 'approved',
                    'approved_at' => now(),
                    'admin_notes' => 'Auto-approved based on refund policy'
                ]);

                // Send approval notification
                $refund->user->notify(new RefundStatusNotification($refund));

                // Notify event owner
                $eventOwner = $refund->ticketPurchase->event->user;
                if ($eventOwner && $eventOwner->id !== $refund->user_id) {
                    $eventOwner->notify(new EventOwnerRefundNotification($refund, 'approved'));
                }
            }

            // Process approved refunds
            if ($refund->status === 'approved') {
                $this->processApprovedRefund($refund);
            }

        } catch (\Exception $e) {
            Log::error('Failed to process refund request', [
                'refund_id' => $this->refundRequest->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Mark as failed
            $this->refundRequest->markAsFailed('Processing error: ' . $e->getMessage());

            // Send failure notification
            $this->refundRequest->user->notify(new RefundStatusNotification($this->refundRequest));

            throw $e;
        }
    }

    /**
     * Process an approved refund request.
     */
    protected function processApprovedRefund(RefundRequest $refund): void
    {
        DB::transaction(function () use ($refund) {
            // Mark as processing
            $refund->markAsProcessing();

            // Send processing notification
            $refund->user->notify(new RefundStatusNotification($refund));

            try {
                // Try multiple approaches to find the payment
                $payment = null;

                // First, try the direct payment_id on the refund
                if ($refund->payment_id) {
                    $payment = Payment::find($refund->payment_id);
                }

                // If not found, try to get payment from the ticket purchase
                if (!$payment && $refund->ticketPurchase && $refund->ticketPurchase->payment_id) {
                    $payment = Payment::find($refund->ticketPurchase->payment_id);

                    // Update the refund with the correct payment_id for future reference
                    if ($payment) {
                        $refund->update(['payment_id' => $payment->id]);
                    }
                }

                // If still not found, try to get the payment via relationship
                if (!$payment && $refund->ticketPurchase && $refund->ticketPurchase->payment) {
                    $payment = $refund->ticketPurchase->payment;

                    // Update the refund with the correct payment_id for future reference
                    if ($payment) {
                        $refund->update(['payment_id' => $payment->id]);
                    }
                }

                // If all attempts fail, mark refund as requiring manual processing
                if (!$payment) {
                    Log::error('Payment record not found', [
                        'refund_id' => $refund->id,
                        'refund_reference' => $refund->refund_reference,
                        'ticket_purchase_id' => $refund->ticket_purchase_id,
                        'payment_id' => $refund->payment_id
                    ]);

                    // Update status to require manual processing instead of throwing exception
                    $refund->markAsManualReview('Payment record not found. Requires manual processing.');

                    // Send notification about manual processing
                    $refund->user->notify(new RefundStatusNotification($refund->fresh()));

                    return; // Exit function without throwing exception
                }
            } catch (\Exception $e) {
                // Log the error
                Log::error('Error processing refund in transaction', [
                    'refund_id' => $refund->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                // Mark as failed and notify
                $refund->markAsFailed('Error processing refund: ' . $e->getMessage());
                $refund->user->notify(new RefundStatusNotification($refund->fresh()));

                throw $e; // Re-throw to be caught by the main try-catch
            }

                // Process the actual refund through payment gateway
                $paymentService = new PaymentService();
                $result = $paymentService->processRefund($payment, (float)$refund->refund_amount);

                if ($result['success']) {
                    // Update ticket purchase status
                    $ticketPurchase = $refund->ticketPurchase;
                    $ticketPurchase->update([
                        'status' => 'refunded',
                        'cancellation_reason' => $refund->reason,
                        'cancelled_at' => now(),
                        'refund_amount' => $refund->refund_amount,
                        'refunded_at' => now()
                    ]);

                    // Return tickets to available pool
                    $ticketPurchase->ticket->decrement('quantity_sold', $ticketPurchase->quantity);

                    // Mark refund as completed
                    $refund->markAsCompleted(
                        $result['refund_id'] ?? null,
                        $result['response'] ?? null
                    );

                    Log::info('Refund processed successfully', [
                        'refund_id' => $refund->id,
                        'refund_reference' => $refund->refund_reference,
                        'amount' => $refund->refund_amount,
                        'gateway_refund_id' => $result['refund_id'] ?? null
                    ]);

                } else {
                    // Mark as failed
                    $refund->markAsFailed(
                        $result['error'] ?? 'Payment gateway error',
                        $result['response'] ?? null
                    );

                    Log::error('Refund processing failed', [
                        'refund_id' => $refund->id,
                        'error' => $result['error'] ?? 'Unknown error',
                        'response' => $result['response'] ?? null
                    ]);
                }

                // Send final status notification
                $refund->user->notify(new RefundStatusNotification($refund->fresh()));

                // Notify event owner of completion/failure
                $eventOwner = $refund->ticketPurchase->event->user;
                if ($eventOwner && $eventOwner->id !== $refund->user_id) {
                    $notificationType = $refund->status === 'completed' ? 'completed' : 'failed';
                    $eventOwner->notify(new EventOwnerRefundNotification($refund->fresh(), $notificationType));
                }
            });
    }

    /**
     * Determine if a refund should be auto-approved.
     */
    protected function shouldAutoApprove(RefundRequest $refund): bool
    {
        // Auto-approve if:
        // 1. Refund amount is below a certain threshold (e.g., $100)
        // 2. Event is more than 48 hours away
        // 3. User has no previous refund abuse

        $autoApprovalThreshold = 100; // $100
        $hoursBeforeEvent = 48;

        // Check amount threshold
        if ($refund->refund_amount > $autoApprovalThreshold) {
            return false;
        }

        // Check time before event
        $eventStart = $refund->ticketPurchase->event->start;
        $hoursUntilEvent = now()->diffInHours($eventStart);

        if ($hoursUntilEvent < $hoursBeforeEvent) {
            return false;
        }

        // Check user refund history (prevent abuse)
        $recentRefunds = RefundRequest::where('user_id', $refund->user_id)
            ->where('created_at', '>=', now()->subDays(30))
            ->where('id', '!=', $refund->id)
            ->count();

        if ($recentRefunds >= 3) { // Max 3 refunds per month
            return false;
        }

        return true;
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('ProcessRefundRequest job failed', [
            'refund_id' => $this->refundRequest->id,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);

        // Mark refund as failed
        $this->refundRequest->markAsFailed('Job processing failed: ' . $exception->getMessage());

        // Send failure notification
        $this->refundRequest->user->notify(new RefundStatusNotification($this->refundRequest));
    }
}
