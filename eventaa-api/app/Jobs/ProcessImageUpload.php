<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManagerStatic as Image;

class ProcessImageUpload implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $filePath;
    protected $sizes;
    protected $model;
    protected $modelId;
    protected $field;

    public $tries = 3;
    public $timeout = 300;

    /**
     * Create a new job instance.
     */
    public function __construct(string $filePath, array $sizes = [], string $model = null, int $modelId = null, string $field = null)
    {
        $this->filePath = $filePath;
        $this->sizes = $sizes ?: [
            'thumbnail' => ['width' => 150, 'height' => 150],
            'medium' => ['width' => 300, 'height' => 300],
            'large' => ['width' => 800, 'height' => 600]
        ];
        $this->model = $model;
        $this->modelId = $modelId;
        $this->field = $field;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('Processing image upload', [
                'file_path' => $this->filePath,
                'sizes' => $this->sizes
            ]);

            if (!Storage::exists($this->filePath)) {
                throw new \Exception("Source file not found: {$this->filePath}");
            }

            $originalPath = Storage::path($this->filePath);
            $pathInfo = pathinfo($this->filePath);
            $directory = $pathInfo['dirname'];
            $filename = $pathInfo['filename'];
            $extension = $pathInfo['extension'];

            $processedImages = [];

            foreach ($this->sizes as $sizeName => $dimensions) {
                $resizedPath = "{$directory}/{$filename}_{$sizeName}.{$extension}";

                $image = Image::make($originalPath);

                if (isset($dimensions['width']) && isset($dimensions['height'])) {
                    $image->fit($dimensions['width'], $dimensions['height']);
                } elseif (isset($dimensions['width'])) {
                    $image->widen($dimensions['width']);
                } elseif (isset($dimensions['height'])) {
                    $image->heighten($dimensions['height']);
                }

                $image->encode($extension, 85);

                Storage::put($resizedPath, $image->stream());

                $processedImages[$sizeName] = $resizedPath;

                Log::info("Created image variant", [
                    'size' => $sizeName,
                    'path' => $resizedPath,
                    'dimensions' => $dimensions
                ]);
            }

            // Update model if provided
            if ($this->model && $this->modelId && $this->field) {
                $modelClass = "App\\Models\\{$this->model}";
                if (class_exists($modelClass)) {
                    $modelInstance = $modelClass::find($this->modelId);
                    if ($modelInstance) {
                        $currentImages = $modelInstance->{$this->field} ?? [];
                        if (is_string($currentImages)) {
                            $currentImages = json_decode($currentImages, true) ?? [];
                        }

                        $currentImages['processed_variants'] = $processedImages;
                        $modelInstance->{$this->field} = $currentImages;
                        $modelInstance->save();
                    }
                }
            }

            Log::info('Image processing completed successfully', [
                'original_path' => $this->filePath,
                'processed_variants' => count($processedImages)
            ]);

        } catch (\Exception $e) {
            Log::error('Image processing failed', [
                'file_path' => $this->filePath,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Image processing job failed', [
            'file_path' => $this->filePath,
            'error' => $exception->getMessage()
        ]);
    }
}
