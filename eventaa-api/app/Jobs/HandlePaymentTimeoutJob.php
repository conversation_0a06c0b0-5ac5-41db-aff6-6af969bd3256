<?php

namespace App\Jobs;

use App\Models\PaymentTransaction;
use App\Models\TicketPurchase;
use App\Events\PaymentStatusUpdated;
use App\Notifications\PaymentTimeoutNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class HandlePaymentTimeoutJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $transactionId;
    protected $timeoutMinutes;

    /**
     * Create a new job instance.
     */
    public function __construct(string $transactionId, int $timeoutMinutes = 30)
    {
        $this->transactionId = $transactionId;
        $this->timeoutMinutes = $timeoutMinutes;
        
        // Delay the job execution by the timeout period
        $this->delay(now()->addMinutes($timeoutMinutes));
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $transaction = PaymentTransaction::where('transaction_id', $this->transactionId)->first();
            
            if (!$transaction) {
                Log::warning('Payment timeout job: Transaction not found', [
                    'transaction_id' => $this->transactionId
                ]);
                return;
            }

            // Skip if payment is already completed or failed
            if (in_array($transaction->status, ['completed', 'failed', 'cancelled'])) {
                Log::info('Payment timeout job: Transaction already processed', [
                    'transaction_id' => $this->transactionId,
                    'status' => $transaction->status
                ]);
                return;
            }

            // Check if payment is still pending after timeout period
            if ($transaction->status === 'pending') {
                $this->handleTimeout($transaction);
            }

        } catch (\Exception $e) {
            Log::error('Payment timeout job failed', [
                'transaction_id' => $this->transactionId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Handle payment timeout
     */
    private function handleTimeout(PaymentTransaction $transaction): void
    {
        $oldStatus = $transaction->status;
        
        // Update transaction status
        $transaction->status = 'failed';
        $transaction->gateway_response = array_merge(
            $transaction->gateway_response ?? [],
            [
                'timeout_reason' => 'Payment not completed within timeout period',
                'timeout_minutes' => $this->timeoutMinutes,
                'timed_out_at' => now()->toISOString()
            ]
        );
        $transaction->save();

        // Handle rollback based on payment type
        $this->handleRollback($transaction);

        // Notify user about timeout
        if ($transaction->user) {
            $transaction->user->notify(new PaymentTimeoutNotification($transaction));
        }

        // Broadcast payment status update
        event(new PaymentStatusUpdated($transaction, $oldStatus, 'failed'));

        Log::warning('Payment timed out', [
            'transaction_id' => $this->transactionId,
            'payment_type' => $transaction->payment_type,
            'amount' => $transaction->amount,
            'timeout_minutes' => $this->timeoutMinutes
        ]);
    }

    /**
     * Handle rollback operations for different payment types
     */
    private function handleRollback(PaymentTransaction $transaction): void
    {
        switch ($transaction->payment_type) {
            case 'ticket_purchase':
                $this->rollbackTicketPurchase($transaction);
                break;
            
            case 'subscription':
                $this->rollbackSubscription($transaction);
                break;
            
            case 'booking':
                $this->rollbackBooking($transaction);
                break;
        }
    }

    /**
     * Rollback ticket purchase
     */
    private function rollbackTicketPurchase(PaymentTransaction $transaction): void
    {
        try {
            $purchases = TicketPurchase::where('purchase_reference', $transaction->transaction_id)->get();
            
            foreach ($purchases as $purchase) {
                // Restore ticket quantity
                if ($purchase->ticket) {
                    $purchase->ticket->decrement('quantity_sold', $purchase->quantity);
                }
                
                // Mark purchase as failed
                $purchase->update(['status' => 'failed']);
            }

            Log::info('Ticket purchase rollback completed', [
                'transaction_id' => $this->transactionId,
                'purchases_count' => $purchases->count()
            ]);
        } catch (\Exception $e) {
            Log::error('Ticket purchase rollback failed', [
                'transaction_id' => $this->transactionId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Rollback subscription
     */
    private function rollbackSubscription(PaymentTransaction $transaction): void
    {
        try {
            if ($transaction->bookable_type === \App\Models\Subscription::class && $transaction->bookable) {
                $subscription = $transaction->bookable;
                
                // Mark subscription as failed if it hasn't been activated
                if (!$subscription->start_date) {
                    $subscription->delete();
                }
            }

            Log::info('Subscription rollback completed', [
                'transaction_id' => $this->transactionId
            ]);
        } catch (\Exception $e) {
            Log::error('Subscription rollback failed', [
                'transaction_id' => $this->transactionId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Rollback booking
     */
    private function rollbackBooking(PaymentTransaction $transaction): void
    {
        try {
            if ($transaction->bookable_type === \App\Models\VendorBooking::class && $transaction->bookable) {
                $booking = $transaction->bookable;
                
                // Reset booking payment status
                $booking->is_paid = false;
                $booking->save();
            }

            Log::info('Booking rollback completed', [
                'transaction_id' => $this->transactionId
            ]);
        } catch (\Exception $e) {
            Log::error('Booking rollback failed', [
                'transaction_id' => $this->transactionId,
                'error' => $e->getMessage()
            ]);
        }
    }
}
