<?php

namespace App\Jobs;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class GenerateReport implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $reportType;
    protected $parameters;
    protected $user;
    protected $fileName;

    public $tries = 3;
    public $timeout = 600; // 10 minutes for report generation

    /**
     * Create a new job instance.
     */
    public function __construct(string $reportType, array $parameters, User $user, string $fileName = null)
    {
        $this->reportType = $reportType;
        $this->parameters = $parameters;
        $this->user = $user;
        $this->fileName = $fileName ?: Str::slug($reportType) . '_' . now()->format('Y_m_d_H_i_s') . '.csv';
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('Generating report', [
                'report_type' => $this->reportType,
                'user_id' => $this->user->id,
                'file_name' => $this->fileName
            ]);

            $reportData = $this->generateReportData();
            $filePath = $this->generateReportFile($reportData);

            // Store report metadata in database (you may need to create a reports table)
            $this->storeReportMetadata($filePath);

            // Send notification to user that report is ready
            dispatch(new SendEmailNotification(
                $this->user,
                'Report Generated Successfully',
                "Your {$this->reportType} report has been generated and is ready for download.",
                null,
                ['download_link' => Storage::url($filePath)]
            ));

            Log::info('Report generated successfully', [
                'report_type' => $this->reportType,
                'user_id' => $this->user->id,
                'file_path' => $filePath
            ]);

        } catch (\Exception $e) {
            Log::error('Report generation failed', [
                'report_type' => $this->reportType,
                'user_id' => $this->user->id,
                'error' => $e->getMessage()
            ]);

            // Send error notification to user
            dispatch(new SendEmailNotification(
                $this->user,
                'Report Generation Failed',
                "Sorry, there was an error generating your {$this->reportType} report. Please try again later."
            ));

            throw $e;
        }
    }

    /**
     * Generate report data based on type.
     */
    private function generateReportData(): array
    {
        switch ($this->reportType) {
            case 'ticket_sales':
                return $this->generateTicketSalesReport();
            case 'event_analytics':
                return $this->generateEventAnalyticsReport();
            case 'user_activities':
                return $this->generateUserActivitiesReport();
            case 'financial_summary':
                return $this->generateFinancialSummaryReport();
            default:
                throw new \Exception("Unknown report type: {$this->reportType}");
        }
    }

    /**
     * Generate ticket sales report data.
     */
    private function generateTicketSalesReport(): array
    {
        $startDate = $this->parameters['start_date'] ?? now()->subMonth();
        $endDate = $this->parameters['end_date'] ?? now();
        $eventId = $this->parameters['event_id'] ?? null;

        $query = \DB::table('ticket_purchases')
            ->join('tickets', 'ticket_purchases.ticket_id', '=', 'tickets.id')
            ->join('events', 'ticket_purchases.event_id', '=', 'events.id')
            ->join('users', 'ticket_purchases.user_id', '=', 'users.id')
            ->select([
                'events.title as event_title',
                'tickets.name as ticket_name',
                'users.name as customer_name',
                'users.email as customer_email',
                'ticket_purchases.quantity',
                'ticket_purchases.unit_price',
                'ticket_purchases.total_amount',
                'ticket_purchases.status',
                'ticket_purchases.purchased_at'
            ])
            ->whereBetween('ticket_purchases.purchased_at', [$startDate, $endDate]);

        if ($eventId) {
            $query->where('ticket_purchases.event_id', $eventId);
        }

        return $query->get()->toArray();
    }

    /**
     * Generate event analytics report data.
     */
    private function generateEventAnalyticsReport(): array
    {
        $startDate = $this->parameters['start_date'] ?? now()->subMonth();
        $endDate = $this->parameters['end_date'] ?? now();

        return \DB::table('events')
            ->leftJoin('ticket_purchases', 'events.id', '=', 'ticket_purchases.event_id')
            ->select([
                'events.title',
                'events.start_date',
                'events.end_date',
                'events.location',
                \DB::raw('COUNT(ticket_purchases.id) as total_tickets_sold'),
                \DB::raw('SUM(ticket_purchases.total_amount) as total_revenue'),
                \DB::raw('COUNT(DISTINCT ticket_purchases.user_id) as unique_customers')
            ])
            ->whereBetween('events.start_date', [$startDate, $endDate])
            ->groupBy('events.id', 'events.title', 'events.start_date', 'events.end_date', 'events.location')
            ->get()
            ->toArray();
    }

    /**
     * Generate user activities report data.
     */
    private function generateUserActivitiesReport(): array
    {
        $startDate = $this->parameters['start_date'] ?? now()->subMonth();
        $endDate = $this->parameters['end_date'] ?? now();

        return \DB::table('users')
            ->leftJoin('ticket_purchases', 'users.id', '=', 'ticket_purchases.user_id')
            ->select([
                'users.name',
                'users.email',
                'users.created_at as registration_date',
                \DB::raw('COUNT(ticket_purchases.id) as total_purchases'),
                \DB::raw('SUM(ticket_purchases.total_amount) as total_spent'),
                \DB::raw('MAX(ticket_purchases.purchased_at) as last_purchase_date')
            ])
            ->whereBetween('ticket_purchases.purchased_at', [$startDate, $endDate])
            ->groupBy('users.id', 'users.name', 'users.email', 'users.created_at')
            ->get()
            ->toArray();
    }

    /**
     * Generate financial summary report data.
     */
    private function generateFinancialSummaryReport(): array
    {
        $startDate = $this->parameters['start_date'] ?? now()->subMonth();
        $endDate = $this->parameters['end_date'] ?? now();

        return \DB::table('payments')
            ->select([
                'payment_method',
                'type',
                \DB::raw('COUNT(*) as transaction_count'),
                \DB::raw('SUM(amount) as total_amount'),
                \DB::raw('AVG(amount) as average_amount'),
                \DB::raw('DATE(created_at) as transaction_date')
            ])
            ->whereBetween('created_at', [$startDate, $endDate])
            ->where('status', 'completed')
            ->groupBy('payment_method', 'type', \DB::raw('DATE(created_at)'))
            ->orderBy('transaction_date', 'desc')
            ->get()
            ->toArray();
    }

    /**
     * Generate report file from data.
     */
    private function generateReportFile(array $data): string
    {
        $filePath = "reports/{$this->fileName}";

        if (empty($data)) {
            $csvContent = "No data available for the selected criteria.\n";
        } else {
            $csvContent = $this->arrayToCsv($data);
        }

        Storage::put($filePath, $csvContent);

        return $filePath;
    }

    /**
     * Convert array data to CSV format.
     */
    private function arrayToCsv(array $data): string
    {
        if (empty($data)) {
            return '';
        }

        $output = fopen('php://temp', 'r+');

        // Add headers
        $headers = array_keys((array) $data[0]);
        fputcsv($output, $headers);

        // Add data rows
        foreach ($data as $row) {
            fputcsv($output, (array) $row);
        }

        rewind($output);
        $csv = stream_get_contents($output);
        fclose($output);

        return $csv;
    }

    /**
     * Store report metadata in database.
     */
    private function storeReportMetadata(string $filePath): void
    {
        // You may want to create a reports table to track generated reports
        // For now, we'll log the information
        Log::info('Report metadata', [
            'user_id' => $this->user->id,
            'report_type' => $this->reportType,
            'file_path' => $filePath,
            'parameters' => $this->parameters,
            'generated_at' => now()
        ]);
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Report generation job failed', [
            'report_type' => $this->reportType,
            'user_id' => $this->user->id,
            'error' => $exception->getMessage()
        ]);
    }
}
