<?php

namespace App\Jobs;

use App\Models\PaymentTransaction;
use App\Models\Payment;
use App\Services\PayChanguService;
use App\Events\PaymentStatusUpdated;
use App\Models\TicketPurchase;
use App\Models\Subscription;
use App\Models\User;
use App\Models\VendorBooking;
use App\Events\TicketPurchaseCompleted;
use App\Events\SubscriptionActivated;
use App\Notifications\TicketPurchaseNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class VerifyPaymentJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $transactionId;
    protected $maxAttempts = 5;
    protected $retryAfter = 30;

    public function __construct(string $transactionId)
    {
        $this->transactionId = $transactionId;
    }

    private function isTestNumber(string $phoneNumber): bool
    {
        $testNumbers = [
            '899817565',
            '888888888',
            '999999999',
            '123456789',
        ];

        return in_array($phoneNumber, $testNumbers);
    }

    public function handle(): void
    {
        try {
            $transaction = PaymentTransaction::where('transaction_id', $this->transactionId)->first();

            if (!$transaction) {
                Log::warning('Payment verification job: Transaction not found', [
                    'transaction_id' => $this->transactionId
                ]);
                return;
            }

            if (in_array($transaction->status, ['completed', 'failed', 'cancelled'])) {
                Log::info('Payment verification job: Transaction already processed', [
                    'transaction_id' => $this->transactionId,
                    'status' => $transaction->status
                ]);
                return;
            }

            $phoneNumber = $transaction->metadata['phone_number'] ?? '';
            if ($this->isTestNumber($phoneNumber)) {
                Log::info('Processing test number payment', [
                    'transaction_id' => $this->transactionId,
                    'phone_number' => $phoneNumber
                ]);

                $this->handleSuccessfulPayment($transaction, [
                    'status' => 'success',
                    'charge_id' => $transaction->metadata['charge_id'] ?? null,
                    'amount' => $transaction->amount,
                    'currency' => $transaction->currency,
                    'test_payment' => true
                ], $transaction->status);
                return;
            }

            $payChanguService = new PayChanguService();
            $result = $payChanguService->verifyPayment($this->transactionId);

            if (!$result['status']) {
                Log::warning('Payment verification failed', [
                    'transaction_id' => $this->transactionId,
                    'result' => $result
                ]);

                if ($this->attempts() < $this->maxAttempts) {
                    $this->release($this->retryAfter);
                    return;
                }

                $this->markTransactionAsFailed($transaction, 'Verification failed after maximum attempts');
                return;
            }

            $paymentData = $result['data'];
            $oldStatus = $transaction->status;

            if (isset($paymentData['status'])) {
                switch ($paymentData['status']) {
                    case 'success':
                    case 'completed':
                        $this->handleSuccessfulPayment($transaction, $paymentData, $oldStatus);
                        break;

                    case 'failed':
                    case 'cancelled':
                        $this->markTransactionAsFailed($transaction, 'Payment failed at gateway');
                        break;

                    case 'pending':
                    case 'processing':
                        if ($this->attempts() < $this->maxAttempts) {
                            $this->release($this->retryAfter);
                        } else {
                            $this->markTransactionAsFailed($transaction, 'Payment timeout - exceeded maximum verification attempts');
                        }
                        break;

                    default:
                        Log::warning('Unknown payment status received', [
                            'transaction_id' => $this->transactionId,
                            'status' => $paymentData['status']
                        ]);

                        if ($this->attempts() < $this->maxAttempts) {
                            $this->release($this->retryAfter);
                        }
                        break;
                }
            }

        } catch (\Exception $e) {
            Log::error('Payment verification job failed', [
                'transaction_id' => $this->transactionId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'attempt' => $this->attempts()
            ]);

            if ($this->attempts() < $this->maxAttempts) {
                $this->release($this->retryAfter);
            } else {
                $transaction = PaymentTransaction::where('transaction_id', $this->transactionId)->first();
                if ($transaction) {
                    $this->markTransactionAsFailed($transaction, 'Verification job failed: ' . $e->getMessage());
                }
            }
        }
    }

    /**
     * Handle successful payment and update transaction status
     *
     * @param PaymentTransaction $transaction The payment transaction
     * @param array $paymentData Payment gateway response data
     * @param string $oldStatus Previous transaction status
     * @return void
     */
    private function handleSuccessfulPayment(PaymentTransaction $transaction, array $paymentData, string $oldStatus): void
    {
        $metadata = $transaction->metadata ?? [];

        // If this is a ticket transaction, find or associate the payment record
        if (strpos($transaction->transaction_id, 'TKT-') === 0) {
            $metadata = $this->findAndAssociatePayment($transaction, $metadata);
        }

        // Update transaction record
        $this->updateTransactionAsCompleted($transaction, $metadata, $paymentData);

        Log::info('Handling successful payment', [
            'transaction_id' => $this->transactionId,
            'payment_type' => $transaction->payment_type
        ]);

        // Process different payment types
        switch ($transaction->payment_type) {
            case 'booking':
                $this->processBookingPayment($transaction);
                break;

            case 'subscription':
                $this->processSubscriptionPayment($transaction);
                break;

            case 'ticket_purchase':
                $this->processTicketPurchasePayment($transaction);
                break;
        }

        // Broadcast payment status updated event
        event(new PaymentStatusUpdated($transaction, $oldStatus, 'completed'));

        Log::info('Payment verification successful', [
            'transaction_id' => $this->transactionId,
            'payment_type' => $transaction->payment_type,
            'amount' => $transaction->amount
        ]);
    }

    /**
     * Find and associate payment record with transaction
     *
     * @param PaymentTransaction $transaction
     * @param array $metadata
     * @return array Updated metadata
     */
    private function findAndAssociatePayment(PaymentTransaction $transaction, array $metadata): array
    {
        $payment = Payment::where('transaction_id', $transaction->transaction_id)->first();
        if ($payment) {
            $metadata['payment_id'] = $payment->id;
            Log::info('Found payment record for transaction', [
                'transaction_id' => $this->transactionId,
                'payment_id' => $payment->id
            ]);
        } else {
            // Try to find payment with a similar transaction pattern
            $possiblePrefix = substr($transaction->transaction_id, 0, 5); // e.g. "TKT-2"
            $possiblePayments = Payment::where('created_at', '>=', now()->subHour())
                ->whereRaw("transaction_id LIKE ?", ["{$possiblePrefix}%"])
                ->get();

            if ($possiblePayments->isNotEmpty()) {
                $payment = $possiblePayments->first();
                $metadata['payment_id'] = $payment->id;
                $metadata['possible_payment_ids'] = $possiblePayments->pluck('id')->toArray();

                Log::info('Found possible payment records for transaction by prefix', [
                    'transaction_id' => $this->transactionId,
                    'prefix' => $possiblePrefix,
                    'payment_id' => $payment->id,
                    'payment_transaction_id' => $payment->transaction_id,
                    'match_count' => $possiblePayments->count()
                ]);
            }
        }

        return $metadata;
    }

    /**
     * Update transaction record to completed status
     *
     * @param PaymentTransaction $transaction
     * @param array $metadata
     * @param array $paymentData
     * @return void
     */
    private function updateTransactionAsCompleted(PaymentTransaction $transaction, array $metadata, array $paymentData): void
    {
        $transaction->status = 'completed';
        $transaction->payment_date = now();
        $transaction->metadata = $metadata;
        $transaction->gateway_response = array_merge(
            $transaction->gateway_response ?? [],
            ['verification_job' => $paymentData]
        );
        $transaction->save();
    }

    /**
     * Process booking payment
     *
     * @param PaymentTransaction $transaction
     * @return void
     */
    private function processBookingPayment(PaymentTransaction $transaction): void
    {
        if ($transaction->bookable_type === VendorBooking::class) {
            $booking = $transaction->bookable;
            if ($booking) {
                $booking->is_paid = true;
                $booking->save();
            }
        }
    }

    /**
     * Process subscription payment
     *
     * @param PaymentTransaction $transaction
     * @return void
     */
    private function processSubscriptionPayment(PaymentTransaction $transaction): void
    {
        $subscription = $transaction->bookable;
        if (!$subscription || !is_object($subscription)) {
            return;
        }

        try {
            $subscriptionModel = $subscription;
            $userSubscriptions = app('db')
                ->table($subscriptionModel->getTable())
                ->where('user_id', $transaction->user_id)
                ->where('id', '!=', $subscriptionModel->id)
                ->where(function ($query) {
                    $query->whereNull('end_date')
                        ->orWhere('end_date', '>', now());
                })
                ->update(['end_date' => now()]);

            Log::info('Deactivated existing user subscriptions', [
                'transaction_id' => $this->transactionId,
                'updated_count' => $userSubscriptions,
                'user_id' => $transaction->user_id
            ]);

            // Activate the new subscription
            $subscriptionModel->start_date = now();
            $subscriptionModel->save();

            // Assign host role to the user if they don't have it
            $user = User::find($transaction->user_id);
            if ($user && method_exists($user, 'hasRole') && !$user->hasRole('host')) {
                $user->assignRole('host');
                Log::info('Assigned host role to user', [
                    'transaction_id' => $this->transactionId,
                    'user_id' => $user->id
                ]);
            }

            // Only fire event if we can safely determine subscription is the right type
            if ($user && class_exists('\App\Models\Subscription') && $subscriptionModel instanceof \App\Models\Subscription) {
                event(new \App\Events\SubscriptionActivated($subscriptionModel, $user));
                Log::info('SubscriptionActivated event fired', [
                    'transaction_id' => $this->transactionId,
                    'subscription_id' => $subscriptionModel->id,
                    'user_id' => $user->id
                ]);
            } else {
                Log::warning('Could not fire SubscriptionActivated event - type mismatch', [
                    'transaction_id' => $this->transactionId,
                    'subscription_type' => get_class($subscriptionModel),
                    'user_type' => $user ? get_class($user) : 'null'
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Error processing subscription activation', [
                'transaction_id' => $this->transactionId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Process ticket purchase payment
     *
     * @param PaymentTransaction $transaction
     * @return void
     */
    private function processTicketPurchasePayment(PaymentTransaction $transaction): void
    {
        // Find ticket purchases
        $purchases = $this->findTicketPurchases($transaction);

        // Store transaction-purchase relationship in metadata
        if ($purchases->isNotEmpty()) {
            $this->storeTicketPurchaseRelationship($transaction, $purchases);

            // Update purchases status to completed
            $purchases->each(function ($purchase) {
                $purchase->update(['status' => 'completed']);
            });

            // Broadcast ticket purchase completion
            $this->broadcastTicketPurchaseCompletion($transaction, $purchases);
        } else {
            Log::error('🎫 Cannot complete ticket purchase - no tickets found', [
                'transaction_id' => $this->transactionId,
                'metadata' => $transaction->metadata,
                'has_tickets_metadata' => isset($transaction->metadata['tickets']),
                'user_id' => $transaction->user_id
            ]);
        }
    }

    /**
     * Find ticket purchases related to the transaction
     *
     * @param PaymentTransaction $transaction
     * @return \Illuminate\Support\Collection
     */
    private function findTicketPurchases(PaymentTransaction $transaction): \Illuminate\Support\Collection
    {
        // Try direct match first (transaction_id and purchase_reference are the same)
        $purchases = TicketPurchase::where('purchase_reference', $transaction->transaction_id)
            ->with(['event', 'ticket.tier'])
            ->get();

        Log::info('🎫 Searching for ticket purchases', [
            'transaction_id' => $this->transactionId,
            'purchase_reference' => $transaction->transaction_id,
            'purchase_count' => $purchases->count()
        ]);

        // If no purchases found, look in metadata for ticket_purchase_reference
        if ($purchases->isEmpty() && isset($transaction->metadata['ticket_purchase_reference'])) {
            $purchaseRef = $transaction->metadata['ticket_purchase_reference'];
            Log::info('🎫 Searching for ticket purchases using metadata reference', [
                'transaction_id' => $this->transactionId,
                'ticket_purchase_reference' => $purchaseRef
            ]);

            $purchases = TicketPurchase::where('purchase_reference', $purchaseRef)
                ->with(['event', 'ticket.tier'])
                ->get();
        }

        // Last resort: try to find by payment_id if we have it in metadata
        if ($purchases->isEmpty() && isset($transaction->metadata['payment_id'])) {
            $paymentId = $transaction->metadata['payment_id'];
            Log::info('🎫 Searching for ticket purchases using payment_id', [
                'transaction_id' => $this->transactionId,
                'payment_id' => $paymentId
            ]);

            $purchases = TicketPurchase::where('payment_id', $paymentId)
                ->with(['event', 'ticket.tier'])
                ->get();
        }

        // If still not found, try to create from metadata or find by transaction pattern
        if ($purchases->isEmpty()) {
            $purchases = $this->handleMissingTicketPurchases($transaction);
        }

        return $purchases;
    }

    /**
     * Handle case when no ticket purchases are found
     *
     * @param PaymentTransaction $transaction
     * @return \Illuminate\Support\Collection
     */
    private function handleMissingTicketPurchases(PaymentTransaction $transaction): \Illuminate\Support\Collection
    {
        Log::warning('🎫 No ticket purchases found - checking all TicketPurchase records', [
            'transaction_id' => $this->transactionId,
            'total_ticket_purchases' => TicketPurchase::count(),
            'recent_purchases' => TicketPurchase::latest()->take(5)->pluck('purchase_reference', 'id')->toArray()
        ]);

        // Try to create TicketPurchase records from transaction metadata
        if (isset($transaction->metadata['tickets'])) {
            return $this->createTicketPurchasesFromMetadata($transaction);
        } else {
            // Look for any transactions that might match by pattern
            return $this->findTicketPurchasesByPattern($transaction);
        }
    }

    /**
     * Create ticket purchases from transaction metadata
     *
     * @param PaymentTransaction $transaction
     * @return \Illuminate\Support\Collection
     */
    private function createTicketPurchasesFromMetadata(PaymentTransaction $transaction): \Illuminate\Support\Collection
    {
        Log::info('🎫 Creating TicketPurchase records from transaction metadata', [
            'transaction_id' => $this->transactionId,
            'tickets_data' => $transaction->metadata['tickets']
        ]);

        $user = User::find($transaction->user_id);
        if (!$user) {
            Log::error('🎫 User not found for ticket purchase', [
                'transaction_id' => $this->transactionId,
                'user_id' => $transaction->user_id
            ]);
            return collect();
        }

        // Create Payment record if it doesn't exist
        $payment = \App\Models\Payment::where('transaction_id', $transaction->transaction_id)->first();
        if (!$payment) {
            $payment = \App\Models\Payment::create([
                'user_id' => $transaction->user_id,
                'amount' => $transaction->amount,
                'status' => 'completed',
                'type' => 'ticket_purchase',
                'payment_method' => 'paychangu',
                'transaction_id' => $transaction->transaction_id,
                'metadata' => $transaction->metadata
            ]);
        }

        // Create TicketPurchase records from metadata
        $createdPurchases = collect();
        foreach ($transaction->metadata['tickets'] as $ticketData) {
            $ticket = \App\Models\Ticket::find($ticketData['ticket_id']);
            if ($ticket) {
                $purchase = TicketPurchase::create([
                    'user_id' => $transaction->user_id,
                    'event_id' => $ticketData['event_id'],
                    'ticket_id' => $ticketData['ticket_id'],
                    'payment_id' => $payment->id,
                    'purchase_reference' => $transaction->transaction_id,
                    'quantity' => $ticketData['quantity'],
                    'unit_price' => $ticketData['unit_price'],
                    'total_amount' => $ticketData['total_price'],
                    'status' => 'completed',
                    'purchased_at' => now(),
                    'attendee_name' => $user->name,
                    'attendee_email' => $user->email,
                    'attendee_phone' => $user->phone,
                ]);
                $createdPurchases->push($purchase);
            }
        }

        if ($createdPurchases->isNotEmpty()) {
            Log::info('🎫 Created TicketPurchase records from metadata', [
                'transaction_id' => $this->transactionId,
                'purchases_created' => $createdPurchases->count(),
                'purchase_ids' => $createdPurchases->pluck('id')->toArray()
            ]);
        }

        return $createdPurchases;
    }

    /**
     * Find ticket purchases by transaction ID pattern
     *
     * @param PaymentTransaction $transaction
     * @return \Illuminate\Support\Collection
     */
    private function findTicketPurchasesByPattern(PaymentTransaction $transaction): \Illuminate\Support\Collection
    {
        if (strpos($transaction->transaction_id, 'TKT-') !== 0) {
            return collect();
        }

        // Extract more specific parts: TKT-{user_id}-{timestamp}-{microseconds}
        $parts = explode('-', $transaction->transaction_id);
        if (count($parts) < 4) {
            return collect();
        }

        // Use first 4 parts for more specific matching
        $specificPrefix = implode('-', array_slice($parts, 0, 4));

        $potentialMatches = TicketPurchase::where('created_at', '>=', now()->subHour())
            ->where('created_at', '<=', $transaction->created_at->addMinutes(10)) // Only match purchases created within 10 minutes of transaction
            ->whereRaw("purchase_reference LIKE ?", ["{$specificPrefix}%"])
            ->with(['event', 'ticket.tier'])
            ->get();

        if ($potentialMatches->isNotEmpty()) {
            Log::info('🎫 Found potential ticket purchase matches by specific prefix', [
                'transaction_id' => $this->transactionId,
                'specific_prefix' => $specificPrefix,
                'match_count' => $potentialMatches->count(),
                'matches' => $potentialMatches->pluck('purchase_reference', 'id')->toArray()
            ]);
        }

        return $potentialMatches;
    }

    /**
     * Store ticket purchase IDs in transaction metadata
     *
     * @param PaymentTransaction $transaction
     * @param \Illuminate\Support\Collection $purchases
     * @return void
     */
    private function storeTicketPurchaseRelationship(PaymentTransaction $transaction, \Illuminate\Support\Collection $purchases): void
    {
        if (isset($transaction->metadata['ticket_purchase_ids'])) {
            return; // Already stored
        }

        $purchaseIds = $purchases->pluck('id')->toArray();
        $purchaseRefs = $purchases->pluck('purchase_reference')->toArray();

        $metadata = $transaction->metadata ?? [];
        $metadata['ticket_purchase_ids'] = $purchaseIds;
        $metadata['ticket_purchase_references'] = $purchaseRefs;

        $transaction->metadata = $metadata;
        $transaction->save();

        Log::info('🎫 Saved ticket purchase references to transaction metadata', [
            'transaction_id' => $this->transactionId,
            'purchase_ids' => $purchaseIds,
            'purchase_references' => $purchaseRefs
        ]);
    }

    /**
     * Broadcast ticket purchase completion event and send notifications
     *
     * @param PaymentTransaction $transaction
     * @param \Illuminate\Support\Collection $purchases
     * @return void
     */
    private function broadcastTicketPurchaseCompletion(PaymentTransaction $transaction, \Illuminate\Support\Collection $purchases): void
    {
        Log::info('🎫 About to broadcast TicketPurchaseCompleted event', [
            'transaction_id' => $this->transactionId,
            'user_id' => $transaction->user_id,
            'purchase_count' => $purchases->count(),
            'purchase_ids' => $purchases->pluck('id')->toArray(),
            'channel' => 'tickets.user.' . $transaction->user_id
        ]);

        // Get user
        $user = $transaction->user;
        if (!$user) {
            $user = User::find($transaction->user_id);
        }

        if (!$user) {
            Log::error('🎫 Cannot find user for ticket purchase', [
                'transaction_id' => $this->transactionId,
                'user_id' => $transaction->user_id
            ]);
            return; // Skip event if no user found
        }

        try {
            // Check if we already broadcasted this event to avoid duplicates
            $alreadyBroadcasted = $transaction->metadata['ticket_event_broadcasted'] ?? false;

            if (!$alreadyBroadcasted) {
                // Broadcast the event and send notification
                $this->sendTicketPurchaseEvent($transaction, $purchases, $user);
            } else {
                Log::info('🎫 TicketPurchaseCompleted event already broadcasted - skipping', [
                    'transaction_id' => $this->transactionId,
                    'broadcast_time' => $transaction->metadata['broadcast_time'] ?? 'unknown'
                ]);
            }
        } catch (\Exception $e) {
            Log::error('🎫 Error broadcasting ticket purchase event', [
                'transaction_id' => $this->transactionId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Send ticket purchase event and notification
     *
     * @param PaymentTransaction $transaction
     * @param \Illuminate\Support\Collection $purchases
     * @param User $user
     * @return void
     */
    private function sendTicketPurchaseEvent(PaymentTransaction $transaction, \Illuminate\Support\Collection $purchases, User $user): void
    {
        // Make sure we have all the necessary data loaded by refreshing each model
        $purchases = $purchases->map(function ($purchase) {
            return $purchase->fresh(['event', 'ticket.tier', 'user']);
        });

        // Get primary event name if there are multiple events
        $eventName = $this->getEventNameFromPurchases($purchases);

        // Get PDF URL for tickets
        $pdfUrl = $this->getOrGenerateTicketPDF($transaction, $purchases, $user);

        // Verify purchase data integrity and log details for debugging
        $purchaseDetails = $this->logPurchaseDetails($purchases);

        // Fire the event
        $ticketEvent = new TicketPurchaseCompleted(
            $purchases,
            $user,
            (float) $transaction->amount,
            $pdfUrl,
            $eventName
        );

        Log::info('🎫 Firing TicketPurchaseCompleted event now', [
            'transaction_id' => $this->transactionId,
            'user_id' => $user->id,
            'purchase_count' => $purchases->count(),
            'event_name' => $eventName,
            'channel' => 'tickets.user.' . $user->id,
            'event_data' => [
                'total_amount' => (float) $transaction->amount,
                'purchase_ids' => $purchases->pluck('id')->toArray()
            ]
        ]);

        event($ticketEvent);

        // Mark as broadcasted to prevent duplicate events
        $metadata = $transaction->metadata ?? [];
        $metadata['ticket_event_broadcasted'] = true;
        $metadata['broadcast_time'] = now()->toIso8601String();
        $transaction->metadata = $metadata;
        $transaction->save();

        Log::info('🎫 TicketPurchaseCompleted event fired successfully and marked as broadcasted');

        // Send notification
        $this->sendTicketPurchaseNotification($transaction, $purchases, $user);
    }

    /**
     * Get event name from purchases
     *
     * @param \Illuminate\Support\Collection $purchases
     * @return string|null
     */
    private function getEventNameFromPurchases(\Illuminate\Support\Collection $purchases): ?string
    {
        if ($purchases->isEmpty()) {
            return null;
        }

        $eventIds = $purchases->pluck('event_id')->unique()->toArray();
        if (count($eventIds) === 1) {
            // If all purchases are for the same event, use that event's name
            $event = $purchases->first()->event;
            return $event ? $event->name : null;
        } else {
            // Multiple events, use the first one and add "(+ more)"
            $event = $purchases->first()->event;
            return $event ? $event->name . ' (+ more)' : null;
        }
    }

    /**
     * Get or generate ticket PDF URL
     *
     * @param PaymentTransaction $transaction
     * @param \Illuminate\Support\Collection $purchases
     * @param User $user
     * @return string|null
     */
    private function getOrGenerateTicketPDF(PaymentTransaction $transaction, \Illuminate\Support\Collection $purchases, User $user): ?string
    {
        // Check if we already have PDF URL
        if (isset($transaction->metadata['ticket_pdf_url'])) {
            return $transaction->metadata['ticket_pdf_url'];
        }

        // Try to generate PDF tickets if we have valid purchase data
        try {
            if ($purchases->isNotEmpty()) {
                $ticketService = app(\App\Services\TicketService::class);
                $ticketData = [];

                foreach ($purchases as $purchase) {
                    if ($purchase->event && $purchase->ticket) {
                        for ($i = 0; $i < $purchase->quantity; $i++) {
                            // Get banner from tier if available, otherwise from ticket, then event
                            $banner = null;
                            if ($purchase->ticket->tier && $purchase->ticket->tier->banner) {
                                $banner = $purchase->ticket->tier->banner;
                            } elseif ($purchase->ticket->banner) {
                                $banner = $purchase->ticket->banner;
                            } elseif ($purchase->event->cover_art) {
                                $banner = $purchase->event->cover_art;
                            }

                            $ticketData[] = [
                                'user_ticket_id' => $purchase->id . '-' . ($i+1),
                                'event_name' => $purchase->event->name ?? $purchase->event->title ?? 'Event',
                                'event_date' => $this->formatEventDate($purchase->event->start),
                                'event_location' => $purchase->event->location ?? '',
                                'ticket_name' => $purchase->ticket->name ?? 'Ticket',
                                'price' => number_format((float) ($purchase->unit_price ?? 0), 2),
                                'attendee_name' => $purchase->attendee_name ?? ($user ? $user->name : 'Attendee'),
                                'ticket_uuid' => ($purchase->purchase_reference ?? $purchase->id ?? uniqid()) . '-' . ($i+1),
                                'ticket_number' => ($i+1) . ' of ' . $purchase->quantity,
                                'banner' => $banner,
                            ];
                        }
                    }
                }

                if (!empty($ticketData)) {
                    try {
                        $purchaseRef = $purchases->first()->purchase_reference ?? $this->transactionId;
                        Log::info('🎫 Generating PDF for purchase', [
                            'transaction_id' => $this->transactionId,
                            'purchase_ref' => $purchaseRef,
                            'ticket_count' => count($ticketData)
                        ]);

                        $pdfUrl = $ticketService->generatePurchaseTicketsPDF($purchaseRef, $ticketData);

                        $metadata = $transaction->metadata ?? [];
                        $metadata['ticket_pdf_url'] = $pdfUrl;
                        $transaction->metadata = $metadata;
                        $transaction->save();

                        Log::info('🎫 Generated ticket PDF', [
                            'transaction_id' => $this->transactionId,
                            'pdf_url' => $pdfUrl
                        ]);

                        return $pdfUrl;
                    } catch (\Exception $pdfError) {
                        Log::error('🎫 Error in PDF generation step', [
                            'transaction_id' => $this->transactionId,
                            'error' => $pdfError->getMessage(),
                            'trace' => $pdfError->getTraceAsString()
                        ]);
                    }
                } else {
                    Log::warning('🎫 No ticket data available for PDF generation', [
                        'transaction_id' => $this->transactionId
                    ]);
                }
            }
        } catch (\Exception $e) {
            Log::error('🎫 Failed to generate ticket PDF', [
                'transaction_id' => $this->transactionId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        return null;
    }

    /**
     * Log purchase details for debugging
     *
     * @param \Illuminate\Support\Collection $purchases
     * @return array
     */
    private function logPurchaseDetails(\Illuminate\Support\Collection $purchases): array
    {
        $purchaseDetails = $purchases->map(function ($purchase) {
            return [
                'id' => $purchase->id,
                'event_id' => $purchase->event_id,
                'event_name' => $purchase->event ? $purchase->event->name : 'Unknown Event',
                'ticket_id' => $purchase->ticket_id,
                'ticket_name' => $purchase->ticket ? $purchase->ticket->name : 'Unknown Ticket',
                'quantity' => $purchase->quantity,
                'unit_price' => $purchase->unit_price,
                'total_amount' => $purchase->total_amount
            ];
        })->toArray();

        Log::info('🎫 Ticket purchase details before broadcasting event', [
            'transaction_id' => $this->transactionId,
            'purchase_details' => $purchaseDetails
        ]);

        return $purchaseDetails;
    }

    /**
     * Send ticket purchase notification to user
     *
     * @param PaymentTransaction $transaction
     * @param \Illuminate\Support\Collection $purchases
     * @param User $user
     * @return void
     */
    private function sendTicketPurchaseNotification(PaymentTransaction $transaction, \Illuminate\Support\Collection $purchases, User $user): void
    {
        try {
            // Ensure all related data is loaded for the notification
            $purchases->load(['event', 'ticket.tier', 'user']);

            // Check if we have at least one complete purchase
            $validPurchases = $purchases->filter(function($purchase) {
                return $purchase->event && $purchase->ticket;
            });

            if ($validPurchases->isEmpty()) {
                Log::warning('🎫 No valid purchases found for notification', [
                    'transaction_id' => $this->transactionId,
                    'total_purchases' => $purchases->count(),
                    'purchase_ids' => $purchases->pluck('id')->toArray()
                ]);
            }

            $purchaseDetails = $validPurchases->map(function($purchase) {
                return [
                    'id' => $purchase->id,
                    'event_name' => $purchase->event->name ?? 'Unknown',
                    'ticket_name' => $purchase->ticket->name ?? 'Unknown',
                    'quantity' => $purchase->quantity,
                    'unit_price' => (string)$purchase->unit_price,
                    'status' => $purchase->status
                ];
            })->toArray();

            Log::info('🎫 Sending notification with purchase details', [
                'transaction_id' => $this->transactionId,
                'user_id' => $user->id,
                'purchase_details' => $purchaseDetails
            ]);

            $user->notify(new TicketPurchaseNotification($purchases, $transaction));

            Log::info('Ticket purchase notifications sent', [
                'transaction_id' => $this->transactionId,
                'user_id' => $user->id,
                'user_email' => $user->email,
                'total_tickets' => $purchases->sum('quantity')
            ]);
        } catch (\Exception $notifyError) {
            Log::error('Error sending ticket purchase notification', [
                'transaction_id' => $this->transactionId,
                'error' => $notifyError->getMessage(),
                'trace' => $notifyError->getTraceAsString()
            ]);
        }
    }

    /**
     * Mark transaction as failed
     */
    private function markTransactionAsFailed(PaymentTransaction $transaction, string $reason): void
    {
        $oldStatus = $transaction->status;
        $transaction->status = 'failed';
        $transaction->gateway_response = array_merge(
            $transaction->gateway_response ?? [],
            ['failure_reason' => $reason, 'failed_at' => now()->toISOString()]
        );
        $transaction->save();

        // Broadcast payment status update
        event(new PaymentStatusUpdated($transaction, $oldStatus, 'failed'));

        Log::warning('Payment marked as failed', [
            'transaction_id' => $this->transactionId,
            'reason' => $reason
        ]);
    }

    /**
     * Format event date safely
     *
     * @param mixed $date The event date which could be a DateTime object, string or null
     * @return string Formatted date string or empty string if invalid
     */
    private function formatEventDate($date): string
    {
        if (empty($date)) {
            return '';
        }

        try {
            // If it's already a DateTime object
            if ($date instanceof \DateTime || $date instanceof \Carbon\Carbon) {
                return $date->format('M d, Y \a\t g:i A');
            }

            // If it's a string, try to parse it
            if (is_string($date)) {
                return \Carbon\Carbon::parse($date)->format('M d, Y \a\t g:i A');
            }

            // For any other case
            return (string) $date;
        } catch (\Exception $e) {
            Log::warning('Failed to format event date', [
                'date' => $date,
                'error' => $e->getMessage()
            ]);
            return '';
        }
    }

    /**
     * Handle job failure
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Payment verification job permanently failed', [
            'transaction_id' => $this->transactionId,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);

        $transaction = PaymentTransaction::where('transaction_id', $this->transactionId)->first();
        if ($transaction && $transaction->status === 'pending') {
            $this->markTransactionAsFailed($transaction, 'Job permanently failed: ' . $exception->getMessage());
        }
    }
}
