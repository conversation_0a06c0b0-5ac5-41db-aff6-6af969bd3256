<?php

namespace App\Jobs;

use App\Models\NewsletterSubscription;
use App\Mail\NewsletterMail;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendNewsletterEmail implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $subscription;
    protected $type;
    protected $subject;
    protected $content;

    public $tries = 3;
    public $timeout = 60;

    /**
     * Create a new job instance.
     */
    public function __construct(NewsletterSubscription $subscription, string $type, ?string $subject, array $content)
    {
        $this->subscription = $subscription;
        $this->type = $type;
        $this->subject = $subject;
        $this->content = $content;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('Sending newsletter email', [
                'subscription_id' => $this->subscription->id,
                'email' => $this->subscription->email,
                'type' => $this->type
            ]);

            // Generate subject if not provided
            $subject = $this->subject ?? $this->generateSubject();

            // Send the newsletter email
            Mail::to($this->subscription->email)->send(
                new NewsletterMail($this->subscription, $this->type, $subject, $this->content)
            );

            Log::info('Newsletter email sent successfully', [
                'subscription_id' => $this->subscription->id,
                'email' => $this->subscription->email,
                'type' => $this->type
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send newsletter email', [
                'subscription_id' => $this->subscription->id,
                'email' => $this->subscription->email,
                'type' => $this->type,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Generate subject based on newsletter type
     */
    private function generateSubject(): string
    {
        switch ($this->type) {
            case 'latest_events':
                return '🎉 Latest Events This Week - EventaHub Malawi';
            case 'recommended_events':
                return '⭐ Recommended Events Just for You - EventaHub Malawi';
            case 'new_venues':
                return '🏢 New Venues Added - EventaHub Malawi';
            case 'weekly_digest':
                return '📅 Your Weekly Event Digest - EventaHub Malawi';
            default:
                return '📧 Newsletter Update - EventaHub Malawi';
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Newsletter email job failed', [
            'subscription_id' => $this->subscription->id,
            'email' => $this->subscription->email,
            'type' => $this->type,
            'error' => $exception->getMessage()
        ]);
    }
}
