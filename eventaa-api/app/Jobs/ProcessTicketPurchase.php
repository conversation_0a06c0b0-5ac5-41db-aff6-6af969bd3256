<?php

namespace App\Jobs;

use App\Models\TicketPurchase;
use App\Models\UserTicket;
use App\Services\TicketService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ProcessTicketPurchase implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $ticketPurchase;
    protected $confirmationCode;

    /**
     * Create a new job instance.
     */
    public function __construct(TicketPurchase $ticketPurchase, string $confirmationCode)
    {
        $this->ticketPurchase = $ticketPurchase;
        $this->confirmationCode = $confirmationCode;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('Processing ticket purchase', [
                'purchase_id' => $this->ticketPurchase->id,
                'confirmation_code' => $this->confirmationCode
            ]);

            // Generate individual ticket instances
            $generatedTickets = [];
            $ticketService = new TicketService();

            for ($i = 0; $i < $this->ticketPurchase->quantity; $i++) {
                $ticketUuid = Str::uuid()->toString();

                // Create user ticket instance
                $userTicket = UserTicket::create([
                    'user_id' => $this->ticketPurchase->user_id,
                    'ticket_id' => $this->ticketPurchase->ticket_id
                ]);

                // Generate QR code and ticket image
                $qrCode = $ticketService->generateTicketImage($userTicket->id, $this->ticketPurchase->ticket->tier);

                $generatedTickets[] = [
                    'user_ticket_id' => $userTicket->id,
                    'ticket_uuid' => $ticketUuid,
                    'qr_code' => $qrCode,
                    'attendee_name' => $this->ticketPurchase->attendee_name,
                    'ticket_name' => $this->ticketPurchase->ticket->name
                ];
            }

            // Update purchase status
            $this->ticketPurchase->update([
                'status' => 'completed',
                'purchased_at' => now(),
                'metadata' => array_merge(
                    $this->ticketPurchase->metadata ?? [],
                    ['generated_tickets' => $generatedTickets]
                )
            ]);

            // Generate PDF for the tickets
            $pdfUrl = null;
            $eventName = $this->ticketPurchase->event->name ?? 'Event';

            try {
                $purchaseReference = $this->ticketPurchase->purchase_reference ?? $this->ticketPurchase->id;

                // Prepare ticket data for PDF generation
                $ticketData = [];
                foreach ($generatedTickets as $ticket) {
                    $ticketData[] = [
                        'attendee_name' => $this->ticketPurchase->user->name,
                        'ticket_name' => $ticket['ticket_name'] ?? 'General Admission',
                        'event_name' => $eventName,
                        'event_date' => $this->ticketPurchase->event->start ?? now(),
                        'event_location' => $this->ticketPurchase->event->location ?? 'TBA',
                        'ticket_uuid' => $ticket['ticket_uuid'] ?? '',
                        'qr_code_data' => $ticket['ticket_uuid'] ?? '',
                        'price' => $this->ticketPurchase->ticket->price ?? 0,
                        'currency_symbol' => $this->ticketPurchase->event->currency_symbol ?? 'MK'
                    ];
                }

                $pdfUrl = $ticketService->generatePurchaseTicketsPDF($purchaseReference, $ticketData);

                Log::info('PDF generated for ticket purchase', [
                    'purchase_id' => $this->ticketPurchase->id,
                    'pdf_url' => $pdfUrl
                ]);
            } catch (\Exception $e) {
                Log::error('Failed to generate PDF for ticket purchase', [
                    'purchase_id' => $this->ticketPurchase->id,
                    'error' => $e->getMessage()
                ]);
            }

            // Dispatch email job
            SendTicketConfirmationEmail::dispatch(
                $this->ticketPurchase->user,
                [$this->ticketPurchase],
                $generatedTickets
            );

            // Get the user and broadcast ticket purchase completed event with PDF URL
            $user = \App\Models\User::find($this->ticketPurchase->user_id);
            event(new \App\Events\TicketPurchaseCompleted(
                [$this->ticketPurchase],
                $user,
                (float) $this->ticketPurchase->total_amount,
                $pdfUrl,
                $eventName
            ));

            Log::info('Ticket purchase processed successfully', [
                'purchase_id' => $this->ticketPurchase->id,
                'tickets_generated' => count($generatedTickets),
                'pdf_generated' => !empty($pdfUrl)
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to process ticket purchase', [
                'purchase_id' => $this->ticketPurchase->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Mark purchase as failed
            $this->ticketPurchase->update([
                'status' => 'failed',
                'metadata' => array_merge(
                    $this->ticketPurchase->metadata ?? [],
                    ['error' => $e->getMessage()]
                )
            ]);

            // Re-throw to trigger job failure
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Ticket purchase job failed permanently', [
            'purchase_id' => $this->ticketPurchase->id,
            'error' => $exception->getMessage()
        ]);

        // You could notify administrators or take other recovery actions here
    }
}
