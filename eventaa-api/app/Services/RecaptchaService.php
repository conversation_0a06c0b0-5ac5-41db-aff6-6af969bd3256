<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class RecaptchaService
{
    private string $secretKey;
    private string $verifyUrl;

    public function __construct()
    {
        $this->secretKey = config('services.recaptcha.secret_key', '6LeIxAcTAAAAAGG-vFI1TnRWxMZNFuojJ4WifJWe'); // Test key
        $this->verifyUrl = 'https://www.google.com/recaptcha/api/siteverify';
    }

    /**
     * Verify reCAPTCHA token
     */
    public function verify(string $token, string $userIp = null): bool
    {
        try {
            $response = Http::asForm()->post($this->verifyUrl, [
                'secret' => $this->secretKey,
                'response' => $token,
                'remoteip' => $userIp,
            ]);

            $result = $response->json();

            Log::info('reCAPTCHA verification', [
                'success' => $result['success'] ?? false,
                'score' => $result['score'] ?? null,
                'action' => $result['action'] ?? null,
                'error_codes' => $result['error-codes'] ?? [],
            ]);

            return $result['success'] ?? false;

        } catch (\Exception $e) {
            Log::error('reCAPTCHA verification failed', [
                'error' => $e->getMessage(),
                'token' => $token,
            ]);

            return false;
        }
    }

    /**
     * Verify reCAPTCHA token with score threshold (for v3)
     */
    public function verifyWithScore(string $token, float $threshold = 0.5, string $userIp = null): bool
    {
        try {
            $response = Http::asForm()->post($this->verifyUrl, [
                'secret' => $this->secretKey,
                'response' => $token,
                'remoteip' => $userIp,
            ]);

            $result = $response->json();

            Log::info('reCAPTCHA v3 verification', [
                'success' => $result['success'] ?? false,
                'score' => $result['score'] ?? null,
                'threshold' => $threshold,
                'action' => $result['action'] ?? null,
                'error_codes' => $result['error-codes'] ?? [],
            ]);

            if (!($result['success'] ?? false)) {
                return false;
            }

            // For v3, also check the score
            $score = $result['score'] ?? 0;
            return $score >= $threshold;

        } catch (\Exception $e) {
            Log::error('reCAPTCHA v3 verification failed', [
                'error' => $e->getMessage(),
                'token' => $token,
            ]);

            return false;
        }
    }
}
