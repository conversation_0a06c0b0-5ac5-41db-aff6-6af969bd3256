<?php

namespace App\Services;

use <PERSON><PERSON>\Sanctum\NewAccessToken;
use <PERSON><PERSON>\Sanctum\PersonalAccessToken;

class TokenService
{
    /**
     * Create a new personal access token with custom expiration
     */
    public static function createAccessToken($user, string $name, array $abilities = ['*'], \DateTimeInterface $expiresAt = null): NewAccessToken
    {
        $plainTextToken = $user->createToken($name, $abilities)->plainTextToken;

        if ($expiresAt) {
            // Find the token we just created and update its expiration
            $tokenModel = PersonalAccessToken::findToken($plainTextToken);
            if ($tokenModel) {
                $tokenModel->expires_at = $expiresAt;
                $tokenModel->save();
            }
        }

        return new NewAccessToken($tokenModel, $plainTextToken);
    }

    /**
     * Create access and refresh tokens with proper expiration
     */
    public static function createTokenPair($user): array
    {
        // Create access token (2 hours)
        $accessToken = $user->createToken(
            env('APP_NAME', 'EventaaHub') . '-access',
            ['access']
        );

        // Create refresh token (30 days)
        $refreshToken = $user->createToken(
            env('APP_NAME', 'EventaaHub') . '-refresh',
            ['refresh']
        );

        // Set expiration times
        $accessTokenModel = PersonalAccessToken::findToken($accessToken->plainTextToken);
        if ($accessTokenModel) {
            $accessTokenModel->expires_at = now()->addHours(2);
            $accessTokenModel->save();
        }

        $refreshTokenModel = PersonalAccessToken::findToken($refreshToken->plainTextToken);
        if ($refreshTokenModel) {
            $refreshTokenModel->expires_at = now()->addDays(30);
            $refreshTokenModel->save();
        }

        return [
            'access_token' => $accessToken->plainTextToken,
            'refresh_token' => $refreshToken->plainTextToken,
        ];
    }
}
