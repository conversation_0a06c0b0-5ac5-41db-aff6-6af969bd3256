<?php

namespace App\Services;

use App\Models\RefundRequest;
use App\Models\TicketPurchase;
use App\Models\Payment;
use App\Notifications\RefundStatusNotification;
use Illuminate\Support\Facades\Log;

class RefundService
{
    protected PaymentService $paymentService;

    public function __construct(PaymentService $paymentService)
    {
        $this->paymentService = $paymentService;
    }

    /**
     * Create a refund request.
     */
    public function createRefundRequest(
        TicketPurchase $purchase,
        string $reason,
        ?int $userId = null
    ): RefundRequest {
        $userId = $userId ?? $purchase->user_id;

        $refundAmount = $purchase->calculateRefundAmount();
        $processingFee = $purchase->total_amount - $refundAmount;
        $feePercentage = $purchase->ticket->tier->refund_fee_percentage ??
                        $purchase->ticket->refund_fee_percentage ?? 5;

        return RefundRequest::create([
            'user_id' => $userId,
            'ticket_purchase_id' => $purchase->id,
            'event_id' => $purchase->event_id,
            'payment_id' => $purchase->payment_id,
            'reason' => $reason,
            'original_amount' => $purchase->total_amount,
            'refund_amount' => $refundAmount,
            'processing_fee' => $processingFee,
            'fee_percentage' => $feePercentage,
            'payment_method' => $purchase->payment->payment_method,
            'status' => 'pending',
            'requested_at' => now(),
        ]);
    }

    /**
     * Process a refund through the payment gateway.
     */
    public function processRefund(RefundRequest $refundRequest): array
    {
        try {
            Log::info('Processing refund', [
                'refund_id' => $refundRequest->id,
                'refund_reference' => $refundRequest->refund_reference,
                'amount' => $refundRequest->refund_amount,
                'payment_method' => $refundRequest->payment_method
            ]);

            $refundRequest->markAsProcessing();

            // Check if payment exists before processing refund
            if ($refundRequest->payment) {
                $result = $this->paymentService->processRefund(
                    $refundRequest->payment,
                    $refundRequest->refund_amount
                );
            } else {
                // Handle case where no payment record exists
                // This might happen for free tickets or test data
                $result = [
                    'success' => true,
                    'refund_id' => 'NO_PAYMENT_' . time(),
                    'response' => ['message' => 'No payment to refund - ticket was free or payment record missing']
                ];
            }

            if ($result['success']) {
                $this->updateTicketPurchaseForRefund($refundRequest);

                $refundRequest->markAsCompleted(
                    $result['refund_id'] ?? null,
                    $result['response'] ?? null
                );

                $refundRequest->user->notify(new RefundStatusNotification($refundRequest));

                Log::info('Refund processed successfully', [
                    'refund_id' => $refundRequest->id,
                    'gateway_refund_id' => $result['refund_id'] ?? null
                ]);

                return [
                    'success' => true,
                    'refund_id' => $result['refund_id'] ?? null,
                    'message' => 'Refund processed successfully'
                ];

            } else {
                $refundRequest->markAsFailed(
                    $result['error'] ?? 'Payment gateway error',
                    $result['response'] ?? null
                );

                $refundRequest->user->notify(new RefundStatusNotification($refundRequest));

                Log::error('Refund processing failed', [
                    'refund_id' => $refundRequest->id,
                    'error' => $result['error'] ?? 'Unknown error'
                ]);

                return [
                    'success' => false,
                    'error' => $result['error'] ?? 'Payment gateway error',
                    'message' => 'Failed to process refund'
                ];
            }

        } catch (\Exception $e) {
            Log::error('Refund processing exception', [
                'refund_id' => $refundRequest->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            $refundRequest->markAsFailed('Processing exception: ' . $e->getMessage());
            $refundRequest->user->notify(new RefundStatusNotification($refundRequest));

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Exception occurred during refund processing'
            ];
        }
    }

    /**
     * Update ticket purchase when refund is completed.
     */
    protected function updateTicketPurchaseForRefund(RefundRequest $refundRequest): void
    {
        $purchase = $refundRequest->ticketPurchase;

        $purchase->update([
            'status' => 'refunded',
            'cancellation_reason' => $refundRequest->reason,
            'cancelled_at' => now(),
            'refund_amount' => $refundRequest->refund_amount,
            'refunded_at' => now()
        ]);

        $purchase->ticket->decrement('quantity_sold', $purchase->quantity);
    }

    /**
     * Check if a purchase is eligible for refund.
     */
    public function checkRefundEligibility(TicketPurchase $purchase): array
    {
        $eligible = $purchase->canBeRefunded();
        $refundAmount = $eligible ? $purchase->calculateRefundAmount() : 0;
        $processingFee = $eligible ? ($purchase->total_amount - $refundAmount) : 0;

        $response = [
            'eligible' => $eligible,
            'refund_amount' => $refundAmount,
            'processing_fee' => $processingFee,
            'original_amount' => $purchase->total_amount,
        ];

        if (!$eligible) {
            if ($purchase->status !== 'completed') {
                $response['reason'] = 'Purchase is not completed';
            } elseif (!$purchase->ticket->is_refundable) {
                $response['reason'] = 'This ticket type is non-refundable';
            } elseif ($purchase->event->start < now()) {
                $response['reason'] = 'Event has already started';
            } else {
                $refundCutoff = $purchase->event->start->subHours(24);
                if (now() > $refundCutoff) {
                    $response['reason'] = 'Refund deadline has passed (24 hours before event)';
                }
            }
        } else {
            $refundDeadline = $purchase->event->start->subHours(24);
            $response['refund_deadline'] = $refundDeadline->toISOString();
        }

        return $response;
    }

    /**
     * Get refund statistics.
     */
    public function getRefundStatistics(array $filters = []): array
    {
        $query = RefundRequest::query();

        if (isset($filters['event_id'])) {
            $query->where('event_id', $filters['event_id']);
        }

        if (isset($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        $totalRequests = $query->count();
        $totalAmount = $query->sum('refund_amount');
        $totalFees = $query->sum('processing_fee');

        $statusCounts = RefundRequest::selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        return [
            'total_requests' => $totalRequests,
            'total_refund_amount' => $totalAmount,
            'total_processing_fees' => $totalFees,
            'status_breakdown' => $statusCounts,
            'average_refund_amount' => $totalRequests > 0 ? $totalAmount / $totalRequests : 0,
            'average_processing_time' => $this->calculateAverageProcessingTime($filters),
        ];
    }

    /**
     * Calculate average processing time for completed refunds.
     */
    protected function calculateAverageProcessingTime(array $filters = []): ?float
    {
        $query = RefundRequest::where('status', 'completed')
            ->whereNotNull('completed_at')
            ->whereNotNull('requested_at');

        if (isset($filters['event_id'])) {
            $query->where('event_id', $filters['event_id']);
        }

        if (isset($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        $refunds = $query->get(['requested_at', 'completed_at']);

        if ($refunds->isEmpty()) {
            return null;
        }

        $totalHours = $refunds->sum(function ($refund) {
            return $refund->requested_at->diffInHours($refund->completed_at);
        });

        return $totalHours / $refunds->count();
    }
}
