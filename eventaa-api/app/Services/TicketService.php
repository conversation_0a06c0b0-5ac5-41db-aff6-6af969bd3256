<?php

namespace App\Services;

use App\Models\Ticket;
use App\Models\Event;
use App\Models\Tier;
use chillerlan\QRCode\QRCode;
use Exception;
use Intervention\Image\Facades\Image;
use Illuminate\Support\Str;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Storage;

class TicketService
{
    public function generate($eventId): string
    {
        try {
            $randomNumber = mt_rand(100000, 999999);
            return 'eventaa-' . strval($eventId) . '-' . str_pad(strval($randomNumber), 6, '0', STR_PAD_LEFT);
        } catch (Exception $e) {
            return "";
        }
    }

    public function verifyAndUpdate($ticketUUID): bool
    {
        // First try to find the ticket by UUID
        $ticket = Ticket::where('uuid', $ticketUUID)->first();

        if ($ticket) {
            // Find the most recent ticket purchase for this ticket that hasn't been scanned
            $ticketPurchase = $ticket->purchases()
                ->where('status', 'completed')
                ->where('scanned', false)
                ->latest()
                ->first();

            if ($ticketPurchase) {
                // Update the ticket purchase scan status
                $ticketPurchase->scanned = true;
                $ticketPurchase->scanned_at = now();
                $ticketPurchase->save();

                // Also update the ticket itself for backward compatibility
                $ticket->scanned = true;
                $ticket->scanned_at = now();
                $ticket->save();

                return true;
            }

            // If no unscanned purchase found, check if ticket was already scanned
            $scannedPurchase = $ticket->purchases()
                ->where('status', 'completed')
                ->where('scanned', true)
                ->exists();

            if ($scannedPurchase) {
                // Ticket already scanned
                return false;
            }

            // If we reach here, ticket exists but no completed purchases found
            return false;
        }

        return false;
    }

    public function generateTicketImage($ticketId, $tier)
    {
        $ticket = Ticket::find($ticketId);
        if (!$ticket) {
            abort(404, 'Ticket not found');
        }

        if (!$tier) {
            dd($tier);
            abort(404, 'Tier not found');
        }


        $event = Event::find($tier->event_id);
        $tier = Tier::findOrFail($tier->id);

        $qrcode = new QRCode;
        $qrImageData = $qrcode->render($ticket->uuid);

        $ticketWidth = 400;
        $ticketHeight = 200;

        $coverArtPath = public_path('storage/banners/' . $tier->banner);
        $ticketImage = Image::make($coverArtPath)
            ->fit($ticketWidth, $ticketHeight);

        $qrCode = Image::make($qrImageData)->resize(60, 60);
        $ticketImage->insert($qrCode, 'bottom-right', 10, 10);
        $ticketImage->text($event->name, 20, 30, function ($font) {
            $font->file(public_path('fonts/MacanPanWeb-Regular.ttf'));
            $font->size(18);
            $font->color('#ffffff');
        });

        $imagePath = 'tickets/' . strtolower($ticket->uuid) . '-ticket.png';
        Storage::disk('public')->put($imagePath, $ticketImage->encode('png'));

        return $ticketId;
    }

    public function generateTicketsPDF($event_id, $ticketIds)
    {
        try {
            // Set memory and time limits for PDF generation
            ini_set('memory_limit', '512M');
            set_time_limit(120);

            $ticketImages = [];
            $event = Event::findOrFail($event_id);

            // Process tickets in smaller batches to avoid memory issues
            $batchSize = 5; // Process 5 tickets at a time to reduce memory usage
            $ticketBatches = array_chunk($ticketIds, $batchSize);

            foreach ($ticketBatches as $batch) {
                foreach ($batch as $ticketId) {
                    $ticket = Ticket::find($ticketId);
                    if ($ticket) {
                        $imagePath = storage_path('app/public/tickets/' . strtolower($ticket->uuid) . '-ticket.png');
                        if (file_exists($imagePath)) {
                            // Read and encode image with memory optimization
                            $imageContent = file_get_contents($imagePath);
                            if ($imageContent !== false) {
                                $imageData = base64_encode($imageContent);
                                $ticketImages[] = 'data:image/png;base64,' . $imageData;

                                // Free memory immediately
                                unset($imageContent, $imageData);
                            }
                        }
                    }
                }

                // Force garbage collection after each batch
                if (function_exists('gc_collect_cycles')) {
                    gc_collect_cycles();
                }
            }

            // Configure PDF options for better performance
            $pdf = Pdf::loadView('tickets', compact('ticketImages', 'event'))
                ->setPaper('a4', 'portrait')
                ->setOptions([
                    'isHtml5ParserEnabled' => true,
                    'isPhpEnabled' => true,
                    'defaultFont' => 'sans-serif',
                    'dpi' => 96, // Lower DPI for faster generation
                    'enable_remote' => false,
                    'chroot' => storage_path('app/public'),
                ]);

            $pdfPath = 'tickets/event-' . $event_id . '-tickets-' . time() . '.pdf';
            Storage::disk('public')->put($pdfPath, $pdf->output());

            return url(Storage::url($pdfPath));

        } catch (\Exception $e) {
            \Log::error('PDF generation failed', [
                'event_id' => $event_id,
                'ticket_count' => count($ticketIds),
                'error' => $e->getMessage(),
                'memory_usage' => memory_get_peak_usage(true)
            ]);

            throw new \Exception('Failed to generate PDF: ' . $e->getMessage());
        }
    }

    /**
     * Generate a unique purchase reference with multiple entropy sources
     */
    public function generatePurchaseReference(string $prefix = 'PUR', int $userId = null): string
    {
        $userId = $userId ?? auth()->id() ?? 0;
        $attempts = 0;
        $maxAttempts = 20;

        do {
            $attempts++;

            // Use multiple entropy sources for maximum uniqueness
            $microtime = microtime(true);
            $microseconds = (int)(($microtime - floor($microtime)) * 1000000);
            $randomString = strtoupper(Str::random(6));

            $reference = sprintf(
                '%s-%d-%d-%d-%s',
                $prefix,
                $userId,
                time(),
                $microseconds,
                $randomString
            );

            // Check if reference exists
            $exists = \App\Models\TicketPurchase::where('purchase_reference', $reference)->exists();

            if (!$exists) {
                break;
            }

            // Add progressive delay to avoid rapid retries
            usleep($attempts * 1000); // Progressive delay: 1ms, 2ms, 3ms, etc.

        } while ($attempts < $maxAttempts);

        if ($attempts >= $maxAttempts) {
            throw new \Exception('Unable to generate unique purchase reference after ' . $maxAttempts . ' attempts');
        }

        return $reference;
    }

    /**
     * Calculate total price with fees and taxes
     */
    public function calculateTotalPrice(array $ticketItems, float $feePercentage = 3.0, float $taxPercentage = 5.0): array
    {
        $subtotal = 0;

        foreach ($ticketItems as $item) {
            $subtotal += $item['price'] * $item['quantity'];
        }

        $fees = $subtotal * ($feePercentage / 100);
        $taxes = $subtotal * ($taxPercentage / 100);
        $total = $subtotal + $fees + $taxes;

        return [
            'subtotal' => round($subtotal, 2),
            'fees' => round($fees, 2),
            'taxes' => round($taxes, 2),
            'total' => round($total, 2),
            'fee_percentage' => $feePercentage,
            'tax_percentage' => $taxPercentage
        ];
    }

    /**
     * Validate ticket availability for purchase
     */
    public function validateTicketAvailability($ticketId, int $requestedQuantity): array
    {
        $ticket = Ticket::find($ticketId);

        if (!$ticket) {
            return [
                'valid' => false,
                'message' => 'Ticket not found'
            ];
        }

        if (!$ticket->isAvailable()) {
            return [
                'valid' => false,
                'message' => "Ticket '{$ticket->name}' is not available for purchase"
            ];
        }

        if ($ticket->remaining_quantity < $requestedQuantity) {
            return [
                'valid' => false,
                'message' => "Only {$ticket->remaining_quantity} tickets remaining for '{$ticket->name}'"
            ];
        }

        return [
            'valid' => true,
            'ticket' => $ticket
        ];
    }

    /**
     * Generate enhanced ticket with purchase details
     */
    public function generatePurchaseTicketImage($userTicketId, $purchaseDetails, $tier = null)
    {
        $userTicket = \App\Models\UserTicket::with(['ticket', 'user'])->find($userTicketId);
        if (!$userTicket) {
            abort(404, 'User ticket not found');
        }

        $ticket = $userTicket->ticket;
        $user = $userTicket->user;
        $event = $ticket->event;

        if (!$tier) {
            $tier = $ticket->tier;
        }

        $qrcode = new QRCode;
        $qrData = json_encode([
            'ticket_id' => $ticket->id,
            'user_ticket_id' => $userTicketId,
            'uuid' => $ticket->uuid,
            'user_id' => $user->id,
            'event_id' => $event->id,
            'purchase_ref' => $purchaseDetails['purchase_reference'] ?? null
        ]);
        $qrImageData = $qrcode->render($qrData);

        $ticketWidth = 600;
        $ticketHeight = 300;

        // Create ticket base image
        $ticketImage = Image::canvas($ticketWidth, $ticketHeight, '#ffffff');

        // Add event banner if available
        if ($tier && $tier->banner) {
            $coverArtPath = public_path('storage/banners/' . $tier->banner);
            if (file_exists($coverArtPath)) {
                $bannerImage = Image::make($coverArtPath)->fit(200, $ticketHeight);
                $ticketImage->insert($bannerImage, 'left');
            }
        }

        // Add QR code
        $qrCode = Image::make($qrImageData)->resize(80, 80);
        $ticketImage->insert($qrCode, 'bottom-right', 15, 15);

        // Add text information
        $textStartX = 220;
        $lineHeight = 25;
        $currentY = 30;

        // Event name
        $ticketImage->text($event->name, $textStartX, $currentY, function ($font) {
            $font->size(22);
            $font->color('#333333');
            $font->angle(0);
        });

        $currentY += $lineHeight + 10;

        // Ticket type
        $ticketImage->text('Ticket: ' . ($ticket->name ?? 'General'), $textStartX, $currentY, function ($font) {
            $font->size(16);
            $font->color('#666666');
        });

        $currentY += $lineHeight;

        // Attendee name
        $attendeeName = $purchaseDetails['attendee_name'] ?? $user->name;
        $ticketImage->text('Attendee: ' . $attendeeName, $textStartX, $currentY, function ($font) {
            $font->size(14);
            $font->color('#666666');
        });

        $currentY += $lineHeight;

        // Event date
        if ($event->start_date) {
            $ticketImage->text('Date: ' . $event->start_date->format('M d, Y H:i'), $textStartX, $currentY, function ($font) {
                $font->size(14);
                $font->color('#666666');
            });
            $currentY += $lineHeight;
        }

        // Ticket ID
        $ticketImage->text('Ticket ID: ' . $ticket->uuid, $textStartX, $currentY, function ($font) {
            $font->size(12);
            $font->color('#999999');
        });

        // Save the enhanced ticket
        $imagePath = 'tickets/purchase-' . $userTicketId . '-' . strtolower($ticket->uuid) . '.png';
        Storage::disk('public')->put($imagePath, $ticketImage->encode('png'));

        return $userTicketId;
    }

    /**
     * Generate horizontal ticket image with QR code and all details
     */
    public function generateHorizontalTicketImage($ticketData)
    {
        // Horizontal ticket dimensions
        $ticketWidth = 800;
        $ticketHeight = 300;

        // Create base ticket image with gradient background
        $ticket = Image::canvas($ticketWidth, $ticketHeight, '#ffffff');

        // Add gradient background
        $gradient = Image::canvas($ticketWidth, $ticketHeight);
        $gradient->fill('#f8fafc');

        // Create left section with banner background and red overlay
        $leftSection = Image::canvas(200, $ticketHeight, '#dc2626');

        // Add banner image as background if available
        if (isset($ticketData['banner']) && $ticketData['banner']) {
            $bannerPath = public_path('storage/banners/' . $ticketData['banner']);
            if (file_exists($bannerPath)) {
                try {
                    $bannerImage = Image::make($bannerPath)->fit(200, $ticketHeight);
                    $leftSection = $bannerImage;

                    // Add red overlay with opacity
                    $redOverlay = Image::canvas(200, $ticketHeight, '#dc2626');
                    $leftSection->insert($redOverlay, 'top-left', 0, 0);
                    $leftSection->opacity(85); // 85% opacity for the overlay effect
                } catch (\Exception $e) {
                    // Fallback to solid red if banner processing fails
                    $leftSection = Image::canvas(200, $ticketHeight, '#dc2626');
                }
            }
        }

        $ticket->insert($leftSection, 'top-left');

        // Generate QR Code with just the ticket UUID
        $qrcode = new QRCode;
        $qrImageData = $qrcode->render($ticketData['ticket_uuid']);
        $qrCode = Image::make($qrImageData)->resize(120, 120);

        // Insert QR code in the left section
        $ticket->insert($qrCode, 'top-left', 40, 90);

        // Add event title
        $ticket->text($ticketData['event_name'], 220, 40, function ($font) {
            $fontPath = public_path('fonts/MacanPanWeb-Regular.ttf');
            if (file_exists($fontPath)) {
                $font->file($fontPath);
            }
            $font->size(24);
            $font->color('#1f2937');
            $font->align('left');
        });

        // Helper function for font
        $addTextWithFont = function($text, $x, $y, $size, $color) use ($ticket) {
            $ticket->text($text, $x, $y, function ($font) use ($size, $color) {
                $fontPath = public_path('fonts/MacanPanWeb-Regular.ttf');
                if (file_exists($fontPath)) {
                    $font->file($fontPath);
                }
                $font->size($size);
                $font->color($color);
                $font->align('left');
            });
        };

        // Add ticket type/name
        $addTextWithFont($ticketData['ticket_name'], 220, 75, 16, '#6b7280');

        // Add attendee name
        $addTextWithFont('Attendee: ' . $ticketData['attendee_name'], 220, 110, 14, '#374151');

        // Add event date
        if (isset($ticketData['event_date'])) {
            $addTextWithFont('Date: ' . $ticketData['event_date'], 220, 135, 14, '#374151');
        }

        // Add event location
        if (isset($ticketData['event_location'])) {
            $addTextWithFont('Location: ' . $ticketData['event_location'], 220, 160, 14, '#374151');
        }

        // Add price
        if (isset($ticketData['price'])) {
            $addTextWithFont('Price: ' . $ticketData['price'], 220, 185, 14, '#374151');
        }

        // Add ticket number if available
        if (isset($ticketData['ticket_number'])) {
            $addTextWithFont('Ticket: ' . $ticketData['ticket_number'], 220, 210, 12, '#6b7280');
        }

        // Add decorative elements
        $ticket->rectangle(210, 0, 212, $ticketHeight, function ($draw) {
            $draw->background('#e5e7eb');
        });

        // Save the ticket image
        $imagePath = 'tickets/horizontal-' . $ticketData['user_ticket_id'] . '-' . strtolower($ticketData['ticket_uuid']) . '.png';
        Storage::disk('public')->put($imagePath, $ticket->encode('png'));

        return $imagePath;
    }

    /**
     * Generate PDF for purchased tickets with horizontal layout
     */
    public function generatePurchaseTicketsPDF($purchaseReference, array $ticketData)
    {
        $ticketImages = [];
        $purchaseInfo = null;

        foreach ($ticketData as $data) {
            // Generate horizontal ticket image
            $imagePath = $this->generateHorizontalTicketImage($data);
            $fullImagePath = storage_path('app/public/' . $imagePath);

            if (file_exists($fullImagePath)) {
                $imageData = base64_encode(file_get_contents($fullImagePath));
                $ticketImages[] = [
                    'image' => 'data:image/png;base64,' . $imageData,
                    'attendee_name' => $data['attendee_name'],
                    'ticket_name' => $data['ticket_name']
                ];
            }

            if (!$purchaseInfo) {
                $purchaseInfo = $data;
            }
        }

        $pdf = Pdf::loadView('purchase-tickets', compact('ticketImages', 'purchaseReference', 'purchaseInfo'));

        // Add timestamp and microseconds to ensure unique filename even for same purchase reference
        $microtime = microtime(true);
        $microseconds = (int)(($microtime - floor($microtime)) * 1000000);
        $timestamp = time();

        $pdfPath = 'tickets/purchase-' . $purchaseReference . '-' . $timestamp . '-' . $microseconds . '-tickets.pdf';
        Storage::disk('public')->put($pdfPath, $pdf->output());

        return url(Storage::url($pdfPath));
    }
}
