<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;

class GeocodingService
{
    public function getCoordinatesFromAddress($street, $city, $country)
    {
        $apiKey = env('GOOGLE_MAPS_API_KEY');
        $address = urlencode("{$street}, {$city}, {$country}");
        $url = "https://maps.googleapis.com/maps/api/geocode/json?address={$address}&key={$apiKey}";

        $response = Http::get($url);
        $data = $response->json();

        if ($response->successful() && $data['status'] === 'OK') {
            $location = $data['results'][0]['geometry']['location'];
            return [
                'latitude' => $location['lat'],
                'longitude' => $location['lng'],
            ];
        }

        return [
            'latitude' => '',
            'longitude' => '',
        ];
    }
}
