<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EventSponsor extends Model
{
    use HasFactory;

    protected $fillable = ['event_id', 'sponsor_id'];

    public function event() : BelongsTo
    {
        return $this->belongsTo(Event::class);
    }

    public function sponsor() : BelongsTo
    {
        return $this->belongsTo(Sponsor::class);
    }
}
