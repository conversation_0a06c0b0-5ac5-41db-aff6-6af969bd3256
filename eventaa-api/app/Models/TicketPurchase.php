<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TicketPurchase extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'event_id',
        'ticket_id',
        'payment_id',
        'purchase_reference',
        'quantity',
        'unit_price',
        'total_amount',
        'fees',
        'taxes',
        'status',
        'purchased_at',
        'attendee_name',
        'attendee_email',
        'attendee_phone',
        'attendee_details',
        'cancellation_reason',
        'cancelled_at',
        'refund_amount',
        'refunded_at',
        'metadata',
        'scanned',
        'scanned_at',
        'scanned_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'unit_price' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'fees' => 'decimal:2',
        'taxes' => 'decimal:2',
        'refund_amount' => 'decimal:2',
        'purchased_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'refunded_at' => 'datetime',
        'scanned_at' => 'datetime',
        'attendee_details' => 'json',
        'metadata' => 'json',
        'scanned' => 'boolean',
    ];

    /**
     * Get the user who purchased the ticket.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the event for this purchase.
     */
    public function event(): BelongsTo
    {
        return $this->belongsTo(Event::class);
    }

    /**
     * Get the ticket for this purchase.
     */
    public function ticket(): BelongsTo
    {
        return $this->belongsTo(Ticket::class);
    }

    /**
     * Get the payment for this purchase.
     */
    public function payment(): BelongsTo
    {
        return $this->belongsTo(Payment::class);
    }

    /**
     * Check if the purchase can be refunded.
     */
    public function canBeRefunded(): bool
    {
        if ($this->status !== 'completed') {
            return false;
        }

        // Check if the ticket tier is refundable
        if (!$this->ticket->tier || !$this->ticket->tier->is_refundable) {
            return false;
        }

        // Check if event has passed
        if (\Carbon\Carbon::parse($this->event->start) < now()) {
            return false;
        }

        // Check if refund window has passed (e.g., 24 hours before event)
        $refundCutoff = \Carbon\Carbon::parse($this->event->start)->subHours(24);
        if (now() > $refundCutoff) {
            return false;
        }

        return true;
    }

    /**
     * Calculate refund amount after fees.
     */
    public function calculateRefundAmount(): float
    {
        if (!$this->canBeRefunded()) {
            return 0;
        }

        $refundFeePercentage = $this->ticket->tier->refund_fee_percentage ??
            $this->ticket->refund_fee_percentage ?? 5;
        $refundFee = ($this->total_amount * $refundFeePercentage) / 100;

        return max(0, $this->total_amount - $refundFee);
    }

    /**
     * Scope for completed purchases.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for pending purchases.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for refunded purchases.
     */
    public function scopeRefunded($query)
    {
        return $query->where('status', 'refunded');
    }

    /**
     * Scope for scanned purchases.
     */
    public function scopeScanned($query)
    {
        return $query->where('scanned', true);
    }

    /**
     * Scope for unscanned purchases.
     */
    public function scopeUnscanned($query)
    {
        return $query->where('scanned', false);
    }

    /**
     * Scope for purchases with attendee information (bought).
     */
    public function scopeBought($query)
    {
        return $query->where(function ($q) {
            $q->whereNotNull('attendee_name')
              ->orWhereNotNull('attendee_email')
              ->orWhereNotNull('user_id');
        });
    }

    /**
     * Scope for purchases without attendee information (not bought).
     */
    public function scopeNotBought($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('attendee_name')
              ->whereNull('attendee_email')
              ->whereNull('user_id');
        });
    }
}
