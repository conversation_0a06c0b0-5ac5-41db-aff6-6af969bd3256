<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Tier extends Model
{
    use HasFactory;

    protected $fillable = ['event_id', 'name', 'banner', 'price', 'description', 'is_refundable', 'refund_fee_percentage', 'platform_fee_percentage', 'platform_fee_amount', 'seat'];

    protected $casts = [
        'price' => 'decimal:2',
        'refund_fee_percentage' => 'decimal:2',
        'platform_fee_percentage' => 'decimal:2',
        'platform_fee_amount' => 'decimal:2',
        'is_refundable' => 'boolean',
    ];

    public function event() : BelongsTo{
        return $this->belongsTo(Event::class);
    }

    public function currency() : BelongsTo{
        return $this->belongsTo(Currency::class);
    }
}
