<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class EventInvitation extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'event_id',
        'user_id',
        'email',
        'name',
        'avatar',
        'status',
        'invited_by',
        'invitation_token',
        'message',
        'responded_at',
        'resent_at'
    ];

    protected $casts = [
        'responded_at' => 'datetime',
        'resent_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    protected $dates = [
        'responded_at',
        'resent_at',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    /**
     * Boot method to generate invitation token
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($invitation) {
            if (!$invitation->invitation_token) {
                $invitation->invitation_token = Str::uuid();
            }
        });
    }

    /**
     * Get the event that this invitation belongs to
     */
    public function event(): BelongsTo
    {
        return $this->belongsTo(Event::class);
    }

    /**
     * Get the user that was invited (if they have an account)
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user who sent the invitation
     */
    public function invitedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'invited_by');
    }

    /**
     * Check if the invitation is still pending
     */
    public function isPending(): bool
    {
        return in_array($this->status, ['pending', 'sent']);
    }

    /**
     * Check if the invitation has been accepted
     */
    public function isAccepted(): bool
    {
        return $this->status === 'accepted';
    }

    /**
     * Check if the invitation has been declined
     */
    public function isDeclined(): bool
    {
        return $this->status === 'declined';
    }

    /**
     * Get the invitation acceptance URL
     */
    public function getAcceptanceUrl(): string
    {
        return config('app.frontend_url') . '/invitations/accept/' . $this->invitation_token;
    }

    /**
     * Get the invitation decline URL
     */
    public function getDeclineUrl(): string
    {
        return config('app.frontend_url') . '/invitations/decline/' . $this->invitation_token;
    }

    /**
     * Scope for pending invitations
     */
    public function scopePending($query)
    {
        return $query->whereIn('status', ['pending', 'sent']);
    }

    /**
     * Scope for accepted invitations
     */
    public function scopeAccepted($query)
    {
        return $query->where('status', 'accepted');
    }

    /**
     * Scope for declined invitations
     */
    public function scopeDeclined($query)
    {
        return $query->where('status', 'declined');
    }

    /**
     * Scope for a specific event
     */
    public function scopeForEvent($query, $eventId)
    {
        return $query->where('event_id', $eventId);
    }
}
