<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentGateway extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'is_active',
        'config',
        'logo',
        'supported_currencies',
        'test_mode'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'config' => 'array',
        'supported_currencies' => 'array',
        'test_mode' => 'boolean'
    ];

    /**
     * Get all vendor payment methods associated with this gateway
     */
    public function vendorPaymentMethods()
    {
        return $this->hasMany(VendorPaymentMethod::class);
    }
}
