<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Currency extends Model
{
    use HasFactory;

    protected $fillable = ['name'];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * Get all users who have this currency as their preference
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get all venue prices using this currency
     */
    public function venuePrices(): HasMany
    {
        return $this->hasMany(VenuePrice::class);
    }

    /**
     * Scope to get currencies ordered by name
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('name');
    }

    /**
     * Get formatted currency name
     */
    public function getFormattedNameAttribute(): string
    {
        return strtoupper($this->name);
    }
}
