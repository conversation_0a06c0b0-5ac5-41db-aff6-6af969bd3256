<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class VenuePrice extends Model
{
    protected $fillable = [
        'venue_id',
        'price',
        'currency_id',
        'ammenities',
    ];

    public function venue()
    {
        return $this->belongsTo(Venue::class);
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class);
    }
}
