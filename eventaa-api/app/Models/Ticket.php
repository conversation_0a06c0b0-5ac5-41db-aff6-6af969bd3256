<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Ticket extends Model
{
    use HasFactory;

    protected $fillable = [
        'event_id',
        'tier_id',
        'uuid',
        'name',
        'price',
        'banner',
        'description',
        'status',
        'quantity_available',
        'quantity_sold',
        'sale_start_date',
        'sale_end_date',
        'is_refundable',
        'refund_fee_percentage',
        'scanned',
        'scanned_at',
        'metadata'
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'refund_fee_percentage' => 'decimal:2',
        'sale_start_date' => 'datetime',
        'sale_end_date' => 'datetime',
        'scanned_at' => 'datetime',
        'metadata' => 'json',
        'scanned' => 'boolean',
        'is_refundable' => 'boolean',
    ];

    /**
     * Get the users who have this ticket
     * This allows you to access users directly through the ticket
     */
    public function user_ticket(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_tickets');
    }

    /**
     * Get the event associated with this ticket
     */
    public function event(): BelongsTo
    {
        return $this->belongsTo(Event::class);
    }

    /**
     * Get the tier associated with this ticket
     */
    public function tier(): BelongsTo
    {
        return $this->belongsTo(Tier::class);
    }

    /**
     * Get all purchases for this ticket
     */
    public function purchases(): HasMany
    {
        return $this->hasMany(TicketPurchase::class);
    }

    /**
     * Check if ticket is available for purchase
     */
    public function isAvailable(): bool
    {
        if ($this->status !== 'available') {
            return false;
        }

        if ($this->quantity_sold >= $this->quantity_available) {
            return false;
        }

        $now = now();

        if ($this->sale_start_date && $now < $this->sale_start_date) {
            return false;
        }

        if ($this->sale_end_date && $now > $this->sale_end_date) {
            return false;
        }

        return true;
    }

    /**
     * Get remaining quantity
     */
    public function getRemainingQuantityAttribute(): int
    {
        return max(0, $this->quantity_available - $this->quantity_sold);
    }

    /**
     * Check if ticket sale has started
     */
    public function saleHasStarted(): bool
    {
        return !$this->sale_start_date || now() >= $this->sale_start_date;
    }

    /**
     * Check if ticket sale has ended
     */
    public function saleHasEnded(): bool
    {
        return $this->sale_end_date && now() > $this->sale_end_date;
    }

    /**
     * Scope for available tickets
     */
    public function scopeAvailable($query)
    {
        return $query->where('status', 'available')
                    ->where('quantity_sold', '<', 'quantity_available')
                    ->where(function ($q) {
                        $q->whereNull('sale_start_date')
                          ->orWhere('sale_start_date', '<=', now());
                    })
                    ->where(function ($q) {
                        $q->whereNull('sale_end_date')
                          ->orWhere('sale_end_date', '>', now());
                    });
    }
}
