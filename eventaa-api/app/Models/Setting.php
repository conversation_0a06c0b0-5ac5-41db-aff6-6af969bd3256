<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Setting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'name',
        'description',
        'type',
        'enabled'
    ];

    protected $casts = [
        'enabled' => 'boolean'
    ];

    /**
     * Get all users who have this setting enabled
     */
    public function users()
    {
        return $this->belongsToMany(User::class, 'user_settings');
    }
}
