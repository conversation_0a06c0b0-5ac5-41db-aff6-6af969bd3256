<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class HostBalance extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'available_balance',
        'pending_balance',
        'total_earned',
        'total_withdrawn',
        'platform_fees_collected',
        'last_payout_at',
    ];

    protected $casts = [
        'available_balance' => 'decimal:2',
        'pending_balance' => 'decimal:2',
        'total_earned' => 'decimal:2',
        'total_withdrawn' => 'decimal:2',
        'platform_fees_collected' => 'decimal:2',
        'last_payout_at' => 'datetime',
    ];

    /**
     * Get the user that owns the balance
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get all withdrawals for this host
     */
    public function withdrawals(): HasMany
    {
        return $this->hasMany(Withdrawal::class, 'user_id', 'user_id');
    }

    /**
     * Add earnings to the host balance
     */
    public function addEarnings(float $amount, float $platformFee = 0): void
    {
        $this->increment('available_balance', $amount);
        $this->increment('total_earned', $amount + $platformFee);
        $this->increment('platform_fees_collected', $platformFee);
    }

    /**
     * Process a withdrawal request
     */
    public function processWithdrawal(float $amount): bool
    {
        if ($this->available_balance < $amount) {
            return false;
        }

        $this->decrement('available_balance', $amount);
        $this->increment('total_withdrawn', $amount);
        $this->update(['last_payout_at' => now()]);

        return true;
    }

    /**
     * Get or create host balance for a user
     */
    public static function getOrCreateForUser(int $userId): HostBalance
    {
        return static::firstOrCreate(['user_id' => $userId]);
    }
}
