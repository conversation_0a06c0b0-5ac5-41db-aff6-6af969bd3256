<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class RefundRequest extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'ticket_purchase_id',
        'event_id',
        'payment_id',
        'refund_reference',
        'reason',
        'original_amount',
        'refund_amount',
        'processing_fee',
        'fee_percentage',
        'status',
        'priority',
        'processed_by',
        'admin_notes',
        'rejection_reason',
        'payment_method',
        'gateway_refund_id',
        'gateway_response',
        'requested_at',
        'approved_at',
        'rejected_at',
        'processed_at',
        'completed_at',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'original_amount' => 'decimal:2',
        'refund_amount' => 'decimal:2',
        'processing_fee' => 'decimal:2',
        'fee_percentage' => 'decimal:2',
        'gateway_response' => 'json',
        'metadata' => 'json',
        'requested_at' => 'datetime',
        'approved_at' => 'datetime',
        'rejected_at' => 'datetime',
        'processed_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    /**
     * Status constants
     */
    const STATUS_PENDING = 'pending';
    const STATUS_APPROVED = 'approved';
    const STATUS_PROCESSING = 'processing';
    const STATUS_COMPLETED = 'completed';
    const STATUS_REJECTED = 'rejected';
    const STATUS_FAILED = 'failed';
    const STATUS_MANUAL_REVIEW = 'manual_review';

    /**
     * Valid status values
     */
    public static $validStatuses = [
        self::STATUS_PENDING,
        self::STATUS_APPROVED,
        self::STATUS_PROCESSING,
        self::STATUS_COMPLETED,
        self::STATUS_REJECTED,
        self::STATUS_FAILED,
        self::STATUS_MANUAL_REVIEW,
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($refundRequest) {
            if (empty($refundRequest->refund_reference)) {
                $refundRequest->refund_reference = 'REF-' . strtoupper(Str::random(12)) . '-' . time();
            }
            if (empty($refundRequest->requested_at)) {
                $refundRequest->requested_at = now();
            }
        });
    }

    /**
     * Get the user who requested the refund.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the ticket purchase being refunded.
     */
    public function ticketPurchase(): BelongsTo
    {
        return $this->belongsTo(TicketPurchase::class);
    }

    /**
     * Get the event for this refund.
     */
    public function event(): BelongsTo
    {
        return $this->belongsTo(Event::class);
    }

    /**
     * Get the payment being refunded.
     */
    public function payment(): BelongsTo
    {
        return $this->belongsTo(Payment::class);
    }

    /**
     * Get the admin who processed the refund.
     */
    public function processedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'processed_by');
    }

    /**
     * Scope for pending refunds.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for approved refunds.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope for completed refunds.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for failed refunds.
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Check if refund can be approved.
     */
    public function canBeApproved(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if refund can be rejected.
     */
    public function canBeRejected(): bool
    {
        return in_array($this->status, ['pending', 'approved']);
    }

    /**
     * Check if refund can be processed.
     */
    public function canBeProcessed(): bool
    {
        return $this->status === 'approved';
    }

    /**
     * Approve the refund request.
     */
    public function approve(User $admin, string $notes = null): bool
    {
        if (!$this->canBeApproved()) {
            return false;
        }

        $this->update([
            'status' => 'approved',
            'processed_by' => $admin->id,
            'admin_notes' => $notes,
            'approved_at' => now(),
        ]);

        return true;
    }

    /**
     * Reject the refund request.
     */
    public function reject(User $admin, string $reason, string $notes = null): bool
    {
        if (!$this->canBeRejected()) {
            return false;
        }

        $this->update([
            'status' => 'rejected',
            'processed_by' => $admin->id,
            'rejection_reason' => $reason,
            'admin_notes' => $notes,
            'rejected_at' => now(),
        ]);

        return true;
    }

    /**
     * Mark refund as processing.
     */
    public function markAsProcessing(): bool
    {
        if (!$this->canBeProcessed()) {
            return false;
        }

        $this->update([
            'status' => 'processing',
            'processed_at' => now(),
        ]);

        return true;
    }

    /**
     * Mark refund as completed.
     */
    public function markAsCompleted(string $gatewayRefundId = null, array $gatewayResponse = null): bool
    {
        $this->update([
            'status' => 'completed',
            'gateway_refund_id' => $gatewayRefundId,
            'gateway_response' => $gatewayResponse,
            'completed_at' => now(),
        ]);

        return true;
    }

    /**
     * Mark refund as failed.
     */
    public function markAsFailed(string $reason = null, array $gatewayResponse = null): bool
    {
        return $this->update([
            'status' => 'failed',
            'rejection_reason' => $reason ?: 'Processing failed',
            'gateway_response' => $gatewayResponse,
            'processed_at' => now()
        ]);
    }

    /**
     * Mark this refund request for manual review.
     */
    public function markAsManualReview(string $reason = null): bool
    {
        return $this->update([
            'status' => self::STATUS_MANUAL_REVIEW,
            'admin_notes' => $reason ?: 'Requires manual review',
            'processed_at' => now()
        ]);
    }

    /**
     * Get status badge color.
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'pending' => 'yellow',
            'approved' => 'blue',
            'rejected' => 'red',
            'processing' => 'indigo',
            'completed' => 'green',
            'failed' => 'red',
            default => 'gray',
        };
    }

    /**
     * Get human readable status.
     */
    public function getStatusTextAttribute(): string
    {
        return match ($this->status) {
            'pending' => 'Pending Review',
            'approved' => 'Approved',
            'rejected' => 'Rejected',
            'processing' => 'Processing',
            'completed' => 'Completed',
            'failed' => 'Failed',
            default => 'Unknown',
        };
    }
}
