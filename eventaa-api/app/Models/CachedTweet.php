<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CachedTweet extends Model
{
    use HasFactory;

    protected $fillable = [
        'hashtag',
        'tweet_id',
        'content',
        'author_id',
        'metadata',
        'tweet_created_at'
    ];

    protected $casts = [
        'metadata' => 'array',
        'tweet_created_at' => 'datetime'
    ];
}
