<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class VendorCustomPricing extends Model
{
    protected $fillable = [
        'vendor_id',
        'hourly_rate',
        'day_rate',
        'currency_id',
        'notes',
    ];

    protected $casts = [
        'hourly_rate' => 'decimal:2',
        'day_rate' => 'decimal:2',
    ];

    /**
     * Get the vendor that owns the custom pricing.
     */
    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class);
    }

    /**
     * Get the currency associated with the custom pricing.
     */
    public function currency(): BelongsTo
    {
        return $this->belongsTo(Currency::class);
    }
}
