<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class VendorPrice extends Model
{
    protected $fillable = [
        'vendor_id',
        'service_id',
        'price',
        'currency_id',
        'name',
        'duration',
        'description',
        'active'
    ];

    protected $casts = [
        'active' => 'boolean',
        'price' => 'decimal:2'
    ];

    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }

    public function service()
    {
        return $this->belongsTo(VendorService::class);
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class);
    }
}
