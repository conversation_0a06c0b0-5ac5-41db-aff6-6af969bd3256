<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VendorPaymentMethod extends Model
{
    use HasFactory;

    protected $fillable = [
        'vendor_id',
        'payment_gateway_id',
        'is_default',
        'account_details',
        'status',
        'last_used_at'
    ];

    protected $casts = [
        'is_default' => 'boolean',
        'account_details' => 'array',
        'last_used_at' => 'datetime'
    ];

    /**
     * Get the vendor that owns the payment method
     */
    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }

    /**
     * Get the payment gateway for this method
     */
    public function paymentGateway()
    {
        return $this->belongsTo(PaymentGateway::class);
    }

    /**
     * Get all transactions made with this payment method
     */
    public function transactions()
    {
        return $this->hasMany(PaymentTransaction::class);
    }
}
