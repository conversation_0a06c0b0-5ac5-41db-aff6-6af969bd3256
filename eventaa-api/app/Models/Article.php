<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Article extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'content',
        'image',
        'user_id',
        'category_id',
        'slug',
        'is_published',
        'published_at',
    ];

    public function user() : BelongsTo{
        return $this->belongsTo(User::class);
    }

    public function category() : BelongsTo{
        return $this->belongsTo(Category::class);
    }

    public function tags() : HasMany{
        return $this->hasMany(ArticleTag::class);
    }
}
