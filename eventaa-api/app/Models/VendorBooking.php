<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class VendorBooking extends Model
{
    protected $fillable = [
        'user_id',
        'vendor_id',
        'vendor_service_id',
        'category_id',
        'booking_from',
        'booking_to',
        'number_of_guests',
        'message',
        'status',
        'rejection_reason',
        'total_price',
        'is_paid'
    ];

    protected $casts = [
        'booking_from' => 'datetime',
        'booking_to' => 'datetime',
        'is_paid' => 'boolean',
        'total_price' => 'decimal:2',
        'number_of_guests' => 'integer'
    ];

    /**
     * Get the user that made the booking.
     *
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the vendor being booked.
     *
     * @return BelongsTo
     */
    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class);
    }

    /**
     * Get the vendor service being booked.
     *
     * @return BelongsTo
     */
    public function vendorService(): BelongsTo
    {
        return $this->belongsTo(VendorService::class);
    }

    /**
     * Get the category of the booking.
     *
     * @return BelongsTo
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }
}
