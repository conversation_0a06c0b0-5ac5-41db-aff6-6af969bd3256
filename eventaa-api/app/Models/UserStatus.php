<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserStatus extends Model
{
    protected $fillable = [
        'user_id',
        'is_online',
        'last_seen_at',
        'socket_id',
    ];

    protected $casts = [
        'is_online' => 'boolean',
        'last_seen_at' => 'datetime',
    ];

    /**
     * Get the user that owns the status.
     *
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Update the user's online status.
     *
     * @param bool $isOnline
     * @param string|null $socketId
     * @return bool
     */
    public function updateOnlineStatus(bool $isOnline, ?string $socketId = null): bool
    {
        $this->is_online = $isOnline;

        if (!$isOnline) {
            $this->last_seen_at = now();
            $this->socket_id = null;
        } else {
            $this->socket_id = $socketId;
        }

        return $this->save();
    }
}
