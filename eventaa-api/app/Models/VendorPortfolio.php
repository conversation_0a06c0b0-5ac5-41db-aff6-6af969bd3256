<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class VendorPortfolio extends Model
{
    protected $fillable = [
        'vendor_id',
        'title',
        'description',
        'image_path',
        'media_type',
        'category',
        'is_featured'
    ];

    protected $casts = [
        'is_featured' => 'boolean',
    ];

    /**
     * Get the vendor that owns the portfolio item.
     */
    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }
}
