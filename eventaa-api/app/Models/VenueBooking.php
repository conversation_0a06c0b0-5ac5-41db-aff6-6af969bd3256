<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\Venue;

class VenueBooking extends Model
{
    protected $fillable = [
        'venue_id',
        'user_id',
        'category_id',
        'booking_from',
        'booking_to',
        'message',
        'status',
        'venue_price_id',
        'number_of_guests'
    ];

    public function venue()
    {
        return $this->belongsTo(Venue::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function venuePrice()
    {
        return $this->belongsTo(VenuePrice::class);
    }
}
