<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Venue extends Model
{
    protected $fillable = [
        'user_id',
        'name',
        'address',
        'city',
        'state',
        'zip',
        'country',
        'phone',
        'email',
        'website',
        'description',
        'logo',
        'capacity',
        'latitude',
        'longitude',
        'slug'
    ];

    public function images()
    {
        return $this->hasMany(VenueImage::class);
    }

    public function videos()
    {
        return $this->hasMany(VenueVideo::class);
    }

    public function prices()
    {
        return $this->hasMany(VenuePrice::class);
    }

    public function bookings()
    {
        return $this->hasMany(VenueBooking::class);
    }

    public function ratings()
    {
        return $this->hasMany(VenueRating::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function activities()
    {
        return $this->hasMany(VenueActivity::class);
    }
}
