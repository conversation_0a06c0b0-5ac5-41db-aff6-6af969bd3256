<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'transaction_id',
        'vendor_payment_method_id',
        'user_id',
        'amount',
        'currency',
        'status',
        'payment_type',
        'bookable_id',
        'bookable_type',
        'metadata',
        'payment_date',
        'gateway_response'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'metadata' => 'array',
        'gateway_response' => 'array',
        'payment_date' => 'datetime'
    ];

    /**
     * Get the payment method used for this transaction
     */
    public function paymentMethod()
    {
        return $this->belongsTo(VendorPaymentMethod::class, 'vendor_payment_method_id');
    }

    /**
     * Get the user who made the payment
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the bookable model (polymorphic)
     */
    public function bookable()
    {
        return $this->morphTo();
    }
}
