<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Vendor extends Model
{
    protected $fillable = [
        'user_id',
        'name',
        'slug',
        'logo',
        'bio',
        'location',
        'languages',
        'phone',
        'business_email',
        'website',
        'facebook',
        'instagram',
        'twitter',
        'likes_count',
        'is_verified',
        'is_available',
        'average_rating',
        'views',
        'status',
        'rejection_reason',
        'approved_at',
        'rejected_at',
    ];

    protected $casts = [
        'is_verified' => 'boolean',
        'is_available' => 'boolean',
        'likes_count' => 'integer',
        'views' => 'integer',
        'average_rating' => 'decimal:2',
        'approved_at' => 'datetime',
        'rejected_at' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function services()
    {
        return $this->hasMany(VendorService::class);
    }

    public function ratings()
    {
        return $this->hasMany(VendorRating::class);
    }

    public function media()
    {
        return $this->hasMany(VendorMedia::class);
    }

    public function prices()
    {
        return $this->hasMany(VendorPrice::class);
    }

    /**
     * Get the conversations for the vendor.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function conversations()
    {
        return $this->hasMany(Conversation::class);
    }

    /**
     * Get the bookings for the vendor.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function bookings()
    {
        return $this->hasMany(VendorBooking::class);
    }

    /**
     * Get the likes for the vendor.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function likes()
    {
        return $this->hasMany(VendorLike::class);
    }

    /**
     * Get the shares for the vendor.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function shares()
    {
        return $this->hasMany(VendorShare::class);
    }

    /**
     * Get the portfolio items for the vendor.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function portfolioItems()
    {
        return $this->hasMany(VendorPortfolio::class);
    }

    /**
     * Increment the view count for this vendor.
     *
     * @param \Illuminate\Http\Request|null $request
     * @return void
     */
    public function incrementViews($request = null)
    {
        $userId = $request && $request->user() ? $request->user()->id : ($request ? $request->ip() : 'anonymous');
        $cacheKey = 'viewed_vendor_' . $userId . '_' . $this->id;

        if (!cache()->has($cacheKey)) {
            $this->increment('views');
            cache()->put($cacheKey, true, now()->addMinutes(30));
        }
    }
}
