<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class NewsletterSubscription extends Model
{
    use HasFactory;

    protected $fillable = [
        'email',
        'name',
        'preferences',
        'is_active',
        'unsubscribe_token',
        'subscribed_at',
        'unsubscribed_at'
    ];

    protected $casts = [
        'preferences' => 'array',
        'is_active' => 'boolean',
        'subscribed_at' => 'datetime',
        'unsubscribed_at' => 'datetime'
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->unsubscribe_token)) {
                $model->unsubscribe_token = Str::random(64);
            }
        });
    }

    /**
     * Scope to get only active subscriptions
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get subscriptions with specific preference
     */
    public function scopeWithPreference($query, $preference)
    {
        return $query->whereJsonContains('preferences', $preference);
    }

    /**
     * Check if subscription has specific preference
     */
    public function hasPreference($preference)
    {
        $preferences = $this->preferences ?? [];
        return in_array($preference, $preferences);
    }

    /**
     * Get default preferences
     */
    public static function getDefaultPreferences()
    {
        return [
            'latest_events',
            'recommended_events',
            'new_venues',
            'event_updates'
        ];
    }

    /**
     * Unsubscribe from newsletter
     */
    public function unsubscribe()
    {
        $this->update([
            'is_active' => false,
            'unsubscribed_at' => now()
        ]);
    }

    /**
     * Resubscribe to newsletter
     */
    public function resubscribe()
    {
        $this->update([
            'is_active' => true,
            'unsubscribed_at' => null
        ]);
    }
}
