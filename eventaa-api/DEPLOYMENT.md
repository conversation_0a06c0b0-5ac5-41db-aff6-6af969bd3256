# EventaHub API - Deployment Configuration

This repository includes automated deployment to your VPS using GitHub Actions.

## Setup Instructions

### 1. VPS Preparation

Ensure your VPS is set up with the EventaHub server configuration:

```bash
# Run the server setup script (if not already done)
sudo chmod +x server-setup.sh
sudo ./server-setup.sh
```

### 2. GitHub Secrets Configuration

Add the following secrets to your GitHub repository:

**Repository Settings → Secrets and variables → Actions → New repository secret**

- `VPS_HOST`: Your VPS IP address or domain
- `VPS_USERNAME`: SSH username (usually your user account, not root)
- `VPS_SSH_KEY`: Your private SSH key for VPS access
- `VPS_PORT`: SSH port (optional, defaults to 22)

#### Creating SSH Key Pair (if needed)

```bash
# On your local machine
ssh-keygen -t rsa -b 4096 -C "github-actions@eventahub"

# Copy public key to VPS
ssh-copy-id -i ~/.ssh/id_rsa.pub user@your-vps-ip

# Add private key content to GitHub secret VPS_SSH_KEY
cat ~/.ssh/id_rsa
```

### 3. Environment File Setup

Create a production environment file on your VPS:

```bash
# On your VPS
sudo cp /var/www/eventahub/eventaa-api/.env /var/backups/eventahub/.env
sudo cp /var/www/eventahub/eventaa-api/.env /var/www/eventahub/eventaa-api/.env.production
```

### 4. Deployment Process

The deployment happens automatically when you push to the `main` branch:

1. **Build Phase**:
   - Validates composer dependencies
   - Installs PHP dependencies (production only)
   - Builds frontend assets
   - Creates deployment archive

2. **Deploy Phase**:
   - Creates backup of current application
   - Stops running services
   - Deploys new code
   - Runs Laravel deployment tasks
   - Restarts services

3. **Health Check**:
   - Verifies application is responding
   - Reports deployment status

### 5. Manual Deployment

You can also trigger deployment manually:

1. Go to **Actions** tab in GitHub
2. Select **Deploy to VPS** workflow
3. Click **Run workflow**
4. Select `main` branch and run

### 6. Rollback Process

If deployment fails, you can rollback using the backup:

```bash
# On your VPS
sudo systemctl stop eventahub-app eventahub-reverb
cd /var/www/eventahub
sudo tar -xzf /var/backups/eventahub/eventahub-backup-YYYYMMDD_HHMMSS.tar.gz
sudo chown -R www-data:www-data eventaa-api
sudo systemctl start eventahub-app eventahub-reverb
```

### 7. Monitoring

Check deployment status and logs:

```bash
# Service status
sudo systemctl status eventahub-app
sudo systemctl status eventahub-reverb

# Application logs
sudo journalctl -u eventahub-app -f
sudo journalctl -u eventahub-reverb -f

# Nginx logs
sudo tail -f /var/log/nginx/error.log
sudo tail -f /var/log/nginx/access.log
```

## Deployment Features

- ✅ Automatic backup before deployment
- ✅ Zero-downtime deployment process
- ✅ Automatic rollback on failure
- ✅ Health checks after deployment
- ✅ Proper Laravel optimization
- ✅ Database migrations
- ✅ Asset compilation
- ✅ Cache clearing and optimization
- ✅ Queue worker restart
- ✅ Service management

## Troubleshooting

### Common Issues

1. **SSH Connection Failed**:
   - Verify VPS_HOST, VPS_USERNAME, and VPS_SSH_KEY secrets
   - Ensure SSH key has proper permissions
   - Check VPS firewall settings

2. **Permission Denied**:
   - Ensure deployment user has sudo privileges
   - Check file permissions on VPS

3. **Service Start Failed**:
   - Check systemd service configuration
   - Verify environment file exists
   - Check database connectivity

4. **Health Check Failed**:
   - Verify application configuration
   - Check nginx configuration
   - Review application logs

### Getting Help

1. Check the GitHub Actions workflow logs
2. SSH into your VPS and check service status
3. Review application and system logs
4. Verify environment configuration

## Security Notes

- SSH keys should be unique for deployment
- Never commit sensitive information to the repository
- Regularly rotate SSH keys and secrets
- Monitor deployment logs for suspicious activity
- Keep your VPS system updated
