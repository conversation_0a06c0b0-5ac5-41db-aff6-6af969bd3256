#!/bin/bash

# EventaHub Management Script
# Simple script to manage EventaHub services

set -e

APP_NAME="eventahub"
APP_DIR="/var/www/eventahub"

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

show_help() {
    echo -e "${BLUE}EventaHub Management Script${NC}"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start     - Start all EventaHub services"
    echo "  stop      - Stop all EventaHub services"
    echo "  restart   - Restart all EventaHub services"
    echo "  status    - Show status of all services"
    echo "  deploy    - Deploy latest changes"
    echo "  logs      - Show logs for all services"
    echo "  queue     - Show queue status and workers"
    echo "  clear     - Clear caches and optimize"
    echo "  backup    - Create application backup"
    echo "  help      - Show this help message"
    echo ""
}

start_services() {
    print_status "Starting EventaHub services..."
    systemctl start ${APP_NAME}.target
    systemctl start nginx
    supervisorctl start all
    print_status "All services started successfully!"
}

stop_services() {
    print_status "Stopping EventaHub services..."
    supervisorctl stop all
    systemctl stop ${APP_NAME}.target
    print_status "All services stopped successfully!"
}

restart_services() {
    print_status "Restarting EventaHub services..."
    supervisorctl restart all
    systemctl restart ${APP_NAME}.target
    systemctl restart nginx
    print_status "All services restarted successfully!"
}

show_status() {
    echo -e "${BLUE}=== EventaHub Services Status ===${NC}"
    echo ""
    echo -e "${YELLOW}Systemd Services:${NC}"
    systemctl is-active ${APP_NAME}-app.service && echo -e "  ✓ Laravel App: ${GREEN}Active${NC}" || echo -e "  ✗ Laravel App: ${RED}Inactive${NC}"
    systemctl is-active ${APP_NAME}-reverb.service && echo -e "  ✓ Reverb WebSocket: ${GREEN}Active${NC}" || echo -e "  ✗ Reverb WebSocket: ${RED}Inactive${NC}"
    systemctl is-active ${APP_NAME}-queue.service && echo -e "  ✓ Queue Worker: ${GREEN}Active${NC}" || echo -e "  ✗ Queue Worker: ${RED}Inactive${NC}"
    systemctl is-active nginx.service && echo -e "  ✓ Nginx: ${GREEN}Active${NC}" || echo -e "  ✗ Nginx: ${RED}Inactive${NC}"

    echo ""
    echo -e "${YELLOW}Supervisor Workers:${NC}"
    supervisorctl status | grep ${APP_NAME}

    echo ""
    echo -e "${YELLOW}Port Status:${NC}"
    netstat -tulpn | grep -E ':80|:8000|:7001 ' || echo "  No services listening on expected ports"
}

deploy_app() {
    print_status "Deploying EventaHub..."

    if [ ! -f "$APP_DIR/deploy.sh" ]; then
        print_error "Deploy script not found at $APP_DIR/deploy.sh"
        exit 1
    fi

    cd $APP_DIR
    ./deploy.sh
    print_status "Deployment completed!"
}

show_logs() {
    echo -e "${BLUE}=== Recent Logs ===${NC}"
    echo ""
    echo -e "${YELLOW}Laravel App Logs:${NC}"
    journalctl -u ${APP_NAME}-app.service --no-pager -n 10

    echo ""
    echo -e "${YELLOW}Reverb WebSocket Logs:${NC}"
    journalctl -u ${APP_NAME}-reverb.service --no-pager -n 10

    echo ""
    echo -e "${YELLOW}Queue Worker Logs:${NC}"
    journalctl -u ${APP_NAME}-queue.service --no-pager -n 10

    echo ""
    echo -e "${YELLOW}Application Logs:${NC}"
    if [ -f "$APP_DIR/storage/logs/laravel.log" ]; then
        tail -n 10 $APP_DIR/storage/logs/laravel.log
    else
        echo "No application logs found"
    fi
}

show_queue_status() {
    echo -e "${BLUE}=== Queue Status ===${NC}"
    echo ""

    if [ -d "$APP_DIR" ]; then
        cd $APP_DIR
        echo -e "${YELLOW}Queue Statistics:${NC}"
        php artisan queue:monitor

        echo ""
        echo -e "${YELLOW}Failed Jobs:${NC}"
        php artisan queue:failed

        echo ""
        echo -e "${YELLOW}Queue Workers:${NC}"
        supervisorctl status | grep queue
    else
        print_error "Application directory not found: $APP_DIR"
    fi
}

clear_caches() {
    print_status "Clearing caches and optimizing..."

    if [ -d "$APP_DIR" ]; then
        cd $APP_DIR
        php artisan config:clear
        php artisan route:clear
        php artisan view:clear
        php artisan cache:clear

        php artisan config:cache
        php artisan route:cache
        php artisan view:cache

        composer dump-autoload --optimize

        print_status "Caches cleared and optimized!"
    else
        print_error "Application directory not found: $APP_DIR"
    fi
}

backup_app() {
    print_status "Creating application backup..."

    BACKUP_DIR="/var/backups/eventahub"
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    BACKUP_FILE="$BACKUP_DIR/eventahub_backup_$TIMESTAMP.tar.gz"

    mkdir -p $BACKUP_DIR

    # Backup application files (excluding vendor and node_modules)
    tar -czf $BACKUP_FILE -C $(dirname $APP_DIR) \
        --exclude='vendor' \
        --exclude='node_modules' \
        --exclude='storage/logs' \
        --exclude='storage/framework/cache' \
        --exclude='storage/framework/sessions' \
        --exclude='storage/framework/views' \
        $(basename $APP_DIR)

    print_status "Backup created: $BACKUP_FILE"

    # Backup database if possible
    if [ -f "$APP_DIR/.env" ]; then
        DB_NAME=$(grep DB_DATABASE $APP_DIR/.env | cut -d '=' -f2)
        DB_USER=$(grep DB_USERNAME $APP_DIR/.env | cut -d '=' -f2)
        DB_PASS=$(grep DB_PASSWORD $APP_DIR/.env | cut -d '=' -f2)

        if [ ! -z "$DB_NAME" ]; then
            DB_BACKUP="$BACKUP_DIR/database_backup_$TIMESTAMP.sql"
            mysqldump -u$DB_USER -p$DB_PASS $DB_NAME > $DB_BACKUP 2>/dev/null || true
            if [ -f "$DB_BACKUP" ]; then
                print_status "Database backup created: $DB_BACKUP"
            fi
        fi
    fi
}

# Main script logic
case "${1:-help}" in
    start)
        start_services
        ;;
    stop)
        stop_services
        ;;
    restart)
        restart_services
        ;;
    status)
        show_status
        ;;
    deploy)
        deploy_app
        ;;
    logs)
        show_logs
        ;;
    queue)
        show_queue_status
        ;;
    clear)
        clear_caches
        ;;
    backup)
        backup_app
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
